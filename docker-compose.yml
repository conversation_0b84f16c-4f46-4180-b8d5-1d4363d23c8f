services:
  postgres:
    image: postgres:alpine
    container_name: pharmacy-postgres
    restart: unless-stopped
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_DB: apotek
      POSTGRES_MULTIPLE_DATABASES: apotek,apotek_test
      POSTGRES_HOST_AUTH_METHOD: password
    ports:
      - "5432:5432"
    volumes:
      - ./volumes/postgres:/var/lib/postgresql/data
      - ./docker-init/postgres:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # redis:
  #   image: redis:alpine
  #   container_name: apotek-redis
  #   restart: unless-stopped
  #   ports:
  #     - "6379:6379"
  #   volumes:
  #     - ./volumes/redis:/data
  #   command: redis-server --appendonly yes
  #   healthcheck:
  #     test: ["CMD", "redis-cli", "ping"]
  #     interval: 10s
  #     timeout: 5s
  #     retries: 5

volumes:
  postgres:
    driver: local
  redis:
    driver: local
