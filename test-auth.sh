#!/bin/bash

# Test script for Apotek App Authentication System
echo "🧪 Testing Apotek App Authentication System"
echo "============================================"

API_URL="http://localhost:3001/api"
TEST_EMAIL="<EMAIL>"
TEST_PASSWORD="TestPassword123"
TEST_FIRST_NAME="Staff"
TEST_LAST_NAME="Apotek"

echo ""
echo "1. Testing API Health Check..."
curl -s -X GET $API_URL | grep -q "Hello World" && echo "✅ API is running" || echo "❌ API is not responding"

echo ""
echo "2. Testing Apotek Staff Registration..."
REGISTER_RESPONSE=$(curl -s -X POST $API_URL/auth/register \
  -H "Content-Type: application/json" \
  -d "{\"firstName\": \"$TEST_FIRST_NAME\", \"lastName\": \"$TEST_LAST_NAME\", \"email\": \"$TEST_EMAIL\", \"password\": \"$TEST_PASSWORD\", \"role\": \"PHARMACIST\"}")

if echo $REGISTER_RESPONSE | grep -q "accessToken"; then
    echo "✅ Apotek staff registration successful"
    ACCESS_TOKEN=$(echo $REGISTER_RESPONSE | grep -o '"accessToken":"[^"]*"' | cut -d'"' -f4)
else
    echo "❌ Apotek staff registration failed"
    echo "Response: $REGISTER_RESPONSE"
fi

echo ""
echo "3. Testing Apotek Staff Login..."
LOGIN_RESPONSE=$(curl -s -X POST $API_URL/auth/login \
  -H "Content-Type: application/json" \
  -d "{\"email\": \"$TEST_EMAIL\", \"password\": \"$TEST_PASSWORD\"}")

if echo $LOGIN_RESPONSE | grep -q "accessToken"; then
    echo "✅ Apotek staff login successful"
    ACCESS_TOKEN=$(echo $LOGIN_RESPONSE | grep -o '"accessToken":"[^"]*"' | cut -d'"' -f4)
else
    echo "❌ Apotek staff login failed"
    echo "Response: $LOGIN_RESPONSE"
fi

echo ""
echo "4. Testing Protected Route (/auth/me)..."
if [ ! -z "$ACCESS_TOKEN" ]; then
    ME_RESPONSE=$(curl -s -X GET $API_URL/auth/me \
      -H "Authorization: Bearer $ACCESS_TOKEN")
    
    if echo $ME_RESPONSE | grep -q "$TEST_EMAIL"; then
        echo "✅ Protected route access successful"
    else
        echo "❌ Protected route access failed"
        echo "Response: $ME_RESPONSE"
    fi
else
    echo "❌ No access token available for testing"
fi

echo ""
echo "5. Testing Invalid Login..."
INVALID_LOGIN=$(curl -s -X POST $API_URL/auth/login \
  -H "Content-Type: application/json" \
  -d "{\"email\": \"$TEST_EMAIL\", \"password\": \"wrongpassword\"}")

if echo $INVALID_LOGIN | grep -q "Invalid credentials"; then
    echo "✅ Invalid login properly rejected"
else
    echo "❌ Invalid login not properly handled"
    echo "Response: $INVALID_LOGIN"
fi

echo ""
echo "6. Testing Apotek Settings API..."
if [ ! -z "$ACCESS_TOKEN" ]; then
    SETTINGS_RESPONSE=$(curl -s -X GET $API_URL/settings/pharmacy \
      -H "Authorization: Bearer $ACCESS_TOKEN")

    if echo $SETTINGS_RESPONSE | grep -q "pharmacyName"; then
        echo "✅ Apotek settings API working"
    else
        echo "❌ Apotek settings API failed"
        echo "Response: $SETTINGS_RESPONSE"
    fi
else
    echo "❌ No access token available for settings test"
fi

echo ""
echo "7. Testing Apotek App Frontend..."
if curl -s http://localhost:3002 | grep -q "Apotek"; then
    echo "✅ Apotek App frontend is running and accessible"
else
    echo "❌ Apotek App frontend is not responding"
fi

echo ""
echo "============================================"
echo "🎉 Apotek App Authentication System Test Complete!"
echo ""
echo "Frontend: http://localhost:3002"
echo "Backend API: http://localhost:3001/api"
echo ""
echo "Test Apotek Staff Credentials:"
echo "Email: $TEST_EMAIL"
echo "Password: $TEST_PASSWORD"
echo ""
echo "You can now:"
echo "1. Open http://localhost:3002 in your browser"
echo "2. Register a new apotek staff account or use the test credentials"
echo "3. Test the complete authentication flow and dashboard"
echo "4. Access the comprehensive dashboard with Indonesian interface"
echo "5. Navigate through different apotek management sections"
echo "6. Begin building medicine inventory, sales, and other apotek features"
