---
description: 
globs: 
alwaysApply: true
---
- always check existing class/functions/enums/types and use it, dont try to be redundant
- use bun as package manager
- dont use npx, npm, pnpm
- do not redundant
- Use indonesian for UI text such as headers, label, pharagraph, title, etc
- Use english for table name, code, comments, and other things for codes
- Do not run `bun run build` for frontend and backend, just use `tsc --noEmit`
- Do not run `bun run dev` for frontend and backend, just use `tsc --noEmit`
- Beware with routes order in NestJS
- Use your diagnostics tools every finished editing files
- Beware editing with large amount of codes (more than 300 lines of codes)
- Do not mixing test expectations with production code