import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Params } from 'nestjs-pino';
import pino from 'pino';
import * as path from 'path';
import * as fs from 'fs';

/**
 * Logger configuration interface
 */
export interface LoggerConfig {
  level: string;
  environment: string;
  enableLogtail: boolean;
  logtailToken?: string;
  logtailIngestingHost?: string;
  prettyPrint: boolean;
  redact: string[];
  fileRotation: {
    enabled: boolean;
    directory: string;
    filename: string;
    maxSize: string;
    frequency: 'daily' | 'hourly';
    retentionDays: number;
  };
  developmentFileLogging: boolean;
}

/**
 * Logger configuration service for Pino with Betterstack Logtail integration
 */
@Injectable()
export class LoggerConfigService {
  constructor(private readonly configService: ConfigService) {
    this.ensureLogDirectoryExists();
  }

  /**
   * Ensure log directory exists
   */
  private ensureLogDirectoryExists(): void {
    const config = this.getLoggerConfig();
    if (config.fileRotation.enabled) {
      try {
        if (!fs.existsSync(config.fileRotation.directory)) {
          fs.mkdirSync(config.fileRotation.directory, { recursive: true });
        }
      } catch (error) {
        console.error('Failed to create log directory:', error);
      }
    }
  }

  /**
   * Get logger configuration based on environment
   */
  getLoggerConfig(): LoggerConfig {
    const environment = this.configService.get<string>('NODE_ENV', 'development');
    const isProduction = environment === 'production';
    const isTest = environment === 'test';
    const isDevelopment = environment === 'development';

    // In development, log ALL levels to files for comprehensive debugging
    const developmentLevel = 'trace';
    const productionLevel = 'info';
    const testLevel = 'error';

    const logLevel = this.configService.get<string>(
      'LOG_LEVEL',
      isTest ? testLevel : isProduction ? productionLevel : developmentLevel
    );

    const logDirectory = this.configService.get<string>('LOG_DIRECTORY', './logs');
    const timestamp = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
    const filename = `pharmacy-backend-${timestamp}.log`;

    return {
      level: logLevel,
      environment,
      enableLogtail: isProduction && !!this.configService.get<string>('LOGTAIL_TOKEN'),
      logtailToken: this.configService.get<string>('LOGTAIL_TOKEN'),
      logtailIngestingHost: this.configService.get<string>('LOGTAIL_INGESTING_HOST'),
      prettyPrint: !isProduction && !isTest,
      developmentFileLogging: isDevelopment,
      fileRotation: {
        enabled: !isTest, // Disable file logging in tests
        directory: logDirectory,
        filename,
        maxSize: this.configService.get<string>('LOG_MAX_SIZE', '10m'),
        frequency: this.configService.get<'daily' | 'hourly'>('LOG_FREQUENCY', 'daily'),
        retentionDays: this.configService.get<number>('LOG_RETENTION_DAYS', 30),
      },
      redact: [
        'password',
        'token',
        'authorization',
        'cookie',
        'secret',
        'key',
        'req.headers.authorization',
        'req.headers.cookie',
        'res.headers["set-cookie"]',
        'req.body.password',
        'req.body.confirmPassword',
        'req.body.oldPassword',
        'req.body.newPassword',
      ],
    };
  }

  /**
   * Create transport targets for different environments
   */
  private createTransportTargets(config: LoggerConfig): any[] {
    const targets: any[] = [];

    // Console output for development
    if (config.prettyPrint) {
      targets.push({
        target: 'pino-pretty',
        level: config.level,
        options: {
          colorize: true,
          translateTime: 'yyyy-mm-dd HH:MM:ss',
          ignore: 'pid,hostname',
          singleLine: false,
          hideObject: false,
          messageFormat: '{service} [{level}] {msg}',
        },
      });
    } else {
      // Production console output (structured JSON)
      targets.push({
        target: 'pino/file',
        level: config.level,
        options: { destination: 1 }, // stdout
      });
    }

    // File rotation for all environments except test
    if (config.fileRotation.enabled) {
      const logFilePath = path.join(config.fileRotation.directory, config.fileRotation.filename);

      targets.push({
        target: 'pino-roll',
        level: config.developmentFileLogging ? 'trace' : config.level,
        options: {
          file: logFilePath,
          frequency: config.fileRotation.frequency,
          size: config.fileRotation.maxSize,
          mkdir: true,
          sync: false, // Async for better performance
        },
      });
    }

    // Betterstack Logtail for production
    if (config.enableLogtail && config.logtailToken) {
      targets.push({
        target: '@logtail/pino',
        level: config.level,
        options: {
          sourceToken: config.logtailToken,
          ...(config.logtailIngestingHost && {
            endpoint: config.logtailIngestingHost,
          }),
        },
      });
    }

    return targets;
  }

  /**
   * Create Pino logger options for NestJS
   */
  createPinoOptions(): Params {
    const config = this.getLoggerConfig();

    // Base configuration for pinoHttp
    const pinoHttpOptions: any = {
      level: config.level,
      redact: config.redact,
      serializers: {
        req: (req: any) => ({
          id: req.id,
          method: req.method,
          url: req.url,
          headers: {
            host: req.headers?.host,
            'user-agent': req.headers?.['user-agent'],
            'content-type': req.headers?.['content-type'],
          },
          remoteAddress: req.remoteAddress,
          remotePort: req.remotePort,
        }),
        res: (res: any) => ({
          statusCode: res.statusCode,
          headers: {
            'content-type': res.headers?.['content-type'],
            'content-length': res.headers?.['content-length'],
          },
        }),
        err: pino.stdSerializers.err,
      },
      formatters: {
        level: (label: string) => ({ level: label }),
        log: (object: any) => ({
          ...object,
          environment: config.environment,
          service: 'pharmacy-backend',
          timestamp: new Date().toISOString(),
        }),
      },
      autoLogging: true,
      quietReqLogger: config.environment === 'test',
      customLogLevel: (req: any, res: any, err: any) => {
        if (res.statusCode >= 400 && res.statusCode < 500) {
          return 'warn';
        } else if (res.statusCode >= 500 || err) {
          return 'error';
        }
        return 'info';
      },
      customSuccessMessage: (req: any, res: any) => {
        return `${req.method} ${req.url} - ${res.statusCode}`;
      },
      customErrorMessage: (req: any, res: any, err: any) => {
        return `${req.method} ${req.url} - ${res.statusCode} - ${err.message}`;
      },
      customAttributeKeys: {
        req: 'request',
        res: 'response',
        err: 'error',
        responseTime: 'duration',
      },
    };

    // Configure transport based on environment
    const targets = this.createTransportTargets(config);

    if (targets.length === 1) {
      // Single target: keep formatters
      pinoHttpOptions.transport = targets[0];
    } else {
      // Multiple targets: remove formatters to avoid conflicts
      pinoHttpOptions.transport = { targets };
      delete pinoHttpOptions.formatters;
    }

    return {
      pinoHttp: pinoHttpOptions,
    };
  }

  /**
   * Create standalone Pino logger instance
   */
  createStandaloneLogger(context?: string): pino.Logger {
    const config = this.getLoggerConfig();

    const baseOptions: pino.LoggerOptions = {
      level: config.level as pino.Level,
      redact: config.redact,
      base: {
        service: 'pharmacy-backend',
        environment: config.environment,
        context: context || 'Application',
      },
      serializers: {
        err: pino.stdSerializers.err,
      },
      formatters: {
        level: (label: string) => ({ level: label }),
        log: (object: any) => ({
          ...object,
          timestamp: new Date().toISOString(),
        }),
      },
    };

    // Configure transport using the same logic as HTTP logger
    const targets = this.createTransportTargets(config);

    if (targets.length === 1) {
      // Single target: keep formatters
      return pino({
        ...baseOptions,
        transport: targets[0],
      });
    } else {
      // Multiple targets: remove formatters to avoid conflicts
      const { formatters, ...optionsWithoutFormatters } = baseOptions;
      return pino({
        ...optionsWithoutFormatters,
        transport: { targets },
      });
    }
  }
}

/**
 * Factory function to create logger options
 */
export const createLoggerOptions = (configService: ConfigService): Params => {
  const loggerConfigService = new LoggerConfigService(configService);
  return loggerConfigService.createPinoOptions();
};

/**
 * Factory function to create standalone logger
 */
export const createStandaloneLogger = (configService: ConfigService, context?: string): pino.Logger => {
  const loggerConfigService = new LoggerConfigService(configService);
  return loggerConfigService.createStandaloneLogger(context);
};
