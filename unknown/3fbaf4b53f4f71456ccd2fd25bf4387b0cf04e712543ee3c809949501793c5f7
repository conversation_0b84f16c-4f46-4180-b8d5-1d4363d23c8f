import { defineConfig } from 'cypress'

export default defineConfig({
  // Global configuration
  viewportWidth: 1280,
  viewportHeight: 720,
  video: true,
  screenshotOnRunFailure: true,
  defaultCommandTimeout: 10000,
  requestTimeout: 10000,
  responseTimeout: 10000,
  
  // E2E Testing configuration
  e2e: {
    baseUrl: 'http://localhost:3000',
    specPattern: 'cypress/e2e/**/*.cy.{js,jsx,ts,tsx}',
    supportFile: 'cypress/support/e2e.ts',
    fixturesFolder: 'cypress/fixtures',
    screenshotsFolder: 'cypress/screenshots',
    videosFolder: 'cypress/videos',
    downloadsFolder: 'cypress/downloads',
    
    // Test isolation
    testIsolation: true,
    
    // Setup node events
    setupNodeEvents(on, config) {
      // implement node event listeners here
      
      // Task for database seeding/cleanup
      on('task', {
        // Database tasks
        'db:seed': () => {
          // Implement database seeding logic
          return null
        },
        'db:cleanup': () => {
          // Implement database cleanup logic
          return null
        },
        
        // Log tasks
        log(message) {
          console.log(message)
          return null
        },
        
        // File system tasks
        'readFile': (filename) => {
          return new Promise((resolve, reject) => {
            const fs = require('fs')
            fs.readFile(filename, 'utf8', (err: any, data: any) => {
              if (err) {
                reject(err)
              } else {
                resolve(data)
              }
            })
          })
        },
      })
      
      return config
    },
    
    // Environment variables
    env: {
      apiUrl: 'http://localhost:3001',
      coverage: false,
    },
  },

  // Component Testing configuration
  component: {
    devServer: {
      framework: 'next',
      bundler: 'webpack',
    },
    specPattern: 'src/**/*.cy.{js,jsx,ts,tsx}',
    supportFile: 'cypress/support/component.ts',
    fixturesFolder: 'cypress/fixtures',
    screenshotsFolder: 'cypress/screenshots',
    videosFolder: 'cypress/videos',
    
    // Setup node events for component testing
    setupNodeEvents(on, config) {
      // implement node event listeners here
      return config
    },
    
    // Environment variables for component testing
    env: {
      coverage: false,
    },
  },
  
  // Reporter configuration
  reporter: 'spec',
  reporterOptions: {
    mochaFile: 'cypress/results/test-results-[hash].xml',
    toConsole: true,
  },
  
  // Retry configuration
  retries: {
    runMode: 2,
    openMode: 0,
  },
  
  // Experimental features
  experimentalStudio: true,
  experimentalWebKitSupport: false,
})
