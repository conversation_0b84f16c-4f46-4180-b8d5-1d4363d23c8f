const fs = require('fs-extra');
const path = require('path');
const sqlite3 = require('sqlite3').verbose();

const DB_PATH = path.join(__dirname, '../data/tracking.db');
const BACKUP_DIR = path.join(__dirname, '../backups');

class BackupManager {
    constructor() {
        this.db = null;
    }

    async connect() {
        return new Promise((resolve, reject) => {
            this.db = new sqlite3.Database(DB_PATH, (err) => {
                if (err) {
                    reject(err);
                } else {
                    console.log('Connected to tracking database for backup');
                    resolve();
                }
            });
        });
    }

    async createBackup() {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupFileName = `tracking-backup-${timestamp}.json`;
        const backupPath = path.join(BACKUP_DIR, backupFileName);

        // Ensure backup directory exists
        fs.ensureDirSync(BACKUP_DIR);

        try {
            // Get all tasks
            const tasks = await this.getAllTasks();
            
            // Get progress statistics
            const progressStats = await this.getProgressStats();

            // Create backup data
            const backupData = {
                backup_info: {
                    created_at: new Date().toISOString(),
                    version: '1.0.0',
                    total_tasks: tasks.length,
                    database_path: DB_PATH
                },
                tasks: tasks,
                progress_statistics: progressStats
            };

            // Write backup file
            await fs.writeJson(backupPath, backupData, { spaces: 2 });

            console.log(`Backup created successfully: ${backupFileName}`);
            console.log(`Backup location: ${backupPath}`);
            console.log(`Total tasks backed up: ${tasks.length}`);

            return {
                success: true,
                backup_file: backupFileName,
                backup_path: backupPath,
                total_tasks: tasks.length
            };

        } catch (error) {
            console.error('Error creating backup:', error);
            throw error;
        }
    }

    async getAllTasks() {
        return new Promise((resolve, reject) => {
            const query = 'SELECT * FROM tasks ORDER BY phase, category, id';
            
            this.db.all(query, [], (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(rows);
                }
            });
        });
    }

    async getProgressStats() {
        return new Promise((resolve, reject) => {
            const query = `
                SELECT 
                    phase,
                    COUNT(*) as total_tasks,
                    SUM(is_completed) as completed_tasks,
                    ROUND(AVG(progress_percentage), 2) as avg_progress,
                    ROUND((SUM(is_completed) * 100.0 / COUNT(*)), 2) as completion_percentage
                FROM tasks 
                GROUP BY phase 
                ORDER BY phase
            `;
            
            this.db.all(query, [], (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(rows);
                }
            });
        });
    }

    async restoreFromBackup(backupFilePath) {
        try {
            console.log(`Restoring from backup: ${backupFilePath}`);
            
            // Read backup file
            const backupData = await fs.readJson(backupFilePath);
            
            if (!backupData.tasks || !Array.isArray(backupData.tasks)) {
                throw new Error('Invalid backup file format');
            }

            // Clear existing tasks
            await this.clearAllTasks();
            
            // Insert tasks from backup
            const result = await this.insertTasks(backupData.tasks);
            
            console.log(`Restore completed successfully`);
            console.log(`Restored ${result.inserted} tasks, ${result.errors} errors`);
            
            return result;

        } catch (error) {
            console.error('Error restoring from backup:', error);
            throw error;
        }
    }

    async clearAllTasks() {
        return new Promise((resolve, reject) => {
            this.db.run('DELETE FROM tasks', (err) => {
                if (err) {
                    reject(err);
                } else {
                    console.log('Cleared all existing tasks');
                    resolve();
                }
            });
        });
    }

    async insertTasks(tasks) {
        return new Promise((resolve, reject) => {
            const stmt = this.db.prepare(`
                INSERT INTO tasks (
                    phase, category, task_description, is_completed, 
                    completion_date, progress_percentage, notes, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            `);

            let inserted = 0;
            let errors = 0;

            tasks.forEach((task) => {
                stmt.run([
                    task.phase,
                    task.category,
                    task.task_description,
                    task.is_completed ? 1 : 0,
                    task.completion_date,
                    task.progress_percentage,
                    task.notes,
                    task.created_at || new Date().toISOString(),
                    task.updated_at || new Date().toISOString()
                ], (err) => {
                    if (err) {
                        console.error('Error inserting task:', err);
                        errors++;
                    } else {
                        inserted++;
                    }

                    if (inserted + errors === tasks.length) {
                        stmt.finalize();
                        resolve({ inserted, errors });
                    }
                });
            });
        });
    }

    async listBackups() {
        try {
            if (!fs.existsSync(BACKUP_DIR)) {
                return [];
            }

            const files = await fs.readdir(BACKUP_DIR);
            const backupFiles = files
                .filter(file => file.startsWith('tracking-backup-') && file.endsWith('.json'))
                .map(file => {
                    const filePath = path.join(BACKUP_DIR, file);
                    const stats = fs.statSync(filePath);
                    return {
                        filename: file,
                        path: filePath,
                        size: stats.size,
                        created: stats.birthtime,
                        modified: stats.mtime
                    };
                })
                .sort((a, b) => b.created - a.created); // Sort by creation date, newest first

            return backupFiles;
        } catch (error) {
            console.error('Error listing backups:', error);
            return [];
        }
    }

    close() {
        if (this.db) {
            this.db.close();
        }
    }
}

// Command line interface
async function main() {
    const command = process.argv[2];
    const backupManager = new BackupManager();

    try {
        await backupManager.connect();

        switch (command) {
            case 'create':
                await backupManager.createBackup();
                break;

            case 'list':
                const backups = await backupManager.listBackups();
                console.log('\nAvailable backups:');
                if (backups.length === 0) {
                    console.log('No backups found');
                } else {
                    backups.forEach((backup, index) => {
                        console.log(`${index + 1}. ${backup.filename}`);
                        console.log(`   Created: ${backup.created.toLocaleString()}`);
                        console.log(`   Size: ${(backup.size / 1024).toFixed(2)} KB`);
                        console.log('');
                    });
                }
                break;

            case 'restore':
                const backupFile = process.argv[3];
                if (!backupFile) {
                    console.error('Please specify backup file path');
                    process.exit(1);
                }
                await backupManager.restoreFromBackup(backupFile);
                break;

            default:
                console.log('Usage:');
                console.log('  node backup.js create          - Create new backup');
                console.log('  node backup.js list            - List available backups');
                console.log('  node backup.js restore <file>  - Restore from backup file');
                break;
        }

    } catch (error) {
        console.error('Backup operation failed:', error);
        process.exit(1);
    } finally {
        backupManager.close();
    }
}

// Run if called directly
if (require.main === module) {
    main();
}

module.exports = { BackupManager };
