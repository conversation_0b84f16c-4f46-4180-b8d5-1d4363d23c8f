const fs = require('fs-extra');
const path = require('path');
const sqlite3 = require('sqlite3').verbose();

const DB_PATH = path.join(__dirname, '../data/tracking.db');

function initializeDatabase() {
  return new Promise((resolve, reject) => {
    console.log('Initializing development tracking database...');
    
    // Ensure data directory exists
    fs.ensureDirSync(path.dirname(DB_PATH));
    
    // Remove existing database if it exists
    if (fs.existsSync(DB_PATH)) {
      console.log('Removing existing database...');
      fs.removeSync(DB_PATH);
    }
    
    // Create new database
    const db = new sqlite3.Database(DB_PATH, (err) => {
      if (err) {
        console.error('Error creating database:', err);
        reject(err);
        return;
      }
      
      console.log('Database created successfully');
      
      // Create tables
      const createTasksTable = `
        CREATE TABLE tasks (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          phase TEXT NOT NULL,
          category TEXT NOT NULL,
          task_description TEXT NOT NULL,
          is_completed BOOLEAN DEFAULT 0,
          completion_date DATE,
          progress_percentage INTEGER DEFAULT 0,
          notes TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `;
      
      db.run(createTasksTable, (err) => {
        if (err) {
          console.error('Error creating tasks table:', err);
          reject(err);
        } else {
          console.log('Tasks table created successfully');
          
          // Create indexes for better performance
          const createIndexes = [
            'CREATE INDEX idx_tasks_phase ON tasks(phase)',
            'CREATE INDEX idx_tasks_category ON tasks(category)',
            'CREATE INDEX idx_tasks_completed ON tasks(is_completed)',
            'CREATE INDEX idx_tasks_progress ON tasks(progress_percentage)'
          ];
          
          let indexesCreated = 0;
          createIndexes.forEach((indexSQL, i) => {
            db.run(indexSQL, (err) => {
              if (err) {
                console.error(`Error creating index ${i + 1}:`, err);
              } else {
                console.log(`Index ${i + 1} created successfully`);
              }
              
              indexesCreated++;
              if (indexesCreated === createIndexes.length) {
                db.close((err) => {
                  if (err) {
                    console.error('Error closing database:', err);
                    reject(err);
                  } else {
                    console.log('Database initialization completed successfully');
                    resolve();
                  }
                });
              }
            });
          });
        }
      });
    });
  });
}

// Run if called directly
if (require.main === module) {
  initializeDatabase()
    .then(() => {
      console.log('\n✅ Database initialization completed!');
      console.log('Next steps:');
      console.log('1. Run "npm run sync-tasks" to populate with documentation data');
      console.log('2. Run "npm start" to start the tracking server');
      process.exit(0);
    })
    .catch((err) => {
      console.error('\n❌ Database initialization failed:', err);
      process.exit(1);
    });
}

module.exports = { initializeDatabase };
