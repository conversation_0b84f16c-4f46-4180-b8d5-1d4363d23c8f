const fs = require('fs-extra');
const path = require('path');
const sqlite3 = require('sqlite3').verbose();

// Path to the pharmacy workflow documentation
const DOCS_PATH = path.join(__dirname, '../../../PHARMACY_WORKFLOW_DOCUMENTATION.md');
const DB_PATH = path.join(__dirname, '../data/tracking.db');

class TaskParser {
  constructor() {
    this.tasks = [];
    this.currentPhase = '';
    this.currentCategory = '';
  }

  parseDocumentation() {
    console.log('Reading documentation file...');

    if (!fs.existsSync(DOCS_PATH)) {
      throw new Error(`Documentation file not found: ${DOCS_PATH}`);
    }

    const content = fs.readFileSync(DOCS_PATH, 'utf8');
    const lines = content.split('\n');

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      this.parseLine(line, i + 1);
    }

    console.log(`Parsed ${this.tasks.length} tasks from documentation`);
    return this.tasks;
  }

  parseLine(line, lineNumber) {
    // Skip empty lines
    if (!line) return;

    // Detect phase headers (Month X, Phase X, etc.)
    if (this.isPhaseHeader(line)) {
      this.currentPhase = this.extractPhase(line);
      console.log(`Found phase: ${this.currentPhase}`);
      return;
    }

    // Detect category headers (Database Foundation, API Endpoints, etc.)
    if (this.isCategoryHeader(line)) {
      this.currentCategory = this.extractCategory(line);
      return;
    }

    // Detect task items (checkbox items)
    if (this.isTaskItem(line)) {
      const task = this.parseTaskItem(line, lineNumber);
      if (task) {
        this.tasks.push(task);
      }
    }
  }

  isPhaseHeader(line) {
    // Match patterns like "#### **Month 1:", "### **Phase 1:", etc.
    return /^#{2,4}\s*\*\*(?:Month|Phase|Week)\s+\d+[AB]?[:\-]/.test(line) ||
      /^#{2,4}\s*\*\*(?:Month|Phase|Week)\s+\d+[AB]?\s*\*\*/.test(line);
  }

  extractPhase(line) {
    // Extract phase name from headers
    const match = line.match(/\*\*(Month|Phase|Week)\s+\d+[AB]?[:\-\s]/);
    if (match) {
      return match[0].replace(/[\*\:\-\s]+$/, '').replace(/^\*\*/, '');
    }
    return 'Unknown Phase';
  }

  isCategoryHeader(line) {
    // Match patterns like "- [x] **Database Foundation**", "- [ ] **API Endpoints**"
    return /^-\s*\[\s*[x\s]\s*\]\s*\*\*[^*]+\*\*/.test(line) ||
      /^\s*\*\*[^*]+\*\*\s*✅/.test(line) ||
      /^\s*\*\*[^*]+\*\*\s*$/.test(line);
  }

  extractCategory(line) {
    // Extract category name from bold text
    const match = line.match(/\*\*([^*]+)\*\*/);
    if (match) {
      return match[1].trim();
    }
    return 'General';
  }

  isTaskItem(line) {
    // Match checkbox items: "- [ ]" or "- [x]"
    return /^(\s*)-\s*\[\s*[x\s]\s*\]/.test(line);
  }

  parseTaskItem(line, lineNumber) {
    const checkboxMatch = line.match(/^(\s*)-\s*\[(\s*[x\s]\s*)\]\s*(.+)$/);
    if (!checkboxMatch) return null;

    const indent = checkboxMatch[1].length;
    const isCompleted = checkboxMatch[2].trim().toLowerCase() === 'x';
    let description = checkboxMatch[3].trim();

    // Remove markdown formatting and status indicators
    description = description
      .replace(/\*\*([^*]+)\*\*/g, '$1') // Remove bold
      .replace(/✅.*$/, '') // Remove completion indicators
      .replace(/\s*\*\*COMPLETED.*$/, '') // Remove completion status
      .trim();

    // Skip if description is empty after cleaning
    if (!description) return null;

    // Determine category based on context
    let category = this.currentCategory || 'General';

    // If no explicit category, try to infer from description
    if (category === 'General') {
      category = this.inferCategory(description);
    }

    return {
      phase: this.currentPhase || 'Unknown Phase',
      category: category,
      task_description: description,
      is_completed: isCompleted,
      completion_date: isCompleted ? new Date().toISOString().split('T')[0] : null,
      progress_percentage: isCompleted ? 100 : 0,
      notes: null,
      line_number: lineNumber,
      indent_level: Math.floor(indent / 2)
    };
  }

  inferCategory(description) {
    const desc = description.toLowerCase();

    if (desc.includes('database') || desc.includes('model') || desc.includes('migration')) {
      return 'Database Foundation';
    }
    if (desc.includes('api') || desc.includes('endpoint') || desc.includes('controller')) {
      return 'API Endpoints';
    }
    if (desc.includes('interface') || desc.includes('ui') || desc.includes('frontend')) {
      return 'Frontend Implementation';
    }
    if (desc.includes('test') || desc.includes('testing')) {
      return 'Testing';
    }
    if (desc.includes('service') || desc.includes('business logic')) {
      return 'Business Logic';
    }

    return 'General';
  }
}

class DatabaseManager {
  constructor() {
    this.db = null;
  }

  connect() {
    return new Promise((resolve, reject) => {
      this.db = new sqlite3.Database(DB_PATH, (err) => {
        if (err) {
          reject(err);
        } else {
          console.log('Connected to tracking database');
          resolve();
        }
      });
    });
  }

  clearTasks() {
    return new Promise((resolve, reject) => {
      this.db.run('DELETE FROM tasks', (err) => {
        if (err) {
          reject(err);
        } else {
          console.log('Cleared existing tasks');
          resolve();
        }
      });
    });
  }

  insertTasks(tasks) {
    return new Promise((resolve, reject) => {
      const stmt = this.db.prepare(`
        INSERT INTO tasks (
          phase, category, task_description, is_completed, 
          completion_date, progress_percentage, notes
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      `);

      let inserted = 0;
      let errors = 0;

      tasks.forEach((task) => {
        stmt.run([
          task.phase,
          task.category,
          task.task_description,
          task.is_completed ? 1 : 0,
          task.completion_date,
          task.progress_percentage,
          task.notes
        ], (err) => {
          if (err) {
            console.error('Error inserting task:', err);
            errors++;
          } else {
            inserted++;
          }

          if (inserted + errors === tasks.length) {
            stmt.finalize();
            console.log(`Inserted ${inserted} tasks, ${errors} errors`);
            resolve({ inserted, errors });
          }
        });
      });
    });
  }

  close() {
    if (this.db) {
      this.db.close();
    }
  }
}

// Main execution
async function syncTasks() {
  const parser = new TaskParser();
  const dbManager = new DatabaseManager();

  try {
    console.log('Starting task synchronization...');

    // Parse documentation
    const tasks = parser.parseDocumentation();

    if (tasks.length === 0) {
      console.log('No tasks found in documentation');
      return;
    }

    // Connect to database
    await dbManager.connect();

    // Clear existing tasks and insert new ones
    await dbManager.clearTasks();
    const result = await dbManager.insertTasks(tasks);

    console.log('\nSynchronization completed successfully!');
    console.log(`Total tasks processed: ${tasks.length}`);
    console.log(`Successfully inserted: ${result.inserted}`);
    console.log(`Errors: ${result.errors}`);

    // Show summary by phase
    console.log('\nTasks by phase:');
    const phaseStats = {};
    tasks.forEach(task => {
      if (!phaseStats[task.phase]) {
        phaseStats[task.phase] = { total: 0, completed: 0 };
      }
      phaseStats[task.phase].total++;
      if (task.is_completed) {
        phaseStats[task.phase].completed++;
      }
    });

    Object.entries(phaseStats).forEach(([phase, stats]) => {
      const percentage = ((stats.completed / stats.total) * 100).toFixed(1);
      console.log(`  ${phase}: ${stats.completed}/${stats.total} (${percentage}%)`);
    });

  } catch (error) {
    console.error('Error during synchronization:', error);
    process.exit(1);
  } finally {
    dbManager.close();
  }
}

// Run if called directly
if (require.main === module) {
  syncTasks();
}

module.exports = { TaskParser, DatabaseManager, syncTasks };
