const express = require('express');
const cors = require('cors');
const path = require('path');
const sqlite3 = require('sqlite3').verbose();
const fs = require('fs-extra');

const app = express();
const PORT = process.env.PORT || 3002;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, 'public')));

// Database connection
const dbPath = path.join(__dirname, 'data', 'tracking.db');
let db;

// Initialize database connection
function initDatabase() {
  return new Promise((resolve, reject) => {
    // Ensure data directory exists
    fs.ensureDirSync(path.dirname(dbPath));

    db = new sqlite3.Database(dbPath, (err) => {
      if (err) {
        console.error('Error opening database:', err);
        reject(err);
      } else {
        console.log('Connected to SQLite database');
        createTables().then(resolve).catch(reject);
      }
    });
  });
}

// Create database tables
function createTables() {
  return new Promise((resolve, reject) => {
    const createTasksTable = `
      CREATE TABLE IF NOT EXISTS tasks (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        phase TEXT NOT NULL,
        category TEXT NOT NULL,
        task_description TEXT NOT NULL,
        is_completed BOOLEAN DEFAULT 0,
        completion_date DATE,
        progress_percentage INTEGER DEFAULT 0,
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `;

    db.run(createTasksTable, (err) => {
      if (err) {
        console.error('Error creating tasks table:', err);
        reject(err);
      } else {
        console.log('Tasks table created or already exists');
        resolve();
      }
    });
  });
}

// API Routes

// Get all tasks with filtering
app.get('/api/tracking/tasks', (req, res) => {
  const { phase, category, completed } = req.query;

  let query = 'SELECT * FROM tasks WHERE 1=1';
  const params = [];

  if (phase) {
    query += ' AND phase = ?';
    params.push(phase);
  }

  if (category) {
    query += ' AND category = ?';
    params.push(category);
  }

  if (completed !== undefined) {
    query += ' AND is_completed = ?';
    params.push(completed === 'true' ? 1 : 0);
  }

  query += ' ORDER BY phase, category, id';

  db.all(query, params, (err, rows) => {
    if (err) {
      console.error('Error fetching tasks:', err);
      res.status(500).json({ error: 'Failed to fetch tasks' });
    } else {
      res.json(rows);
    }
  });
});

// Update task completion status and progress
app.patch('/api/tracking/tasks/:id', (req, res) => {
  const { id } = req.params;
  const { is_completed, completion_date, progress_percentage, notes } = req.body;

  const updates = [];
  const params = [];

  if (is_completed !== undefined) {
    updates.push('is_completed = ?');
    params.push(is_completed ? 1 : 0);
  }

  if (completion_date !== undefined) {
    updates.push('completion_date = ?');
    params.push(completion_date);
  }

  if (progress_percentage !== undefined) {
    updates.push('progress_percentage = ?');
    params.push(progress_percentage);
  }

  if (notes !== undefined) {
    updates.push('notes = ?');
    params.push(notes);
  }

  updates.push('updated_at = CURRENT_TIMESTAMP');
  params.push(id);

  const query = `UPDATE tasks SET ${updates.join(', ')} WHERE id = ?`;

  db.run(query, params, function (err) {
    if (err) {
      console.error('Error updating task:', err);
      res.status(500).json({ error: 'Failed to update task' });
    } else if (this.changes === 0) {
      res.status(404).json({ error: 'Task not found' });
    } else {
      res.json({ message: 'Task updated successfully', changes: this.changes });
    }
  });
});

// Get overall progress statistics by phase
app.get('/api/tracking/progress', (req, res) => {
  const query = `
    SELECT
      phase,
      COUNT(*) as total_tasks,
      SUM(is_completed) as completed_tasks,
      ROUND(AVG(progress_percentage), 2) as avg_progress,
      ROUND((SUM(is_completed) * 100.0 / COUNT(*)), 2) as completion_percentage
    FROM tasks
    GROUP BY phase
    ORDER BY phase
  `;

  db.all(query, [], (err, rows) => {
    if (err) {
      console.error('Error fetching progress:', err);
      res.status(500).json({ error: 'Failed to fetch progress statistics' });
    } else {
      // Sort phases in natural development order
      const sortedRows = rows.sort((a, b) => {
        const phaseOrder = {
          'Unknown Phase': 0,
          'Phase 1': 1,
          'Month 1': 2,
          'Month 2': 3,
          'Month 3': 4,
          'Phase 2': 5,
          'Month 4A': 6,
          'Month 4B': 7,
          'Month 5': 8,
          'Month 6A': 9,
          'Month 6B': 10,
          'Month 7': 11,
          'Month 8': 12,
          'Phase 3': 13,
          'Month 9': 14,
          'Month 10': 15,
          'Month 11': 16,
          'Phase 4': 17,
          'Month 12': 18,
          'Month 13': 19,
          'Month 14': 20
        };

        const orderA = phaseOrder[a.phase] !== undefined ? phaseOrder[a.phase] : 999;
        const orderB = phaseOrder[b.phase] !== undefined ? phaseOrder[b.phase] : 999;

        return orderA - orderB;
      });

      res.json(sortedRows);
    }
  });
});

// Re-sync tasks from documentation file
app.post('/api/tracking/sync', async (req, res) => {
  try {
    console.log('Starting task synchronization from API endpoint...');

    // Import the sync function
    const { syncTasks } = require('./scripts/sync-tasks');

    // Run synchronization
    await syncTasks();

    res.json({
      message: 'Tasks synchronized successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error during sync:', error);
    res.status(500).json({
      error: 'Failed to synchronize tasks',
      details: error.message
    });
  }
});

// Serve the main HTML page
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Something went wrong!' });
});

// Start server
initDatabase()
  .then(() => {
    app.listen(PORT, () => {
      console.log(`Development Tracking Server running on http://localhost:${PORT}`);
      console.log('Database initialized successfully');
    });
  })
  .catch((err) => {
    console.error('Failed to initialize database:', err);
    process.exit(1);
  });

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\nShutting down gracefully...');
  if (db) {
    db.close((err) => {
      if (err) {
        console.error('Error closing database:', err);
      } else {
        console.log('Database connection closed');
      }
      process.exit(0);
    });
  } else {
    process.exit(0);
  }
});
