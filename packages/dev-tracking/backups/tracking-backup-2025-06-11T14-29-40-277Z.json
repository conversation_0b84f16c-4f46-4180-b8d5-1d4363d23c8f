{"backup_info": {"created_at": "2025-06-11T14:29:40.281Z", "version": "1.0.0", "total_tasks": 234, "database_path": "/Users/<USER>/Documents/augment-projects/pharmacy-store/dev-tracking/data/tracking.db"}, "tasks": [{"id": 177, "phase": "Month 1", "category": "Basic Product Management", "task_description": "Product CRUD API endpoints with comprehensive validation and error handling", "is_completed": 1, "completion_date": "2025-06-11", "progress_percentage": 100, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 178, "phase": "Month 1", "category": "Basic Product Management", "task_description": "Advanced product management interface with Indonesian localization", "is_completed": 1, "completion_date": "2025-06-11", "progress_percentage": 100, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 179, "phase": "Month 1", "category": "Basic Product Management", "task_description": "Complete unit conversion functionality with real-time calculations and validation", "is_completed": 1, "completion_date": "2025-06-11", "progress_percentage": 100, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 180, "phase": "Month 1", "category": "Basic Product Management", "task_description": "Product search and filtering with multiple criteria and responsive design", "is_completed": 1, "completion_date": "2025-06-11", "progress_percentage": 100, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 169, "phase": "Month 1", "category": "Database Foundation", "task_description": "Product model implementation with Indonesian medicine classification", "is_completed": 1, "completion_date": "2025-06-11", "progress_percentage": 100, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 170, "phase": "Month 1", "category": "Database Foundation", "task_description": "ProductUnit and ProductUnitHierarchy models with 25+ Indonesian pharmacy units", "is_completed": 1, "completion_date": "2025-06-11", "progress_percentage": 100, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 171, "phase": "Month 1", "category": "Database Foundation", "task_description": "Advanced unit conversion logic with mathematical precision", "is_completed": 1, "completion_date": "2025-06-11", "progress_percentage": 100, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 172, "phase": "Month 1", "category": "Database Foundation", "task_description": "Database migrations and comprehensive Indonesian pharmacy seed data", "is_completed": 1, "completion_date": "2025-06-11", "progress_percentage": 100, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 173, "phase": "Month 1", "category": "Database Foundation", "task_description": "Indonesian medicine classification system (<PERSON><PERSON>, <PERSON><PERSON>, Jamu, etc.)", "is_completed": 1, "completion_date": "2025-06-11", "progress_percentage": 100, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 174, "phase": "Month 1", "category": "Database Foundation", "task_description": "BPOM registration tracking and regulatory symbols", "is_completed": 1, "completion_date": "2025-06-11", "progress_percentage": 100, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 175, "phase": "Month 1", "category": "Database Foundation", "task_description": "Removed barcode support as requested", "is_completed": 1, "completion_date": "2025-06-11", "progress_percentage": 100, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 176, "phase": "Month 1", "category": "Database Foundation", "task_description": "Comprehensive test suite with 100% passing unit conversion tests", "is_completed": 1, "completion_date": "2025-06-11", "progress_percentage": 100, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 6, "phase": "Month 1", "category": "Week 1-2: Database Foundation", "task_description": "Design and implement Product model with Indonesian medicine classification", "is_completed": 1, "completion_date": "2025-06-11", "progress_percentage": 100, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 7, "phase": "Month 1", "category": "Week 1-2: Database Foundation", "task_description": "Create ProductUnit and ProductUnitHierarchy models with 25+ Indonesian pharmacy units", "is_completed": 1, "completion_date": "2025-06-11", "progress_percentage": 100, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 8, "phase": "Month 1", "category": "Week 1-2: Database Foundation", "task_description": "Set up comprehensive unit conversion logic with mathematical precision", "is_completed": 1, "completion_date": "2025-06-11", "progress_percentage": 100, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 9, "phase": "Month 1", "category": "Week 1-2: Database Foundation", "task_description": "Create database migrations and seed data with realistic Indonesian pharmacy products", "is_completed": 1, "completion_date": "2025-06-11", "progress_percentage": 100, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 10, "phase": "Month 1", "category": "Week 1-2: Database Foundation", "task_description": "Remove barcode support as requested", "is_completed": 1, "completion_date": "2025-06-11", "progress_percentage": 100, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 11, "phase": "Month 1", "category": "Week 1-2: Database Foundation", "task_description": "Implement Indonesian medicine classification system (<PERSON><PERSON>, <PERSON><PERSON>, Jamu, etc.)", "is_completed": 1, "completion_date": "2025-06-11", "progress_percentage": 100, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 12, "phase": "Month 1", "category": "Week 1-2: Database Foundation", "task_description": "Add BPOM registration tracking and regulatory symbols", "is_completed": 1, "completion_date": "2025-06-11", "progress_percentage": 100, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 13, "phase": "Month 1", "category": "Week 1-2: Database Foundation", "task_description": "Create comprehensive test suite with 100% passing unit conversion tests", "is_completed": 1, "completion_date": "2025-06-11", "progress_percentage": 100, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 14, "phase": "Month 1", "category": "Week 3-4: Basic Product Management", "task_description": "Build product CRUD API endpoints with comprehensive validation", "is_completed": 1, "completion_date": "2025-06-11", "progress_percentage": 100, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 15, "phase": "Month 1", "category": "Week 3-4: Basic Product Management", "task_description": "Create advanced product management interface with Indonesian localization", "is_completed": 1, "completion_date": "2025-06-11", "progress_percentage": 100, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 16, "phase": "Month 1", "category": "Week 3-4: Basic Product Management", "task_description": "Implement complete unit conversion functionality with real-time calculations", "is_completed": 1, "completion_date": "2025-06-11", "progress_percentage": 100, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 17, "phase": "Month 1", "category": "Week 3-4: Basic Product Management", "task_description": "Add product search and filtering with multiple criteria", "is_completed": 1, "completion_date": "2025-06-11", "progress_percentage": 100, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 143, "phase": "Month 10", "category": "Expense Management", "task_description": "Create OperationalExpense model", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 144, "phase": "Month 10", "category": "Expense Management", "task_description": "Build expense recording interface", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 145, "phase": "Month 10", "category": "Expense Management", "task_description": "Implement expense categorization", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 146, "phase": "Month 10", "category": "Expense Management", "task_description": "Add basic budget tracking", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 147, "phase": "Month 10", "category": "Expense Management", "task_description": "Integrate with procurement system for purchase expenses", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 148, "phase": "Month 11", "category": "Financial Reporting", "task_description": "Integrate all financial data sources", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 149, "phase": "Month 11", "category": "Financial Reporting", "task_description": "Create cash flow statements", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 150, "phase": "Month 11", "category": "Financial Reporting", "task_description": "Build profit/loss reports", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 151, "phase": "Month 11", "category": "Financial Reporting", "task_description": "Add tax calculation features", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 152, "phase": "Month 12", "category": "Regulatory Compliance", "task_description": "Implement PPN tax calculations for procurement and sales", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 153, "phase": "Month 12", "category": "Regulatory Compliance", "task_description": "Add BPOM compliance features", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 154, "phase": "Month 12", "category": "Regulatory Compliance", "task_description": "Create regulatory reporting", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 155, "phase": "Month 12", "category": "Regulatory Compliance", "task_description": "Build audit trail system", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 156, "phase": "Month 12", "category": "Regulatory Compliance", "task_description": "Integrate procurement compliance tracking", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 157, "phase": "Month 12", "category": "Regulatory Compliance", "task_description": "Stock opname compliance reporting for pharmacy inspections", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 158, "phase": "Month 13", "category": "System Enhancement", "task_description": "Advanced analytics dashboard", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 159, "phase": "Month 13", "category": "System Enhancement", "task_description": "Inventory optimization features", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 160, "phase": "Month 13", "category": "System Enhancement", "task_description": "Advanced search and filtering", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 161, "phase": "Month 13", "category": "System Enhancement", "task_description": "Bulk operations support", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 162, "phase": "Month 13", "category": "System Enhancement", "task_description": "Procurement analytics and supplier performance tracking", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 163, "phase": "Month 13", "category": "System Enhancement", "task_description": "Stock opname analytics with cycle counting optimization", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 164, "phase": "Month 14", "category": "Production Preparation", "task_description": "Production deployment setup", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 165, "phase": "Month 14", "category": "Production Preparation", "task_description": "Backup and recovery systems", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 166, "phase": "Month 14", "category": "Production Preparation", "task_description": "Monitoring and logging", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 167, "phase": "Month 14", "category": "Production Preparation", "task_description": "Documentation completion", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 168, "phase": "Month 14", "category": "Production Preparation", "task_description": "User acceptance testing", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 18, "phase": "Month 2", "category": "Week 1-2: Inventory Foundation", "task_description": "Implement InventoryItem model", "is_completed": 1, "completion_date": "2025-06-11", "progress_percentage": 100, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 19, "phase": "Month 2", "category": "Week 1-2: Inventory Foundation", "task_description": "Create StockMovement tracking system", "is_completed": 1, "completion_date": "2025-06-11", "progress_percentage": 100, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 20, "phase": "Month 2", "category": "Week 1-2: Inventory Foundation", "task_description": "Build basic stock adjustment functionality", "is_completed": 1, "completion_date": "2025-06-11", "progress_percentage": 100, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 21, "phase": "Month 2", "category": "Week 1-2: Inventory Foundation", "task_description": "Implement comprehensive FIFO/FEFO logic with cross-batch allocation", "is_completed": 1, "completion_date": "2025-06-11", "progress_percentage": 100, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 22, "phase": "Month 2", "category": "Week 1-2: Inventory Foundation", "task_description": "Add stock consumption API endpoints with Indonesian compliance", "is_completed": 1, "completion_date": "2025-06-11", "progress_percentage": 100, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 23, "phase": "Month 2", "category": "Week 1-2: Inventory Foundation", "task_description": "Create comprehensive integration tests (110/110 tests passing)", "is_completed": 1, "completion_date": "2025-06-11", "progress_percentage": 100, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 24, "phase": "Month 2", "category": "Week 3-4: Inventory Interface", "task_description": "Create inventory dashboard", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 25, "phase": "Month 2", "category": "Week 3-4: Inventory Interface", "task_description": "Build stock adjustment interface", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 26, "phase": "Month 2", "category": "Week 3-4: Inventory Interface", "task_description": "Add low stock alerts", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 27, "phase": "Month 2", "category": "Week 3-4: Inventory Interface", "task_description": "Implement basic inventory reports", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 185, "phase": "Month 3", "category": "POS Interface", "task_description": "Basic POS interface", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 186, "phase": "Month 3", "category": "POS Interface", "task_description": "Product search and selection", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 187, "phase": "Month 3", "category": "POS Interface", "task_description": "Customer selection functionality", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 188, "phase": "Month 3", "category": "POS Interface", "task_description": "Basic sales reporting", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 181, "phase": "Month 3", "category": "Sales Foundation", "task_description": "Customer, Sale, and SaleItem models", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 182, "phase": "Month 3", "category": "Sales Foundation", "task_description": "Basic POS API endpoints", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 183, "phase": "Month 3", "category": "Sales Foundation", "task_description": "Simple transaction processing", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 184, "phase": "Month 3", "category": "Sales Foundation", "task_description": "Receipt generation system", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 28, "phase": "Month 3", "category": "Week 1-2: Sales Foundation", "task_description": "Create Customer, Sale, and SaleItem models", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 29, "phase": "Month 3", "category": "Week 1-2: Sales Foundation", "task_description": "Implement basic POS API endpoints", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 30, "phase": "Month 3", "category": "Week 1-2: Sales Foundation", "task_description": "Build simple transaction processing", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 31, "phase": "Month 3", "category": "Week 1-2: Sales Foundation", "task_description": "Create receipt generation system", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 32, "phase": "Month 3", "category": "Week 3-4: POS Interface", "task_description": "Build basic POS interface", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 33, "phase": "Month 3", "category": "Week 3-4: POS Interface", "task_description": "Implement product search and selection", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 34, "phase": "Month 3", "category": "Week 3-4: POS Interface", "task_description": "Add customer selection functionality", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 35, "phase": "Month 3", "category": "Week 3-4: POS Interface", "task_description": "Create basic sales reporting", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 42, "phase": "Month 4A", "category": "API Endpoints & Controllers", "task_description": "Purchase Order CRUD endpoints (`/api/purchase-orders`)", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 43, "phase": "Month 4A", "category": "API Endpoints & Controllers", "task_description": "Purchase Order Item management endpoints", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 44, "phase": "Month 4A", "category": "API Endpoints & Controllers", "task_description": "Goods Receipt CRUD endpoints (`/api/goods-receipts`)", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 45, "phase": "Month 4A", "category": "API Endpoints & Controllers", "task_description": "Quality control workflow endpoints", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 46, "phase": "Month 4A", "category": "API Endpoints & Controllers", "task_description": "Purchase order approval workflow endpoints", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 47, "phase": "Month 4A", "category": "API Endpoints & Controllers", "task_description": "Status update and tracking endpoints", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 195, "phase": "Month 4A", "category": "API Endpoints & Controllers", "task_description": "Complete purchase order CRUD API with filtering and search", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 196, "phase": "Month 4A", "category": "API Endpoints & Controllers", "task_description": "Purchase order approval workflow endpoints", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 197, "phase": "Month 4A", "category": "API Endpoints & Controllers", "task_description": "Goods receipt processing API with quality control", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 198, "phase": "Month 4A", "category": "API Endpoints & Controllers", "task_description": "Integration endpoints with supplier and inventory management", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 199, "phase": "Month 4A", "category": "API Endpoints & Controllers", "task_description": "Status tracking and reporting endpoints", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 200, "phase": "Month 4A", "category": "API Endpoints & Controllers", "task_description": "Multi-unit conversion API integration", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 48, "phase": "Month 4A", "category": "Business Logic & Services", "task_description": "PurchaseOrderService with approval workflows", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 49, "phase": "Month 4A", "category": "Business Logic & Services", "task_description": "GoodsReceiptService with quality control logic", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 50, "phase": "Month 4A", "category": "Business Logic & Services", "task_description": "Integration with existing SupplierService", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 51, "phase": "Month 4A", "category": "Business Logic & Services", "task_description": "Integration with existing InventoryService", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 52, "phase": "Month 4A", "category": "Business Logic & Services", "task_description": "Unit conversion logic for procurement", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 53, "phase": "Month 4A", "category": "Business Logic & Services", "task_description": "Cost calculation and PPN handling", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 201, "phase": "Month 4A", "category": "Business Logic & Services", "task_description": "PurchaseOrderService with approval workflows and cost calculations", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 202, "phase": "Month 4A", "category": "Business Logic & Services", "task_description": "GoodsReceiptService with quality control logic and inventory integration", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 203, "phase": "Month 4A", "category": "Business Logic & Services", "task_description": "Integration with existing SupplierService and InventoryService", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 204, "phase": "Month 4A", "category": "Business Logic & Services", "task_description": "Unit conversion logic for procurement with price calculations", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 205, "phase": "Month 4A", "category": "Business Logic & Services", "task_description": "PPN tax calculation and Indonesian compliance features", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 206, "phase": "Month 4A", "category": "Business Logic & Services", "task_description": "Comprehensive error handling and validation", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 36, "phase": "Month 4A", "category": "Database Models & Migrations", "task_description": "Implement PurchaseOrder model with status tracking", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 37, "phase": "Month 4A", "category": "Database Models & Migrations", "task_description": "Implement PurchaseOrderItem model with multi-unit support", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 38, "phase": "Month 4A", "category": "Database Models & Migrations", "task_description": "Implement GoodsReceipt model with quality control fields", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 39, "phase": "Month 4A", "category": "Database Models & Migrations", "task_description": "Implement GoodsReceiptItem model with batch/expiry tracking", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 40, "phase": "Month 4A", "category": "Database Models & Migrations", "task_description": "Create database migrations and relationships", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 41, "phase": "Month 4A", "category": "Database Models & Migrations", "task_description": "Add comprehensive validation rules", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 189, "phase": "Month 4A", "category": "Database Models & Migrations", "task_description": "PurchaseOrder model with comprehensive status tracking", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 190, "phase": "Month 4A", "category": "Database Models & Migrations", "task_description": "PurchaseOrderItem model with multi-unit support and cost tracking", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 191, "phase": "Month 4A", "category": "Database Models & Migrations", "task_description": "GoodsReceipt model with quality control and approval workflows", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 192, "phase": "Month 4A", "category": "Database Models & Migrations", "task_description": "GoodsReceiptItem model with batch/expiry/location tracking", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 193, "phase": "Month 4A", "category": "Database Models & Migrations", "task_description": "Database migrations with proper relationships and constraints", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 194, "phase": "Month 4A", "category": "Database Models & Migrations", "task_description": "Indonesian-specific validation rules (supplier integration, unit conversion)", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 54, "phase": "Month 4A", "category": "Integration Tests", "task_description": "Purchase order lifecycle tests (create → approve → receive)", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 55, "phase": "Month 4A", "category": "Integration Tests", "task_description": "Goods receipt processing tests with quality control", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 56, "phase": "Month 4A", "category": "Integration Tests", "task_description": "Supplier integration tests", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 57, "phase": "Month 4A", "category": "Integration Tests", "task_description": "Inventory creation after goods receipt approval", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 58, "phase": "Month 4A", "category": "Integration Tests", "task_description": "Multi-unit conversion tests", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 59, "phase": "Month 4A", "category": "Integration Tests", "task_description": "Error handling and validation tests", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 207, "phase": "Month 4A", "category": "Integration Tests", "task_description": "Complete purchase order lifecycle tests (draft → approved → sent → received)", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 208, "phase": "Month 4A", "category": "Integration Tests", "task_description": "Goods receipt processing with quality control pass/fail scenarios", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 209, "phase": "Month 4A", "category": "Integration Tests", "task_description": "Cross-batch allocation and inventory creation tests", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 210, "phase": "Month 4A", "category": "Integration Tests", "task_description": "Supplier integration and multi-unit conversion tests", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 211, "phase": "Month 4A", "category": "Integration Tests", "task_description": "Error handling, validation, and edge case tests", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 212, "phase": "Month 4A", "category": "Integration Tests", "task_description": "Performance tests with realistic Indonesian pharmacy data", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 66, "phase": "Month 4B", "category": "Goods Receipt Processing Interface", "task_description": "Goods receipt creation from purchase orders", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 67, "phase": "Month 4B", "category": "Goods Receipt Processing Interface", "task_description": "Batch number and expiry date input forms", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 68, "phase": "Month 4B", "category": "Goods Receipt Processing Interface", "task_description": "Quality control checklist interface", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 69, "phase": "Month 4B", "category": "Goods Receipt Processing Interface", "task_description": "Quantity verification with discrepancy handling", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 70, "phase": "Month 4B", "category": "Goods Receipt Processing Interface", "task_description": "Photo upload for quality documentation", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 71, "phase": "Month 4B", "category": "Goods Receipt Processing Interface", "task_description": "Integration with inventory creation workflow", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 219, "phase": "Month 4B", "category": "Goods Receipt Processing Interface", "task_description": "Goods receipt creation from purchase orders with auto-population", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 220, "phase": "Month 4B", "category": "Goods Receipt Processing Interface", "task_description": "Batch number and expiry date input with validation", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 221, "phase": "Month 4B", "category": "Goods Receipt Processing Interface", "task_description": "Quality control checklist interface with photo upload support", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 222, "phase": "Month 4B", "category": "Goods Receipt Processing Interface", "task_description": "Quantity verification with discrepancy handling and notes", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 223, "phase": "Month 4B", "category": "Goods Receipt Processing Interface", "task_description": "Integration with inventory creation workflow", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 224, "phase": "Month 4B", "category": "Goods Receipt Processing Interface", "task_description": "Consistent styling with shadcn/ui components and existing patterns", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 72, "phase": "Month 4B", "category": "Integration & Polish", "task_description": "Integration with existing supplier management UI", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 73, "phase": "Month 4B", "category": "Integration & Polish", "task_description": "Consistent styling with shadcn/ui components", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 74, "phase": "Month 4B", "category": "Integration & Polish", "task_description": "Mobile-responsive design for all procurement interfaces", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 75, "phase": "Month 4B", "category": "Integration & Polish", "task_description": "Indonesian language localization for all UI text", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 76, "phase": "Month 4B", "category": "Integration & Polish", "task_description": "AlertDialog confirmations for destructive actions", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 77, "phase": "Month 4B", "category": "Integration & Polish", "task_description": "Loading states and error handling", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 225, "phase": "Month 4B", "category": "Integration & Polish", "task_description": "Seamless integration with existing supplier and inventory management UI", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 226, "phase": "Month 4B", "category": "Integration & Polish", "task_description": "TanStack Query integration for data fetching and caching", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 227, "phase": "Month 4B", "category": "Integration & Polish", "task_description": "Loading states, error handling, and user feedback", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 228, "phase": "Month 4B", "category": "Integration & Polish", "task_description": "Complete Indonesian language localization for all UI text", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 229, "phase": "Month 4B", "category": "Integration & Polish", "task_description": "Mobile-first responsive design for all procurement interfaces", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 230, "phase": "Month 4B", "category": "Integration & Polish", "task_description": "Comprehensive frontend integration tests with real backend APIs", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 231, "phase": "Month 4B", "category": "Next Phase (Week 3-4: Inventory Interface):", "task_description": "Inventory dashboard with FIFO/FEFO visualization", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 232, "phase": "Month 4B", "category": "Next Phase (Week 3-4: Inventory Interface):", "task_description": "Stock adjustment interface with batch selection", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 233, "phase": "Month 4B", "category": "Next Phase (Week 3-4: Inventory Interface):", "task_description": "Low stock alerts with reorder recommendations", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 234, "phase": "Month 4B", "category": "Next Phase (Week 3-4: Inventory Interface):", "task_description": "Inventory reports with movement analysis", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 60, "phase": "Month 4B", "category": "Purchase Order Management Interface", "task_description": "Purchase order creation form with product selection", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 61, "phase": "Month 4B", "category": "Purchase Order Management Interface", "task_description": "Multi-unit quantity input with conversion display", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 62, "phase": "Month 4B", "category": "Purchase Order Management Interface", "task_description": "Supplier selection integration with existing supplier management", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 63, "phase": "Month 4B", "category": "Purchase Order Management Interface", "task_description": "Purchase order list/table with filtering and search", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 64, "phase": "Month 4B", "category": "Purchase Order Management Interface", "task_description": "Purchase order detail view with status tracking", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 65, "phase": "Month 4B", "category": "Purchase Order Management Interface", "task_description": "Approval workflow interface (if user has approval rights)", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 213, "phase": "Month 4B", "category": "Purchase Order Management Interface", "task_description": "Purchase order creation form with product selection and unit conversion", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 214, "phase": "Month 4B", "category": "Purchase Order Management Interface", "task_description": "Supplier selection integration with existing supplier management UI", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 215, "phase": "Month 4B", "category": "Purchase Order Management Interface", "task_description": "Purchase order list/table with advanced filtering, search, and sorting", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 216, "phase": "Month 4B", "category": "Purchase Order Management Interface", "task_description": "Purchase order detail view with status tracking and approval workflow", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 217, "phase": "Month 4B", "category": "Purchase Order Management Interface", "task_description": "Mobile-responsive design with Indonesian localization", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 218, "phase": "Month 4B", "category": "Purchase Order Management Interface", "task_description": "AlertDialog confirmations for all destructive actions", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 78, "phase": "Month 5", "category": "Enhanced POS System", "task_description": "Implement barcode scanning support", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 79, "phase": "Month 5", "category": "Enhanced POS System", "task_description": "Add multi-unit sales with conversion", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 80, "phase": "Month 5", "category": "Enhanced POS System", "task_description": "Create advanced receipt formatting", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 81, "phase": "Month 5", "category": "Enhanced POS System", "task_description": "Build payment method handling", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 82, "phase": "Month 5", "category": "Enhanced POS System", "task_description": "Add sales return functionality", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 88, "phase": "Month 6A", "category": "API Endpoints & Controllers", "task_description": "Stock opname CRUD endpoints (`/api/stock-opname`)", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 89, "phase": "Month 6A", "category": "API Endpoints & Controllers", "task_description": "Stock counting workflow endpoints with mobile optimization", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 90, "phase": "Month 6A", "category": "API Endpoints & Controllers", "task_description": "Variance analysis and discrepancy management endpoints", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 91, "phase": "Month 6A", "category": "API Endpoints & Controllers", "task_description": "Integration endpoints with inventory and procurement systems", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 92, "phase": "Month 6A", "category": "API Endpoints & Controllers", "task_description": "Adjustment approval workflow endpoints", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 93, "phase": "Month 6A", "category": "API Endpoints & Controllers", "task_description": "Reporting and analytics endpoints for compliance", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 94, "phase": "Month 6A", "category": "Business Logic & Services", "task_description": "StockOpnameService with counting workflows and variance calculation", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 95, "phase": "Month 6A", "category": "Business Logic & Services", "task_description": "Integration with existing InventoryService and FIFO/FEFO logic", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 96, "phase": "Month 6A", "category": "Business Logic & Services", "task_description": "Integration with ProcurementService for auto-purchase order generation", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 97, "phase": "Month 6A", "category": "Business Logic & Services", "task_description": "Integration with ConsignmentService for separate consignment counting", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 98, "phase": "Month 6A", "category": "Business Logic & Services", "task_description": "StockAdjustmentService with approval workflows and cost impact analysis", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 99, "phase": "Month 6A", "category": "Business Logic & Services", "task_description": "Comprehensive audit trail and BPOM compliance features", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 83, "phase": "Month 6A", "category": "Database Models & Migrations", "task_description": "Implement StockOpname model with comprehensive status tracking", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 84, "phase": "Month 6A", "category": "Database Models & Migrations", "task_description": "Implement StockOpnameItem model with variance calculation", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 85, "phase": "Month 6A", "category": "Database Models & Migrations", "task_description": "Implement StockAdjustment model with audit trail integration", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 86, "phase": "Month 6A", "category": "Database Models & Migrations", "task_description": "Create database migrations with proper relationships", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 87, "phase": "Month 6A", "category": "Database Models & Migrations", "task_description": "Add Indonesian-specific validation rules and compliance features", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 100, "phase": "Month 6A", "category": "Integration Tests", "task_description": "Complete stock opname lifecycle tests (draft → counting → completed → approved)", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 101, "phase": "Month 6A", "category": "Integration Tests", "task_description": "Variance calculation and discrepancy handling tests", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 102, "phase": "Month 6A", "category": "Integration Tests", "task_description": "Integration with inventory adjustment and stock movement tests", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 103, "phase": "Month 6A", "category": "Integration Tests", "task_description": "Procurement integration tests (auto-PO generation from shortages)", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 104, "phase": "Month 6A", "category": "Integration Tests", "task_description": "Consignment integration tests (separate counting workflows)", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 105, "phase": "Month 6A", "category": "Integration Tests", "task_description": "Performance tests with realistic Indonesian pharmacy data", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 118, "phase": "Month 6B", "category": "Integration & Polish", "task_description": "Seamless integration with existing inventory and procurement UI", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 119, "phase": "Month 6B", "category": "Integration & Polish", "task_description": "TanStack Query integration for real-time data synchronization", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 120, "phase": "Month 6B", "category": "Integration & Polish", "task_description": "AlertDialog confirmations for all adjustment actions", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 121, "phase": "Month 6B", "category": "Integration & Polish", "task_description": "Complete Indonesian language localization for all UI text", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 122, "phase": "Month 6B", "category": "Integration & Polish", "task_description": "Mobile-first responsive design for counting operations", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 123, "phase": "Month 6B", "category": "Integration & Polish", "task_description": "Comprehensive frontend integration tests with real backend APIs", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 106, "phase": "Month 6B", "category": "Stock Counting Interface", "task_description": "Mobile-optimized counting interface with barcode scanning support", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 107, "phase": "Month 6B", "category": "Stock Counting Interface", "task_description": "Product search and selection with batch number verification", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 108, "phase": "Month 6B", "category": "Stock Counting Interface", "task_description": "Real-time variance detection and discrepancy flagging", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 109, "phase": "Month 6B", "category": "Stock Counting Interface", "task_description": "Photo upload for discrepancy documentation", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 110, "phase": "Month 6B", "category": "Stock Counting Interface", "task_description": "Offline counting support with sync capabilities", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 111, "phase": "Month 6B", "category": "Stock Counting Interface", "task_description": "Multi-user counting with role-based access", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 112, "phase": "Month 6B", "category": "Variance Analysis Dashboard", "task_description": "Comprehensive variance analysis with cost impact visualization", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 113, "phase": "Month 6B", "category": "Variance Analysis Dashboard", "task_description": "Discrepancy investigation interface with reason categorization", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 114, "phase": "Month 6B", "category": "Variance Analysis Dashboard", "task_description": "Adjustment approval workflow with Indonesian localization", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 115, "phase": "Month 6B", "category": "Variance Analysis Dashboard", "task_description": "Integration with existing inventory management UI", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 116, "phase": "Month 6B", "category": "Variance Analysis Dashboard", "task_description": "Procurement integration (auto-generate POs from shortages)", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 117, "phase": "Month 6B", "category": "Variance Analysis Dashboard", "task_description": "Consignment counting with separate tracking and reporting", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 128, "phase": "Month 7", "category": "Basic Reporting", "task_description": "Daily sales reports", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 129, "phase": "Month 7", "category": "Basic Reporting", "task_description": "Inventory status reports", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 130, "phase": "Month 7", "category": "Basic Reporting", "task_description": "Customer purchase reports", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 131, "phase": "Month 7", "category": "Basic Reporting", "task_description": "Basic financial summaries", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 132, "phase": "Month 7", "category": "Basic Reporting", "task_description": "Procurement reports (purchase orders, goods receipts)", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 133, "phase": "Month 7", "category": "Basic Reporting", "task_description": "Stock opname reports with variance analysis and compliance documentation", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 124, "phase": "Month 7", "category": "Customer Management", "task_description": "Build customer registration system", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 125, "phase": "Month 7", "category": "Customer Management", "task_description": "Implement purchase history tracking", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 126, "phase": "Month 7", "category": "Customer Management", "task_description": "Add customer search and management", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 127, "phase": "Month 7", "category": "Customer Management", "task_description": "Create customer loyalty features", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 134, "phase": "Month 8", "category": "Quality Improvements", "task_description": "Performance optimization", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 135, "phase": "Month 8", "category": "Quality Improvements", "task_description": "User interface refinements", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 136, "phase": "Month 8", "category": "Quality Improvements", "task_description": "Bug fixes and stability improvements", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 137, "phase": "Month 8", "category": "Quality Improvements", "task_description": "Mobile responsiveness enhancements", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 138, "phase": "Month 9", "category": "Consignment Foundation", "task_description": "Implement ConsignmentAgreement model", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 139, "phase": "Month 9", "category": "Consignment Foundation", "task_description": "Create consignment inventory tracking", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 140, "phase": "Month 9", "category": "Consignment Foundation", "task_description": "Build consignment receipt processing", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 141, "phase": "Month 9", "category": "Consignment Foundation", "task_description": "Add basic settlement calculations", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 142, "phase": "Month 9", "category": "Consignment Foundation", "task_description": "Integrate with existing procurement and stock opname systems", "is_completed": 0, "completion_date": null, "progress_percentage": 0, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 1, "phase": "Unknown Phase", "category": "Supplier Management", "task_description": "PBF (Pedagang Besar Farmasi) supplier types", "is_completed": 1, "completion_date": "2025-06-11", "progress_percentage": 100, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 2, "phase": "Unknown Phase", "category": "Supplier Management", "task_description": "NPWP validation for Indonesian tax compliance", "is_completed": 1, "completion_date": "2025-06-11", "progress_percentage": 100, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 3, "phase": "Unknown Phase", "category": "Supplier Management", "task_description": "Payment tracking with Indonesian reference format", "is_completed": 1, "completion_date": "2025-06-11", "progress_percentage": 100, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 4, "phase": "Unknown Phase", "category": "Supplier Management", "task_description": "Document management with payment proof handling", "is_completed": 1, "completion_date": "2025-06-11", "progress_percentage": 100, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}, {"id": 5, "phase": "Unknown Phase", "category": "Supplier Management", "task_description": "Import/export functionality with CSV/XLSX support", "is_completed": 1, "completion_date": "2025-06-11", "progress_percentage": 100, "notes": null, "created_at": "2025-06-11 14:28:00", "updated_at": "2025-06-11 14:28:00"}], "progress_statistics": [{"phase": "Month 1", "total_tasks": 24, "completed_tasks": 24, "avg_progress": 100, "completion_percentage": 100}, {"phase": "Month 10", "total_tasks": 5, "completed_tasks": 0, "avg_progress": 0, "completion_percentage": 0}, {"phase": "Month 11", "total_tasks": 4, "completed_tasks": 0, "avg_progress": 0, "completion_percentage": 0}, {"phase": "Month 12", "total_tasks": 6, "completed_tasks": 0, "avg_progress": 0, "completion_percentage": 0}, {"phase": "Month 13", "total_tasks": 6, "completed_tasks": 0, "avg_progress": 0, "completion_percentage": 0}, {"phase": "Month 14", "total_tasks": 5, "completed_tasks": 0, "avg_progress": 0, "completion_percentage": 0}, {"phase": "Month 2", "total_tasks": 10, "completed_tasks": 6, "avg_progress": 60, "completion_percentage": 60}, {"phase": "Month 3", "total_tasks": 16, "completed_tasks": 0, "avg_progress": 0, "completion_percentage": 0}, {"phase": "Month 4A", "total_tasks": 48, "completed_tasks": 0, "avg_progress": 0, "completion_percentage": 0}, {"phase": "Month 4B", "total_tasks": 40, "completed_tasks": 0, "avg_progress": 0, "completion_percentage": 0}, {"phase": "Month 5", "total_tasks": 5, "completed_tasks": 0, "avg_progress": 0, "completion_percentage": 0}, {"phase": "Month 6A", "total_tasks": 23, "completed_tasks": 0, "avg_progress": 0, "completion_percentage": 0}, {"phase": "Month 6B", "total_tasks": 18, "completed_tasks": 0, "avg_progress": 0, "completion_percentage": 0}, {"phase": "Month 7", "total_tasks": 10, "completed_tasks": 0, "avg_progress": 0, "completion_percentage": 0}, {"phase": "Month 8", "total_tasks": 4, "completed_tasks": 0, "avg_progress": 0, "completion_percentage": 0}, {"phase": "Month 9", "total_tasks": 5, "completed_tasks": 0, "avg_progress": 0, "completion_percentage": 0}, {"phase": "Unknown Phase", "total_tasks": 5, "completed_tasks": 5, "avg_progress": 100, "completion_percentage": 100}]}