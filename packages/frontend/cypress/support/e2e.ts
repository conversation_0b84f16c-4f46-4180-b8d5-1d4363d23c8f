// ***********************************************************
// This example support/e2e.ts is processed and
// loaded automatically before your test files.
//
// This is a great place to put global configuration and
// behavior that modifies Cypress.
//
// You can change the location of this file or turn off
// automatically serving support files with the
// 'supportFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/configuration
// ***********************************************************

// Import commands.js using ES2015 syntax:
import './commands'

// Alternatively you can use CommonJS syntax:
// require('./commands')

// Import custom commands
import './commands/auth'
import './commands/api'
import './commands/database'

// Global configuration
Cypress.on('uncaught:exception', (err, runnable) => {
  // Returning false here prevents <PERSON><PERSON> from failing the test
  // on uncaught exceptions. This is useful for handling expected
  // errors in the application.
  
  // Don't fail on Next.js hydration errors
  if (err.message.includes('Hydration failed')) {
    return false
  }
  
  // Don't fail on ResizeObserver errors
  if (err.message.includes('ResizeObserver loop limit exceeded')) {
    return false
  }
  
  // Don't fail on network errors during development
  if (err.message.includes('Loading chunk')) {
    return false
  }
  
  // Let other errors fail the test
  return true
})

// Before each test
beforeEach(() => {
  // Set up API interceptors for consistent testing
  cy.intercept('GET', '/api/auth/session', { fixture: 'auth/session.json' }).as('getSession')
  
  // Clear local storage and cookies
  cy.clearLocalStorage()
  cy.clearCookies()
  
  // Set viewport for consistent testing
  cy.viewport(1280, 720)
})

// After each test
afterEach(() => {
  // Clean up any test data if needed
  // This can be customized based on your needs
})

// Global custom commands and utilities
declare global {
  namespace Cypress {
    interface Chainable {
      // Auth commands
      login(email?: string, password?: string): Chainable<void>
      logout(): Chainable<void>
      
      // API commands
      apiRequest(method: string, url: string, body?: any): Chainable<any>
      
      // Database commands
      seedDatabase(): Chainable<void>
      cleanDatabase(): Chainable<void>
      
      // UI commands
      getByTestId(testId: string): Chainable<JQuery<HTMLElement>>
      waitForLoadingToFinish(): Chainable<void>
      fillForm(formData: Record<string, string>): Chainable<void>
    }
  }
}
