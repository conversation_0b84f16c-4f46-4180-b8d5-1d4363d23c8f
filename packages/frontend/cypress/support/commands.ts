/// <reference types="cypress" />

// ***********************************************
// This example commands.ts shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************

// Basic utility commands
Cypress.Commands.add('getByTestId', (testId: string) => {
  return cy.get(`[data-testid="${testId}"]`)
})

Cypress.Commands.add('waitForLoadingToFinish', () => {
  // Wait for loading spinners to disappear
  cy.get('[data-testid="loading"]', { timeout: 10000 }).should('not.exist')
  cy.get('.animate-spin', { timeout: 10000 }).should('not.exist')
  
  // Wait for skeleton loaders to disappear
  cy.get('[data-testid="skeleton"]', { timeout: 10000 }).should('not.exist')
  cy.get('.animate-pulse', { timeout: 10000 }).should('not.exist')
})

Cypress.Commands.add('fillForm', (formData: Record<string, string>) => {
  Object.entries(formData).forEach(([field, value]) => {
    cy.get(`[name="${field}"]`).clear().type(value)
  })
})

// Custom command to handle file uploads
Cypress.Commands.add('uploadFile', (selector: string, fileName: string, fileType = '') => {
  cy.get(selector).then(subject => {
    cy.fixture(fileName, 'base64').then(content => {
      const el = subject[0] as HTMLInputElement
      const blob = Cypress.Blob.base64StringToBlob(content, fileType)
      const file = new File([blob], fileName, { type: fileType })
      const dataTransfer = new DataTransfer()
      dataTransfer.items.add(file)
      el.files = dataTransfer.files
      
      // Trigger change event
      cy.wrap(subject).trigger('change', { force: true })
    })
  })
})

// Custom command to handle drag and drop
Cypress.Commands.add('dragAndDrop', (sourceSelector: string, targetSelector: string) => {
  cy.get(sourceSelector).trigger('dragstart')
  cy.get(targetSelector).trigger('drop')
})

// Custom command to wait for API calls
Cypress.Commands.add('waitForApi', (alias: string, timeout = 10000) => {
  cy.wait(alias, { timeout })
})

// Custom command to check toast notifications
Cypress.Commands.add('checkToast', (message: string, type: 'success' | 'error' | 'info' | 'warning' = 'success') => {
  cy.get('[data-sonner-toast]', { timeout: 5000 })
    .should('be.visible')
    .and('contain.text', message)
    .and('have.attr', 'data-type', type)
})

// Custom command to dismiss toast notifications
Cypress.Commands.add('dismissToast', () => {
  cy.get('[data-sonner-toast] button[aria-label="Close toast"]').click({ multiple: true })
})

// Custom command to handle modals
Cypress.Commands.add('openModal', (triggerSelector: string) => {
  cy.get(triggerSelector).click()
  cy.get('[role="dialog"]').should('be.visible')
})

Cypress.Commands.add('closeModal', () => {
  cy.get('[role="dialog"] button[aria-label="Close"]').click()
  cy.get('[role="dialog"]').should('not.exist')
})

// Custom command to handle dropdowns
Cypress.Commands.add('selectDropdownOption', (triggerSelector: string, optionText: string) => {
  cy.get(triggerSelector).click()
  cy.get('[role="option"]').contains(optionText).click()
})

// Custom command to handle date picker
Cypress.Commands.add('selectDate', (datePickerSelector: string, date: string) => {
  cy.get(datePickerSelector).click()
  cy.get('[role="gridcell"]').contains(date).click()
})

// Custom command to handle table interactions
Cypress.Commands.add('sortTable', (columnHeader: string, direction: 'asc' | 'desc' = 'asc') => {
  cy.get('th').contains(columnHeader).click()
  if (direction === 'desc') {
    cy.get('th').contains(columnHeader).click()
  }
})

Cypress.Commands.add('filterTable', (filterInput: string, value: string) => {
  cy.get(filterInput).clear().type(value)
  cy.waitForLoadingToFinish()
})

// Custom command to handle pagination
Cypress.Commands.add('goToPage', (pageNumber: number) => {
  cy.get('[data-testid="pagination"]').within(() => {
    cy.get('button').contains(pageNumber.toString()).click()
  })
  cy.waitForLoadingToFinish()
})

// Custom command to setup API interceptors for batch validation
Cypress.Commands.add('setupApiInterceptors', () => {
  // Intercept batch validation API calls
  cy.intercept('POST', '/api/procurement/batch-validation/real-time', {
    statusCode: 200,
    body: {
      isValid: true,
      level: 'success',
      message: 'Batch number valid dan dapat digunakan',
      suggestions: []
    }
  }).as('validateBatchRealTime')

  // Intercept batch validation rules
  cy.intercept('GET', '/api/procurement/batch-validation/rules', {
    statusCode: 200,
    body: {
      formats: {
        'Standard': {
          description: 'Format standar untuk produk farmasi',
          example: 'ABC123-2024-001',
          pattern: '^[A-Z0-9]{3,}-[0-9]{4}-[0-9]{3}$'
        },
        'Kimia Farma': {
          description: 'Format khusus Kimia Farma',
          example: 'KF-240101-001',
          pattern: '^KF-[0-9]{6}-[0-9]{3}$'
        }
      }
    }
  }).as('getBatchValidationRules')

  // Intercept batch history
  cy.intercept('GET', '/api/procurement/batch-validation/history/*', {
    statusCode: 200,
    body: {
      batchNumber: 'BATCH-HISTORY-001',
      usageHistory: [
        {
          id: '1',
          type: 'goods_receipt',
          productName: 'Paracetamol 500mg',
          quantity: 100,
          unitName: 'Strip',
          date: new Date().toISOString(),
          reference: 'GR-001'
        }
      ],
      totalReceived: 100,
      totalUsed: 25,
      currentStock: 75,
      lastActivity: new Date().toISOString(),
      createdAt: new Date().toISOString(),
      status: 'active',
      storageLocations: ['Gudang Utama - Rak A1']
    }
  }).as('getBatchHistory')

  // Intercept goods receipt creation
  cy.intercept('POST', '/api/goods-receipts', {
    statusCode: 201,
    body: {
      id: 'gr-test-001',
      receiptNumber: 'GR-TEST-001',
      message: 'Penerimaan barang berhasil dicatat'
    }
  }).as('createGoodsReceipt')

  // Intercept products API
  cy.intercept('GET', '/api/products*', {
    statusCode: 200,
    body: {
      data: [
        {
          id: 'product-1',
          name: 'Paracetamol 500mg',
          code: 'PRD-001'
        }
      ]
    }
  }).as('getProducts')

  // Intercept suppliers API
  cy.intercept('GET', '/api/suppliers*', {
    statusCode: 200,
    body: {
      data: [
        {
          id: 'supplier-1',
          name: 'PT Kimia Farma',
          code: 'SUP-001'
        }
      ]
    }
  }).as('getSuppliers')
})

// Custom command to login as admin
Cypress.Commands.add('loginAsAdmin', () => {
  // Mock authentication
  cy.window().then((win) => {
    win.localStorage.setItem('auth-token', 'mock-admin-token')
    win.localStorage.setItem('user-role', 'admin')
  })
})

// Custom command to clean test data
Cypress.Commands.add('cleanTestData', () => {
  // Clear any test data or reset state
  cy.window().then((win) => {
    win.localStorage.removeItem('test-data')
  })
})

// Custom command for keyboard navigation
Cypress.Commands.add('tab', { prevSubject: 'element' }, (subject) => {
  return cy.wrap(subject).trigger('keydown', { key: 'Tab' })
})

// Type definitions are now in cypress/support/index.d.ts
