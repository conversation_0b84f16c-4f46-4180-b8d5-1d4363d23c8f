// ***********************************************************
// This example support/component.ts is processed and
// loaded automatically before your component test files.
//
// This is a great place to put global configuration and
// behavior that modifies Cypress.
//
// You can change the location of this file or turn off
// automatically serving support files with the
// 'supportFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/configuration
// ***********************************************************

// Import commands.js using ES2015 syntax:
import './commands'

// Import React and mount command
import * as React from 'react'
import { mount } from 'cypress/react18'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ThemeProvider } from '@/components/theme-provider'

// Augment the Cypress namespace to include type definitions for
// your custom command.
// Alternatively, can be defined in cypress/support/component.d.ts
// with a <reference path="./component" /> at the top of your spec.
declare global {
  namespace Cypress {
    interface Chainable {
      mount: typeof mount
      mountWithProviders(component: React.ReactNode, options?: any): Chainable<any>
    }
  }
}

// Custom mount command with providers
Cypress.Commands.add('mountWithProviders', (component: React.ReactNode, options: Record<string, unknown> = {}) => {
  // Create a fresh QueryClient for each mount to prevent state leakage
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: 0,
        staleTime: 0,
      },
      mutations: {
        retry: false,
      },
    },
  })

  // Clear any existing cache to prevent state leakage
  queryClient.clear()

  const Wrapper = ({ children }: { children: React.ReactNode }) => {
    return React.createElement(
      QueryClientProvider,
      { client: queryClient },
      React.createElement(
        ThemeProvider,
        {
          attribute: "class",
          defaultTheme: "system",
          enableSystem: true,
          disableTransitionOnChange: true,
        },
        children
      )
    )
  }

  // Mount with providers by wrapping the component
  const wrappedComponent = React.createElement(Wrapper, { children: component })
  return mount(wrappedComponent, options) as Cypress.Chainable<unknown>
})

Cypress.Commands.add('mount', mount)

// Example use:
// cy.mount(<MyComponent />)
// cy.mountWithProviders(<MyComponent />)

// Global configuration for component testing
beforeEach(() => {
  // Set up any global state or mocks needed for component testing
})

// Handle uncaught exceptions in component tests
Cypress.on('uncaught:exception', (err, _runnable) => {
  // Don't fail on React development warnings
  if (err.message.includes('Warning:')) {
    return false
  }
  
  // Don't fail on ResizeObserver errors
  if (err.message.includes('ResizeObserver loop limit exceeded')) {
    return false
  }
  
  return true
})
