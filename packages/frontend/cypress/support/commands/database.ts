/// <reference types="cypress" />

// Database related commands for test data management
Cypress.Commands.add('seedDatabase', () => {
  cy.task('db:seed')
})

Cypress.Commands.add('cleanDatabase', () => {
  cy.task('db:cleanup')
})

// Seed specific data types
Cypress.Commands.add('seedProducts', (count = 10) => {
  cy.task('db:seed:products', count)
})

Cypress.Commands.add('seedCustomers', (count = 10) => {
  cy.task('db:seed:customers', count)
})

Cypress.Commands.add('seedSuppliers', (count = 5) => {
  cy.task('db:seed:suppliers', count)
})

Cypress.Commands.add('seedInventory', (count = 20) => {
  cy.task('db:seed:inventory', count)
})

// Create specific test data
Cypress.Commands.add('createTestProduct', (productData: Record<string, unknown> = {}) => {
  const defaultProduct = {
    code: 'TEST-PRD-001',
    name: 'Test Product',
    description: 'Test product for E2E testing',
    category: 'Medicine',
    manufacturer: 'Test Manufacturer',
    isActive: true,
    ...productData
  }

  return cy.task('db:create:product', defaultProduct)
})

Cypress.Commands.add('createTestCustomer', (customerData: Record<string, unknown> = {}) => {
  const defaultCustomer = {
    code: 'TEST-CUST-001',
    fullName: 'Test Customer',
    phoneNumber: '081234567890',
    email: '<EMAIL>',
    address: 'Test Address',
    type: 'REGISTERED',
    membershipLevel: 'BRONZE',
    isActive: true,
    ...customerData
  }

  return cy.task('db:create:customer', defaultCustomer)
})

Cypress.Commands.add('createTestSupplier', (supplierData: Record<string, unknown> = {}) => {
  const defaultSupplier = {
    code: 'TEST-SUP-001',
    name: 'Test Supplier',
    contactPerson: 'Test Contact',
    phoneNumber: '081234567890',
    email: '<EMAIL>',
    address: 'Test Supplier Address',
    isActive: true,
    ...supplierData
  }

  return cy.task('db:create:supplier', defaultSupplier)
})

// Clean specific data types
// Clean test data to prevent state leakage
Cypress.Commands.add('cleanTestData', () => {
  cy.task('db:clean:test-data')
  // Also clear browser storage to prevent state leakage
  cy.clearLocalStorage()
  cy.clearCookies()
  cy.clearAllSessionStorage()
})

Cypress.Commands.add('cleanProducts', () => {
  cy.task('db:clean:products')
})

Cypress.Commands.add('cleanCustomers', () => {
  cy.task('db:clean:customers')
})

Cypress.Commands.add('cleanSuppliers', () => {
  cy.task('db:clean:suppliers')
})

// Reset database to initial state
Cypress.Commands.add('resetDatabase', () => {
  cy.task('db:reset')
})

// Backup and restore database state
Cypress.Commands.add('backupDatabase', (backupName: string) => {
  cy.task('db:backup', backupName)
})

Cypress.Commands.add('restoreDatabase', (backupName: string) => {
  cy.task('db:restore', backupName)
})
