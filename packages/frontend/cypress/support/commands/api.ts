/// <reference types="cypress" />

// API related commands
Cypress.Commands.add('apiRequest', (method: string, url: string, body?: any) => {
  const apiUrl = Cypress.env('apiUrl') || 'http://localhost:3001'
  
  return cy.request({
    method,
    url: `${apiUrl}${url}`,
    body,
    headers: {
      'Content-Type': 'application/json',
    },
    failOnStatusCode: false,
  })
})

// Setup API interceptors for common endpoints
Cypress.Commands.add('setupApiInterceptors', () => {
  // Products API
  cy.intercept('GET', '/api/products*', { fixture: 'products/list.json' }).as('getProducts')
  cy.intercept('GET', '/api/products/*', { fixture: 'products/detail.json' }).as('getProduct')
  cy.intercept('POST', '/api/products', { fixture: 'products/created.json' }).as('createProduct')
  cy.intercept('PUT', '/api/products/*', { fixture: 'products/updated.json' }).as('updateProduct')
  cy.intercept('DELETE', '/api/products/*', { statusCode: 204 }).as('deleteProduct')
  
  // Customers API
  cy.intercept('GET', '/api/customers*', { fixture: 'customers/list.json' }).as('getCustomers')
  cy.intercept('GET', '/api/customers/*', { fixture: 'customers/detail.json' }).as('getCustomer')
  cy.intercept('POST', '/api/customers', { fixture: 'customers/created.json' }).as('createCustomer')
  cy.intercept('PUT', '/api/customers/*', { fixture: 'customers/updated.json' }).as('updateCustomer')
  cy.intercept('DELETE', '/api/customers/*', { statusCode: 204 }).as('deleteCustomer')
  
  // Inventory API
  cy.intercept('GET', '/api/inventory*', { fixture: 'inventory/list.json' }).as('getInventory')
  cy.intercept('GET', '/api/inventory/*', { fixture: 'inventory/detail.json' }).as('getInventoryItem')
  cy.intercept('POST', '/api/inventory', { fixture: 'inventory/created.json' }).as('createInventoryItem')
  cy.intercept('PUT', '/api/inventory/*', { fixture: 'inventory/updated.json' }).as('updateInventoryItem')
  
  // Sales API
  cy.intercept('GET', '/api/sales*', { fixture: 'sales/list.json' }).as('getSales')
  cy.intercept('POST', '/api/sales', { fixture: 'sales/created.json' }).as('createSale')
  
  // Purchase Orders API
  cy.intercept('GET', '/api/purchase-orders*', { fixture: 'purchase-orders/list.json' }).as('getPurchaseOrders')
  cy.intercept('POST', '/api/purchase-orders', { fixture: 'purchase-orders/created.json' }).as('createPurchaseOrder')
  
  // Suppliers API
  cy.intercept('GET', '/api/suppliers*', { fixture: 'suppliers/list.json' }).as('getSuppliers')
  cy.intercept('POST', '/api/suppliers', { fixture: 'suppliers/created.json' }).as('createSupplier')
  
  // Batch Management API
  cy.intercept('GET', '/api/batch-management*', { fixture: 'batch-management/list.json' }).as('getBatches')
  cy.intercept('POST', '/api/batch-management', { fixture: 'batch-management/created.json' }).as('createBatch')
  
  // Real-time batch validation API
  cy.intercept('POST', '/api/procurement/batch-validation/real-time', { fixture: 'batch-validation/real-time.json' }).as('validateBatchRealTime')
})

// Mock API responses with custom data
Cypress.Commands.add('mockApiResponse', (method: string, url: string, response: unknown, statusCode = 200) => {
  const httpMethod = method.toUpperCase() as 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  cy.intercept(httpMethod, url, {
    statusCode,
    body: response,
  }).as(`mock${method}${url.replace(/[^a-zA-Z0-9]/g, '')}`)
})

// Mock API error responses
Cypress.Commands.add('mockApiError', (method: string, url: string, statusCode = 500, message = 'Internal Server Error') => {
  const httpMethod = method.toUpperCase() as 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  cy.intercept(httpMethod, url, {
    statusCode,
    body: {
      error: message,
      statusCode,
    },
  }).as(`mockError${method}${url.replace(/[^a-zA-Z0-9]/g, '')}`)
})

// Wait for multiple API calls
Cypress.Commands.add('waitForApis', (aliases: string[], timeout = 10000) => {
  aliases.forEach(alias => {
    cy.wait(alias, { timeout })
  })
})

// Check API call was made with specific data
Cypress.Commands.add('checkApiCall', (alias: string, expectedData?: unknown) => {
  cy.get(alias).then((interception: unknown) => {
    if (expectedData && typeof interception === 'object' && interception !== null) {
      const intercept = interception as { request: { body: unknown } }
      // Use Cypress expect instead of Jest expect
      cy.wrap(intercept.request.body).should('deep.include', expectedData)
    }
  })
})

// Type definitions are now in cypress/support/index.d.ts
