/// <reference types="cypress" />

// Authentication related commands
Cypress.Commands.add('login', (email = '<EMAIL>', password = 'password123') => {
  cy.session([email, password], () => {
    // Visit login page
    cy.visit('/auth/signin')
    
    // Fill login form
    cy.get('[name="email"]').type(email)
    cy.get('[name="password"]').type(password)
    
    // Submit form
    cy.get('button[type="submit"]').click()
    
    // Wait for redirect to dashboard
    cy.url().should('include', '/dashboard')
    
    // Verify user is logged in
    cy.get('[data-testid="user-menu"]').should('be.visible')
  })
})

Cypress.Commands.add('logout', () => {
  // Click user menu
  cy.get('[data-testid="user-menu"]').click()
  
  // Click logout button
  cy.get('[data-testid="logout-button"]').click()
  
  // Verify redirect to login page
  cy.url().should('include', '/auth/signin')
})

// Login with specific role
Cypress.Commands.add('loginAsAdmin', () => {
  cy.login('<EMAIL>', 'password123')
})

Cypress.Commands.add('loginAsPharmacist', () => {
  cy.login('<EMAIL>', 'password123')
})

Cypress.Commands.add('loginAsCashier', () => {
  cy.login('<EMAIL>', 'password123')
})

// Check authentication state
Cypress.Commands.add('checkAuthState', (shouldBeAuthenticated = true) => {
  if (shouldBeAuthenticated) {
    cy.get('[data-testid="user-menu"]').should('be.visible')
    cy.url().should('not.include', '/auth/signin')
  } else {
    cy.url().should('include', '/auth/signin')
  }
})

// Mock authentication for component tests
Cypress.Commands.add('mockAuth', (user: Record<string, unknown> = {}) => {
  const defaultUser = {
    id: '1',
    email: '<EMAIL>',
    name: 'Test User',
    role: 'ADMIN',
    ...user
  }

  cy.window().then((win) => {
    win.localStorage.setItem('auth-user', JSON.stringify(defaultUser))
  })
})

// Clear authentication
Cypress.Commands.add('clearAuth', () => {
  cy.clearLocalStorage()
  cy.clearCookies()
})
