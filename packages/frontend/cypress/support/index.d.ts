/// <reference types="cypress" />

declare global {
  namespace Cypress {
    interface Chainable {
      // Basic utility commands
      getByTestId(testId: string): Chainable<JQuery<HTMLElement>>
      waitForLoadingToFinish(): Chainable<void>
      fillForm(formData: Record<string, string>): Chainable<void>
      
      // File and interaction commands
      uploadFile(selector: string, fileName: string, fileType?: string): Chainable<void>
      dragAndDrop(sourceSelector: string, targetSelector: string): Chainable<void>
      
      // API and waiting commands
      waitForApi(alias: string, timeout?: number): Chainable<void>
      
      // Toast and modal commands
      checkToast(message: string, type?: 'success' | 'error' | 'info' | 'warning'): Chainable<void>
      dismissToast(): Chainable<void>
      openModal(triggerSelector: string): Chainable<void>
      closeModal(): Chainable<void>
      
      // Form interaction commands
      selectDropdownOption(triggerSelector: string, optionText: string): Chainable<void>
      selectDate(datePickerSelector: string, date: string): Chainable<void>
      
      // Table interaction commands
      sortTable(columnHeader: string, direction?: 'asc' | 'desc'): Chainable<void>
      filterTable(filterInput: string, value: string): Chainable<void>
      goToPage(pageNumber: number): Chainable<void>
      
      // Authentication commands
      login(email?: string, password?: string): Chainable<void>
      logout(): Chainable<void>
      loginAsAdmin(): Chainable<void>
      loginAsPharmacist(): Chainable<void>
      loginAsCashier(): Chainable<void>
      checkAuthState(shouldBeAuthenticated?: boolean): Chainable<void>
      mockAuth(user?: Record<string, unknown>): Chainable<void>
      clearAuth(): Chainable<void>
      
      // API commands
      apiRequest(method: string, url: string, body?: unknown): Chainable<Response<unknown>>
      setupApiInterceptors(): Chainable<void>
      mockApiResponse(method: string, url: string, response: unknown, statusCode?: number): Chainable<void>
      mockApiError(method: string, url: string, statusCode?: number, message?: string): Chainable<void>
      waitForApis(aliases: string[], timeout?: number): Chainable<void>
      checkApiCall(alias: string, expectedData?: unknown): Chainable<void>
      
      // Database commands
      seedDatabase(): Chainable<void>
      cleanDatabase(): Chainable<void>
      seedProducts(count?: number): Chainable<void>
      seedCustomers(count?: number): Chainable<void>
      seedSuppliers(count?: number): Chainable<void>
      seedInventory(count?: number): Chainable<void>
      createTestProduct(productData?: Record<string, unknown>): Chainable<unknown>
      createTestCustomer(customerData?: Record<string, unknown>): Chainable<unknown>
      createTestSupplier(supplierData?: Record<string, unknown>): Chainable<unknown>
      cleanTestData(): Chainable<void>
      cleanProducts(): Chainable<void>
      cleanCustomers(): Chainable<void>
      cleanSuppliers(): Chainable<void>
      resetDatabase(): Chainable<void>
      backupDatabase(backupName: string): Chainable<void>
      restoreDatabase(backupName: string): Chainable<void>
      
      // Component testing commands
      mountWithProviders(component: React.ReactNode, options?: Record<string, unknown>): Chainable<unknown>

      // Keyboard navigation commands
      tab(): Chainable<JQuery<HTMLElement>>
    }
  }
}

export {}
