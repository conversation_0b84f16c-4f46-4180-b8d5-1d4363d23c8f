describe('Manajemen Produk', () => {
  beforeEach(() => {
    // Setup API interceptors
    cy.setupApiInterceptors()
    
    // Login as admin
    cy.loginAsAdmin()
    
    // Navigate to products page
    cy.visit('/dashboard/products')
    cy.waitForLoadingToFinish()
  })

  afterEach(() => {
    // Clean up test data
    cy.cleanTestData()
  })

  describe('Daftar Produk', () => {
    it('should display product list correctly', () => {
      // Check page title
      cy.contains('h1', 'Daftar Produk').should('be.visible')
      
      // Check table headers
      cy.get('table thead').within(() => {
        cy.contains('Kode Produk').should('be.visible')
        cy.contains('Nama Produk').should('be.visible')
        cy.contains('Kategori').should('be.visible')
        cy.contains('Produsen').should('be.visible')
        cy.contains('Status').should('be.visible')
        cy.contains('Aksi').should('be.visible')
      })
      
      // Check if products are loaded
      cy.get('table tbody tr').should('have.length.greaterThan', 0)
      
      // Verify API call was made
      cy.wait('@getProducts')
    })

    it('should search products correctly', () => {
      const searchTerm = 'Paracetamol'
      
      // Use search input
      cy.get('[data-testid="product-search"]').type(searchTerm)
      
      // Wait for debounced search
      cy.waitForLoadingToFinish()
      
      // Check filtered results
      cy.get('table tbody tr').each(($row) => {
        cy.wrap($row).should('contain.text', searchTerm)
      })
      
      // Clear search
      cy.get('[data-testid="product-search"]').clear()
      cy.waitForLoadingToFinish()
    })

    it('should filter products by category', () => {
      // Open category filter
      cy.get('[data-testid="category-filter"]').click()
      
      // Select a category
      cy.selectDropdownOption('[data-testid="category-filter"]', 'Obat Bebas')
      
      // Wait for filter to apply
      cy.waitForLoadingToFinish()
      
      // Verify filtered results
      cy.get('table tbody tr').each(($row) => {
        cy.wrap($row).should('contain.text', 'Obat Bebas')
      })
    })

    it('should sort products by name', () => {
      // Click on name column header to sort
      cy.sortTable('Nama Produk', 'asc')
      
      // Verify sorting
      cy.get('table tbody tr td:nth-child(2)').then(($cells) => {
        const names = Array.from($cells).map(cell => cell.textContent?.trim() || '')
        const sortedNames = [...names].sort()
        cy.wrap(names).should('deep.equal', sortedNames)
      })
    })

    it('should navigate through pagination', () => {
      // Go to page 2
      cy.goToPage(2)
      
      // Verify page change
      cy.get('[data-testid="pagination"]').should('contain.text', '2')
      
      // Go back to page 1
      cy.goToPage(1)
      
      // Verify page change
      cy.get('[data-testid="pagination"]').should('contain.text', '1')
    })
  })

  describe('Tambah Produk', () => {
    it('should create new product successfully', () => {
      const newProduct = {
        code: 'PRD-TEST-001',
        name: 'Test Product E2E',
        description: 'Produk test untuk E2E testing',
        category: 'Obat Bebas',
        manufacturer: 'Test Manufacturer',
      }

      // Click add product button
      cy.get('[data-testid="add-product-button"]').click()
      
      // Verify modal opened
      cy.get('[role="dialog"]').should('be.visible')
      cy.contains('Tambah Produk Baru').should('be.visible')
      
      // Fill form
      cy.fillForm({
        code: newProduct.code,
        name: newProduct.name,
        description: newProduct.description,
        manufacturer: newProduct.manufacturer,
      })
      
      // Select category
      cy.selectDropdownOption('[name="category"]', newProduct.category)
      
      // Submit form
      cy.get('button[type="submit"]').click()
      
      // Wait for API call
      cy.wait('@createProduct')
      
      // Verify success message
      cy.checkToast('Produk berhasil ditambahkan', 'success')
      
      // Verify modal closed
      cy.get('[role="dialog"]').should('not.exist')
      
      // Verify product appears in list
      cy.contains(newProduct.name).should('be.visible')
    })

    it('should validate required fields', () => {
      // Click add product button
      cy.get('[data-testid="add-product-button"]').click()
      
      // Try to submit empty form
      cy.get('button[type="submit"]').click()
      
      // Check validation errors
      cy.contains('Kode produk wajib diisi').should('be.visible')
      cy.contains('Nama produk wajib diisi').should('be.visible')
      cy.contains('Kategori wajib dipilih').should('be.visible')
    })

    it('should validate unique product code', () => {
      // Click add product button
      cy.get('[data-testid="add-product-button"]').click()
      
      // Enter existing product code
      cy.get('[name="code"]').type('PRD-001')
      
      // Wait for validation
      cy.waitForLoadingToFinish()
      
      // Check validation error
      cy.contains('Kode produk sudah digunakan').should('be.visible')
    })
  })

  describe('Edit Produk', () => {
    it('should edit product successfully', () => {
      const updatedData = {
        name: 'Updated Product Name',
        description: 'Updated description',
      }

      // Click edit button on first product
      cy.get('table tbody tr:first-child [data-testid="edit-product-button"]').click()
      
      // Verify modal opened with existing data
      cy.get('[role="dialog"]').should('be.visible')
      cy.contains('Edit Produk').should('be.visible')
      
      // Update form fields
      cy.get('[name="name"]').clear().type(updatedData.name)
      cy.get('[name="description"]').clear().type(updatedData.description)
      
      // Submit form
      cy.get('button[type="submit"]').click()
      
      // Wait for API call
      cy.wait('@updateProduct')
      
      // Verify success message
      cy.checkToast('Produk berhasil diperbarui', 'success')
      
      // Verify updated data in list
      cy.contains(updatedData.name).should('be.visible')
    })
  })

  describe('Hapus Produk', () => {
    it('should delete product successfully', () => {
      // Click delete button on first product
      cy.get('table tbody tr:first-child [data-testid="delete-product-button"]').click()
      
      // Verify confirmation dialog
      cy.get('[role="alertdialog"]').should('be.visible')
      cy.contains('Hapus Produk').should('be.visible')
      cy.contains('Apakah Anda yakin ingin menghapus produk ini?').should('be.visible')
      
      // Confirm deletion
      cy.get('[data-testid="confirm-delete-button"]').click()
      
      // Wait for API call
      cy.wait('@deleteProduct')
      
      // Verify success message
      cy.checkToast('Produk berhasil dihapus', 'success')
    })

    it('should cancel product deletion', () => {
      // Click delete button
      cy.get('table tbody tr:first-child [data-testid="delete-product-button"]').click()
      
      // Cancel deletion
      cy.get('[data-testid="cancel-delete-button"]').click()
      
      // Verify dialog closed
      cy.get('[role="alertdialog"]').should('not.exist')
      
      // Verify no API call was made
      cy.get('@deleteProduct.all').should('have.length', 0)
    })
  })

  describe('Import/Export Produk', () => {
    it('should download product template', () => {
      // Click export button
      cy.get('[data-testid="export-template-button"]').click()
      
      // Verify download started (file should be in downloads folder)
      cy.readFile('cypress/downloads/template-produk.xlsx').should('exist')
    })

    it('should import products from file', () => {
      // Click import button
      cy.get('[data-testid="import-products-button"]').click()
      
      // Upload file
      cy.uploadFile('[data-testid="file-upload"]', 'products-import.xlsx', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
      
      // Submit import
      cy.get('[data-testid="submit-import-button"]').click()
      
      // Wait for import to complete
      cy.waitForLoadingToFinish()
      
      // Verify success message
      cy.checkToast('Produk berhasil diimpor', 'success')
    })
  })

  describe('Responsive Design', () => {
    it('should work correctly on mobile devices', () => {
      // Set mobile viewport
      cy.viewport('iphone-x')
      
      // Check mobile layout
      cy.get('[data-testid="mobile-menu-button"]').should('be.visible')
      
      // Open mobile menu
      cy.get('[data-testid="mobile-menu-button"]').click()
      
      // Navigate to products
      cy.get('[data-testid="mobile-nav-products"]').click()
      
      // Verify products page loads on mobile
      cy.contains('Daftar Produk').should('be.visible')
      
      // Check mobile table layout
      cy.get('[data-testid="mobile-product-card"]').should('be.visible')
    })
  })
})
