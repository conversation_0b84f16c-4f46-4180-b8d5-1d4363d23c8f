describe('Advanced Batch Number Input', () => {
  beforeEach(() => {
    // Setup basic API interceptors
    cy.intercept('POST', '/api/procurement/batch-validation/real-time', {
      statusCode: 200,
      body: {
        isValid: true,
        level: 'success',
        message: 'Batch number valid dan dapat digunakan',
        suggestions: []
      }
    }).as('validateBatchRealTime')

    cy.intercept('GET', '/api/procurement/batch-validation/rules', {
      statusCode: 200,
      body: {
        formats: {
          'Standard': {
            description: 'Format standar untuk produk farmasi',
            example: 'ABC123-2024-001',
            pattern: '^[A-Z0-9]{3,}-[0-9]{4}-[0-9]{3}$'
          }
        }
      }
    }).as('getBatchValidationRules')

    cy.intercept('GET', '/api/procurement/batch-validation/history/*', {
      statusCode: 200,
      body: {
        batchNumber: 'BATCH-HISTORY-001',
        usageHistory: [],
        totalReceived: 100,
        totalUsed: 25,
        currentStock: 75,
        status: 'active',
        storageLocations: ['Gudang Utama']
      }
    }).as('getBatchHistory')

    // Navigate to new goods receipt page where the component is used
    cy.visit('/dashboard/goods-receipts/new')

    // Wait for page to load
    cy.get('h1').contains('Penerimaan Barang Baru').should('be.visible')
  })

  describe('Komponen Dasar', () => {
    it('should render batch number input correctly', () => {
      // Check if the batch number input exists
      cy.get('[name="items.0.batchNumber"]').should('be.visible')
      cy.get('[name="items.0.batchNumber"]').should('have.attr', 'placeholder', 'Masukkan nomor batch...')
    })

    it('should accept batch number input', () => {
      const batchNumber = 'BATCH-TEST-001'
      
      // Type in batch number
      cy.get('[name="items.0.batchNumber"]').type(batchNumber)
      
      // Verify the value is entered
      cy.get('[name="items.0.batchNumber"]').should('have.value', batchNumber.toUpperCase())
    })

    it('should show validation icon when typing', () => {
      // First select a product to enable validation
      cy.selectDropdownOption('[name="items.0.productId"]', 'Paracetamol 500mg')
      
      // Type batch number
      cy.get('[name="items.0.batchNumber"]').type('BATCH-001')
      
      // Wait for validation to trigger
      cy.wait(600) // Wait for debounce
      
      // Check for validation icon
      cy.get('[name="items.0.batchNumber"]').parent().within(() => {
        cy.get('svg').should('be.visible')
      })
    })
  })

  describe('Dialog Panduan Format', () => {
    beforeEach(() => {
      // Switch to mobile view where card variant is used (showFormatGuide=true)
      cy.viewport('iphone-x')
      cy.waitForLoadingToFinish()
    })

    it('should show format guide button when in card view (mobile)', () => {
      // Check if format guide button exists in mobile card view
      cy.contains('button', 'Panduan Format').should('be.visible')
    })

    it('should open format guide dialog when button clicked', () => {
      // Click format guide button
      cy.contains('button', 'Panduan Format').click()
      
      // Verify dialog opened
      cy.get('[role="dialog"]').should('be.visible')
      cy.contains('Panduan Format Nomor Batch').should('be.visible')
      cy.contains('Pilih format yang sesuai dengan jenis produk dan standar yang berlaku').should('be.visible')
    })

    it('should display format examples in dialog', () => {
      // Open format guide dialog
      cy.contains('button', 'Panduan Format').click()
      
      // Check for format examples
      cy.get('[role="dialog"]').within(() => {
        // Should show format badges
        cy.get('[data-testid="format-badge"]').should('exist')
        
        // Should show examples
        cy.contains('Contoh:').should('be.visible')
        
        // Should show descriptions
        cy.get('.text-muted-foreground').should('contain.text', 'Format')
      })
    })

    it('should close format guide dialog', () => {
      // Open dialog
      cy.contains('button', 'Panduan Format').click()
      cy.get('[role="dialog"]').should('be.visible')
      
      // Close dialog by clicking outside or close button
      cy.get('body').type('{esc}')
      
      // Verify dialog closed
      cy.get('[role="dialog"]').should('not.exist')
    })
  })

  describe('Dialog Riwayat Batch', () => {
    beforeEach(() => {
      // Switch to mobile view where card variant is used (showHistory=true)
      cy.viewport('iphone-x')
      cy.waitForLoadingToFinish()

      // Enter a batch number first
      cy.get('[name="items.0.batchNumber"]').type('BATCH-HISTORY-001')
    })

    it('should show history button when in card view (mobile) and value exists', () => {
      // Check if history button exists
      cy.contains('button', 'Riwayat Batch').should('be.visible')
    })

    it('should not show history button when value is empty', () => {
      // Clear the batch number
      cy.get('[name="items.0.batchNumber"]').clear()
      
      // History button should not be visible
      cy.contains('button', 'Riwayat Batch').should('not.exist')
    })

    it('should open batch history dialog when button clicked', () => {
      // Click history button
      cy.contains('button', 'Riwayat Batch').click()
      
      // Verify dialog opened
      cy.get('[role="dialog"]').should('be.visible')
      cy.contains('Riwayat Batch BATCH-HISTORY-001').should('be.visible')
      cy.contains('Riwayat lengkap penggunaan dan pergerakan batch number ini').should('be.visible')
    })

    it('should display batch history tabs', () => {
      // Open history dialog
      cy.contains('button', 'Riwayat Batch').click()
      
      // Check for tabs
      cy.get('[role="dialog"]').within(() => {
        cy.contains('Ringkasan').should('be.visible')
        cy.contains('Riwayat Aktivitas').should('be.visible')
        cy.contains('Lokasi Penyimpanan').should('be.visible')
      })
    })

    it('should close batch history dialog', () => {
      // Open dialog
      cy.contains('button', 'Riwayat Batch').click()
      cy.get('[role="dialog"]').should('be.visible')
      
      // Close dialog
      cy.get('body').type('{esc}')
      
      // Verify dialog closed
      cy.get('[role="dialog"]').should('not.exist')
    })
  })

  describe('Real-time Validation', () => {
    it('should validate batch number when product is selected', () => {
      // Type batch number
      cy.get('[name="items.0.batchNumber"]').type('BATCH-VALID-001')

      // Wait for debounce and validation
      cy.wait(600)

      // Check validation result - should show validation icon
      cy.get('[name="items.0.batchNumber"]').parent().within(() => {
        // Should show validation icon (success, warning, or error)
        cy.get('svg').should('be.visible')
      })
    })

    it('should show warning when batch number exists but no product selected', () => {
      // Type batch number without selecting product
      cy.get('[name="items.0.batchNumber"]').type('BATCH-NO-PRODUCT')
      
      // Should show warning styling
      cy.get('[name="items.0.batchNumber"]').should('have.class', 'border-yellow-400')
    })

    it('should show validation tooltip on hover', () => {
      // Enter batch number
      cy.get('[name="items.0.batchNumber"]').type('BATCH-TOOLTIP-001')

      // Wait for validation
      cy.wait(600)

      // Hover over validation icon
      cy.get('[name="items.0.batchNumber"]').parent().within(() => {
        cy.get('svg').trigger('mouseover')
      })

      // Check tooltip appears
      cy.get('[role="tooltip"]').should('be.visible')
    })
  })

  describe('Integrasi dengan Form', () => {
    it('should validate required batch number on form submission', () => {
      // Try to submit without batch number
      cy.get('button[type="submit"]').click()

      // Check validation error
      cy.contains('Nomor batch wajib diisi').should('be.visible')
    })

    it('should accept batch number input in form', () => {
      // Fill batch number
      cy.get('[name="items.0.batchNumber"]').type('BATCH-FORM-001')

      // Verify the value is entered and converted to uppercase
      cy.get('[name="items.0.batchNumber"]').should('have.value', 'BATCH-FORM-001')
    })
  })

  describe('Responsive Design', () => {
    it('should work correctly on mobile devices', () => {
      // Set mobile viewport
      cy.viewport('iphone-x')
      
      // Check mobile layout
      cy.get('[name="items.0.batchNumber"]').should('be.visible')
      
      // Test input functionality on mobile
      cy.get('[name="items.0.batchNumber"]').type('MOBILE-BATCH-001')
      cy.get('[name="items.0.batchNumber"]').should('have.value', 'MOBILE-BATCH-001')
      
      // Test dialog on mobile (already in mobile viewport)
      cy.contains('button', 'Panduan Format').click()
      
      // Dialog should be responsive
      cy.get('[role="dialog"]').should('be.visible')
      cy.get('[role="dialog"]').should('have.css', 'max-width')
    })
  })

  describe('Accessibility', () => {
    it('should be accessible with keyboard navigation', () => {
      // Focus on batch input
      cy.get('[name="items.0.batchNumber"]').focus()
      cy.get('[name="items.0.batchNumber"]').should('have.focus')
      
      // Type with keyboard
      cy.get('[name="items.0.batchNumber"]').type('KEYBOARD-BATCH-001')
      
      // Switch to mobile view to access dialog buttons
      cy.viewport('iphone-x')
      cy.waitForLoadingToFinish()

      // Navigate to format button and test keyboard interaction
      cy.contains('button', 'Panduan Format').focus()
      cy.contains('button', 'Panduan Format').should('have.focus')

      // Open dialog with Enter
      cy.focused().type('{enter}')
      cy.get('[role="dialog"]').should('be.visible')

      // Close with Escape
      cy.get('body').type('{esc}')
      cy.get('[role="dialog"]').should('not.exist')
    })

    it('should have proper ARIA attributes', () => {
      // Check input has proper attributes
      cy.get('[name="items.0.batchNumber"]').should('have.attr', 'type', 'text')
      
      // Switch to mobile view to access dialog buttons
      cy.viewport('iphone-x')
      cy.waitForLoadingToFinish()

      // Check buttons have proper labels
      cy.contains('button', 'Panduan Format').should('have.attr', 'type', 'button')
      cy.contains('button', 'Riwayat Batch').should('not.exist') // Should not exist without value

      // Add value and check history button
      cy.get('[name="items.0.batchNumber"]').type('ARIA-BATCH-001')
      cy.contains('button', 'Riwayat Batch').should('have.attr', 'type', 'button')
    })
  })
})
