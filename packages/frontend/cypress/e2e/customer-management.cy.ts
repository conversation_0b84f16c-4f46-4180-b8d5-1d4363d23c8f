describe('Manajemen Pelanggan', () => {
  beforeEach(() => {
    // Setup API interceptors
    cy.setupApiInterceptors()
    
    // Login as admin
    cy.loginAsAdmin()
    
    // Navigate to customers page
    cy.visit('/dashboard/customers')
    cy.waitForLoadingToFinish()
  })

  afterEach(() => {
    // Clean up test data
    cy.cleanTestData()
  })

  describe('Daftar Pelanggan', () => {
    it('should display customer list correctly', () => {
      // Check page title
      cy.contains('h1', 'Daftar Pelanggan').should('be.visible')
      
      // Check table headers
      cy.get('table thead').within(() => {
        cy.contains('Kode Pelanggan').should('be.visible')
        cy.contains('<PERSON>a <PERSON>').should('be.visible')
        cy.contains('No. Telepon').should('be.visible')
        cy.contains('Tipe').should('be.visible')
        cy.contains('Level Membership').should('be.visible')
        cy.contains('Status').should('be.visible')
        cy.contains('Aksi').should('be.visible')
      })
      
      // Check if customers are loaded
      cy.get('table tbody tr').should('have.length.greaterThan', 0)
      
      // Verify API call was made
      cy.wait('@getCustomers')
    })

    it('should search customers correctly', () => {
      const searchTerm = 'Budi'
      
      // Use search input
      cy.get('[data-testid="customer-search"]').type(searchTerm)
      
      // Wait for debounced search
      cy.waitForLoadingToFinish()
      
      // Check filtered results
      cy.get('table tbody tr').each(($row) => {
        cy.wrap($row).should('contain.text', searchTerm)
      })
      
      // Clear search
      cy.get('[data-testid="customer-search"]').clear()
      cy.waitForLoadingToFinish()
    })

    it('should filter customers by type', () => {
      // Open type filter
      cy.get('[data-testid="type-filter"]').click()
      
      // Select registered customers
      cy.selectDropdownOption('[data-testid="type-filter"]', 'Terdaftar')
      
      // Wait for filter to apply
      cy.waitForLoadingToFinish()
      
      // Verify filtered results
      cy.get('table tbody tr').each(($row) => {
        cy.wrap($row).should('contain.text', 'Terdaftar')
      })
    })

    it('should filter customers by membership level', () => {
      // Open membership filter
      cy.get('[data-testid="membership-filter"]').click()
      
      // Select Gold members
      cy.selectDropdownOption('[data-testid="membership-filter"]', 'Gold')
      
      // Wait for filter to apply
      cy.waitForLoadingToFinish()
      
      // Verify filtered results
      cy.get('table tbody tr').each(($row) => {
        cy.wrap($row).should('contain.text', 'Gold')
      })
    })
  })

  describe('Tambah Pelanggan', () => {
    it('should create new registered customer successfully', () => {
      const newCustomer = {
        code: 'CUST-TEST-001',
        fullName: 'Test Customer E2E',
        phoneNumber: '081234567890',
        email: '<EMAIL>',
        address: 'Jl. Test No. 123, Jakarta',
        type: 'Terdaftar',
        membershipLevel: 'Bronze',
      }

      // Click add customer button
      cy.get('[data-testid="add-customer-button"]').click()
      
      // Verify modal opened
      cy.get('[role="dialog"]').should('be.visible')
      cy.contains('Tambah Pelanggan Baru').should('be.visible')
      
      // Fill form
      cy.fillForm({
        code: newCustomer.code,
        fullName: newCustomer.fullName,
        phoneNumber: newCustomer.phoneNumber,
        email: newCustomer.email,
        address: newCustomer.address,
      })
      
      // Select customer type
      cy.selectDropdownOption('[name="type"]', newCustomer.type)
      
      // Select membership level
      cy.selectDropdownOption('[name="membershipLevel"]', newCustomer.membershipLevel)
      
      // Submit form
      cy.get('button[type="submit"]').click()
      
      // Wait for API call
      cy.wait('@createCustomer')
      
      // Verify success message
      cy.checkToast('Pelanggan berhasil ditambahkan', 'success')
      
      // Verify modal closed
      cy.get('[role="dialog"]').should('not.exist')
      
      // Verify customer appears in list
      cy.contains(newCustomer.fullName).should('be.visible')
    })

    it('should create walk-in customer successfully', () => {
      const walkInCustomer = {
        fullName: 'Customer Walk-in Test',
        type: 'Walk-in',
      }

      // Click add customer button
      cy.get('[data-testid="add-customer-button"]').click()
      
      // Fill minimal form for walk-in customer
      cy.get('[name="fullName"]').type(walkInCustomer.fullName)
      
      // Select walk-in type
      cy.selectDropdownOption('[name="type"]', walkInCustomer.type)
      
      // Submit form
      cy.get('button[type="submit"]').click()
      
      // Wait for API call
      cy.wait('@createCustomer')
      
      // Verify success message
      cy.checkToast('Pelanggan berhasil ditambahkan', 'success')
    })

    it('should validate required fields for registered customer', () => {
      // Click add customer button
      cy.get('[data-testid="add-customer-button"]').click()
      
      // Select registered type first
      cy.selectDropdownOption('[name="type"]', 'Terdaftar')
      
      // Try to submit empty form
      cy.get('button[type="submit"]').click()
      
      // Check validation errors
      cy.contains('Nama lengkap wajib diisi').should('be.visible')
      cy.contains('No. telepon wajib diisi').should('be.visible')
      cy.contains('Email wajib diisi').should('be.visible')
    })

    it('should validate phone number format', () => {
      // Click add customer button
      cy.get('[data-testid="add-customer-button"]').click()
      
      // Enter invalid phone number
      cy.get('[name="phoneNumber"]').type('123')
      
      // Check validation error
      cy.contains('Format nomor telepon tidak valid').should('be.visible')
    })

    it('should validate email format', () => {
      // Click add customer button
      cy.get('[data-testid="add-customer-button"]').click()
      
      // Enter invalid email
      cy.get('[name="email"]').type('invalid-email')
      
      // Check validation error
      cy.contains('Format email tidak valid').should('be.visible')
    })
  })

  describe('Edit Pelanggan', () => {
    it('should edit customer successfully', () => {
      const updatedData = {
        fullName: 'Updated Customer Name',
        phoneNumber: '081987654321',
        email: '<EMAIL>',
      }

      // Click edit button on first customer
      cy.get('table tbody tr:first-child [data-testid="edit-customer-button"]').click()
      
      // Verify modal opened with existing data
      cy.get('[role="dialog"]').should('be.visible')
      cy.contains('Edit Pelanggan').should('be.visible')
      
      // Update form fields
      cy.get('[name="fullName"]').clear().type(updatedData.fullName)
      cy.get('[name="phoneNumber"]').clear().type(updatedData.phoneNumber)
      cy.get('[name="email"]').clear().type(updatedData.email)
      
      // Submit form
      cy.get('button[type="submit"]').click()
      
      // Wait for API call
      cy.wait('@updateCustomer')
      
      // Verify success message
      cy.checkToast('Pelanggan berhasil diperbarui', 'success')
      
      // Verify updated data in list
      cy.contains(updatedData.fullName).should('be.visible')
    })

    it('should upgrade membership level', () => {
      // Click edit button on a Bronze customer
      cy.get('table tbody tr').contains('Bronze').parent('tr').within(() => {
        cy.get('[data-testid="edit-customer-button"]').click()
      })
      
      // Change membership level to Silver
      cy.selectDropdownOption('[name="membershipLevel"]', 'Silver')
      
      // Submit form
      cy.get('button[type="submit"]').click()
      
      // Wait for API call
      cy.wait('@updateCustomer')
      
      // Verify success message
      cy.checkToast('Pelanggan berhasil diperbarui', 'success')
    })
  })

  describe('Detail Pelanggan', () => {
    it('should view customer details', () => {
      // Click view button on first customer
      cy.get('table tbody tr:first-child [data-testid="view-customer-button"]').click()
      
      // Verify detail modal opened
      cy.get('[role="dialog"]').should('be.visible')
      cy.contains('Detail Pelanggan').should('be.visible')
      
      // Check customer information sections
      cy.contains('Informasi Dasar').should('be.visible')
      cy.contains('Informasi Kontak').should('be.visible')
      cy.contains('Informasi Membership').should('be.visible')
      
      // Check if customer data is displayed
      cy.get('[data-testid="customer-code"]').should('not.be.empty')
      cy.get('[data-testid="customer-name"]').should('not.be.empty')
      cy.get('[data-testid="customer-type"]').should('not.be.empty')
    })

    it('should view customer purchase history', () => {
      // Click view button on first customer
      cy.get('table tbody tr:first-child [data-testid="view-customer-button"]').click()
      
      // Navigate to purchase history tab
      cy.get('[data-testid="purchase-history-tab"]').click()
      
      // Check purchase history table
      cy.get('[data-testid="purchase-history-table"]').should('be.visible')
      
      // Verify table headers
      cy.contains('Tanggal').should('be.visible')
      cy.contains('No. Transaksi').should('be.visible')
      cy.contains('Total').should('be.visible')
      cy.contains('Status').should('be.visible')
    })
  })

  describe('Customer Code Generation', () => {
    it('should generate customer code automatically', () => {
      // Click add customer button
      cy.get('[data-testid="add-customer-button"]').click()
      
      // Select customer type
      cy.selectDropdownOption('[name="type"]', 'Terdaftar')
      
      // Click generate code button
      cy.get('[data-testid="generate-code-button"]').click()
      
      // Verify code is generated
      cy.get('[name="code"]').should('not.have.value', '')
      cy.get('[name="code"]').should('match', /^CUST-\d{3}$/)
    })

    it('should validate customer code uniqueness', () => {
      // Click add customer button
      cy.get('[data-testid="add-customer-button"]').click()
      
      // Enter existing customer code
      cy.get('[name="code"]').type('CUST-001')
      
      // Wait for validation
      cy.waitForLoadingToFinish()
      
      // Check validation error
      cy.contains('Kode pelanggan sudah digunakan').should('be.visible')
    })
  })

  describe('Customer Search and Selection', () => {
    it('should search customers in selector component', () => {
      // Navigate to POS page where customer selector is used
      cy.visit('/dashboard/pos')
      cy.waitForLoadingToFinish()
      
      // Open customer selector
      cy.get('[data-testid="customer-selector"]').click()
      
      // Search for customer
      cy.get('[data-testid="customer-search-input"]').type('Budi')
      
      // Wait for search results
      cy.waitForLoadingToFinish()
      
      // Verify search results
      cy.get('[data-testid="customer-option"]').should('contain.text', 'Budi')
      
      // Select customer
      cy.get('[data-testid="customer-option"]').first().click()
      
      // Verify customer is selected
      cy.get('[data-testid="selected-customer"]').should('contain.text', 'Budi')
    })
  })

  describe('Bulk Operations', () => {
    it('should export customer data', () => {
      // Click export button
      cy.get('[data-testid="export-customers-button"]').click()
      
      // Verify export options modal
      cy.get('[role="dialog"]').should('be.visible')
      cy.contains('Ekspor Data Pelanggan').should('be.visible')
      
      // Select export format
      cy.selectDropdownOption('[name="format"]', 'Excel')
      
      // Confirm export
      cy.get('[data-testid="confirm-export-button"]').click()
      
      // Verify download started
      cy.readFile('cypress/downloads/data-pelanggan.xlsx').should('exist')
    })

    it('should import customer data', () => {
      // Click import button
      cy.get('[data-testid="import-customers-button"]').click()
      
      // Upload file
      cy.uploadFile('[data-testid="file-upload"]', 'customers-import.xlsx', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
      
      // Submit import
      cy.get('[data-testid="submit-import-button"]').click()
      
      // Wait for import to complete
      cy.waitForLoadingToFinish()
      
      // Verify success message
      cy.checkToast('Data pelanggan berhasil diimpor', 'success')
    })
  })
})
