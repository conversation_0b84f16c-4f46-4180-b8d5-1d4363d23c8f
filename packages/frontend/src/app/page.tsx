'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export default function Home() {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    if (status === 'loading') return; // Still loading
    if (session) {
      router.push('/dashboard');
    }
  }, [session, status, router]);

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  if (session) {
    return null; // Will redirect to dashboard
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-16">
        <div className="text-center mb-16">
          <h1 className="text-4xl font-bold mb-4">Selamat Datang di Apotek App</h1>
          <p className="text-xl text-muted-foreground mb-8">
            Solusi lengkap manajemen apotek untuk Indonesia
          </p>
          <div className="flex gap-4 justify-center">
            <Link href="/auth/login">
              <Button size="lg">Masuk</Button>
            </Link>
            <Link href="/auth/register">
              <Button variant="outline" size="lg">Buat Akun</Button>
            </Link>
          </div>
        </div>

        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3 max-w-6xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle>🔐 Autentikasi Aman</CardTitle>
              <CardDescription>
                Sistem autentikasi lengkap dengan pendaftaran, masuk, dan rute terlindungi
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Autentikasi berbasis JWT</li>
                <li>• Validasi kata sandi</li>
                <li>• Manajemen sesi</li>
                <li>• Perlindungan rute</li>
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>🏗️ Arsitektur Modern</CardTitle>
              <CardDescription>
                Dibangun dengan teknologi terbaru dan praktik terbaik
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Next.js 15 dengan App Router</li>
                <li>• API backend NestJS</li>
                <li>• Database PostgreSQL</li>
                <li>• Prisma ORM</li>
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>🎨 Antarmuka Indah</CardTitle>
              <CardDescription>
                Antarmuka pengguna yang bersih dan responsif dengan Tailwind CSS
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Tailwind CSS v4</li>
                <li>• Komponen UI kustom</li>
                <li>• Desain responsif</li>
                <li>• Dukungan mode gelap</li>
              </ul>
            </CardContent>
          </Card>
        </div>

        <div className="mt-16 text-center">
          <Card className="max-w-2xl mx-auto">
            <CardHeader>
              <CardTitle>Siap untuk Memulai?</CardTitle>
              <CardDescription>
                Buat akun Anda atau masuk untuk mengakses dashboard apotek
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex gap-4 justify-center">
                <Link href="/auth/register">
                  <Button>Buat Akun</Button>
                </Link>
                <Link href="/auth/login">
                  <Button variant="outline">Masuk</Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
