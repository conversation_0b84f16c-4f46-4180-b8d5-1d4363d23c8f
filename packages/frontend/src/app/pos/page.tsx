'use client';

import { useState } from 'react';
import { useSession } from 'next-auth/react';
import { PosClient, PosTab } from '@/components/pos/PosClient';
import { PosHeader } from '@/components/pos/PosHeader';
import { PosHelpDialog } from '@/components/pos/PosHelpDialog';
import { SessionData } from '@/types/session';

export default function PosPage() {
  const { data: session, status } = useSession();
  const [sessionData, setSessionData] = useState<SessionData | null>(null);
  const [endSessionHandler, setEndSessionHandler] = useState<(() => void) | null>(null);
  const [activeTab, setActiveTab] = useState<PosTab>('cart');
  const [showHelp, setShowHelp] = useState(false);

  const handleSessionChange = (newSessionData: SessionData | null) => {
    setSessionData(newSessionData);
  };

  const handleEndSessionRequest = (handler: () => void) => {
    setEndSessionHandler(() => handler);
  };

  const handleEndSession = () => {
    if (endSessionHandler) {
      endSessionHandler();
    }
  };

  if (status === 'loading') {
    return <div className="flex min-h-screen items-center justify-center">Loading...</div>;
  }

  if (!session || !session.user) {
    return null; // This will be handled by the layout redirect
  }

  return (
    <div className="flex flex-col h-screen overflow-hidden">
      <div className="flex-none">
        <PosHeader
          user={session.user}
          sessionData={sessionData}
          onEndSession={handleEndSession}
          activeTab={activeTab}
          onTabChange={setActiveTab}
          onShowHelp={() => setShowHelp(true)}
        />
      </div>
      <div className="flex-1 min-h-0">
        <PosClient
          user={session.user}
          onSessionChange={handleSessionChange}
          onEndSessionRequest={handleEndSessionRequest}
          activeTab={activeTab}
          onTabChange={setActiveTab}
        />
      </div>

      <PosHelpDialog
        open={showHelp}
        onOpenChange={setShowHelp}
      />
    </div>
  );
}