'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';

export default function PosLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    // Check authentication
    if (status === 'unauthenticated') {
      router.push('/auth/login');
      return;
    }

    if (status === 'authenticated') {
      // Only ADMIN, PHARMACIST, and CASHIER roles can access POS
      const allowedRoles = ['ADMIN', 'PHARMACIST', 'CASHIER'];
      if (!allowedRoles.includes(session.user.role)) {
        router.push('/dashboard');
      }
    }
  }, [session, status, router]);

  // Set CSS variables for POS layout
  useEffect(() => {
    // Set CSS variables for content height
    document.documentElement.style.setProperty('--pos-header-height', '4rem');
    document.documentElement.style.setProperty('--pos-content-height', 'calc(100vh - var(--pos-header-height))');
    
    // Add class to body for POS-specific styling
    document.body.classList.add('pos-mode');
    
    return () => {
      // Clean up CSS variables and classes
      document.documentElement.style.removeProperty('--pos-header-height');
      document.documentElement.style.removeProperty('--pos-content-height');
      document.body.classList.remove('pos-mode');
    };
  }, []);

  if (status === 'loading') {
    return <div className="flex min-h-screen items-center justify-center">Loading...</div>;
  }

  return (
    <div className="flex min-h-screen flex-col bg-background max-h-screen overflow-hidden">
      {children}
    </div>
  );
} 