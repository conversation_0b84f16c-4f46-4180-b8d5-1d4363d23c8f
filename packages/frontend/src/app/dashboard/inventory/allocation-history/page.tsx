import { requireAuth } from '@/lib/server/auth';
import { SiteHeader } from '@/components/site-header';
import { AllocationHistoryClient } from '@/components/inventory/AllocationHistoryClient';

export default async function AllocationHistoryPage() {
  // Require authentication on server side
  const session = await requireAuth();

  return (
    <>
      <SiteHeader user={session.user} />
      <div className="@container/main h-(--content-height) overflow-y-auto">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          <div className="px-4 lg:px-6">
            <AllocationHistoryClient />
          </div>
        </div>
      </div>
    </>
  );
}

export const metadata = {
  title: 'Riwayat Alokasi Stok - Sistem Manajemen Apotek',
  description: 'Riwayat lengkap alokasi stok dengan metode FIFO/FEFO',
};
