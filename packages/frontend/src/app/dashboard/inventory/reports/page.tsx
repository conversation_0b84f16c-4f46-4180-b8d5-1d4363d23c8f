import { requireAuth } from '@/lib/server/auth';
import { SiteHeader } from '@/components/site-header';
import { InventoryReportsClient } from '@/components/inventory/InventoryReportsClient';

export default async function InventoryReportsPage() {
  // Require authentication on server side
  const session = await requireAuth();

  return (
    <>
      <SiteHeader user={session.user} />
      <div className="@container/main h-(--content-height) overflow-y-auto">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          <div className="px-4 lg:px-6">
            <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
              <div>
                <h1 className="text-2xl font-bold tracking-tight">Laporan Inventori</h1>
                <p className="text-muted-foreground">
                  Buat dan kelola laporan inventori dengan berbagai format dan filter
                </p>
              </div>
            </div>
          </div>

          <div className="px-4 lg:px-6">
            {/* Client-side interactive components */}
            <InventoryReportsClient />
          </div>
        </div>
      </div>
    </>
  );
}
