import { requireAuth } from '@/lib/server/auth';
import { SiteHeader } from '@/components/site-header';
import { InventoryFormClient } from '@/components/inventory/InventoryFormClient';

export default async function CreateInventoryItemPage() {
  // Require authentication on server side
  const session = await requireAuth();

  return (
    <>
      <SiteHeader user={session.user} />
      <div className="@container/main h-(--content-height) overflow-y-auto">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          <div className="px-4 lg:px-6">
            <div>
              <h1 className="text-2xl font-bold tracking-tight">Tambah Item Inventori Baru</h1>
              <p className="text-muted-foreground">
                Tambahkan item inventori baru dengan informasi batch, harga, dan lokasi penyimpanan
              </p>
            </div>
          </div>

          <div className="px-4 lg:px-6">
            <InventoryFormClient />
          </div>
        </div>
      </div>
    </>
  );
}
