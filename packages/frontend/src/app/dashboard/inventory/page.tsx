import { requireAuth } from '@/lib/server/auth';
import { SiteHeader } from '@/components/site-header';
import { InventoryPageClient } from '@/components/inventory/InventoryPageClient';
import { InventoryStatsCards } from '@/components/inventory/InventoryStatsCards';
import { InventoryQueryParams } from '@/types/inventory';

interface InventoryPageProps {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

export default async function InventoryPage({ searchParams }: InventoryPageProps) {
  // Require authentication on server side
  const session = await requireAuth();

  // Await searchParams before accessing properties (Next.js 15+ requirement)
  const resolvedSearchParams = await searchParams;

  // Parse search parameters for filtering
  const filters: InventoryQueryParams = {
    search: typeof resolvedSearchParams.search === 'string' ? resolvedSearchParams.search : undefined,
    productId: typeof resolvedSearchParams.productId === 'string' ? resolvedSearchParams.productId : undefined,
    supplierId: typeof resolvedSearchParams.supplierId === 'string' ? resolvedSearchParams.supplierId : undefined,
    location: typeof resolvedSearchParams.location === 'string' ? resolvedSearchParams.location : undefined,
    isActive: typeof resolvedSearchParams.isActive === 'string' ? resolvedSearchParams.isActive === 'true' : undefined,
    lowStock: typeof resolvedSearchParams.lowStock === 'string' ? resolvedSearchParams.lowStock === 'true' : undefined,
    expiringSoon: typeof resolvedSearchParams.expiringSoon === 'string' ? resolvedSearchParams.expiringSoon === 'true' : undefined,
    expired: typeof resolvedSearchParams.expired === 'string' ? resolvedSearchParams.expired === 'true' : undefined,
    page: typeof resolvedSearchParams.page === 'string' ? parseInt(resolvedSearchParams.page) : 1,
    limit: typeof resolvedSearchParams.limit === 'string' ? parseInt(resolvedSearchParams.limit) : 10,
    sortBy: typeof resolvedSearchParams.sortBy === 'string' ? resolvedSearchParams.sortBy : 'createdAt',
    sortOrder: typeof resolvedSearchParams.sortOrder === 'string' ? resolvedSearchParams.sortOrder as 'asc' | 'desc' : 'desc',
  };

  return (
    <>
      <SiteHeader user={session.user} />
      <div className="@container/main h-(--content-height) overflow-y-auto">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          {/* Stats Cards */}
          <div className="px-4 lg:px-6">
            <InventoryStatsCards />
          </div>

          {/* Client-side interactive components */}
          <div className="px-4 lg:px-6">
            <InventoryPageClient
              initialQuery={filters}
            />
          </div>
        </div>
      </div>
    </>
  );
}
