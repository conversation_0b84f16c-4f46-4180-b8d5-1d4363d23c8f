import { requireAuth } from '@/lib/server/auth';
import { SiteHeader } from '@/components/site-header';
import { createServerInventoryApi } from '@/lib/server/inventory';
import { InventoryDetailClient } from '@/components/inventory/InventoryDetailClient';
import { notFound } from 'next/navigation';

interface InventoryDetailPageProps {
  params: Promise<{ id: string }>;
}

export default async function InventoryDetailPage({ params }: InventoryDetailPageProps) {
  // Require authentication on server side
  const session = await requireAuth();

  // Await params before accessing properties (Next.js 15+ requirement)
  const resolvedParams = await params;
  const { id } = resolvedParams;

  try {
    // Fetch inventory item data on server side
    const inventoryApi = createServerInventoryApi(session);
    const inventoryItem = await inventoryApi.getInventoryItem(id);

    return (
      <>
        <SiteHeader user={session.user} />
        <div className="@container/main h-(--content-height) overflow-y-auto">
          <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
            <div className="px-4 lg:px-6">
              <InventoryDetailClient inventoryItem={inventoryItem} inventoryItemId={id} />
            </div>
          </div>
        </div>
      </>
    );
  } catch (error: any) {
    if (error?.response?.status === 404 || error?.message?.includes('404')) {
      notFound();
    }
    throw error;
  }
}
