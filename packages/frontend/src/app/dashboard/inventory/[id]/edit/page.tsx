import { requireAuth } from '@/lib/server/auth';
import { SiteHeader } from '@/components/site-header';
import { createServerInventoryApi } from '@/lib/server/inventory';
import { InventoryFormClient } from '@/components/inventory/InventoryFormClient';
import { notFound } from 'next/navigation';

interface EditInventoryPageProps {
  params: Promise<{ id: string }>;
}

export default async function EditInventoryPage({ params }: EditInventoryPageProps) {
  // Require authentication on server side
  const session = await requireAuth();

  // Await params before accessing properties (Next.js 15+ requirement)
  const resolvedParams = await params;
  const { id } = resolvedParams;

  try {
    // Fetch inventory item data on server side
    const inventoryApi = createServerInventoryApi(session);
    const inventoryItem = await inventoryApi.getInventoryItem(id);

    return (
      <>
        <SiteHeader user={session.user} />
        <div className="@container/main h-(--content-height) overflow-y-auto">
          <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
            <div className="px-4 lg:px-6">
              <div>
                <h1 className="text-2xl font-bold tracking-tight">Edit Item Inventori</h1>
                <p className="text-muted-foreground">
                  Edit informasi item inventori untuk {inventoryItem.product?.name || 'produk'}
                </p>
              </div>
            </div>

            <div className="px-4 lg:px-6">
              <InventoryFormClient inventoryItem={inventoryItem} mode="edit" />
            </div>
          </div>
        </div>
      </>
    );
  } catch (error: any) {
    if (error?.message?.includes('404') || error?.response?.status === 404) {
      notFound();
    }
    throw error;
  }
}
