import Link from 'next/link';
import { Button } from '@/components/ui/button';

export default function NotFound() {
  return (
    <div className="container mx-auto flex flex-col items-center justify-center py-20">
      <h2 className="text-2xl font-bold mb-4">Transaksi Tidak Ditemukan</h2>
      <p className="text-muted-foreground mb-8 text-center max-w-md">
        Transaksi yang Anda cari tidak ditemukan atau bukan merupakan draft yang dapat diedit.
        Hanya transaksi dengan status DRAFT yang dapat diubah.
      </p>
      <div className="flex gap-4">
        <Button asChild>
          <Link href="/dashboard/sales">
            Kembali ke Daftar Transaksi
          </Link>
        </Button>
        <Button variant="outline" asChild>
          <Link href="/dashboard/sales/create">
            Buat Transaksi Baru
          </Link>
        </Button>
      </div>
    </div>
  );
} 