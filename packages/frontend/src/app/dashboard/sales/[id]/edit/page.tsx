import { notFound } from 'next/navigation';
import { EditSaleFormClient } from '@/components/sales/EditSaleFormClient';
import { getSaleById } from '@/lib/server/sales';
import { SaleStatus } from '@prisma/client';
import { requireAuth } from '@/lib/server/auth';
import { SiteHeader } from '@/components/site-header';

export const metadata = {
    title: 'Edit Draft Penjualan',
};

export default async function EditSalePage({
    params,
}: {
    params: { id: string };
}) {
    try {
        const paramsPage = await params;
        const session = await requireAuth();
        const sale = await getSaleById(paramsPage.id);
        // Only allow editing of draft sales
        if (sale.status !== SaleStatus.DRAFT) {
            return notFound();
        }

        return (
            <>
                <SiteHeader user={session.user} />
                <div className="@container/main h-(--content-height) flex flex-col">
                    <EditSaleFormClient sale={sale} />
                </div>
            </>
        );
    } catch (error) {
        return notFound();
    }
} 