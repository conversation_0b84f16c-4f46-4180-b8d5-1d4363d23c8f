import { requireAuth } from '@/lib/server/auth';
import { SiteHeader } from '@/components/site-header';
import { CreateSaleFormClient } from '@/components/sales/CreateSaleFormClient';

export default async function CreateSalePage() {
  // Require authentication on server side
  const session = await requireAuth();

  return (
    <>
      <SiteHeader user={session.user} />
      {/* This page is in special case, so the structure a bit different from other pages */}
      <div className="@container/main h-(--content-height) flex flex-col">
        <CreateSaleFormClient />
      </div>
    </>
  );
}
