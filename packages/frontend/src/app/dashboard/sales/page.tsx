import { requireAuth } from '@/lib/server/auth';
import { SiteHeader } from '@/components/site-header';
import { SalesPageClient } from '@/components/sales/SalesPageClient';
import { SalesStatsCards } from '@/components/sales/SalesStatsCards';
import { SaleQueryParams } from '@/types/sales';

interface SalesPageProps {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

export default async function SalesPage({ searchParams }: SalesPageProps) {
  // Require authentication on server side
  const session = await requireAuth();

  // Await searchParams before accessing properties (Next.js 15+ requirement)
  const resolvedSearchParams = await searchParams;

  // Parse query parameters
  const filters: SaleQueryParams = {
    page: resolvedSearchParams.page ? parseInt(resolvedSearchParams.page as string) : 1,
    limit: resolvedSearchParams.limit ? parseInt(resolvedSearchParams.limit as string) : 10,
    search: resolvedSearchParams.search as string | undefined,
    status: resolvedSearchParams.status as any,
    paymentMethod: resolvedSearchParams.paymentMethod as any,
    customerId: resolvedSearchParams.customerId as string | undefined,
    cashierId: resolvedSearchParams.cashierId as string | undefined,
    startDate: resolvedSearchParams.startDate as string | undefined,
    endDate: resolvedSearchParams.endDate as string | undefined,
    sortBy: (resolvedSearchParams.sortBy as string) || 'createdAt',
    sortOrder: (resolvedSearchParams.sortOrder as 'asc' | 'desc') || 'desc',
  };

  return (
    <>
      <SiteHeader user={session.user} />
      <div className="@container/main h-(--content-height) overflow-y-auto">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          {/* Stats Cards */}
          <div className="px-4 lg:px-6">
            <SalesStatsCards />
          </div>

          {/* Client-side interactive components */}
          <div className="px-4 lg:px-6">
            <SalesPageClient
              initialQuery={filters}
            />
          </div>
        </div>
      </div>
    </>
  );
}
