import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { SiteHeaderSkeleton } from '@/components/site-header-skeleton';

export default function GoodsReceiptsLoading() {
  return (
    <>
      <SiteHeaderSkeleton />
      <div className="@container/main h-(--content-height) overflow-y-auto">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          {/* Stats Cards Skeleton */}
          <div className="px-4 lg:px-6">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              {Array.from({ length: 4 }).map((_, i) => (
                <Card key={i}>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-4 w-4" />
                  </CardHeader>
                  <CardContent>
                    <Skeleton className="h-8 w-20 mb-2" />
                    <Skeleton className="h-3 w-28" />
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          <div className="px-4 lg:px-6">
            {/* Header Actions Skeleton */}
            <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between mb-6">
              <div>
                <Skeleton className="h-8 w-48" />
                <Skeleton className="h-4 w-80 mt-2" />
              </div>
              <div className="flex flex-col gap-2 sm:flex-row">
                <Skeleton className="h-10 w-24" />
                <Skeleton className="h-10 w-24" />
                <Skeleton className="h-10 w-40" />
              </div>
            </div>

            {/* Data Table Skeleton */}
            <Card className="w-full min-w-0 max-w-full overflow-hidden">
              <CardContent className="p-0 sm:p-6">
                <div className="w-full min-w-0 max-w-full space-y-4">
                  {/* Toolbar Skeleton */}
                  <div className="flex flex-col gap-3 lg:flex-row lg:items-center lg:gap-4">
                    <Skeleton className="h-9 w-64" />
                    <div className="flex flex-wrap items-center gap-2">
                      <Skeleton className="h-9 w-32" />
                      <Skeleton className="h-9 w-40" />
                      <Skeleton className="h-9 w-24" />
                      <Skeleton className="h-9 w-20" />
                    </div>
                  </div>

                  {/* Table Skeleton */}
                  <div className="w-full overflow-hidden rounded-md border">
                    <div className="bg-blue-50 dark:bg-blue-950/30 px-4 py-3 border-b">
                      <Skeleton className="h-4 w-64 mx-auto" />
                      <Skeleton className="h-3 w-80 mx-auto mt-1" />
                    </div>
                    
                    {/* Table Header */}
                    <div className="border-b bg-muted/50">
                      <div className="grid grid-cols-6 gap-4 p-4">
                        {Array.from({ length: 6 }).map((_, i) => (
                          <Skeleton key={i} className="h-4 w-full" />
                        ))}
                      </div>
                    </div>
                    
                    {/* Table Rows */}
                    {Array.from({ length: 8 }).map((_, i) => (
                      <div key={i} className="border-b">
                        <div className="grid grid-cols-6 gap-4 p-4">
                          {Array.from({ length: 6 }).map((_, j) => (
                            <Skeleton key={j} className="h-4 w-full" />
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Pagination Skeleton */}
                  <div className="flex items-center justify-between">
                    <Skeleton className="h-4 w-32" />
                    <div className="flex items-center gap-2">
                      <Skeleton className="h-8 w-8" />
                      <Skeleton className="h-8 w-8" />
                      <Skeleton className="h-8 w-8" />
                      <Skeleton className="h-8 w-8" />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </>
  );
}
