import { Metadata } from 'next';
import { getServerSession } from 'next-auth';
import { redirect } from 'next/navigation';
import { authOptions } from '@/lib/auth';
import { SiteHeader } from '@/components/site-header';
import { GoodsReceiptDetailPageClient } from '@/components/goods-receipts/GoodsReceiptDetailPageClient';

export const metadata: Metadata = {
  title: 'Detail Penerimaan Barang - Apotek App',
  description: 'Detail penerimaan barang',
};

interface GoodsReceiptDetailPageProps {
  params: { id: string };
}

export default async function GoodsReceiptDetailPage({ params }: GoodsReceiptDetailPageProps) {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect('/auth/login');
  }

  const resolvedParams = await params;

  return (
    <>
      <SiteHeader user={session.user} />
      <div className="@container/main h-(--content-height) overflow-y-auto">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          <div className="px-4 lg:px-6">
            <GoodsReceiptDetailPageClient goodsReceiptId={resolvedParams.id} />
          </div>
        </div>
      </div>
    </>
  );
}
