import { Metadata } from 'next';
import { getServerSession } from 'next-auth';
import { redirect } from 'next/navigation';
import { authOptions } from '@/lib/auth';
import { SiteHeader } from '@/components/site-header';
import { EditGoodsReceiptPageClient } from '@/components/goods-receipts/EditGoodsReceiptPageClient';

export const metadata: Metadata = {
  title: 'Edit Penerimaan Barang - Apotek App',
  description: 'Edit penerimaan barang',
};

interface EditGoodsReceiptPageProps {
  params: { id: string };
}

export default async function EditGoodsReceiptPage({ params }: EditGoodsReceiptPageProps) {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect('/auth/login');
  }

  const resolvedParams = await params;

  return (
    <>
      <SiteHeader user={session.user} />
      <div className="@container/main h-(--content-height) overflow-y-auto">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          <div className="px-4 lg:px-6">
            <EditGoodsReceiptPageClient goodsReceiptId={resolvedParams.id} />
          </div>
        </div>
      </div>
    </>
  );
}
