import { Metadata } from 'next';
import { getServerSession } from 'next-auth';
import { redirect } from 'next/navigation';
import { authOptions } from '@/lib/auth';
import { SiteHeader } from '@/components/site-header';
import { NewGoodsReceiptPageClient } from '@/components/goods-receipts/NewGoodsReceiptPageClient';

export const metadata: Metadata = {
  title: 'Penerimaan Barang Baru - Apotek App',
  description: 'Buat penerimaan barang baru',
};

export default async function NewGoodsReceiptPage() {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect('/auth/login');
  }

  return (
    <>
      <SiteHeader user={session.user} />
      <div className="@container/main h-(--content-height) overflow-y-auto">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          <div className="px-4 lg:px-6">
            <NewGoodsReceiptPageClient />
          </div>
        </div>
      </div>
    </>
  );
}
