import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { SiteHeaderSkeleton } from '@/components/site-header-skeleton';

export default function NewGoodsReceiptLoading() {
  return (
    <>
      <SiteHeaderSkeleton />
      <div className="@container/main h-(--content-height) overflow-y-auto">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          <div className="px-4 lg:px-6">
            {/* Header Skeleton */}
            <div className="flex items-center gap-4 mb-6">
              <Skeleton className="h-9 w-20" />
              <div className="space-y-2">
                <Skeleton className="h-8 w-64" />
                <Skeleton className="h-4 w-48" />
              </div>
            </div>

            {/* Form Skeleton */}
            <Card className="shadow-none rounded-none border-x-0 gap-3 md:gap-6 md:border md:rounded-xl md:shadow">
              <CardHeader className="px-0 md:px-6">
                <CardTitle>
                  <Skeleton className="h-6 w-48" />
                </CardTitle>
              </CardHeader>
              <CardContent className="px-0 md:px-6 space-y-6">
                {/* Header Section Skeleton */}
                <div className="space-y-4">
                  <Skeleton className="h-6 w-40" />
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {Array.from({ length: 6 }).map((_, i) => (
                      <div key={i} className="space-y-2">
                        <Skeleton className="h-4 w-24" />
                        <Skeleton className="h-10 w-full" />
                      </div>
                    ))}
                  </div>
                </div>

                {/* Tabs Skeleton */}
                <div className="space-y-4">
                  <div className="flex space-x-1 bg-muted p-1 rounded-lg w-fit">
                    <Skeleton className="h-8 w-24" />
                    <Skeleton className="h-8 w-32" />
                  </div>

                  {/* Items Section Skeleton */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <Skeleton className="h-6 w-32" />
                      <Skeleton className="h-9 w-28" />
                    </div>

                    {/* Items Table Skeleton */}
                    <div className="border rounded-lg">
                      <div className="border-b bg-muted/50 p-4">
                        <div className="grid grid-cols-5 gap-4">
                          {Array.from({ length: 5 }).map((_, i) => (
                            <Skeleton key={i} className="h-4 w-full" />
                          ))}
                        </div>
                      </div>
                      {Array.from({ length: 3 }).map((_, i) => (
                        <div key={i} className="border-b p-4">
                          <div className="grid grid-cols-5 gap-4">
                            {Array.from({ length: 5 }).map((_, j) => (
                              <Skeleton key={j} className="h-8 w-full" />
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>

                    {/* Summary Skeleton */}
                    <div className="flex justify-end">
                      <div className="w-64 space-y-2">
                        {Array.from({ length: 4 }).map((_, i) => (
                          <div key={i} className="flex justify-between">
                            <Skeleton className="h-4 w-20" />
                            <Skeleton className="h-4 w-24" />
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Action Buttons Skeleton */}
                <div className="flex flex-col sm:flex-row gap-3 sm:justify-end">
                  <Skeleton className="h-10 w-24" />
                  <Skeleton className="h-10 w-32" />
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </>
  );
}
