import { requireAuth } from '@/lib/server/auth';
import { SiteHeader } from '@/components/site-header';
import { ProductFormClient } from '@/components/products/ProductFormClient';

export default async function CreateProductPage() {
  // Require authentication on server side
  const session = await requireAuth();

  return (
    <>
      <SiteHeader user={session.user} />
      <div className="@container/main h-(--content-height) overflow-y-auto">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          <div className="px-4 lg:px-6">
            <div>
              <h1 className="text-2xl font-bold tracking-tight">Tambah Produk Baru</h1>
              <p className="text-muted-foreground">
                Tambahkan produk obat, alat kesehatan, atau produk farmasi lainnya
              </p>
            </div>
          </div>

          <div className="px-4 lg:px-6">
            <ProductFormClient />
          </div>
        </div>
      </div>
    </>
  );
}
