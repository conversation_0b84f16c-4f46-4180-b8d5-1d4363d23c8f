import { requireAuth } from '@/lib/server/auth';
import { SiteHeader } from '@/components/site-header';
import { createProductsApi } from '@/lib/server/products';
import { ProductDetailClient } from '@/components/products/ProductDetailClient';
import { notFound } from 'next/navigation';

interface ProductDetailPageProps {
  params: Promise<{ id: string }>;
}

export default async function ProductDetailPage({ params }: ProductDetailPageProps) {
  // Require authentication on server side
  const session = await requireAuth();

  // Await params before accessing properties (Next.js 15+ requirement)
  const resolvedParams = await params;
  const { id } = resolvedParams;

  try {
    // Fetch product data on server side
    const productsApi = createProductsApi(session);
    const product = await productsApi.getProduct(id);

    return (
      <>
        <SiteHeader user={session.user} />
        <div className="@container/main h-(--content-height) overflow-y-auto">
          <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
            <div className="px-4 lg:px-6">
              <ProductDetailClient product={product} productId={id} />
            </div>
          </div>
        </div>
      </>
    );
  } catch (error: any) {
    if (error?.response?.status === 404) {
      notFound();
    }
    throw error;
  }
}
