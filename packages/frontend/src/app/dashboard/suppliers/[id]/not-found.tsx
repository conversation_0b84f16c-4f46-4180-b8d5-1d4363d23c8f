import Link from 'next/link';
import { ArrowLeft, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

export default function SupplierNotFound() {
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Button variant="outline" size="icon" asChild>
          <Link href="/dashboard/suppliers">
            <ArrowLeft className="h-4 w-4" />
          </Link>
        </Button>
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">Supplier Tidak Ditemukan</h1>
          <p className="text-muted-foreground">Supplier yang Anda cari tidak dapat ditemukan</p>
        </div>
      </div>
      
      <Card className="border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20">
        <CardContent className="pt-6">
          <div className="flex items-center gap-3">
            <AlertCircle className="h-5 w-5 text-red-600 dark:text-red-400" />
            <div className="flex-1">
              <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                Data Tidak Ditemukan
              </h3>
              <p className="text-sm text-red-700 dark:text-red-300 mt-1">
                Supplier dengan ID yang diminta tidak ditemukan dalam sistem. 
                Mungkin data telah dihapus atau ID yang digunakan tidak valid.
              </p>
            </div>
          </div>
          <div className="mt-4">
            <Button asChild>
              <Link href="/dashboard/suppliers">
                Kembali ke Daftar Supplier
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
