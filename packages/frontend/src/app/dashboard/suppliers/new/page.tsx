'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { SupplierForm } from '@/components/suppliers/supplier-form';
import { suppliersApi } from '@/lib/api/suppliers';
import { CreateSupplierDto } from '@/types/supplier';
import { toast } from 'sonner';
import { navigateBackToSuppliers } from '@/lib/utils/navigation';

export default function NewSupplierPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (data: CreateSupplierDto) => {
    try {
      setIsLoading(true);
      for (const name in data) {
        // @ts-expect-error
        if (data[name] === '') {
          // @ts-expect-error
          delete data[name];
        }
      }
      const supplier = await suppliersApi.createSupplier(data);

      // Show success message (you can implement toast notifications)
      toast.success('Supplier berhasil ditambahkan!');

      // Redirect to supplier detail page
      router.push(`/dashboard/suppliers/${supplier.id}`);
    } catch (error: any) {
      console.error('Error creating supplier:', error);

      // Show error message
      const errorMessage = error.response?.data?.message || 'Gagal menambahkan supplier';

      toast.error(Array.isArray(errorMessage) ? errorMessage.join('\n') : errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    navigateBackToSuppliers(router);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="icon"
          onClick={() => navigateBackToSuppliers(router)}
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Tambah Supplier Baru</h1>
          <p className="text-muted-foreground">
            Tambahkan supplier atau vendor baru ke dalam sistem
          </p>
        </div>
      </div>

      {/* Form */}
      <div className="max-w-4xl">
        <SupplierForm
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isLoading={isLoading}
          mode="create"
        />
      </div>
    </div>
  );
}
