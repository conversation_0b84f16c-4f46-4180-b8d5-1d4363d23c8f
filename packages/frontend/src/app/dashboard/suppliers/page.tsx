import { requireAuth } from '@/lib/server/auth';
import { SiteHeader } from '@/components/site-header';
import { SuppliersPageClient } from '@/components/suppliers/SuppliersPageClient';
import { SupplierStatsCards } from '@/components/suppliers/SupplierStatsCards';
import { SupplierQueryParams } from '@/types/supplier';


interface SuppliersPageProps {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}



export default async function SuppliersPage({ searchParams }: SuppliersPageProps) {
  // Require authentication on server side
  const session = await requireAuth();

  // Await searchParams before accessing properties (Next.js 15+ requirement)
  const resolvedSearchParams = await searchParams;

  // Parse search parameters for filtering
  const filters: SupplierQueryParams = {
    search: typeof resolvedSearchParams.search === 'string' ? resolvedSearchParams.search : undefined,
    type: typeof resolvedSearchParams.type === 'string' ? resolvedSearchParams.type as any : undefined,
    status: typeof resolvedSearchParams.status === 'string' ? resolvedSearchParams.status as any : undefined,
    province: typeof resolvedSearchParams.province === 'string' ? resolvedSearchParams.province : undefined,
    page: typeof resolvedSearchParams.page === 'string' ? parseInt(resolvedSearchParams.page) : 1,
    limit: typeof resolvedSearchParams.limit === 'string' ? parseInt(resolvedSearchParams.limit) : 10,
  };





  return (
    <>
      <SiteHeader user={session.user} />
      <div className="@container/main h-(--content-height) overflow-y-auto">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          {/* Stats Cards */}
          <div className="px-4 lg:px-6">
            <SupplierStatsCards />
          </div>

          {/* Client-side interactive components */}
          <div className="px-4 lg:px-6">
            <SuppliersPageClient
              initialQuery={filters}
            />
          </div>
        </div>
      </div>
    </>
  );
}
