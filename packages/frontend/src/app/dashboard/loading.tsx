import { SiteHeaderSkeleton } from '@/components/site-header-skeleton';
import { Skeleton } from '@/components/ui/skeleton';

export default function DashboardLoading() {
  return (
    <>
      <SiteHeaderSkeleton />
      <div className="@container/main h-(--content-height) overflow-y-auto">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          {/* Welcome section skeleton */}
          <div className="px-4 lg:px-6">
            <Skeleton className="h-24 w-full rounded-lg" />
          </div>

          {/* Stats cards skeleton */}
          <div className="px-4 lg:px-6">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              {Array.from({ length: 4 }).map((_, i) => (
                <Skeleton key={i} className="h-32 w-full rounded-lg" />
              ))}
            </div>
          </div>

          {/* Main content grid skeleton */}
          <div className="px-4 lg:px-6">
            <div className="grid gap-6 md:grid-cols-2">
              <Skeleton className="h-96 w-full rounded-lg" />
              <Skeleton className="h-96 w-full rounded-lg" />
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
