import { requireAuth } from '@/lib/server/auth';
import { SectionCards } from '@/components/section-cards';
import { SiteHeader } from '@/components/site-header';
import { WelcomeCard } from '@/components/dashboard/WelcomeCard';
import { QuickActions } from '@/components/dashboard/QuickActions';
import { RecentActivity } from '@/components/dashboard/RecentActivity';
import { mockDashboardData } from '@/lib/mock-data';

async function getDashboardData() {
  // Simulate API call - in a real app, this would fetch from your API
  await new Promise(resolve => setTimeout(resolve, 100));
  return mockDashboardData;
}

export default async function DashboardPage() {
  // Require authentication on server side
  const session = await requireAuth();

  // Fetch dashboard data on server side
  const dashboardData = await getDashboardData();

  const getUserDisplayName = () => {
    return session?.user?.name || session?.user?.email || 'Pengguna';
  };

  return (
    <>
      <SiteHeader user={session.user} />
      <div className="@container/main h-(--content-height) overflow-y-auto">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          {/* Welcome Section - First in the layout */}
          <div className="px-4 lg:px-6">
            <WelcomeCard userName={getUserDisplayName()} />
          </div>

          {/* Stats Cards - Using SectionCards component with pharmacy data */}
          <SectionCards stats={dashboardData.stats} />

          {/* Main Dashboard Content Grid */}
          <div className="px-4 lg:px-6">
            <div className="grid gap-6 md:grid-cols-2">
              {/* Recent Activity */}
              <RecentActivity activities={dashboardData.recentActivities} />

              {/* Quick Actions */}
              <QuickActions actions={dashboardData.quickActions} />
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
