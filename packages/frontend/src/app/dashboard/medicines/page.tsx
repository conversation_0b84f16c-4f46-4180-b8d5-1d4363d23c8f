import { requireAuth } from '@/lib/server/auth';
import { SiteHeader } from '@/components/site-header';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Pill, Plus } from 'lucide-react';

export default async function ObatPage() {
  // Require authentication on server side
  const session = await requireAuth();

  return (
    <>
      <SiteHeader user={session.user} />
      <div className="@container/main h-(--content-height) overflow-y-auto">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          <div className="px-4 lg:px-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold tracking-tight">Manajemen Obat</h1>
                <p className="text-muted-foreground">
                  Kelola inventori obat dan produk farmasi
                </p>
              </div>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Tambah Obat
              </Button>
            </div>
          </div>

          <div className="px-4 lg:px-6">
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Pill className="mr-2 h-5 w-5" />
                    Total Obat
                  </CardTitle>
                  <CardDescription>
                    Jumlah total obat dalam inventori
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">1,247</div>
                  <p className="text-xs text-muted-foreground">
                    +12 obat baru bulan ini
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Kategori Obat</CardTitle>
                  <CardDescription>
                    Berbagai kategori obat tersedia
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">24</div>
                  <p className="text-xs text-muted-foreground">
                    Kategori aktif
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Obat Terlaris</CardTitle>
                  <CardDescription>
                    Obat dengan penjualan tertinggi
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-sm font-medium">Paracetamol 500mg</div>
                  <p className="text-xs text-muted-foreground">
                    156 terjual bulan ini
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>

          <div className="px-4 lg:px-6">
            <Card>
              <CardHeader>
                <CardTitle>Fitur Akan Datang</CardTitle>
                <CardDescription>
                  Halaman manajemen obat sedang dalam pengembangan
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <Pill className="mx-auto h-12 w-12 text-muted-foreground" />
                  <h3 className="mt-4 text-lg font-semibold">Manajemen Obat</h3>
                  <p className="mt-2 text-muted-foreground">
                    Fitur lengkap untuk mengelola inventori obat, kategori, dan informasi produk akan segera tersedia.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </>
  );
}
