import { Metadata } from 'next';
import { getServerSession } from 'next-auth';
import { redirect } from 'next/navigation';
import { authOptions } from '@/lib/auth';
import { SiteHeader } from '@/components/site-header';
import { PurchaseOrderDetailPageClient } from '@/components/purchase-orders/PurchaseOrderDetailPageClient';

export const metadata: Metadata = {
  title: 'Detail Purchase Order - Apotek App',
  description: 'Detail purchase order dan status pemesanan',
};

interface PurchaseOrderDetailPageProps {
  params: { id: string };
}

export default async function PurchaseOrderDetailPage({ params }: PurchaseOrderDetailPageProps) {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect('/auth/login');
  }

  const resolvedParams = await params;

  return (
    <>
      <SiteHeader user={session.user} />
      <div className="@container/main h-(--content-height) overflow-y-auto">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          <div className="px-4 lg:px-6">
            <PurchaseOrderDetailPageClient purchaseOrderId={resolvedParams.id} />
          </div>
        </div>
      </div>
    </>
  );
}
