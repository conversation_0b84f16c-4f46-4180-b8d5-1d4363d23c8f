import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { SiteHeaderSkeleton } from '@/components/site-header-skeleton';

export default function NewPurchaseOrderLoading() {
  return (
    <>
      <SiteHeaderSkeleton />
      <div className="@container/main h-(--content-height) overflow-y-auto">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          <div className="px-4 lg:px-6">
            {/* Header Skeleton */}
            <div className="flex flex-col md:flex-row items-center gap-4 mb-6">
              <Skeleton className="h-9 w-20" />
              <div className="space-y-2">
                <Skeleton className="h-8 w-64" />
                <Skeleton className="h-4 w-80" />
              </div>
            </div>

            {/* Form Skeleton */}
            <div className="space-y-6">
              {/* Basic Information Card */}
              <Card>
                <CardHeader>
                  <CardTitle>
                    <Skeleton className="h-6 w-48" />
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {Array.from({ length: 4 }).map((_, i) => (
                      <div key={i} className="space-y-2">
                        <Skeleton className="h-4 w-24" />
                        <Skeleton className="h-10 w-full" />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Items Card */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <Skeleton className="h-6 w-32" />
                    <Skeleton className="h-9 w-28" />
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Items Table Skeleton */}
                  <div className="border rounded-lg">
                    <div className="border-b bg-muted/50 p-4">
                      <div className="grid grid-cols-6 gap-4">
                        {Array.from({ length: 6 }).map((_, i) => (
                          <Skeleton key={i} className="h-4 w-full" />
                        ))}
                      </div>
                    </div>
                    {Array.from({ length: 3 }).map((_, i) => (
                      <div key={i} className="border-b p-4">
                        <div className="grid grid-cols-6 gap-4">
                          {Array.from({ length: 6 }).map((_, j) => (
                            <Skeleton key={j} className="h-8 w-full" />
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Summary Skeleton */}
                  <div className="flex justify-end">
                    <div className="w-64 space-y-2">
                      {Array.from({ length: 5 }).map((_, i) => (
                        <div key={i} className="flex justify-between">
                          <Skeleton className="h-4 w-20" />
                          <Skeleton className="h-4 w-24" />
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Payment & Delivery Card */}
              <Card>
                <CardHeader>
                  <CardTitle>
                    <Skeleton className="h-6 w-48" />
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {Array.from({ length: 6 }).map((_, i) => (
                      <div key={i} className="space-y-2">
                        <Skeleton className="h-4 w-24" />
                        <Skeleton className="h-10 w-full" />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Action Buttons Skeleton */}
              <div className="flex flex-col sm:flex-row gap-3 sm:justify-end">
                <Skeleton className="h-10 w-24" />
                <Skeleton className="h-10 w-40" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
