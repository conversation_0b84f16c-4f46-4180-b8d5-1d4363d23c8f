export interface DashboardStats {
  dailySales: number;
  lowStock: number;
  pendingOrders: number;
  activeCustomers: number;
}

export interface RecentActivity {
  id: string;
  type: 'sale' | 'stock' | 'order' | 'customer';
  title: string;
  description: string;
  timestamp: string;
  amount?: number;
}

export interface QuickAction {
  id: string;
  title: string;
  description: string;
  icon: string;
  href: string;
  variant: 'default' | 'secondary' | 'outline';
}

export interface DashboardData {
  stats: DashboardStats;
  recentActivities: RecentActivity[];
  quickActions: QuickAction[];
}
