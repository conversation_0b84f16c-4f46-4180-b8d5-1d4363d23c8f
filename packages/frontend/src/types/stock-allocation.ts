export enum AllocationMethod {
  FIFO = 'FIFO', // First In First Out
  FEFO = 'FEFO', // First Expired First Out
}

export interface StockAllocationData {
  productId: string;
  requestedQuantity: number;
  method: AllocationMethod;
  previewOnly?: boolean;
  allowPartialAllocation?: boolean;
  nearExpiryWarningDays?: number;
  reason?: string;
  notes?: string;
}

export interface AllocationBatch {
  inventoryItemId: string;
  batchNumber?: string;
  expiryDate?: string;
  receivedDate: string;
  availableQuantity: number;
  allocatedQuantity: number;
  costPrice: number;
  location?: string;
  isNearExpiry?: boolean;
  daysUntilExpiry?: number;
}

export interface AllocationResult {
  success: boolean;
  productId: string;
  requestedQuantity: number;
  allocatedQuantity: number;
  method: AllocationMethod;
  batches: AllocationBatch[];
  shortfall?: number;
  warnings: string[];
  errors: string[];
  totalCost: number;
  averageCostPrice: number;
  previewOnly: boolean;
}

export interface StockSummary {
  totalAvailable: number;
  batchCount: number;
  nearExpiryCount: number;
  nearExpiryQuantity: number;
  expiredCount: number;
  expiredQuantity: number;
  oldestBatch?: string;
  newestBatch?: string;
  earliestExpiry?: string;
  unit?: {
    name: string;
    abbreviation: string;
  };
}

export interface AllocationOptions {
  previewOnly?: boolean;
  allowPartialAllocation?: boolean;
  nearExpiryWarningDays?: number;
  respectMinimumStock?: boolean;
  minimumStockLevel?: number;
}

// Report generation types
export interface AllocationReportRequest {
  allocationResult: AllocationResult;
  format: 'pdf' | 'excel' | 'csv';
  includeDetails: boolean;
  language: 'id' | 'en';
  userInfo?: {
    id: string;
    name: string;
    role: string;
  };
  pharmacyInfo?: {
    name: string;
    address: string;
    licenseNumber: string;
    pharmacistName: string;
  };
}

export interface AllocationReportResponse {
  reportUrl: string;
  reportId: string;
  fileName: string;
  fileSize: number;
  expiresAt: string;
  format: string;
}

export interface AllocationReportHeader {
  reportTitle: string;
  reportNumber: string;
  generatedAt: string;
  generatedBy: string;
  pharmacyInfo: {
    name: string;
    address: string;
    licenseNumber: string;
    pharmacistName: string;
  };
}

export interface BatchAllocationDetail extends AllocationBatch {
  remainingQuantity: number;
  totalCost: number;
  expiryStatus: 'FRESH' | 'NEAR_EXPIRY' | 'EXPIRED';
  daysUntilExpiry?: number;
  allocationOrder: number;
}

export interface ComplianceInfo {
  regulatoryNotes: {
    fifoFefoCompliance: boolean;
    expiryDateValidation: boolean;
    batchTraceability: boolean;
    costAccuracy: boolean;
  };
  auditTrail: {
    allocationTimestamp: string;
    userId: string;
    userRole: string;
    ipAddress?: string;
    systemVersion: string;
  };
  digitalSignature?: string;
}
