// Import Prisma types and enums directly as the single source of truth
import {
  ProductType,
  ProductCategory,
  MedicineClassification,
  UnitType,
  Product as PrismaProduct,
  ProductUnit as PrismaProductUnit,
  ProductUnitHierarchy as PrismaProductUnitHierarchy,
  User as PrismaUser
} from '@prisma/client';
import { Serialized } from './serialized';

// Re-export Prisma enums for convenience
export {
  ProductType,
  ProductCategory,
  MedicineClassification,
  UnitType
};

// Serialized types for frontend use (Date -> string, Decimal -> number)
export type Product = Serialized<PrismaProduct>;
export type ProductUnit = Serialized<PrismaProductUnit>;
export type ProductUnitHierarchy = Serialized<PrismaProductUnitHierarchy>;
export type User = Serialized<PrismaUser>;

// Define serialized types with relations for frontend use
export type ProductUnitHierarchyWithRelations = ProductUnitHierarchy & {
  unit: ProductUnit;
  parentUnit?: ProductUnitHierarchy;
  childUnits?: ProductUnitHierarchy[];
};

export type ProductWithRelations = Product & {
  baseUnit: ProductUnit;
  unitHierarchies: ProductUnitHierarchyWithRelations[];
  createdByUser?: User;
  updatedByUser?: User;
};

// DTO interfaces based on Prisma types
export interface CreateProductUnitHierarchyDto {
  unitId: string;
  parentUnitId?: string;
  conversionFactor: number;
  level: number;
  sellingPrice?: number;
  costPrice?: number;
}

export interface CreateProductDto {
  code: string;
  name: string;
  genericName?: string;
  type: ProductType;
  category: ProductCategory;
  manufacturer?: string;
  bpomNumber?: string;
  medicineClassification: MedicineClassification;
  regulatorySymbol?: string;
  baseUnitId: string;
  minimumStock?: number;
  maximumStock?: number;
  reorderPoint?: number;
  description?: string;
  activeIngredient?: string;
  strength?: string;
  dosageForm?: string;
  indication?: string;
  contraindication?: string;
  sideEffects?: string;
  dosage?: string;
  storage?: string;
  notes?: string;
  unitHierarchies?: CreateProductUnitHierarchyDto[];
}

export interface UpdateProductUnitHierarchyDto extends CreateProductUnitHierarchyDto {
  id?: string;
  isActive?: boolean;
}

export interface UpdateProductDto extends Partial<CreateProductDto> {
  isActive?: boolean;
  unitHierarchies?: UpdateProductUnitHierarchyDto[];
}

export interface ProductQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  type?: ProductType;
  category?: ProductCategory;
  medicineClassification?: MedicineClassification;
  manufacturer?: string;
  isActive?: boolean;
  baseUnitId?: string;
  genericName?: string;
  bpomNumber?: string;
  activeIngredient?: string;
  dosageForm?: string;
  strength?: string;
  createdBy?: string;
  dateFrom?: string;
  dateTo?: string;
  lowStock?: boolean;
  outOfStock?: boolean;
  overStock?: boolean;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface ProductListResponse {
  data: ProductWithRelations[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

export interface ProductStats {
  total: number;
  active: number;
  inactive: number;
  byType: Record<ProductType, number>;
  byCategory: Record<ProductCategory, number>;
  byMedicineClassification: Record<MedicineClassification, number>;
}
