// Receipt data structure matching backend interface
export interface ReceiptData {
  // Header Information
  pharmacy: {
    name: string;
    address: string;
    phone: string;
    email?: string;
    license?: string;
  };

  // Transaction Information
  transaction: {
    saleNumber: string;
    date: string;
    time: string;
    cashier: {
      name: string;
      id: string;
    };
  };

  // Customer Information
  customer: {
    name: string;
    phone?: string;
    type: 'WALK_IN' | 'REGISTERED';
    membershipNumber?: string;
  };

  // Items
  items: Array<{
    name: string;
    code: string;
    quantity: number;
    unit: string;
    unitPrice: number;
    discount: number;
    subtotal: number;
    manufacturer?: string;
  }>;

  // Totals
  totals: {
    subtotal: number;
    discount: number;
    afterDiscount: number;
    tax: number;
    total: number;
  };

  // Payment Information
  payment: {
    method: string;
    amountPaid: number;
    change: number;
  };

  // Footer Information
  footer: {
    thankYouMessage: string;
    returnPolicy: string;
    notes?: string;
  };

  // Thermal Printer Formatting Hints
  formatting: {
    paperWidth: 58 | 80; // mm
    lineBreaks: {
      afterHeader: number;
      afterTransaction: number;
      afterCustomer: number;
      afterItems: number;
      afterTotals: number;
      afterPayment: number;
      beforeFooter: number;
    };
    alignment: {
      header: 'center' | 'left' | 'right';
      items: 'left' | 'right';
      totals: 'right';
      footer: 'center';
    };
    fontSizes: {
      header: 'normal' | 'large' | 'small';
      items: 'normal' | 'small';
      totals: 'normal' | 'large';
      footer: 'small';
    };
  };
}

// Receipt modal props
export interface ReceiptModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  receiptData: ReceiptData | null;
  isLoading?: boolean;
  title?: string;
}

// Receipt display options
export interface ReceiptDisplayOptions {
  showHeader?: boolean;
  showFooter?: boolean;
  showLineBreaks?: boolean;
  compactMode?: boolean;
}
