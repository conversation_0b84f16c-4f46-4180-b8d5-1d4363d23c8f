// Import Prisma types and enums directly as the single source of truth
import { UserRole, User as PrismaUser } from '@prisma/client';
import { Serialized } from './serialized';

// Re-export Prisma enums for convenience
export { UserRole };

// Serialized types for frontend use (Date -> string, Decimal -> number)
export type User = Serialized<PrismaUser>;

export interface UpdateProfileData {
  firstName?: string;
  lastName?: string;
  avatar?: string;
  dateOfBirth?: string;
  phoneNumber?: string;
  address?: string;
}
