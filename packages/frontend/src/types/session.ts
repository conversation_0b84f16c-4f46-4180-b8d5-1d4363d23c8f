import { PaymentMethod } from './sales';

export interface SessionData {
  sessionId: string;
  openedAt: Date;
  closedAt?: Date;
  cashierId: string;
  cashierName: string;
  initialAmount: number;
}

export interface SessionSale {
  id: string;
  saleNumber: string;
  customerId?: string;
  cashierId: string;
  saleDate: string;
  totalAmount: number;
  paymentMethod: PaymentMethod;
  customerName?: string;
  customerPhone?: string;
  customer?: {
    id: string;
    fullName: string;
    code: string;
    type: string;
  };
  saleItems: SessionSaleItem[];
}

export interface SessionSaleItem {
  id: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  product: {
    id: string;
    name: string;
    code: string;
  };
  unit: {
    id: string;
    name: string;
    abbreviation: string;
  };
}

export interface SessionSalesResponse {
  data: SessionSale[];
  meta: {
    total: number;
    sessionDate: string;
    cashierId: string;
  };
}

export interface PaymentMethodBreakdown {
  count: number;
  amount: number;
}

export interface HourlyBreakdown {
  count: number;
  amount: number;
}

export interface SessionStats {
  sessionDate: string;
  cashierId: string;
  totalSalesCount: number;
  totalRevenue: number;
  averageTransactionValue: number;
  paymentMethodBreakdown: Record<PaymentMethod, PaymentMethodBreakdown>;
  hourlyBreakdown: Record<number, HourlyBreakdown>;
  firstSaleTime: number | null;
  lastSaleTime: number | null;
}
