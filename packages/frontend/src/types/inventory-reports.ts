// Inventory Reports Types
export enum ReportType {
  STOCK_LEVELS = 'stock_levels',
  STOCK_MOVEMENTS = 'stock_movements',
  LOW_STOCK = 'low_stock',
  EXPIRY_REPORT = 'expiry_report',
  ALLOCATION_HISTORY = 'allocation_history',
  SUPPLIER_PERFORMANCE = 'supplier_performance',
  CATEGORY_ANALYSIS = 'category_analysis',
  STOCK_AGING = 'stock_aging',
  TURNOVER_ANALYSIS = 'turnover_analysis',
}

export enum ReportFormat {
  PDF = 'pdf',
  EXCEL = 'excel',
  CSV = 'csv',
}

export enum ReportLanguage {
  ID = 'id',
  EN = 'en',
}

export interface ReportFilters {
  // Date range
  startDate?: string;
  endDate?: string;

  // Product filters
  productId?: string;
  productCategory?: string;
  productType?: string;

  // Supplier filters
  supplierId?: string;

  // Stock filters
  location?: string;
  lowStockOnly?: boolean;
  expiredOnly?: boolean;
  expiringSoon?: boolean;
  expiringSoonDays?: number;

  // Status filters
  isActive?: boolean;

  // Quantity filters
  minQuantity?: number;
  maxQuantity?: number;

  // Value filters
  minValue?: number;
  maxValue?: number;
}

export interface UserInfo {
  id: string;
  name: string;
  role: string;
}

export interface PharmacyInfo {
  name: string;
  address: string;
  licenseNumber: string;
  pharmacistName: string;
}

export interface InventoryReportRequest {
  type: ReportType;
  format: ReportFormat;
  language: ReportLanguage;
  filters: ReportFilters;
  includeDetails: boolean;
  includeSummary: boolean;
  includeCharts?: boolean;
  userInfo?: UserInfo;
  pharmacyInfo?: PharmacyInfo;
}

export interface InventoryReportResponse {
  reportUrl: string;
  reportId: string;
  fileName: string;
  fileSize: number;
  expiresAt: string;
  format: string;
  type: string;
  generatedAt: string;
}

export interface ReportGenerationProgress {
  reportId: string;
  status: 'pending' | 'generating' | 'completed' | 'failed';
  progress: number; // 0-100
  message: string;
  estimatedTimeRemaining?: number; // seconds
}

// Report configuration presets
export interface ReportPreset {
  id: string;
  name: string;
  description: string;
  type: ReportType;
  filters: ReportFilters;
  format: ReportFormat;
  isDefault?: boolean;
}

// Quick report options
export const QUICK_REPORT_PRESETS: ReportPreset[] = [
  {
    id: 'current-stock',
    name: 'Laporan Stok Saat Ini',
    description: 'Laporan lengkap stok inventori saat ini',
    type: ReportType.STOCK_LEVELS,
    filters: { isActive: true },
    format: ReportFormat.PDF,
    isDefault: true,
  },
  {
    id: 'low-stock-alert',
    name: 'Peringatan Stok Rendah',
    description: 'Daftar produk dengan stok rendah yang perlu restok',
    type: ReportType.LOW_STOCK,
    filters: { lowStockOnly: true, isActive: true },
    format: ReportFormat.PDF,
  },
  {
    id: 'expiry-alert',
    name: 'Peringatan Kedaluwarsa',
    description: 'Produk yang akan kedaluwarsa dalam 30 hari',
    type: ReportType.EXPIRY_REPORT,
    filters: { expiringSoon: true, expiringSoonDays: 30, isActive: true },
    format: ReportFormat.PDF,
  },
  {
    id: 'monthly-movements',
    name: 'Pergerakan Stok Bulanan',
    description: 'Laporan pergerakan stok dalam 30 hari terakhir',
    type: ReportType.STOCK_MOVEMENTS,
    filters: {
      startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      endDate: new Date().toISOString().split('T')[0],
    },
    format: ReportFormat.EXCEL,
  },
  {
    id: 'allocation-summary',
    name: 'Ringkasan Alokasi',
    description: 'Riwayat alokasi stok dalam 7 hari terakhir',
    type: ReportType.ALLOCATION_HISTORY,
    filters: {
      startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      endDate: new Date().toISOString().split('T')[0],
    },
    format: ReportFormat.PDF,
  },
  {
    id: 'supplier-analysis',
    name: 'Analisis Supplier',
    description: 'Performa supplier berdasarkan stok dan pergerakan',
    type: ReportType.SUPPLIER_PERFORMANCE,
    filters: {
      startDate: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      endDate: new Date().toISOString().split('T')[0],
    },
    format: ReportFormat.EXCEL,
  },
];

// Report type configurations
export const REPORT_TYPE_CONFIG = {
  [ReportType.STOCK_LEVELS]: {
    name: 'Laporan Tingkat Stok',
    description: 'Laporan lengkap tingkat stok inventori saat ini',
    icon: 'Package',
    color: 'blue',
    supportedFormats: [ReportFormat.PDF, ReportFormat.EXCEL, ReportFormat.CSV],
    defaultFormat: ReportFormat.PDF,
  },
  [ReportType.STOCK_MOVEMENTS]: {
    name: 'Laporan Pergerakan Stok',
    description: 'Riwayat pergerakan stok dengan detail transaksi',
    icon: 'Activity',
    color: 'green',
    supportedFormats: [ReportFormat.PDF, ReportFormat.EXCEL, ReportFormat.CSV],
    defaultFormat: ReportFormat.EXCEL,
  },
  [ReportType.LOW_STOCK]: {
    name: 'Laporan Stok Rendah',
    description: 'Daftar produk dengan stok di bawah batas minimum',
    icon: 'AlertTriangle',
    color: 'orange',
    supportedFormats: [ReportFormat.PDF, ReportFormat.EXCEL, ReportFormat.CSV],
    defaultFormat: ReportFormat.PDF,
  },
  [ReportType.EXPIRY_REPORT]: {
    name: 'Laporan Kedaluwarsa',
    description: 'Produk yang sudah atau akan kedaluwarsa',
    icon: 'Clock',
    color: 'red',
    supportedFormats: [ReportFormat.PDF, ReportFormat.EXCEL, ReportFormat.CSV],
    defaultFormat: ReportFormat.PDF,
  },
  [ReportType.ALLOCATION_HISTORY]: {
    name: 'Riwayat Alokasi',
    description: 'Riwayat alokasi stok dengan metode FIFO/FEFO',
    icon: 'GitBranch',
    color: 'purple',
    supportedFormats: [ReportFormat.PDF, ReportFormat.EXCEL, ReportFormat.CSV],
    defaultFormat: ReportFormat.PDF,
  },
  [ReportType.SUPPLIER_PERFORMANCE]: {
    name: 'Performa Supplier',
    description: 'Analisis performa supplier berdasarkan stok dan kualitas',
    icon: 'TrendingUp',
    color: 'indigo',
    supportedFormats: [ReportFormat.PDF, ReportFormat.EXCEL, ReportFormat.CSV],
    defaultFormat: ReportFormat.EXCEL,
  },
  [ReportType.CATEGORY_ANALYSIS]: {
    name: 'Analisis Kategori',
    description: 'Analisis stok berdasarkan kategori produk',
    icon: 'BarChart3',
    color: 'cyan',
    supportedFormats: [ReportFormat.PDF, ReportFormat.EXCEL, ReportFormat.CSV],
    defaultFormat: ReportFormat.EXCEL,
  },
  [ReportType.STOCK_AGING]: {
    name: 'Analisis Umur Stok',
    description: 'Analisis umur stok untuk optimasi inventori',
    icon: 'Calendar',
    color: 'yellow',
    supportedFormats: [ReportFormat.PDF, ReportFormat.EXCEL, ReportFormat.CSV],
    defaultFormat: ReportFormat.EXCEL,
  },
  [ReportType.TURNOVER_ANALYSIS]: {
    name: 'Analisis Perputaran',
    description: 'Analisis tingkat perputaran stok inventori',
    icon: 'RotateCcw',
    color: 'emerald',
    supportedFormats: [ReportFormat.PDF, ReportFormat.EXCEL, ReportFormat.CSV],
    defaultFormat: ReportFormat.EXCEL,
  },
};

// Format configurations
export const FORMAT_CONFIG = {
  [ReportFormat.PDF]: {
    name: 'PDF',
    description: 'Format PDF untuk tampilan dan pencetakan',
    icon: 'FileText',
    mimeType: 'application/pdf',
    extension: '.pdf',
  },
  [ReportFormat.EXCEL]: {
    name: 'Excel',
    description: 'Format Excel untuk analisis data',
    icon: 'FileSpreadsheet',
    mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    extension: '.xlsx',
  },
  [ReportFormat.CSV]: {
    name: 'CSV',
    description: 'Format CSV untuk integrasi sistem',
    icon: 'Database',
    mimeType: 'text/csv',
    extension: '.csv',
  },
};
