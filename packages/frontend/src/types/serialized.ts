/**
 * Serialized Types for Frontend
 * 
 * This file contains serialized versions of Prisma types that are compatible with JSON serialization.
 * - Date objects are converted to string (ISO format)
 * - Decimal objects are converted to number
 * - All relations are preserved but with serialized nested types
 * 
 * This maintains Prisma as the single source of truth while ensuring frontend compatibility.
 */

import { Decimal } from '@prisma/client/runtime/library';

/**
 * Utility type to serialize Prisma types for frontend use
 * Converts Date to string and Decimal to number recursively
 */
export type Serialized<T> = {
  [K in keyof T]: T[K] extends Date
    ? string
    : T[K] extends Decimal
    ? number
    : T[K] extends Date | null
    ? string | null
    : T[K] extends Decimal | null
    ? number | null
    : T[K] extends (infer U)[]
    ? Serialized<U>[]
    : T[K] extends object | null
    ? T[K] extends null
      ? null
      : Serialized<T[K]>
    : T[K];
};

/**
 * Utility type to make certain properties optional (for partial updates)
 */
export type SerializedPartial<T, K extends keyof T = never> = Serialized<
  Omit<T, K> & Partial<Pick<T, K>>
>;
