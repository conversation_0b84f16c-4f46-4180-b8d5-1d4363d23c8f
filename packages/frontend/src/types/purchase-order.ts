// Import Prisma types and enums directly as the single source of truth
import {
  PurchaseOrderStatus,
  PurchaseOrderItemStatus,
  PaymentMethod,
  PurchaseOrder as PrismaPurchaseOrder,
  PurchaseOrderItem as PrismaPurchaseOrderItem,
  Supplier as PrismaSupplier,
  Product as PrismaProduct,
  ProductUnit as PrismaProductUnit,
  User as PrismaUser
} from '@prisma/client';
import { Serialized } from './serialized';

// Re-export Prisma enums for convenience
export {
  PurchaseOrderStatus,
  PurchaseOrderItemStatus,
  PaymentMethod
};

// Serialized types for frontend use (Date -> string, Decimal -> number)
export type PurchaseOrder = Serialized<PrismaPurchaseOrder>;
export type PurchaseOrderItem = Serialized<PrismaPurchaseOrderItem>;
export type Supplier = Serialized<PrismaSupplier>;
export type Product = Serialized<PrismaProduct>;
export type ProductUnit = Serialized<PrismaProductUnit>;
export type User = Serialized<PrismaUser>;

// Define serialized types with relations for frontend use
export type PurchaseOrderItemWithRelations = PurchaseOrderItem & {
  product: Product;
  unit: ProductUnit;
};

export type PurchaseOrderWithRelations = PurchaseOrder & {
  supplier: Supplier;
  createdByUser?: User;
  updatedByUser?: User;
  submittedByUser?: User;
  items: PurchaseOrderItemWithRelations[];
};

export interface PurchaseOrderQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  supplierId?: string;
  status?: PurchaseOrderStatus;
  orderDateFrom?: string;
  orderDateTo?: string;
  expectedDeliveryFrom?: string;
  expectedDeliveryTo?: string;
  createdBy?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PurchaseOrderListResponse {
  data: PurchaseOrderWithRelations[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

// API Response type (matches backend getStats() return)
export interface PurchaseOrderStatsResponse {
  totalOrders: number;
  statusStats: Record<string, number>;
  totalValue: number;
  pendingValue: number;
  activeValue: number;
  completedValue: number;
  valueStats: Record<string, number>;
  recentOrders: PurchaseOrderWithRelations[];
  supplierStats: SupplierStats[];
  processingTimeAnalytics: ProcessingTimeAnalytics;
  volumeTrends: VolumeTrend[];
  period: string;
}

export interface SupplierStats {
  supplierId: string;
  supplierName: string;
  orderCount: number;
  totalValue: number;
}

export interface ProcessingTimeAnalytics {
  averageProcessingTime: number;
  totalOrders: number;
  efficiencyMetrics: {
    fastProcessing: number;
    slowProcessing: number;
    averageProcessing: number;
    fastPercentage: number;
    slowPercentage: number;
  };
}

export interface VolumeTrend {
  date: Date;
  count: number;
}

// Create Purchase Order DTOs
export interface CreatePurchaseOrderItemDto {
  productId: string;
  unitId: string;
  quantityOrdered: number;
  unitPrice: number;
  discountType?: string;
  discountValue?: number;
  expectedDelivery?: string;
  qualitySpecs?: string;
  notes?: string;
  updateSellingPrice?: boolean;
  newSellingPrice?: number;
}

export interface CreatePurchaseOrderDto {
  supplierId: string;
  orderDate?: string;
  expectedDelivery?: string;
  discountType?: string;
  discountValue?: number;
  taxAmount?: number;
  autoCalculateTax?: boolean;
  taxInclusive?: boolean;
  paymentTerms?: number;
  paymentMethod?: PaymentMethod;
  deliveryAddress?: string;
  deliveryContact?: string;
  deliveryPhone?: string;
  deliveryNotes?: string;
  notes?: string;
  internalNotes?: string;
  items: CreatePurchaseOrderItemDto[];
}

export interface UpdatePurchaseOrderDto {
  supplierId?: string;
  orderDate?: string;
  expectedDelivery?: string;
  discountType?: string;
  discountValue?: number;
  taxAmount?: number;
  autoCalculateTax?: boolean;
  taxInclusive?: boolean;
  paymentTerms?: number;
  paymentMethod?: PaymentMethod;
  deliveryAddress?: string;
  deliveryContact?: string;
  deliveryPhone?: string;
  deliveryNotes?: string;
  notes?: string;
  internalNotes?: string;
  items?: CreatePurchaseOrderItemDto[];
  // Additional fields from backend UpdatePurchaseOrderDto
  status?: PurchaseOrderStatus;
  actualDelivery?: string;
  paymentStatus?: string;

}



export interface PurchaseOrderStatusUpdateDto {
  status: PurchaseOrderStatus;
  notes?: string;
}

// Form types for frontend
export interface PurchaseOrderFormItem {
  productId: string;
  unitId: string;
  quantityOrdered: number;
  unitPrice: number;
  discountType?: 'PERCENTAGE' | 'FIXED_AMOUNT';
  discountValue?: number;
  expectedDelivery?: string;
  qualitySpecs?: string;
  notes?: string;

  // Computed fields for display
  totalPrice?: number;
  discountAmount?: number;
  finalPrice?: number;

  // Product info for display
  product?: {
    id: string;
    code: string;
    name: string;
    category: string;
    manufacturer?: string;
  };
  unit?: {
    id: string;
    name: string;
    abbreviation: string;
    conversionFactor: number;
  };
}

export interface PurchaseOrderFormData {
  supplierId: string;
  orderDate: string;
  expectedDelivery?: string;
  discountType?: 'PERCENTAGE' | 'FIXED_AMOUNT';
  discountValue?: number;
  paymentTerms?: number;
  paymentMethod?: PaymentMethod;
  deliveryAddress?: string;
  deliveryContact?: string;
  deliveryPhone?: string;
  deliveryNotes?: string;
  notes?: string;
  internalNotes?: string;
  items: PurchaseOrderFormItem[];

  // Computed totals
  subtotal?: number;
  discountAmount?: number;
  taxAmount?: number;
  totalAmount?: number;
}
