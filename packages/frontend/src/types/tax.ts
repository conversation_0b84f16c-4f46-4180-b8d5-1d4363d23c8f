// Tax-related types for frontend
export enum TaxType {
  PPN = 'PPN',                    // <PERSON><PERSON> (Value Added Tax)
  PPH_21 = 'PPH_21',             // PPh Pasal 21 (Employee Income Tax)
  PPH_23 = 'PPH_23',             // PPh Pasal 23 (Final Income Tax for MSMEs)
  PPH_25 = 'PPH_25',             // PPh Pasal 25 (Income Tax Installments)
  PPH_29 = 'PPH_29',             // PPh Pasal 29 (Annual Tax Due)
  PP_23 = 'PP_23',               // PP 23 (Final Income Tax for MSMEs)
  LUXURY_TAX = 'LUXURY_TAX',     // Pajak Penjualan atas Barang Mewah
  LOCAL_TAX = 'LOCAL_TAX'        // Pajak <PERSON>erah (Regional/Local Taxes)
}

export enum TaxEntityType {
  INDIVIDUAL = 'INDIVIDUAL',     // Wajib <PERSON>jak <PERSON>ribadi
  CORPORATE = 'CORPORATE'        // Wajib <PERSON> (PT)
}

export enum TaxCalculationMode {
  INCLUSIVE = 'INCLUSIVE',       // Tax included in price
  EXCLUSIVE = 'EXCLUSIVE'        // Tax added to price
}

// Tax Configuration Response
export interface TaxConfigurationResponse {
  taxType: TaxType;
  taxRate: number;
  isActive: boolean;
  effectiveFrom: string;
  effectiveTo?: string;
  description?: string;
}

// Tax Calculation Options
export interface TaxCalculationOptions {
  taxType?: TaxType;
  isInclusive?: boolean;
  exemptItems?: string[];
  entityType?: TaxEntityType;
  annualRevenue?: number;
}

// Tax Calculation Request
export interface CalculateTaxRequest {
  subtotal: number;
  discountAmount?: number;
  options?: TaxCalculationOptions;
}

// Tax Calculation Result
export interface TaxCalculationResult {
  subtotal: number;
  taxAmount: number;
  totalAmount: number;
  taxRate: number;
  taxType: TaxType;
  isInclusive: boolean;
}

// Tax Breakdown Response
export interface TaxBreakdownResponse {
  subtotal: number;
  taxRate: number;
  taxAmount: number;
  totalAmount: number;
  taxDescription: string;
  isInclusive: boolean;
}

// Multiple Items Tax Calculation
export interface CalculateMultipleItemsTaxRequest {
  items: Array<{
    subtotal: number;
    discountAmount?: number;
    exemptFromTax?: boolean;
  }>;
  orderLevelDiscount?: number;
  options?: TaxCalculationOptions;
}

// All Tax Settings Response
export interface AllTaxSettingsResponse {
  ppnConfiguration: TaxConfigurationResponse;
  calculationMode: TaxCalculationMode;
  defaultTaxType: TaxType;
  isSystemActive: boolean;
}

// Tax Status Response
export interface TaxStatusResponse {
  isActive: boolean;
  currentRate: number;
  effectiveFrom: string;
  effectiveTo?: string;
  description: string;
}

// Update PPN Configuration Request
export interface UpdatePPNConfigurationRequest {
  taxRate?: number;
  isActive?: boolean;
  effectiveFrom?: string;
  effectiveTo?: string;
  description?: string;
}

// Set Tax Calculation Mode Request
export interface SetTaxCalculationModeRequest {
  mode: TaxCalculationMode;
}

// Error response for tax operations
export interface TaxErrorResponse {
  message: string;
  code?: string;
  details?: any;
}
