// Import Prisma types and enums directly as the single source of truth
import {
  CustomerType,
  Customer as PrismaCustomer,
  User as PrismaUser
} from '@prisma/client';
import { Serialized } from './serialized';

// Re-export Prisma enums for convenience
export { CustomerType };

// Serialized types for frontend use (Date -> string, Decimal -> number)
export type Customer = Serialized<PrismaCustomer>;
export type User = Serialized<PrismaUser>;

// Define serialized types with relations for frontend use
export type CustomerWithRelations = Customer & {
  createdByUser?: User;
  updatedByUser?: User;
};

export interface CreateCustomerDto {
  code?: string;
  type: CustomerType;
  firstName?: string;
  lastName?: string;
  fullName: string;
  phoneNumber?: string;
  email?: string;
  dateOfBirth?: string;
  gender?: string;
  address?: string;
  city?: string;
  province?: string;
  postalCode?: string;
  membershipNumber?: string;
  membershipLevel?: string;
  notes?: string;
}

export interface UpdateCustomerDto extends Partial<CreateCustomerDto> {}

export interface CustomerQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  type?: CustomerType;
  membershipLevel?: string;
  isActive?: boolean;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface CustomerListResponse {
  data: Customer[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

export interface CustomerStats {
  total: number;
  active: number;
  inactive: number;
  walkIn: number;
  registered: number;
  byMembershipLevel: Array<{
    level: string;
    count: number;
  }>;
}

// Search result interface for customer selector
export interface CustomerSearchResult {
  id: string;
  name: string;
  phone?: string;
  code: string;
  type: CustomerType;
  membershipLevel?: string;
}
