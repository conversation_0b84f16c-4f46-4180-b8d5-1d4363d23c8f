import NextAuth from 'next-auth';
import { UserRole } from './User';

declare module 'next-auth' {
  interface Session {
    user: {
      id: string;
      email: string;
      name: string;
      role: UserRole;
      image?: string | null;
    };
    accessToken?: string;
  }

  interface User {
    id: string;
    email: string;
    name: string;
    role: UserRole;
    accessToken?: string;
    firstName?: string;
    lastName?: string;
    avatar?: string;
    dateOfBirth?: string;
    phoneNumber?: string;
    address?: string;
    lastLoginAt?: string;
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    id: string;
    email: string;
    name: string;
    role: UserRole;
    accessToken?: string;
    firstName?: string;
    lastName?: string;
    avatar?: string;
    dateOfBirth?: string;
    phoneNumber?: string;
    address?: string;
    lastLoginAt?: string;
  }
}
