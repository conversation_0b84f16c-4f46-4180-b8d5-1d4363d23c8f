// Import Prisma types and enums directly as the single source of truth
import {
  SaleStatus,
  PaymentMethod,
  CustomerType,
  Sale as PrismaSale,
  SaleItem as PrismaSaleItem,
  Customer as PrismaCustomer,
  User as PrismaUser,
  Product as PrismaProduct,
  ProductUnit as PrismaProductUnit
} from '@prisma/client';
import { Serialized } from './serialized';

// Re-export Prisma enums for convenience
export {
  SaleStatus,
  PaymentMethod,
  CustomerType
};

// Serialized types for frontend use (Date -> string, Decimal -> number)
export type Sale = Serialized<PrismaSale>;
export type SaleItem = Serialized<PrismaSaleItem>;
export type Customer = Serialized<PrismaCustomer>;
export type User = Serialized<PrismaUser>;
export type Product = Serialized<PrismaProduct>;
export type ProductUnit = Serialized<PrismaProductUnit>;

// Define serialized types with relations for frontend use
export type SaleItemWithRelations = SaleItem & {
  product: Product;
  unit: ProductUnit;
};

export type SaleWithRelations = Sale & {
  customer?: Customer;
  cashier: User;
  saleItems: SaleItemWithRelations[];
};

export interface CreateSaleItemDto {
  productId: string;
  unitId: string;
  quantity: number;
  unitPrice: number;
  discountType?: string;
  discountValue?: number;
  notes?: string;
}

export interface CreateSaleDto {
  saleNumber?: string;
  customerId?: string;
  cashierId?: string;
  saleDate?: string;
  discountType?: string;
  discountValue?: number;
  taxAmount?: number;
  paymentMethod: PaymentMethod;
  amountPaid: number;
  customerName?: string;
  customerPhone?: string;
  notes?: string;
  items: CreateSaleItemDto[];
}

export interface UpdateSaleDto extends Partial<CreateSaleDto> {
  status?: SaleStatus;
}

export interface SaleQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: SaleStatus;
  paymentMethod?: PaymentMethod;
  customerId?: string;
  cashierId?: string;
  startDate?: string;
  endDate?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  // Additional filters for enhanced functionality
  minAmount?: number;
  maxAmount?: number;
  customerType?: CustomerType;
  includeItems?: boolean;
}

export interface SaleListResponse {
  data: Sale[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

export interface SaleStats {
  totalSales: number;
  todaySales: number;
  completedSales: number;
  draftSales: number;
  cancelledSales: number;
  refundedSales: number;
  totalRevenue: number;
  todayRevenue: number;
  averageOrderValue: number;
  topPaymentMethod: PaymentMethod;
}

// Filter options for the datatable
export interface SaleStatusOption {
  value: SaleStatus;
  label: string;
}

export interface PaymentMethodOption {
  value: PaymentMethod;
  label: string;
}

// Bulk operations
export interface BulkSaleOperation {
  saleIds: string[];
  operation: 'complete' | 'cancel' | 'delete';
  reason?: string;
}

export interface BulkSaleResult {
  success: number;
  failed: number;
  errors: string[];
}

// Enhanced response types
export interface SaleResponse extends SaleWithRelations {
  // Additional computed fields that might come from the API
  itemCount?: number;
  profitAmount?: number;
  discountPercentage?: number;
}

// Filter state for UI components
export interface SaleFilters {
  status?: SaleStatus | 'all';
  paymentMethod?: PaymentMethod | 'all';
  dateRange?: {
    startDate?: string;
    endDate?: string;
    preset?: string;
  };
  amountRange?: {
    min?: number;
    max?: number;
  };
  customerType?: CustomerType | 'all';
  search?: string;
}

// Column configuration
export interface SaleColumnConfig {
  key: string;
  label: string;
  sortable: boolean;
  width?: string;
  align?: 'left' | 'center' | 'right';
  sticky?: boolean;
}

// Action handlers interface
export interface SaleActionHandlers {
  onView: (sale: SaleWithRelations) => void;
  onEdit: (sale: SaleWithRelations) => void;
  onComplete: (sale: SaleWithRelations) => void;
  onCancel: (sale: SaleWithRelations) => void;
  onRefund: (sale: SaleWithRelations) => void;
  onGenerateReceipt: (sale: SaleWithRelations) => void;
  onDelete?: (sale: SaleWithRelations) => void;
  onDuplicate?: (sale: SaleWithRelations) => void;
}

// Loading states for actions
export interface SaleActionLoadingStates {
  completing?: boolean;
  cancelling?: boolean;
  refunding?: boolean;
  deleting?: boolean;
  generatingReceipt?: boolean;
}
