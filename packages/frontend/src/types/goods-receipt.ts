// Import Prisma types and enums directly as the single source of truth
import {
  GoodsReceiptStatus,
  GoodsReceipt as PrismaGoodsReceipt,
  GoodsReceiptItem as PrismaGoodsReceiptItem,
  Supplier as PrismaSupplier,
  PurchaseOrder as PrismaPurchaseOrder,
  Product as PrismaProduct,
  ProductUnit as PrismaProductUnit,
  User as PrismaUser
} from '@prisma/client';
import { Serialized } from './serialized';

// Re-export Prisma enums for convenience
export { GoodsReceiptStatus };

// Serialized types for frontend use (Date -> string, Decimal -> number)
export type GoodsReceipt = Serialized<PrismaGoodsReceipt>;
export type GoodsReceiptItem = Serialized<PrismaGoodsReceiptItem>;
export type Supplier = Serialized<PrismaSupplier>;
export type PurchaseOrder = Serialized<PrismaPurchaseOrder>;
export type Product = Serialized<PrismaProduct>;
export type ProductUnit = Serialized<PrismaProductUnit>;
export type User = Serialized<PrismaUser>;

// Define serialized types with relations for frontend use
export type GoodsReceiptItemWithRelations = GoodsReceiptItem & {
  product: Product;
  unit: ProductUnit;
  purchaseOrderItem?: {
    id: string;
    quantityOrdered: number;
    unitPrice: number;
  };
};

export type GoodsReceiptWithRelations = GoodsReceipt & {
  supplier: Supplier;
  purchaseOrder?: PurchaseOrder;
  createdByUser?: User;
  updatedByUser?: User;
  items: GoodsReceiptItemWithRelations[];
};

// Query parameters
export interface GoodsReceiptQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  supplierId?: string;
  purchaseOrderId?: string;
  status?: GoodsReceiptStatus;
  receiptDateFrom?: string;
  receiptDateTo?: string;
  deliveryDateFrom?: string;
  deliveryDateTo?: string;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

// API Response types
export interface GoodsReceiptListResponse {
  data: GoodsReceipt[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

/**
 * Shared TypeScript interfaces for Goods Receipt Statistics
 * These interfaces ensure type consistency between backend and frontend
 */
export interface GoodsReceiptStatsResponse {
  // Basic statistics
  totalReceipts: number;
  pending: number;
  rejected: number;
  completed: number;
  totalValue: number;

  // Grouped statistics
  statusStats: Record<string, number>;

  // Recent data
  recentReceipts: GoodsReceiptSummary[];

  // Enhanced analytics (Phase 2)
  processingTimeAnalytics?: ProcessingTimeAnalytics;
  volumeTrends?: VolumeTrend[];

  // Metadata
  period?: string;
}

export interface GoodsReceiptSummary {
  id: string;
  receiptNumber: string;
  status: string;

  totalAmount: number;
  createdAt: Date | string;
  supplier?: {
    name: string;
  };
  purchaseOrder?: {
    orderNumber: string;
  };
}

export interface ProcessingTimeAnalytics {
  averageProcessingTime: number;
  totalReceipts: number;
  efficiencyMetrics: {
    fastProcessing: number;
    slowProcessing: number;
    averageProcessing: number;
    fastPercentage: number;
    slowPercentage: number;
  };
}

export interface VolumeTrend {
  date: string;
  count: number;
  value: number;
}

/**
 * Query parameters for statistics endpoint
 */
export interface GoodsReceiptStatsQuery {
  period?: "7d" | "30d" | "90d" | "quarter" | "year" | "all";
}

/**
 * Error response interface
 */
export interface GoodsReceiptStatsError {
  message: string;
  code: string;
  details?: any;
}

// Create DTOs
export interface CreateGoodsReceiptItemDto {
  purchaseOrderItemId?: string;
  productId: string;
  unitId: string;
  quantityOrdered?: number;
  quantityReceived: number;
  unitPrice: number;
  batchNumber?: string;
  expiryDate?: string;
  manufacturingDate?: string;
  storageLocation?: string;
  storageCondition?: string;
  conditionOnReceipt?: string;
  damageNotes?: string;
  notes?: string;
  // Product Substitution Information
  isSubstitution?: boolean;
  originalProductId?: string;
  substitutionReason?: string;
  substitutionNotes?: string;
}

export interface CreateGoodsReceiptDto {
  receiptNumber?: string;
  purchaseOrderId?: string;
  supplierId: string;
  status?: GoodsReceiptStatus;
  receiptDate?: string;
  deliveryDate?: string;
  invoiceNumber?: string;
  deliveryNote?: string;


  deliveredBy?: string;
  receivedBy?: string;
  deliveryCondition?: string;
  notes?: string;
  internalNotes?: string;
  items: CreateGoodsReceiptItemDto[];
}

// Update DTOs
export interface UpdateGoodsReceiptItemDto {
  id?: string;
  quantityAccepted?: number;
  quantityRejected?: number;
  storageLocation?: string;
  conditionOnReceipt?: string;
  damageNotes?: string;
  // Product Substitution Information
  isSubstitution?: boolean;
  originalProductId?: string;
  substitutionReason?: string;
  substitutionNotes?: string;
}

export interface UpdateGoodsReceiptDto {
  status?: GoodsReceiptStatus;
  receiptDate?: string;
  deliveryDate?: string;
  invoiceNumber?: string;
  deliveryNote?: string;
  deliveredBy?: string;
  receivedBy?: string;
  deliveryCondition?: string;
  notes?: string;
  internalNotes?: string;
  items?: UpdateGoodsReceiptItemDto[];
}

export interface GoodsReceiptStatusUpdateDto {
  status: GoodsReceiptStatus;
  notes?: string;
}

export interface RejectGoodsReceiptDto {
  reason: string;
}

// Form types for frontend
export interface GoodsReceiptFormItem {
  productId: string;
  unitId: string;
  quantityOrdered?: number;
  quantityReceived: number;
  unitPrice: number;
  batchNumber?: string;
  expiryDate?: string;
  manufacturingDate?: string;
  storageLocation?: string;
  conditionOnReceipt?: string;
  notes?: string;

  // Computed fields for display
  totalPrice?: number;

  // Product info for display
  product?: {
    id: string;
    code: string;
    name: string;
    category: string;
    manufacturer?: string;
  };
  unit?: {
    id: string;
    name: string;
    abbreviation: string;
    conversionFactor: number;
  };
}

export interface GoodsReceiptFormData {
  purchaseOrderId?: string;
  supplierId: string;
  receiptDate: string;
  deliveryDate?: string;
  invoiceNumber?: string;
  deliveryNote?: string;
  deliveredBy?: string;
  receivedBy?: string;
  deliveryCondition?: string;
  notes?: string;
  internalNotes?: string;
  items: GoodsReceiptFormItem[];

  // Computed totals
  totalAmount?: number;
}