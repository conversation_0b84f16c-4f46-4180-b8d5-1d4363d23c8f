// Import Prisma types and enums directly as the single source of truth
import {
  SupplierType,
  SupplierStatus,
  PaymentMethod,
  DocumentType,
  Supplier as PrismaSupplier,
  SupplierContact as PrismaSupplierContact,
  SupplierDocument as PrismaSupplierDocument,
  SupplierPayment as PrismaSupplierPayment,
  User as PrismaUser
} from '@prisma/client';
import { Serialized } from './serialized';

// Re-export Prisma enums for convenience
export {
  SupplierType,
  SupplierStatus,
  PaymentMethod,
  DocumentType
};

// Serialized types for frontend use (Date -> string, Decimal -> number)
export type Supplier = Serialized<PrismaSupplier>;
export type SupplierContact = Serialized<PrismaSupplierContact>;
export type SupplierDocument = Serialized<PrismaSupplierDocument>;
export type SupplierPayment = Serialized<PrismaSupplierPayment>;
export type User = Serialized<PrismaUser>;

// Define serialized types with relations for frontend use
export type SupplierWithRelations = Supplier & {
  createdByUser?: User;
  updatedByUser?: User;
  contacts: SupplierContact[];
  documents: SupplierDocument[];
  payments: SupplierPayment[];
  _count: {
    documents: number;
    payments: number;
  };
};

export interface CreateSupplierContactDto {
  name: string;
  position?: string;
  phone?: string;
  email?: string;
  isPrimary?: boolean;
  notes?: string;
}

export interface CreateSupplierDto {
  code: string;
  name: string;
  type: SupplierType;
  address?: string;
  city?: string;
  province?: string;
  postalCode?: string;
  phone?: string;
  email?: string;
  website?: string;
  npwp?: string;
  licenseNumber?: string;
  pharmacyLicense?: string;
  creditLimit?: number;
  paymentTerms?: number;
  preferredPayment?: PaymentMethod;
  bankName?: string;
  bankAccount?: string;
  bankAccountName?: string;
  notes?: string;
  contacts?: CreateSupplierContactDto[];
}

export interface UpdateSupplierDto extends Partial<CreateSupplierDto> {
  status?: SupplierStatus;
}

export interface SupplierQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  type?: SupplierType;
  status?: SupplierStatus;
  city?: string;
  province?: string;
  paymentMethod?: PaymentMethod;
  email?: string;
  phone?: string;
  npwp?: string;
  createdBy?: string;
  dateFrom?: string;
  dateTo?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface SupplierListResponse {
  data: SupplierWithRelations[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

export interface SupplierStats {
  total: number;
  active: number;
  inactive: number;
  byType: Record<SupplierType, number>;
}

export interface CreateSupplierDocumentDto {
  type: DocumentType;
  name: string;
  description?: string;
  fileName?: string;
  filePath?: string;
  fileSize?: number;
  mimeType?: string;
  expiryDate?: string;
}

export interface CreatePaymentProofDto {
  name: string;
  description?: string;
  fileName?: string;
  filePath?: string;
  fileSize?: number;
  mimeType?: string;
}

export interface CreateSupplierPaymentDto {
  invoiceNumber?: string;
  amount: number;
  paymentMethod: PaymentMethod;
  paymentDate: string;
  dueDate?: string;
  status?: string;
  reference?: string;
  notes?: string;
}

// UI Helper Types
export interface SupplierTypeOption {
  value: SupplierType;
  label: string;
}

export interface SupplierStatusOption {
  value: SupplierStatus;
  label: string;
}

export interface PaymentMethodOption {
  value: PaymentMethod;
  label: string;
}

// Document Management Types
export interface DocumentQueryParams {
  page?: number;
  limit?: number;
  type?: DocumentType;
  search?: string;
}

export interface DocumentListResponse {
  data: SupplierDocument[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

// Payment Management Types
export interface PaymentQueryParams {
  page?: number;
  limit?: number;
  status?: string;
  paymentMethod?: PaymentMethod;
  search?: string;
  dateFrom?: string;
  dateTo?: string;
}

export interface PaymentListResponse {
  data: SupplierPayment[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

export interface PaymentSummary {
  summary: {
    totalPaid: number;
    totalPending: number;
    totalOverdue: number;
    countPaid: number;
    countPending: number;
    countOverdue: number;
  };
  recentPayments: SupplierPayment[];
  paymentsByMethod: {
    method: PaymentMethod;
    amount: number;
    count: number;
  }[];
  paymentsByMonth: {
    date: string;
    amount: number;
    count: number;
  }[];
}

// Document Type Options
export interface DocumentTypeOption {
  value: DocumentType;
  label: string;
}

// Payment Status Options
export interface PaymentStatusOption {
  value: string;
  label: string;
}
