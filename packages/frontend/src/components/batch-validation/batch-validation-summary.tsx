'use client';

import { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Separator } from '@/components/ui/separator';
import {
  CheckCircle,
  XCircle,
  AlertTriangle,
  ChevronDown,
  ChevronRight,
  Shield,
  Clock,
  FileCheck,
  Zap
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface ValidationSummaryItem {
  batchNumber: string;
  productName: string;
  isValid: boolean;
  level: 'error' | 'warning' | 'success';
  message?: string;
  details?: {
    formatValid: boolean;
    uniqueValid: boolean;
    bpomCompliant: boolean;
    dateAligned: boolean;
    validationLevel: string;
  };
}

interface BatchValidationSummaryProps {
  items: ValidationSummaryItem[];
  className?: string;
}

export function BatchValidationSummary({ items, className }: BatchValidationSummaryProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const totalItems = items.length;
  const validItems = items.filter(item => item.isValid).length;
  const errorItems = items.filter(item => item.level === 'error').length;
  const warningItems = items.filter(item => item.level === 'warning').length;

  const getStatusIcon = (item: ValidationSummaryItem) => {
    switch (item.level) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return null;
    }
  };

  const getValidationLevelBadge = (level: string) => {
    const variants = {
      'BASIC': 'secondary',
      'STANDARD': 'default',
      'BPOM_COMPLIANT': 'default',
      'CONTROLLED': 'destructive'
    } as const;

    return (
      <Badge variant={variants[level as keyof typeof variants] || 'secondary'} className="text-xs">
        {level}
      </Badge>
    );
  };

  const getDetailIcon = (isValid: boolean, type: string) => {
    const iconClass = isValid ? 'text-green-500' : 'text-red-500';
    
    switch (type) {
      case 'format':
        return <FileCheck className={cn('h-3 w-3', iconClass)} />;
      case 'unique':
        return <Zap className={cn('h-3 w-3', iconClass)} />;
      case 'bpom':
        return <Shield className={cn('h-3 w-3', iconClass)} />;
      case 'date':
        return <Clock className={cn('h-3 w-3', iconClass)} />;
      default:
        return null;
    }
  };

  if (totalItems === 0) {
    return null;
  }

  return (
    <Card className={cn('w-full', className)}>
      <CardHeader className="pb-3">
        <CardTitle className="text-sm font-medium">
          Ringkasan Validasi Batch Number
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Summary Stats */}
        <div className="grid grid-cols-4 gap-4 text-center">
          <div className="space-y-1">
            <div className="text-2xl font-bold">{totalItems}</div>
            <div className="text-xs text-muted-foreground">Total</div>
          </div>
          <div className="space-y-1">
            <div className="text-2xl font-bold text-green-600">{validItems}</div>
            <div className="text-xs text-muted-foreground">Valid</div>
          </div>
          <div className="space-y-1">
            <div className="text-2xl font-bold text-yellow-600">{warningItems}</div>
            <div className="text-xs text-muted-foreground">Warning</div>
          </div>
          <div className="space-y-1">
            <div className="text-2xl font-bold text-red-600">{errorItems}</div>
            <div className="text-xs text-muted-foreground">Error</div>
          </div>
        </div>

        <Separator />

        {/* Expandable Details */}
        <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
          <CollapsibleTrigger asChild>
            <Button variant="ghost" className="w-full justify-between p-0 h-auto">
              <span className="text-sm font-medium">Detail Validasi</span>
              {isExpanded ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="space-y-3 mt-3">
            {items.map((item, index) => (
              <div key={index} className="border rounded-lg p-3 space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {getStatusIcon(item)}
                    <span className="font-mono text-sm">{item.batchNumber}</span>
                    {item.details && getValidationLevelBadge(item.details.validationLevel)}
                  </div>
                  <span className="text-xs text-muted-foreground truncate max-w-[150px]">
                    {item.productName}
                  </span>
                </div>

                {item.message && (
                  <div className="text-xs text-muted-foreground">
                    {item.message}
                  </div>
                )}

                {item.details && (
                  <div className="grid grid-cols-2 gap-2 text-xs">
                    <div className="flex items-center gap-1">
                      {getDetailIcon(item.details.formatValid, 'format')}
                      <span>Format</span>
                    </div>
                    <div className="flex items-center gap-1">
                      {getDetailIcon(item.details.uniqueValid, 'unique')}
                      <span>Uniqueness</span>
                    </div>
                    <div className="flex items-center gap-1">
                      {getDetailIcon(item.details.bpomCompliant, 'bpom')}
                      <span>BPOM</span>
                    </div>
                    <div className="flex items-center gap-1">
                      {getDetailIcon(item.details.dateAligned, 'date')}
                      <span>Date Alignment</span>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </CollapsibleContent>
        </Collapsible>

        {/* Action Buttons */}
        {(errorItems > 0 || warningItems > 0) && (
          <div className="flex gap-2 pt-2">
            {errorItems > 0 && (
              <Badge variant="destructive" className="text-xs">
                {errorItems} error{errorItems > 1 ? 's' : ''} perlu diperbaiki
              </Badge>
            )}
            {warningItems > 0 && (
              <Badge variant="secondary" className="text-xs">
                {warningItems} warning{warningItems > 1 ? 's' : ''} untuk ditinjau
              </Badge>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
