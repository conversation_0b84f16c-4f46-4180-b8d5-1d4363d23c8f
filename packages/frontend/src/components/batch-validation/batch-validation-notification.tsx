'use client';

import { useState, useEffect } from 'react';
import { useImmer } from 'use-immer';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import {
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  Shield,
  FileCheck,
  Zap,
  Loader2,
  RefreshCw,
  Eye,
  EyeOff,
  Info,
  ExternalLink,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  useBatchValidation,
  useBatchValidationComprehensive,
  useBatchUniquenessCheck
} from '@/hooks/use-batch-validation';

interface BatchValidationNotificationProps {
  batchNumber: string;
  productId: string;
  productName?: string;
  supplierId?: string;
  expiryDate?: Date;
  manufacturingDate?: Date;
  onValidationComplete?: (result: ValidationNotificationResult) => void;
  onDetailView?: () => void;
  compact?: boolean;
  showProgress?: boolean;
  autoValidate?: boolean;
  className?: string;
}

interface ValidationNotificationResult {
  isValid: boolean;
  level: 'success' | 'warning' | 'error';
  message: string;
  details: {
    formatValid: boolean;
    uniqueValid: boolean;
    bpomCompliant: boolean;
    dateAligned: boolean;
    validationLevel: string;
  };
  timestamp: Date;
}

interface ValidationStepIndicator {
  id: string;
  label: string;
  status: 'pending' | 'running' | 'success' | 'warning' | 'error';
  message?: string;
}

export function BatchValidationNotification({
  batchNumber,
  productId,
  productName,
  supplierId,
  expiryDate,
  manufacturingDate,
  onValidationComplete,
  onDetailView,
  compact = false,
  showProgress = true,
  autoValidate = true,
  className,
}: BatchValidationNotificationProps) {
  const [steps, setSteps] = useImmer<ValidationStepIndicator[]>([
    { id: 'format', label: 'Format', status: 'pending' },
    { id: 'uniqueness', label: 'Keunikan', status: 'pending' },
    { id: 'bpom', label: 'BPOM', status: 'pending' },
    { id: 'date', label: 'Tanggal', status: 'pending' },
  ]);
  const [isExpanded, setIsExpanded] = useState(!compact);
  const [validationResult, setValidationResult] = useState<ValidationNotificationResult | null>(null);
  const [validationProgress, setValidationProgress] = useState(0);

  const {
    validationResult: realTimeResult,
    isLoading,
    isValid,
    hasWarnings,
    hasErrors,
  } = useBatchValidation(batchNumber, {
    productId,
    supplierId,
    expiryDate,
    manufacturingDate,
    enableRealTimeValidation: autoValidate,
  });

  // Use the newer hooks directly
  const validateComprehensive = useBatchValidationComprehensive();
  const checkUniqueness = useBatchUniquenessCheck();

  // Update steps based on real-time validation
  useEffect(() => {
    if (realTimeResult) {
      updateStepsFromRealTime(realTimeResult);
    }
  }, [realTimeResult]);

  // Auto-validate on mount if enabled
  useEffect(() => {
    if (autoValidate && batchNumber && productId) {
      performComprehensiveValidation();
    }
  }, [batchNumber, productId, autoValidate]);

  const updateStepsFromRealTime = (result: any) => {
    setSteps(draft => {
      // Update based on real-time validation result
      draft[0].status = result.isValid ? 'success' : 'error';
      draft[0].message = result.message;

      // Set other steps to pending until comprehensive validation
      draft[1].status = 'pending';
      draft[2].status = 'pending';
      draft[3].status = 'pending';
    });

    setValidationProgress(25); // 25% for format validation
  };

  const performComprehensiveValidation = async () => {
    if (!batchNumber || !productId) return;

    setValidationProgress(0);

    try {
      // Step 1: Format validation (already done by real-time)
      setValidationProgress(25);

      // Step 2: Uniqueness check
      setSteps(draft => {
        draft[1].status = 'running';
      });

      const uniquenessResult = await checkUniqueness.mutateAsync({
        batchNumber,
        productId,
        supplierId,
      });

      setSteps(draft => {
        draft[1].status = uniquenessResult?.isUnique ? 'success' : 'warning';
        draft[1].message = uniquenessResult?.isUnique
          ? 'Batch unik'
          : `${uniquenessResult?.conflictingBatches?.length || 0} konflik`;
      });

      setValidationProgress(50);

      // Step 3: Comprehensive validation for BPOM compliance
      setSteps(draft => {
        draft[2].status = 'running';
      });

      const comprehensiveResult = await validateComprehensive.mutateAsync({
        batchNumber,
        productId,
        supplierId,
        expiryDate: expiryDate?.toISOString(),
        manufacturingDate: manufacturingDate?.toISOString(),
      });

      setSteps(draft => {
        draft[2].status = comprehensiveResult?.bpomCompliant ? 'success' : 'warning';
        draft[2].message = comprehensiveResult?.bpomCompliant
          ? `Sesuai BPOM (${comprehensiveResult.validationLevel})`
          : 'Tidak sepenuhnya sesuai BPOM';
      });

      setValidationProgress(75);

      // Step 4: Date alignment validation
      setSteps(draft => {
        draft[3].status = 'running';
      });

      const dateValid = validateDateAlignment();

      setSteps(draft => {
        draft[3].status = dateValid.isValid ? 'success' : dateValid.hasError ? 'error' : 'warning';
        draft[3].message = dateValid.message;
      });

      setValidationProgress(100);

      // Generate final result
      const finalResult = generateFinalResult(comprehensiveResult, uniquenessResult, dateValid);
      setValidationResult(finalResult);

      if (onValidationComplete) {
        onValidationComplete(finalResult);
      }

    } catch (error) {
      console.error('Comprehensive validation error:', error);
      setSteps(draft => {
        draft.forEach(step => {
          if (step.status === 'running') {
            step.status = 'error';
            step.message = 'Gagal validasi';
          }
        });
      });
    }
  };

  const validateDateAlignment = () => {
    if (expiryDate && manufacturingDate) {
      const isValid = expiryDate > manufacturingDate;
      const daysDiff = Math.floor((expiryDate.getTime() - manufacturingDate.getTime()) / (1000 * 60 * 60 * 24));

      return {
        isValid,
        hasError: !isValid,
        message: isValid
          ? `Valid (${daysDiff} hari shelf life)`
          : 'Tanggal kadaluarsa harus setelah produksi'
      };
    } else if (expiryDate || manufacturingDate) {
      return {
        isValid: true,
        hasError: false,
        message: 'Sebagian tanggal tersedia'
      };
    } else {
      return {
        isValid: true,
        hasError: false,
        message: 'Tidak ada tanggal untuk divalidasi'
      };
    }
  };

  const generateFinalResult = (comprehensive: any, uniqueness: any, dateValidation: any): ValidationNotificationResult => {
    const hasErrors = steps.some(step => step.status === 'error') || dateValidation.hasError;
    const hasWarnings = steps.some(step => step.status === 'warning') || !dateValidation.hasError && !dateValidation.isValid;

    let level: 'success' | 'warning' | 'error';
    let message: string;

    if (hasErrors) {
      level = 'error';
      message = 'Validasi gagal - terdapat error';
    } else if (hasWarnings) {
      level = 'warning';
      message = 'Validasi berhasil dengan peringatan';
    } else {
      level = 'success';
      message = 'Semua validasi berhasil';
    }

    return {
      isValid: !hasErrors,
      level,
      message,
      details: {
        formatValid: steps[0].status === 'success',
        uniqueValid: steps[1].status === 'success',
        bpomCompliant: steps[2].status === 'success',
        dateAligned: dateValidation.isValid,
        validationLevel: comprehensive?.validationLevel || 'BASIC',
      },
      timestamp: new Date(),
    };
  };

  const getStepIcon = (step: ValidationStepIndicator) => {
    switch (step.status) {
      case 'running':
        return <Loader2 className="h-3 w-3 animate-spin text-blue-500" />;
      case 'success':
        return <CheckCircle className="h-3 w-3 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="h-3 w-3 text-yellow-500" />;
      case 'error':
        return <XCircle className="h-3 w-3 text-red-500" />;
      default:
        return <Clock className="h-3 w-3 text-gray-400" />;
    }
  };

  const getOverallIcon = () => {
    if (isLoading) return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
    if (hasErrors) return <XCircle className="h-4 w-4 text-red-500" />;
    if (hasWarnings) return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
    if (isValid) return <CheckCircle className="h-4 w-4 text-green-500" />;
    return <Clock className="h-4 w-4 text-gray-400" />;
  };

  const getAlertVariant = () => {
    if (hasErrors) return 'destructive';
    if (hasWarnings) return 'default';
    return 'default';
  };

  const getValidationLevelBadge = () => {
    if (!validationResult) return null;

    const level = validationResult.details.validationLevel;
    const variants = {
      'BASIC': 'secondary',
      'STANDARD': 'default',
      'BPOM_COMPLIANT': 'default',
      'CONTROLLED': 'destructive'
    } as const;

    return (
      <Badge variant={variants[level as keyof typeof variants] || 'secondary'} className="text-xs">
        {level}
      </Badge>
    );
  };

  if (compact) {
    return (
      <div className={cn('flex items-center gap-2 p-2 border rounded-lg', className)}>
        {getOverallIcon()}
        <div className="flex-1 min-w-0">
          <div className="text-sm font-medium truncate">
            Batch: {batchNumber}
          </div>
          {validationResult && (
            <div className="text-xs text-muted-foreground">
              {validationResult.message}
            </div>
          )}
        </div>
        <div className="flex items-center gap-1">
          {getValidationLevelBadge()}
          {onDetailView && (
            <Button variant="ghost" size="sm" onClick={onDetailView} className="h-6 w-6 p-0">
              <ExternalLink className="h-3 w-3" />
            </Button>
          )}
        </div>
      </div>
    );
  }

  return (
    <Card className={cn('w-full', className)}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between text-sm">
          <div className="flex items-center gap-2">
            {getOverallIcon()}
            <span>Validasi Batch: {batchNumber}</span>
            {getValidationLevelBadge()}
          </div>
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="h-6 w-6 p-0"
            >
              {isExpanded ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
            </Button>
            {onDetailView && (
              <Button variant="ghost" size="sm" onClick={onDetailView} className="h-6 w-6 p-0">
                <ExternalLink className="h-3 w-3" />
              </Button>
            )}
          </div>
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Progress Bar */}
        {showProgress && (
          <div className="space-y-1">
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>Progress Validasi</span>
              <span>{validationProgress}%</span>
            </div>
            <Progress value={validationProgress} className="h-1" />
          </div>
        )}

        {/* Validation Steps - Compact View */}
        <div className="grid grid-cols-4 gap-2">
          {steps.map((step) => (
            <Tooltip key={step.id}>
              <TooltipTrigger asChild>
                <div className="flex items-center gap-1 p-2 rounded border">
                  {getStepIcon(step)}
                  <span className="text-xs font-medium">{step.label}</span>
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <div className="text-xs">
                  <div className="font-medium">{step.label}</div>
                  {step.message && <div className="text-muted-foreground">{step.message}</div>}
                </div>
              </TooltipContent>
            </Tooltip>
          ))}
        </div>

        {/* Expanded Details */}
        {isExpanded && (
          <div className="space-y-3">
            <Separator />

            {/* Product Info */}
            <div className="text-xs space-y-1">
              <div className="font-medium">Informasi Produk</div>
              <div className="grid grid-cols-2 gap-2 text-muted-foreground">
                <div>Produk: {productName || 'Tidak diketahui'}</div>
                <div>Batch: {batchNumber}</div>
                {expiryDate && (
                  <div>Kadaluarsa: {expiryDate.toLocaleDateString('id-ID')}</div>
                )}
                {manufacturingDate && (
                  <div>Produksi: {manufacturingDate.toLocaleDateString('id-ID')}</div>
                )}
              </div>
            </div>

            {/* Real-time Feedback */}
            {realTimeResult && (
              <Alert variant={getAlertVariant()}>
                <Info className="h-4 w-4" />
                <AlertDescription className="text-sm">
                  {realTimeResult.message || 'Validasi real-time selesai'}
                </AlertDescription>
              </Alert>
            )}

            {/* Final Result */}
            {validationResult && (
              <div className="p-3 rounded-lg border bg-muted/50">
                <div className="flex items-center gap-2 mb-2">
                  {validationResult.level === 'success' && <CheckCircle className="h-4 w-4 text-green-500" />}
                  {validationResult.level === 'warning' && <AlertTriangle className="h-4 w-4 text-yellow-500" />}
                  {validationResult.level === 'error' && <XCircle className="h-4 w-4 text-red-500" />}
                  <span className="text-sm font-medium">{validationResult.message}</span>
                </div>
                <div className="grid grid-cols-2 gap-1 text-xs">
                  <div className="flex items-center gap-1">
                    {validationResult.details.formatValid ? (
                      <CheckCircle className="h-3 w-3 text-green-500" />
                    ) : (
                      <XCircle className="h-3 w-3 text-red-500" />
                    )}
                    <span>Format</span>
                  </div>
                  <div className="flex items-center gap-1">
                    {validationResult.details.uniqueValid ? (
                      <CheckCircle className="h-3 w-3 text-green-500" />
                    ) : (
                      <AlertTriangle className="h-3 w-3 text-yellow-500" />
                    )}
                    <span>Keunikan</span>
                  </div>
                  <div className="flex items-center gap-1">
                    {validationResult.details.bpomCompliant ? (
                      <CheckCircle className="h-3 w-3 text-green-500" />
                    ) : (
                      <AlertTriangle className="h-3 w-3 text-yellow-500" />
                    )}
                    <span>BPOM</span>
                  </div>
                  <div className="flex items-center gap-1">
                    {validationResult.details.dateAligned ? (
                      <CheckCircle className="h-3 w-3 text-green-500" />
                    ) : (
                      <XCircle className="h-3 w-3 text-red-500" />
                    )}
                    <span>Tanggal</span>
                  </div>
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={performComprehensiveValidation}
                disabled={isLoading}
                className="flex items-center gap-1"
              >
                <RefreshCw className="h-3 w-3" />
                Validasi Ulang
              </Button>
              {onDetailView && (
                <Button variant="outline" size="sm" onClick={onDetailView}>
                  Detail Lengkap
                </Button>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
