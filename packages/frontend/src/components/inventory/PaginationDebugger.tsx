'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { InventoryQueryParams } from '@/types/inventory';

interface PaginationDebuggerProps {
  query: InventoryQueryParams;
  filters: InventoryQueryParams;
  meta?: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
  onTestPagination?: (page: number) => void;
}

export function PaginationDebugger({ query, filters, meta, onTestPagination }: PaginationDebuggerProps) {
  return (
    <Card className="w-full max-w-4xl mx-auto mt-4 border-orange-200 bg-orange-50">
      <CardHeader>
        <CardTitle className="text-orange-800 text-sm">
          🔧 Pagination Debugger (Development Only)
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Query State */}
          <div className="space-y-2">
            <h4 className="font-medium text-sm">Query State:</h4>
            <div className="bg-white p-3 rounded border text-xs">
              <div className="space-y-1">
                <div className="flex justify-between">
                  <span>Page:</span>
                  <Badge variant="outline">{query.page || 1}</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Limit:</span>
                  <Badge variant="outline">{query.limit || 10}</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Search:</span>
                  <Badge variant="outline">{query.search || 'None'}</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Active:</span>
                  <Badge variant="outline">{query.isActive?.toString() || 'All'}</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Low Stock:</span>
                  <Badge variant="outline">{query.lowStock?.toString() || 'No'}</Badge>
                </div>
              </div>
            </div>
          </div>

          {/* Filters State */}
          <div className="space-y-2">
            <h4 className="font-medium text-sm">Filters State:</h4>
            <div className="bg-white p-3 rounded border text-xs">
              <div className="space-y-1">
                <div className="flex justify-between">
                  <span>Page:</span>
                  <Badge variant="outline">{filters.page || 1}</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Limit:</span>
                  <Badge variant="outline">{filters.limit || 10}</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Search:</span>
                  <Badge variant="outline">{filters.search || 'None'}</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Active:</span>
                  <Badge variant="outline">{filters.isActive?.toString() || 'All'}</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Low Stock:</span>
                  <Badge variant="outline">{filters.lowStock?.toString() || 'No'}</Badge>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* API Meta Response */}
        {meta && (
          <div className="space-y-2">
            <h4 className="font-medium text-sm">API Response Meta:</h4>
            <div className="bg-white p-3 rounded border">
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-2 text-xs">
                <div className="text-center">
                  <div className="font-medium">Total</div>
                  <Badge variant="default">{meta.total}</Badge>
                </div>
                <div className="text-center">
                  <div className="font-medium">Page</div>
                  <Badge variant="default">{meta.page}</Badge>
                </div>
                <div className="text-center">
                  <div className="font-medium">Limit</div>
                  <Badge variant="default">{meta.limit}</Badge>
                </div>
                <div className="text-center">
                  <div className="font-medium">Total Pages</div>
                  <Badge variant="default">{meta.totalPages}</Badge>
                </div>
                <div className="text-center">
                  <div className="font-medium">Has Previous</div>
                  <Badge variant={meta.hasPreviousPage ? 'default' : 'secondary'}>
                    {meta.hasPreviousPage ? 'Yes' : 'No'}
                  </Badge>
                </div>
                <div className="text-center">
                  <div className="font-medium">Has Next</div>
                  <Badge variant={meta.hasNextPage ? 'default' : 'secondary'}>
                    {meta.hasNextPage ? 'Yes' : 'No'}
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* State Sync Check */}
        <div className="space-y-2">
          <h4 className="font-medium text-sm">State Synchronization Check:</h4>
          <div className="bg-white p-3 rounded border">
            <div className="grid grid-cols-2 gap-4 text-xs">
              <div>
                <div className="font-medium mb-1">Page Sync:</div>
                <Badge variant={query.page === filters.page ? 'default' : 'destructive'}>
                  {query.page === filters.page ? '✓ Synced' : '✗ Out of Sync'}
                </Badge>
              </div>
              <div>
                <div className="font-medium mb-1">Limit Sync:</div>
                <Badge variant={query.limit === filters.limit ? 'default' : 'destructive'}>
                  {query.limit === filters.limit ? '✓ Synced' : '✗ Out of Sync'}
                </Badge>
              </div>
            </div>
          </div>
        </div>

        {/* URL State */}
        <div className="space-y-2">
          <h4 className="font-medium text-sm">Current URL Parameters:</h4>
          <div className="bg-white p-3 rounded border text-xs">
            <code className="break-all">
              {typeof window !== 'undefined' ? window.location.search : 'Loading...'}
            </code>
          </div>
        </div>

        {/* Test Pagination */}
        {onTestPagination && meta && (
          <div className="space-y-2">
            <h4 className="font-medium text-sm">Test Pagination:</h4>
            <div className="bg-white p-3 rounded border">
              <div className="flex flex-wrap gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => onTestPagination(1)}
                  disabled={meta.page === 1}
                >
                  Page 1
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => onTestPagination(2)}
                  disabled={meta.page === 2 || meta.totalPages < 2}
                >
                  Page 2
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => onTestPagination(3)}
                  disabled={meta.page === 3 || meta.totalPages < 3}
                >
                  Page 3
                </Button>
                {meta.totalPages > 3 && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => onTestPagination(meta.totalPages)}
                    disabled={meta.page === meta.totalPages}
                  >
                    Last Page ({meta.totalPages})
                  </Button>
                )}
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
