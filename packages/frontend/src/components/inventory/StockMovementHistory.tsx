'use client';

import { useState } from 'react';
import { format } from 'date-fns';
import { id as localeId } from 'date-fns/locale';
import {
  ArrowUp,
  ArrowDown,
  RefreshCw,
  Filter,
  Calendar,
  User,
  FileText,
  TrendingUp,
  TrendingDown,
  Activity,
  Truck,
  Package,
  ChevronLeft,
  ChevronRight,
} from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';

import { StockMovement, StockMovementType, StockMovementQueryParams } from '@/types/inventory';
import { useStockMovements } from '@/hooks/useInventory';
import { formatCurrency } from '@/lib/utils';

interface StockMovementHistoryProps {
  inventoryItemId: string;
  currentStock: number;
  unitName: string;
  unitAbbreviation: string;
}

export function StockMovementHistory({
  inventoryItemId,
  currentStock,
  unitName,
  unitAbbreviation,
}: StockMovementHistoryProps) {
  const [queryParams, setQueryParams] = useState<StockMovementQueryParams>({
    page: 1,
    limit: 20,
    sortBy: 'createdAt',
    sortOrder: 'desc',
  });

  const [showFilters, setShowFilters] = useState(false);

  const {
    data: stockMovements,
    isLoading,
    error,
    refetch,
  } = useStockMovements(inventoryItemId, queryParams);

  // Helper functions
  const getMovementTypeIcon = (type: StockMovementType) => {
    switch (type) {
      case StockMovementType.IN:
        return <TrendingUp className="h-4 w-4 text-green-600" />;
      case StockMovementType.OUT:
        return <TrendingDown className="h-4 w-4 text-red-600" />;
      case StockMovementType.ADJUSTMENT:
        return <Activity className="h-4 w-4 text-blue-600" />;
      case StockMovementType.TRANSFER:
        return <Truck className="h-4 w-4 text-purple-600" />;
      case StockMovementType.ALLOCATION:
        return <Package className="h-4 w-4 text-orange-600" />;
      default:
        return <Activity className="h-4 w-4 text-gray-600" />;
    }
  };

  const getMovementTypeBadge = (type: StockMovementType) => {
    switch (type) {
      case StockMovementType.IN:
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            Masuk
          </Badge>
        );
      case StockMovementType.OUT:
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
            Keluar
          </Badge>
        );
      case StockMovementType.ADJUSTMENT:
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            Penyesuaian
          </Badge>
        );
      case StockMovementType.TRANSFER:
        return (
          <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
            Transfer
          </Badge>
        );
      case StockMovementType.ALLOCATION:
        return (
          <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">
            Alokasi
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
            Lainnya
          </Badge>
        );
    }
  };

  const getQuantityDisplay = (movement: StockMovement) => {
    const isPositive = movement.quantity > 0;
    const absQuantity = Math.abs(movement.quantity);

    return (
      <div className={`flex items-center gap-1 ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
        {isPositive ? (
          <ArrowUp className="h-3 w-3" />
        ) : (
          <ArrowDown className="h-3 w-3" />
        )}
        <span className="font-medium">
          {isPositive ? '+' : '-'}{absQuantity} {unitAbbreviation}
        </span>
      </div>
    );
  };

  // Calculate running balance (this would ideally come from backend)
  const calculateRunningBalance = (movements: StockMovement[], index: number) => {
    // Start with current stock and work backwards
    let balance = currentStock;

    // Subtract all movements that happened after this one
    for (let i = 0; i < index; i++) {
      balance -= movements[i].quantity;
    }

    return balance;
  };

  const handlePageChange = (newPage: number) => {
    setQueryParams(prev => ({ ...prev, page: newPage }));
  };

  const handleFilterChange = (key: keyof StockMovementQueryParams, value: any) => {
    setQueryParams(prev => ({ ...prev, [key]: value, page: 1 }));
  };

  const clearFilters = () => {
    setQueryParams({
      page: 1,
      limit: 20,
      sortBy: 'createdAt',
      sortOrder: 'desc',
    });
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Riwayat Pergerakan Stok
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Riwayat Pergerakan Stok
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-muted-foreground mb-4">Gagal memuat riwayat pergerakan stok</p>
            <Button variant="outline" onClick={() => refetch()}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Coba Lagi
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Riwayat Pergerakan Stok
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
            >
              <Filter className="mr-2 h-4 w-4" />
              Filter
            </Button>
            <Button variant="outline" size="sm" onClick={() => refetch()}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </Button>
          </div>
        </div>

        {/* Filters */}
        {showFilters && (
          <>
            <Separator className="my-4" />
            <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
              <div className="space-y-2">
                <Label htmlFor="type-filter">Jenis Pergerakan</Label>
                <Select
                  value={queryParams.type || 'all'}
                  onValueChange={(value) =>
                    handleFilterChange('type', value === 'all' ? undefined : value as StockMovementType)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Semua jenis" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Semua Jenis</SelectItem>
                    <SelectItem value={StockMovementType.IN}>Masuk</SelectItem>
                    <SelectItem value={StockMovementType.OUT}>Keluar</SelectItem>
                    <SelectItem value={StockMovementType.ADJUSTMENT}>Penyesuaian</SelectItem>
                    <SelectItem value={StockMovementType.TRANSFER}>Transfer</SelectItem>
                    <SelectItem value={StockMovementType.ALLOCATION}>Alokasi</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="sort-filter">Urutkan Berdasarkan</Label>
                <Select
                  value={queryParams.sortBy || 'createdAt'}
                  onValueChange={(value) => handleFilterChange('sortBy', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="createdAt">Tanggal Dibuat</SelectItem>
                    <SelectItem value="movementDate">Tanggal Pergerakan</SelectItem>
                    <SelectItem value="quantity">Jumlah</SelectItem>
                    <SelectItem value="type">Jenis</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="order-filter">Urutan</Label>
                <Select
                  value={queryParams.sortOrder || 'desc'}
                  onValueChange={(value) => handleFilterChange('sortOrder', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="desc">Terbaru</SelectItem>
                    <SelectItem value="asc">Terlama</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-end">
                <Button variant="outline" onClick={clearFilters} className="w-full">
                  Reset Filter
                </Button>
              </div>
            </div>
          </>
        )}
      </CardHeader>

      <CardContent>
        {!stockMovements?.data?.length ? (
          <div className="text-center py-8">
            <Activity className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <p className="text-muted-foreground">Belum ada riwayat pergerakan stok</p>
          </div>
        ) : (
          <>
            {/* Mobile View */}
            <div className="block sm:hidden space-y-4">
              {stockMovements.data.map((movement, index) => {
                const runningBalance = calculateRunningBalance(stockMovements.data, index);
                return (
                  <Card key={movement.id} className="p-4">
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          {getMovementTypeIcon(movement.type)}
                          {getMovementTypeBadge(movement.type)}
                        </div>
                        {getQuantityDisplay(movement)}
                      </div>

                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Tanggal:</span>
                          <span>{format(new Date(movement.createdAt), 'dd MMM yyyy HH:mm', { locale: localeId })}</span>
                        </div>

                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Saldo Stok:</span>
                          <span className="font-medium">{runningBalance} {unitAbbreviation}</span>
                        </div>

                        {movement.reason && (
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Alasan:</span>
                            <span className="text-right">{movement.reason}</span>
                          </div>
                        )}

                        {movement.referenceNumber && (
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Referensi:</span>
                            <span className="font-mono text-xs">{movement.referenceNumber}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </Card>
                );
              })}
            </div>

            {/* Desktop View */}
            <div className="hidden sm:block">
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Jenis</TableHead>
                      <TableHead>Jumlah</TableHead>
                      <TableHead>Saldo Stok</TableHead>
                      <TableHead>Tanggal</TableHead>
                      <TableHead>Alasan</TableHead>
                      <TableHead>Referensi</TableHead>
                      <TableHead>Catatan</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {stockMovements.data.map((movement, index) => {
                      const runningBalance = calculateRunningBalance(stockMovements.data, index);
                      return (
                        <TableRow key={movement.id}>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              {getMovementTypeIcon(movement.type)}
                              {getMovementTypeBadge(movement.type)}
                            </div>
                          </TableCell>
                          <TableCell>{getQuantityDisplay(movement)}</TableCell>
                          <TableCell>
                            <span className="font-medium">{runningBalance} {unitAbbreviation}</span>
                          </TableCell>
                          <TableCell>
                            <div className="space-y-1">
                              <div className="text-sm">
                                {format(new Date(movement.createdAt), 'dd MMM yyyy', { locale: localeId })}
                              </div>
                              <div className="text-xs text-muted-foreground">
                                {format(new Date(movement.createdAt), 'HH:mm', { locale: localeId })}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <span className="text-sm">{movement.reason || '-'}</span>
                          </TableCell>
                          <TableCell>
                            {movement.referenceNumber ? (
                              <span className="font-mono text-xs bg-gray-100 px-2 py-1 rounded">
                                {movement.referenceNumber}
                              </span>
                            ) : (
                              '-'
                            )}
                          </TableCell>
                          <TableCell>
                            <span className="text-sm text-muted-foreground">
                              {movement.notes || '-'}
                            </span>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </div>
            </div>

            {/* Pagination */}
            {stockMovements.meta && stockMovements.meta.totalPages > 1 && (
              <>
                <Separator className="my-4" />
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                  <div className="text-sm text-muted-foreground">
                    Menampilkan {((stockMovements.meta.page - 1) * stockMovements.meta.limit) + 1} - {Math.min(stockMovements.meta.page * stockMovements.meta.limit, stockMovements.meta.total)} dari {stockMovements.meta.total} pergerakan
                  </div>

                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(stockMovements.meta.page - 1)}
                      disabled={!stockMovements.meta.hasPreviousPage}
                    >
                      <ChevronLeft className="h-4 w-4" />
                      Sebelumnya
                    </Button>

                    <span className="text-sm">
                      Halaman {stockMovements.meta.page} dari {stockMovements.meta.totalPages}
                    </span>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(stockMovements.meta.page + 1)}
                      disabled={!stockMovements.meta.hasNextPage}
                    >
                      Selanjutnya
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}
