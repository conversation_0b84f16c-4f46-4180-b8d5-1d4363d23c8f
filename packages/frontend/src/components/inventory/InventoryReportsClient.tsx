'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  FileText,
  FileSpreadsheet,
  Database,
  Download,
  Upload,
  Plus,
  Clock,
  TrendingUp,
  Package,
  AlertTriangle,
  Activity,
  BarChart3,
  Calendar,
  RotateCcw,
  GitBranch,
  Settings,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ReportGenerationDialog } from './ReportGenerationDialog';
import { ImportInventoryDialog } from './ImportInventoryDialog';
import {
  ReportType,
  ReportFormat,
  QUICK_REPORT_PRESETS,
  REPORT_TYPE_CONFIG,
} from '@/types/inventory-reports';
import {
  useExportCurrentStock,
  useExportLowStock,
  useExportExpiringItems,
  useExportStockMovements,
} from '@/hooks/useInventoryReports';
import { navigateBackWithFallback } from '@/lib/utils/navigation';

export function InventoryReportsClient() {
  const router = useRouter();
  const [showReportDialog, setShowReportDialog] = useState(false);
  const [showImportDialog, setShowImportDialog] = useState(false);
  const [selectedReportType, setSelectedReportType] = useState<ReportType | undefined>();
  const [selectedPreset, setSelectedPreset] = useState<string | undefined>();

  // Export mutations
  const exportCurrentStockMutation = useExportCurrentStock();
  const exportLowStockMutation = useExportLowStock();
  const exportExpiringItemsMutation = useExportExpiringItems();
  const exportStockMovementsMutation = useExportStockMovements();

  const handleQuickExport = async (presetId: string) => {
    const preset = QUICK_REPORT_PRESETS.find(p => p.id === presetId);
    if (!preset) return;

    try {
      switch (preset.type) {
        case ReportType.STOCK_LEVELS:
          if (preset.filters.lowStockOnly) {
            await exportLowStockMutation.mutateAsync(preset.format);
          } else {
            await exportCurrentStockMutation.mutateAsync(preset.format);
          }
          break;
        case ReportType.EXPIRY_REPORT:
          await exportExpiringItemsMutation.mutateAsync({
            days: preset.filters.expiringSoonDays || 30,
            format: preset.format,
          });
          break;
        case ReportType.STOCK_MOVEMENTS:
          if (preset.filters.startDate && preset.filters.endDate) {
            await exportStockMovementsMutation.mutateAsync({
              startDate: preset.filters.startDate,
              endDate: preset.filters.endDate,
              format: preset.format,
            });
          }
          break;
        default:
          // For other types, open the dialog
          setSelectedReportType(preset.type);
          setShowReportDialog(true);
          break;
      }
    } catch (error) {
      console.error('Error exporting report:', error);
    }
  };

  const handleCustomReport = (type?: ReportType) => {
    setSelectedReportType(type);
    setSelectedPreset(undefined); // Clear any preset selection
    setShowReportDialog(true);
  };

  const handlePresetCustomization = (presetId: string) => {
    const preset = QUICK_REPORT_PRESETS.find(p => p.id === presetId);
    if (preset) {
      setSelectedReportType(preset.type);
      setSelectedPreset(presetId);
      setShowReportDialog(true);
    }
  };

  const getReportTypeIcon = (type: ReportType) => {
    switch (type) {
      case ReportType.STOCK_LEVELS:
        return <Package className="h-5 w-5" />;
      case ReportType.STOCK_MOVEMENTS:
        return <Activity className="h-5 w-5" />;
      case ReportType.LOW_STOCK:
        return <AlertTriangle className="h-5 w-5" />;
      case ReportType.EXPIRY_REPORT:
        return <Clock className="h-5 w-5" />;
      case ReportType.ALLOCATION_HISTORY:
        return <GitBranch className="h-5 w-5" />;
      case ReportType.SUPPLIER_PERFORMANCE:
        return <TrendingUp className="h-5 w-5" />;
      case ReportType.CATEGORY_ANALYSIS:
        return <BarChart3 className="h-5 w-5" />;
      case ReportType.STOCK_AGING:
        return <Calendar className="h-5 w-5" />;
      case ReportType.TURNOVER_ANALYSIS:
        return <RotateCcw className="h-5 w-5" />;
      default:
        return <FileText className="h-5 w-5" />;
    }
  };

  const getFormatIcon = (format: ReportFormat) => {
    switch (format) {
      case ReportFormat.PDF:
        return <FileText className="h-4 w-4" />;
      case ReportFormat.EXCEL:
        return <FileSpreadsheet className="h-4 w-4" />;
      case ReportFormat.CSV:
        return <Database className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  const isExporting =
    exportCurrentStockMutation.isPending ||
    exportLowStockMutation.isPending ||
    exportExpiringItemsMutation.isPending ||
    exportStockMovementsMutation.isPending;

  return (
    <div className="space-y-6">
      {/* Action Buttons */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex flex-wrap gap-2">
          <Button
            variant="outline"
            onClick={() => navigateBackWithFallback(router, '/dashboard/inventory')}
          >
            Kembali ke Inventori
          </Button>
        </div>

        <div className="flex flex-wrap gap-2">
          <Button
            variant="outline"
            onClick={() => setShowImportDialog(true)}
          >
            <Upload className="h-4 w-4 mr-2" />
            Import Data
          </Button>
          <Button
            onClick={() => handleCustomReport()}
          >
            <Plus className="h-4 w-4 mr-2" />
            Buat Laporan Custom
          </Button>
        </div>
      </div>

      {/* Quick Reports Section */}
      <div className="space-y-4">
        <div>
          <h2 className="text-lg font-semibold">Laporan Cepat</h2>
          <p className="text-sm text-muted-foreground">
            Buat laporan umum dengan satu klik
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {QUICK_REPORT_PRESETS.map((preset) => (
            <Card
              key={preset.id}
              className="cursor-pointer transition-all hover:shadow-md hover:scale-[1.02]"
            >
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {getReportTypeIcon(preset.type)}
                    <CardTitle className="text-base">{preset.name}</CardTitle>
                  </div>
                  {preset.isDefault && (
                    <Badge variant="secondary" className="text-xs">
                      Default
                    </Badge>
                  )}
                </div>
                <CardDescription className="text-sm">
                  {preset.description}
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {getFormatIcon(preset.format)}
                    <span className="text-sm text-muted-foreground">
                      {preset.format.toUpperCase()}
                    </span>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handlePresetCustomization(preset.id)}
                    >
                      <Settings className="h-4 w-4 mr-1" />
                      Kustomisasi
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => handleQuickExport(preset.id)}
                      disabled={isExporting}
                    >
                      <Download className="h-4 w-4 mr-1" />
                      Export
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      <Separator />

      {/* All Report Types Section */}
      <div className="space-y-4">
        <div>
          <h2 className="text-lg font-semibold">Semua Jenis Laporan</h2>
          <p className="text-sm text-muted-foreground">
            Pilih jenis laporan untuk kustomisasi lebih lanjut
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {Object.entries(REPORT_TYPE_CONFIG).map(([type, config]) => (
            <Card
              key={type}
              className="cursor-pointer transition-all hover:shadow-md hover:scale-[1.02]"
              onClick={() => handleCustomReport(type as ReportType)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center gap-2">
                  {getReportTypeIcon(type as ReportType)}
                  <CardTitle className="text-base">{config.name}</CardTitle>
                </div>
                <CardDescription className="text-sm">
                  {config.description}
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="flex items-center justify-between">
                  <div className="flex flex-wrap gap-1">
                    {config.supportedFormats.map((format) => (
                      <Badge key={format} variant="outline" className="text-xs">
                        {format.toUpperCase()}
                      </Badge>
                    ))}
                  </div>
                  <Button size="sm" variant="outline">
                    Kustomisasi
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      <Separator />

      {/* Import/Export Section */}
      <div className="space-y-4">
        <div>
          <h2 className="text-lg font-semibold">Import & Export Data</h2>
          <p className="text-sm text-muted-foreground">
            Kelola data inventori dengan import dan export
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-base flex items-center gap-2">
                <Upload className="h-5 w-5" />
                Import Data Inventori
              </CardTitle>
              <CardDescription>
                Import data inventori dari file Excel atau CSV
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button
                onClick={() => setShowImportDialog(true)}
                className="w-full"
              >
                <Upload className="h-4 w-4 mr-2" />
                Mulai Import
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-base flex items-center gap-2">
                <Download className="h-5 w-5" />
                Export Data Lengkap
              </CardTitle>
              <CardDescription>
                Export semua data inventori dalam format yang dipilih
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => exportCurrentStockMutation.mutate(ReportFormat.PDF)}
                  disabled={isExporting}
                >
                  <FileText className="h-4 w-4 mr-1" />
                  PDF
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => exportCurrentStockMutation.mutate(ReportFormat.EXCEL)}
                  disabled={isExporting}
                >
                  <FileSpreadsheet className="h-4 w-4 mr-1" />
                  Excel
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => exportCurrentStockMutation.mutate(ReportFormat.CSV)}
                  disabled={isExporting}
                >
                  <Database className="h-4 w-4 mr-1" />
                  CSV
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Dialogs */}
      <ReportGenerationDialog
        open={showReportDialog}
        onOpenChange={(open) => {
          setShowReportDialog(open);
          if (!open) {
            // Clear selections when dialog closes
            setSelectedReportType(undefined);
            setSelectedPreset(undefined);
          }
        }}
        defaultType={selectedReportType}
        defaultPresetId={selectedPreset}
      />

      <ImportInventoryDialog
        open={showImportDialog}
        onOpenChange={setShowImportDialog}
      />
    </div>
  );
}
