import { Select, SelectContent, SelectGroup, SelectItem, Select<PERSON>abel, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ReferenceType } from "@/types/inventory";
import { REFERENCE_TYPE_OPTIONS } from "@/lib/constants/inventory";

interface ReferenceTypeSelectProps {
  value?: ReferenceType | string;
  onValueChange?: (value: ReferenceType | string) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  includeEmpty?: boolean;
  emptyLabel?: string;
}

export function ReferenceTypeSelect({
  value,
  onValueChange,
  placeholder = "<PERSON>lih jenis referensi",
  disabled = false,
  className,
  includeEmpty = false,
  emptyLabel = "Se<PERSON><PERSON> jenis referensi"
}: ReferenceTypeSelectProps) {
  // Group options by category
  const salesTypes: ReferenceType[] = [ReferenceType.SALE, ReferenceType.SALE_CANCELLATION, ReferenceType.SALE_REFUND, ReferenceType.SALE_DELETION];
  const purchaseTypes: ReferenceType[] = [ReferenceType.PURCHASE, ReferenceType.PURCHASE_RETURN];
  const inventoryTypes: ReferenceType[] = [ReferenceType.INITIAL_STOCK, ReferenceType.ADJUSTMENT, ReferenceType.TRANSFER];
  const allocationTypes: ReferenceType[] = [ReferenceType.ALLOCATION, ReferenceType.DEALLOCATION];
  const statusTypes: ReferenceType[] = [ReferenceType.ACTIVATION, ReferenceType.DEACTIVATION];
  const customerTypes: ReferenceType[] = [ReferenceType.CUSTOMER_RESERVATION, ReferenceType.CUSTOMER_ORDER];
  const expiryTypes: ReferenceType[] = [ReferenceType.EXPIRY_WRITE_OFF];
  const otherTypes: ReferenceType[] = [ReferenceType.OTHER];

  const salesOptions = REFERENCE_TYPE_OPTIONS.filter(opt => (salesTypes as readonly ReferenceType[]).includes(opt.value as ReferenceType));
  const purchaseOptions = REFERENCE_TYPE_OPTIONS.filter(opt => (purchaseTypes as readonly ReferenceType[]).includes(opt.value as ReferenceType));
  const inventoryOptions = REFERENCE_TYPE_OPTIONS.filter(opt => (inventoryTypes as readonly ReferenceType[]).includes(opt.value as ReferenceType));
  const allocationOptions = REFERENCE_TYPE_OPTIONS.filter(opt => (allocationTypes as readonly ReferenceType[]).includes(opt.value as ReferenceType));
  const statusOptions = REFERENCE_TYPE_OPTIONS.filter(opt => (statusTypes as readonly ReferenceType[]).includes(opt.value as ReferenceType));
  const customerOptions = REFERENCE_TYPE_OPTIONS.filter(opt => (customerTypes as readonly ReferenceType[]).includes(opt.value as ReferenceType));
  const expiryOptions = REFERENCE_TYPE_OPTIONS.filter(opt => (expiryTypes as readonly ReferenceType[]).includes(opt.value as ReferenceType));
  const otherOptions = REFERENCE_TYPE_OPTIONS.filter(opt => (otherTypes as readonly ReferenceType[]).includes(opt.value as ReferenceType));

  return (
    <Select
      value={value}
      onValueChange={onValueChange}
      disabled={disabled}
    >
      <SelectTrigger className={className}>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {includeEmpty && (
          <SelectItem value="ALL">
            {emptyLabel}
          </SelectItem>
        )}

        <SelectGroup>
          <SelectLabel>Penjualan</SelectLabel>
          {salesOptions.map(option => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectGroup>

        <SelectGroup>
          <SelectLabel>Pembelian</SelectLabel>
          {purchaseOptions.map(option => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectGroup>

        <SelectGroup>
          <SelectLabel>Inventaris</SelectLabel>
          {inventoryOptions.map(option => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectGroup>

        <SelectGroup>
          <SelectLabel>Alokasi</SelectLabel>
          {allocationOptions.map(option => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectGroup>

        <SelectGroup>
          <SelectLabel>Status</SelectLabel>
          {statusOptions.map(option => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectGroup>

        <SelectGroup>
          <SelectLabel>Pelanggan</SelectLabel>
          {customerOptions.map(option => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectGroup>

        <SelectGroup>
          <SelectLabel>Kedaluwarsa</SelectLabel>
          {expiryOptions.map(option => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectGroup>

        <SelectGroup>
          <SelectLabel>Lainnya</SelectLabel>
          {otherOptions.map(option => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectGroup>
      </SelectContent>
    </Select>
  );
} 