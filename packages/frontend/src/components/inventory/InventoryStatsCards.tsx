'use client';

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { useInventoryStats } from '@/hooks/useInventory';
import { formatCurrency } from '@/lib/constants/inventory';
import {
  Package,
  AlertTriangle,
  Clock,
  XCircle,
  TrendingUp,
  DollarSign
} from 'lucide-react';

export function InventoryStatsCards() {
  const { data: stats, isLoading, error } = useInventoryStats();

  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6">
        {Array.from({ length: 6 }).map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-4 w-4" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-16 mb-1" />
              <Skeleton className="h-3 w-24" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <Card className="col-span-full">
        <CardContent className="pt-6">
          <div className="text-center text-muted-foreground">
            <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
            <p>Gagal memuat statistik inventori</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const statsCards = [
    {
      title: 'Total Item',
      value: stats?.totalItems?.toLocaleString('id-ID') || '0',
      description: 'Item inventori',
      icon: Package,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: 'Item Aktif',
      value: stats?.activeItems?.toLocaleString('id-ID') || '0',
      description: 'Item tersedia',
      icon: TrendingUp,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: 'Stok Rendah',
      value: stats?.lowStockItems?.toLocaleString('id-ID') || '0',
      description: 'Perlu restok',
      icon: AlertTriangle,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
    },
    {
      title: 'Akan Kedaluwarsa',
      value: stats?.expiringSoonItems?.toLocaleString('id-ID') || '0',
      description: '30 hari ke depan',
      icon: Clock,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
    },
    {
      title: 'Kedaluwarsa',
      value: stats?.expiredItems?.toLocaleString('id-ID') || '0',
      description: 'Perlu ditangani',
      icon: XCircle,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
    },
    {
      title: 'Nilai Total',
      value: formatCurrency(stats?.totalValue || 0),
      description: 'Nilai inventori',
      icon: DollarSign,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
    },
  ];

  return (
    <div className="grid gap-4 md:grid-cols-2 xl:grid-cols-3">
      {statsCards.map((stat, index) => {
        const Icon = stat.icon;
        return (
          <Card key={index} className="relative overflow-hidden">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                {stat.title}
              </CardTitle>
              <div className={`p-2 rounded-md ${stat.bgColor}`}>
                <Icon className={`h-4 w-4 ${stat.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <p className="text-xs text-muted-foreground mt-1">
                {stat.description}
              </p>

              {/* Add warning badges for critical items */}
              {index === 2 && stats?.lowStockItems && stats.lowStockItems > 0 && (
                <Badge variant="secondary" className="mt-2 text-xs">
                  Perlu Perhatian
                </Badge>
              )}
              {index === 3 && stats?.expiringSoonItems && stats.expiringSoonItems > 0 && (
                <Badge variant="secondary" className="mt-2 text-xs">
                  Segera Tindak Lanjut
                </Badge>
              )}
              {index === 4 && stats?.expiredItems && stats.expiredItems > 0 && (
                <Badge variant="destructive" className="mt-2 text-xs">
                  Tindakan Diperlukan
                </Badge>
              )}
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
