'use client';

import * as React from 'react';
import {
  ColumnDef,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { ChevronDown, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight, Settings2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

import { useDebounce, DEBOUNCE_DELAYS } from '@/lib/utils/debounce';
import { InventoryQueryParams, InventoryResponse } from '@/types/inventory';
import { InventoryFilters } from './InventoryFilters';

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  meta: InventoryResponse['meta'];
  searchPlaceholder?: string;
  onRowClick?: (row: TData) => void;
  onQueryChange?: (query: InventoryQueryParams) => void;
  loading?: boolean;
  query: InventoryQueryParams;
  filters: InventoryQueryParams;
  onFilterChange: (key: keyof InventoryQueryParams, value: any) => void;
  onBatchFilterChange?: (updates: Partial<InventoryQueryParams>) => void;
  onClearFilters: () => void;
}

export function DataTable<TData, TValue>({
  columns,
  data,
  meta,
  searchPlaceholder = 'Cari inventori...',
  onRowClick,
  onQueryChange,
  loading = false,
  query,
  filters,
  onFilterChange,
  onBatchFilterChange,
  onClearFilters,
}: DataTableProps<TData, TValue>) {
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
  const [searchValue, setSearchValue] = React.useState(query.search || '');

  // Debounced search using centralized utility
  const debouncedSearch = useDebounce(searchValue, DEBOUNCE_DELAYS.SEARCH);

  // Sync search value with query changes (when query comes from URL or external source)
  React.useEffect(() => {
    if (query.search !== searchValue) {
      setSearchValue(query.search || '');
    }
  }, [query.search]);

  // Use refs to avoid stale closures
  const queryRef = React.useRef(query);
  const onQueryChangeRef = React.useRef(onQueryChange);

  React.useEffect(() => {
    queryRef.current = query;
    onQueryChangeRef.current = onQueryChange;
  });

  // Trigger query change when debounced search changes
  React.useEffect(() => {
    if (onQueryChangeRef.current && debouncedSearch !== queryRef.current.search) {
      onQueryChangeRef.current({
        ...queryRef.current,
        search: debouncedSearch || undefined,
        page: 1, // Reset to first page when searching
      });
    }
  }, [debouncedSearch]);

  // Handle pagination - use onQueryChange like supplier/product tables
  const handlePageChange = (newPage: number) => {
    onQueryChange?.({
      ...query,
      page: newPage,
    });
  };

  const handleLimitChange = (newLimit: string) => {
    onQueryChange?.({
      ...query,
      limit: parseInt(newLimit),
      page: 1, // Reset to first page
    });
  };

  // Count active filters - use memoization to prevent accumulation issues
  const activeFiltersCount = React.useMemo(() => {
    let count = 0;

    // Basic filters
    if (filters.isActive !== undefined) count++;
    if (filters.productType) count++;

    // Stock status filters (count as one group)
    if (filters.lowStock || filters.expired || filters.expiringSoon ||
      filters.quantityMin !== undefined || filters.quantityMax !== undefined) {
      count++;
    }

    // Advanced filters
    if (filters.productCategory) count++;
    if (filters.location) count++;
    if (filters.batchNumber) count++;
    if (filters.costPriceMin !== undefined) count++;
    if (filters.costPriceMax !== undefined) count++;
    if (filters.sellingPriceMin !== undefined) count++;
    if (filters.sellingPriceMax !== undefined) count++;
    if (filters.lowStockThreshold !== undefined) count++;
    if (filters.expiringSoonDays !== undefined) count++;
    if (filters.search) count++;

    return count;
  }, [filters]);

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    state: {
      sorting,
      columnVisibility,
    },
    manualSorting: true,
    manualFiltering: true,
    manualPagination: true,
  });

  return (
    <div className="w-full min-w-0 max-w-full space-y-4">
      {/* Unified Single-Row Toolbar */}
      <div className="flex flex-col gap-3 lg:gap-4">
        {/* Search Input */}
        <div className="relative flex-1 max-w-sm">
          <Input
            placeholder={searchPlaceholder}
            value={searchValue}
            onChange={(event) => setSearchValue(event.target.value)}
            className="pr-10 h-9"
            disabled={loading}
          />
          {loading && (
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
            </div>
          )}
        </div>
        {/* Main toolbar row - all controls in one line on desktop */}
        <div className="flex flex-col gap-3 lg:flex-row lg:items-center lg:gap-4">
          {/* Filter Controls */}
          <div className="flex flex-wrap items-center gap-2">
            <InventoryFilters
              filters={filters}
              onFilterChange={onFilterChange}
              onBatchFilterChange={onBatchFilterChange}
              onClearFilters={onClearFilters}
              activeFiltersCount={activeFiltersCount}
              loading={loading}
            />

            {/* Column Visibility */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="h-9 px-3">
                  <Settings2 className="h-4 w-4 mr-1" />
                  Kolom
                  <ChevronDown className="h-4 w-4 ml-1" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                {table
                  .getAllColumns()
                  .filter((column) => column.getCanHide())
                  .map((column) => {
                    return (
                      <DropdownMenuCheckboxItem
                        key={column.id}
                        className="capitalize"
                        checked={column.getIsVisible()}
                        onCheckedChange={(value) =>
                          column.toggleVisibility(!!value)
                        }
                      >
                        {column.id}
                      </DropdownMenuCheckboxItem>
                    );
                  })}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>

      {/* Table Container with horizontal scroll and sticky actions */}
      <div className="w-full min-w-0 max-w-full overflow-hidden rounded-md border">
        {/* Enhanced Scroll indicators */}
        <div className="bg-blue-50 dark:bg-blue-950/30 px-4 py-3 text-sm text-blue-700 dark:text-blue-300 border-b border-blue-200 dark:border-blue-800">
          <div className="flex items-center justify-center gap-2">
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
              <span className="font-medium">Tabel dapat digulir horizontal</span>
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
            </div>
          </div>
          <div className="text-xs text-center mt-1 text-blue-600 dark:text-blue-400">
            Geser ke kanan untuk melihat kolom aksi dan informasi lainnya
          </div>
        </div>

        <div className="w-full overflow-x-auto" id="table-scroll-container">
          <Table className="min-w-[1400px]">
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => {
                    const isActionsColumn = header.id === 'actions';
                    return (
                      <TableHead
                        key={header.id}
                        className={`px-2 lg:px-4 ${isActionsColumn
                          ? 'sticky right-0 bg-background border-l shadow-lg z-10'
                          : ''
                          }`}
                      >
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                      </TableHead>
                    );
                  })}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {loading ? (
                // Loading state - maintain table structure with skeleton rows
                Array.from({ length: query.limit || 10 }).map((_, index) => (
                  <TableRow key={`loading-${index}`}>
                    {columns.map((_, colIndex) => (
                      <TableCell key={colIndex} className="px-2 lg:px-4">
                        <div className="h-4 bg-muted animate-pulse rounded"></div>
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    key={row.id}
                    className={onRowClick ? 'cursor-pointer hover:bg-muted/50' : ''}
                    onClick={() => onRowClick?.(row.original)}
                  >
                    {row.getVisibleCells().map((cell) => {
                      const isActionsColumn = cell.column.id === 'actions';
                      return (
                        <TableCell
                          key={cell.id}
                          onClick={(e) => e.stopPropagation()}
                          className={`px-2 lg:px-4 ${isActionsColumn
                            ? 'sticky right-0 bg-background border-l shadow-lg z-10'
                            : ''
                            }`}
                        >
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </TableCell>
                      );
                    })}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    className="h-24 text-center"
                  >
                    Tidak ada data inventori.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Pagination */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between p-4">
        <div className="flex items-center gap-4">
          <div className="text-sm text-muted-foreground">
            Menampilkan {((query.page || 1) - 1) * (query.limit || 10) + 1} - {Math.min((query.page || 1) * (query.limit || 10), meta.total)} dari {meta.total} data
          </div>
          <Select
            value={String(query.limit || 10)}
            onValueChange={handleLimitChange}
            disabled={loading}
          >
            <SelectTrigger className="w-20">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="5">5</SelectItem>
              <SelectItem value="10">10</SelectItem>
              <SelectItem value="20">20</SelectItem>
              <SelectItem value="30">30</SelectItem>
              <SelectItem value="40">40</SelectItem>
              <SelectItem value="50">50</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(1)}
            disabled={loading || !meta.hasPreviousPage}
          >
            <ChevronsLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange((query.page || 1) - 1)}
            disabled={loading || !meta.hasPreviousPage}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <div className="flex items-center gap-1">
            <span className="text-sm">Halaman</span>
            <span className="text-sm font-medium">{query.page || 1}</span>
            <span className="text-sm">dari</span>
            <span className="text-sm font-medium">{meta.totalPages}</span>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange((query.page || 1) + 1)}
            disabled={loading || !meta.hasNextPage}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(meta.totalPages)}
            disabled={loading || !meta.hasNextPage}
          >
            <ChevronsRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
