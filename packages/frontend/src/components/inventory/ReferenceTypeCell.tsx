import { ReferenceType } from "@/types/inventory";
import { getReferenceTypeLabel } from "@/lib/constants/inventory";
import { ReferenceTypeBadge } from "./ReferenceTypeBadge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface ReferenceTypeCellProps {
  type?: ReferenceType | string | null;
  referenceId?: string | null;
  referenceNumber?: string | null;
  showTooltip?: boolean;
}

export function ReferenceTypeCell({ 
  type, 
  referenceId, 
  referenceNumber, 
  showTooltip = true 
}: ReferenceTypeCellProps) {
  if (!type) {
    return <span className="text-muted-foreground text-sm">-</span>;
  }

  const referenceType = type as ReferenceType;
  const label = getReferenceTypeLabel(referenceType);
  
  // If we have additional reference information, show it in a tooltip
  const hasReferenceInfo = referenceId || referenceNumber;
  
  if (hasReferenceInfo && showTooltip) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div>
              <ReferenceTypeBadge type={referenceType} />
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <div className="space-y-1">
              <p className="font-medium">{label}</p>
              {referenceNumber && (
                <p className="text-xs">No. Referensi: {referenceNumber}</p>
              )}
              {referenceId && (
                <p className="text-xs text-muted-foreground">ID: {referenceId}</p>
              )}
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }
  
  return <ReferenceTypeBadge type={referenceType} />;
} 