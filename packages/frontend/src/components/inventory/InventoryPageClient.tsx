'use client';

import { useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { Plus, Download, Upload, RefreshCw, Package } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { TooltipProvider } from '@/components/ui/tooltip';
import { DataTable } from '@/components/inventory/data-table';
import { createInventoryColumns } from '@/components/inventory/columns';
import { InventoryQuickView } from '@/components/inventory/inventory-quick-view';
import { StockAdjustmentDialog } from '@/components/inventory/StockAdjustmentDialog';
import { ReportGenerationDialog } from '@/components/inventory/ReportGenerationDialog';
import { ImportInventoryDialog } from '@/components/inventory/ImportInventoryDialog';
import { InventoryItem, InventoryQueryParams } from '@/types/inventory';
import { ReportType, ReportFormat } from '@/types/inventory-reports';
import {
  useInventoryItems,
  useInventoryInvalidate,
  useActivateInventoryItem,
  useDeactivateInventoryItem,
  useHardDeleteInventoryItem,
} from '@/hooks/useInventory';
import { useExportCurrentStock } from '@/hooks/useInventoryReports';
import { toast } from 'sonner';
import { DEFAULT_INVENTORY_PAGINATION } from '@/lib/constants/inventory';

interface InventoryPageClientProps {
  initialQuery: InventoryQueryParams;
}

export function InventoryPageClient({
  initialQuery,
}: InventoryPageClientProps) {
  const router = useRouter();

  // Query state - this will trigger the TanStack Query
  const [query, setQuery] = useState<InventoryQueryParams>(initialQuery);

  // Quick view state
  const [quickViewItem, setQuickViewItem] = useState<InventoryItem | null>(null);
  const [quickViewOpen, setQuickViewOpen] = useState(false);

  // Stock adjustment state
  const [stockAdjustmentItem, setStockAdjustmentItem] = useState<InventoryItem | null>(null);
  const [stockAdjustmentOpen, setStockAdjustmentOpen] = useState(false);

  // Report and import dialog state
  const [showReportDialog, setShowReportDialog] = useState(false);
  const [showImportDialog, setShowImportDialog] = useState(false);

  // Filter state for UI
  const [filters, setFilters] = useState<InventoryQueryParams>(initialQuery);

  // Use TanStack Query for data fetching
  const { data, isLoading, error, refetch } = useInventoryItems(query);

  // Use invalidation hook for stats refresh
  const { invalidateStats } = useInventoryInvalidate();

  // Use mutation hooks for inventory operations
  const activateInventoryMutation = useActivateInventoryItem();
  const deactivateInventoryMutation = useDeactivateInventoryItem();
  const hardDeleteInventoryMutation = useHardDeleteInventoryItem();

  // Use export hook for quick export functionality
  const exportCurrentStockMutation = useExportCurrentStock();

  // Handle query changes from DataTable - TanStack Query will automatically refetch
  const handleQueryChange = useCallback((newQuery: InventoryQueryParams) => {
    setQuery(newQuery);

    // Update URL without navigation to preserve scroll position
    const params = new URLSearchParams();
    Object.entries(newQuery).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.set(key, String(value));
      }
    });

    const newUrl = `/dashboard/inventory?${params.toString()}`;
    const currentUrl = window.location.pathname + window.location.search;

    if (newUrl !== currentUrl) {
      window.history.replaceState(null, '', newUrl);
    }
  }, []);

  // Handlers
  const handleViewItem = (item: InventoryItem) => {
    router.push(`/dashboard/inventory/${item.id}`);
  };

  const handleEditItem = (item: InventoryItem) => {
    router.push(`/dashboard/inventory/${item.id}/edit`);
  };

  const handleActivateItem = async (item: InventoryItem) => {
    try {
      await activateInventoryMutation.mutateAsync(item.id);
      toast.success('Item inventori berhasil diaktifkan');
    } catch (err: any) {
      toast.error(err?.response?.data?.message || 'Gagal mengaktifkan item inventori');
      console.error('Error activating inventory item:', err);
    }
  };

  const handleDeactivateItem = async (item: InventoryItem) => {
    try {
      await deactivateInventoryMutation.mutateAsync(item.id);
      toast.success('Item inventori berhasil dinonaktifkan');
    } catch (err: any) {
      toast.error(err?.response?.data?.message || 'Gagal menonaktifkan item inventori');
      console.error('Error deactivating inventory item:', err);
    }
  };

  const handleHardDeleteItem = async (item: InventoryItem) => {
    try {
      await hardDeleteInventoryMutation.mutateAsync(item.id);
      toast.success('Item inventori berhasil dihapus permanen');
    } catch (err: any) {
      toast.error(err?.response?.data?.message || 'Gagal menghapus item inventori');
      console.error('Error hard deleting inventory item:', err);
    }
  };

  const handleQuickView = (item: InventoryItem) => {
    setQuickViewItem(item);
    setQuickViewOpen(true);
  };

  const handleQuickViewEdit = (item: InventoryItem) => {
    setQuickViewOpen(false);
    router.push(`/dashboard/inventory/${item.id}/edit`);
  };

  const handleQuickViewFull = (item: InventoryItem) => {
    setQuickViewOpen(false);
    router.push(`/dashboard/inventory/${item.id}`);
  };

  const handleAdjustStock = (item: InventoryItem) => {
    setStockAdjustmentItem(item);
    setStockAdjustmentOpen(true);
  };

  const handleViewAllocationHistory = (item: InventoryItem) => {
    // Navigate to allocation history page with product filter
    router.push(`/dashboard/inventory/allocation-history?productId=${item.productId}`);
  };

  const handleExport = () => {
    // For quick export, use current filters to export filtered data
    exportCurrentStockMutation.mutate(ReportFormat.PDF);
  };

  const handleImport = () => {
    setShowImportDialog(true);
  };

  const handleAdvancedReports = () => {
    router.push('/dashboard/inventory/reports');
  };

  const handleFilterChange = useCallback((key: keyof InventoryQueryParams, value: any) => {
    // Don't reset page for pagination-related changes
    const isPaginationChange = key === 'page' || key === 'limit';

    const newFilters = {
      ...filters,
      [key]: value,
      // Only reset to page 1 for actual filter changes, not pagination changes
      ...(isPaginationChange ? {} : { page: 1 }),
    };

    // Only update if the value actually changed
    if (filters[key] !== value) {
      setFilters(newFilters);
      handleQueryChange(newFilters);
    }
  }, [filters, handleQueryChange]);

  const handleBatchFilterChange = useCallback((updates: Partial<InventoryQueryParams>) => {
    const newFilters = {
      ...filters,
      ...updates,
      page: 1, // Reset to first page when filtering
    };

    setFilters(newFilters);
    handleQueryChange(newFilters);
  }, [filters, handleQueryChange]);

  const clearFilters = useCallback(() => {
    const clearedFilters = {
      page: 1,
      limit: DEFAULT_INVENTORY_PAGINATION.limit,
      sortBy: DEFAULT_INVENTORY_PAGINATION.sortBy,
      sortOrder: DEFAULT_INVENTORY_PAGINATION.sortOrder,
      search: undefined, // Clear search field
    };
    setFilters(clearedFilters);
    handleQueryChange(clearedFilters);
  }, [handleQueryChange]);

  const handleRefresh = async () => {
    try {
      await refetch();
      invalidateStats();
      toast.success('Data inventori berhasil diperbarui');
    } catch (err) {
      toast.error('Gagal memperbarui data inventori');
    }
  };

  const columns = createInventoryColumns(
    {
      onView: handleViewItem,
      onEdit: handleEditItem,
      onQuickView: handleQuickView,
      onAdjustStock: handleAdjustStock,
      onViewAllocationHistory: handleViewAllocationHistory,
      onActivate: handleActivateItem,
      onDeactivate: handleDeactivateItem,
      onHardDelete: handleHardDeleteItem,
    },
    {
      isActivating: activateInventoryMutation.isPending,
      isDeactivating: deactivateInventoryMutation.isPending,
      isHardDeleting: hardDeleteInventoryMutation.isPending,
    }
  );

  if (error) {
    return (
      <div className="w-full min-w-0 max-w-full space-y-6 overflow-hidden">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-muted-foreground">
              <p>Gagal memuat data inventori</p>
              <Button onClick={handleRefresh} className="mt-4">
                <RefreshCw className="h-4 w-4 mr-2" />
                Coba Lagi
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <TooltipProvider>
      <div className="w-full min-w-0 max-w-full space-y-6 overflow-hidden">
        {/* Header Actions */}
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Inventori</h1>
            <p className="text-muted-foreground">
              Kelola stok dan inventori produk farmasi
            </p>
          </div>

          <div className="flex flex-col gap-2 sm:flex-row">
            <Button variant="outline" onClick={handleRefresh} disabled={isLoading}>
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>

            <Button variant="outline" onClick={() => router.push('/dashboard/inventory/allocate')}>
              <Package className="h-4 w-4 mr-2" />
              Alokasi Stok
            </Button>

            <Button variant="outline" onClick={handleImport}>
              <Upload className="h-4 w-4 mr-2" />
              Import
            </Button>

            <Button
              variant="outline"
              onClick={handleExport}
              disabled={exportCurrentStockMutation.isPending}
            >
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>

            <Button variant="outline" onClick={handleAdvancedReports}>
              <Download className="h-4 w-4 mr-2" />
              Laporan
            </Button>

            <Button onClick={() => router.push('/dashboard/inventory/create')}>
              <Plus className="h-4 w-4 mr-2" />
              Tambah Item
            </Button>
          </div>
        </div>

        {/* Data Table */}
        <Card className="w-full min-w-0 max-w-full overflow-hidden">
          <CardContent className="">
            <div className="w-full min-w-0 max-w-full">
              <DataTable
                columns={columns}
                data={data?.data || []}
                meta={data?.meta || { total: 0, page: 1, limit: 10, totalPages: 0, hasNextPage: false, hasPreviousPage: false }}
                query={query}
                onQueryChange={handleQueryChange}
                loading={isLoading}
                searchPlaceholder="Cari inventori..."
                onRowClick={handleViewItem}
                filters={filters}
                onFilterChange={handleFilterChange}
                onBatchFilterChange={handleBatchFilterChange}
                onClearFilters={clearFilters}
              />
            </div>
          </CardContent>
        </Card>

        {/* Quick View Modal */}
        <InventoryQuickView
          item={quickViewItem}
          open={quickViewOpen}
          onOpenChange={setQuickViewOpen}
          onEdit={handleQuickViewEdit}
          onViewFull={handleQuickViewFull}
        />

        {/* Stock Adjustment Dialog */}
        <StockAdjustmentDialog
          open={stockAdjustmentOpen}
          onOpenChange={setStockAdjustmentOpen}
          inventoryItem={stockAdjustmentItem}
        />

        {/* Import Dialog */}
        <ImportInventoryDialog
          open={showImportDialog}
          onOpenChange={setShowImportDialog}
        />

      </div>
    </TooltipProvider>
  );
}
