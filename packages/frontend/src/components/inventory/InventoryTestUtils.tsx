'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useInventoryItems } from '@/hooks/useInventory';
import { InventoryQueryParams } from '@/types/inventory';

export function InventoryTestUtils() {
  const [testQuery, setTestQuery] = useState<InventoryQueryParams>({
    page: 1,
    limit: 5,
    sortBy: 'createdAt',
    sortOrder: 'desc',
  });

  const { data, isLoading, error } = useInventoryItems(testQuery);

  const testPagination = () => {
    console.log('Testing pagination...');
    
    // Test page 1
    setTestQuery({ ...testQuery, page: 1 });
    setTimeout(() => {
      // Test page 2
      setTestQuery({ ...testQuery, page: 2 });
    }, 1000);
  };

  const testFiltering = () => {
    console.log('Testing filtering...');
    
    // Test active filter
    setTestQuery({ ...testQuery, isActive: true, page: 1 });
    setTimeout(() => {
      // Test low stock filter
      setTestQuery({ ...testQuery, lowStock: true, isActive: undefined, page: 1 });
    }, 1000);
    
    setTimeout(() => {
      // Reset filters
      setTestQuery({ page: 1, limit: 5, sortBy: 'createdAt', sortOrder: 'desc' });
    }, 2000);
  };

  const testSorting = () => {
    console.log('Testing sorting...');
    
    // Test sort by quantity
    setTestQuery({ ...testQuery, sortBy: 'quantityOnHand', sortOrder: 'asc', page: 1 });
    setTimeout(() => {
      // Test sort by product name
      setTestQuery({ ...testQuery, sortBy: 'product.name', sortOrder: 'asc', page: 1 });
    }, 1000);
  };

  const testSearch = () => {
    console.log('Testing search...');
    
    // Test search
    setTestQuery({ ...testQuery, search: 'test', page: 1 });
    setTimeout(() => {
      // Clear search
      setTestQuery({ ...testQuery, search: undefined, page: 1 });
    }, 1000);
  };

  return (
    <Card className="w-full max-w-2xl mx-auto mt-8">
      <CardHeader>
        <CardTitle>Inventory API Test Utils</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Current Query Display */}
        <div className="space-y-2">
          <h4 className="font-medium">Current Query:</h4>
          <pre className="bg-muted p-2 rounded text-xs overflow-auto">
            {JSON.stringify(testQuery, null, 2)}
          </pre>
        </div>

        {/* Test Buttons */}
        <div className="flex flex-wrap gap-2">
          <Button onClick={testPagination} variant="outline" size="sm">
            Test Pagination
          </Button>
          <Button onClick={testFiltering} variant="outline" size="sm">
            Test Filtering
          </Button>
          <Button onClick={testSorting} variant="outline" size="sm">
            Test Sorting
          </Button>
          <Button onClick={testSearch} variant="outline" size="sm">
            Test Search
          </Button>
        </div>

        {/* API Response Display */}
        <div className="space-y-2">
          <h4 className="font-medium">API Response:</h4>
          
          {isLoading && (
            <Badge variant="secondary">Loading...</Badge>
          )}
          
          {error && (
            <Badge variant="destructive">Error: {error.message}</Badge>
          )}
          
          {data && (
            <div className="space-y-2">
              <div className="flex gap-2">
                <Badge variant="default">
                  Total: {data.meta.total}
                </Badge>
                <Badge variant="secondary">
                  Page: {data.meta.page}/{data.meta.totalPages}
                </Badge>
                <Badge variant="outline">
                  Items: {data.data.length}
                </Badge>
              </div>
              
              <div className="bg-muted p-2 rounded text-xs max-h-40 overflow-auto">
                <strong>Meta:</strong>
                <pre>{JSON.stringify(data.meta, null, 2)}</pre>
                
                <strong>Sample Items:</strong>
                <pre>
                  {JSON.stringify(
                    data.data.slice(0, 2).map(item => ({
                      id: item.id,
                      product: item.product?.name,
                      quantity: item.quantityOnHand,
                      isActive: item.isActive,
                    })),
                    null,
                    2
                  )}
                </pre>
              </div>
            </div>
          )}
        </div>

        {/* Pagination Test Results */}
        {data?.meta && (
          <div className="space-y-2">
            <h4 className="font-medium">Pagination Status:</h4>
            <div className="flex gap-2">
              <Badge variant={data.meta.hasPreviousPage ? 'default' : 'secondary'}>
                Has Previous: {data.meta.hasPreviousPage ? 'Yes' : 'No'}
              </Badge>
              <Badge variant={data.meta.hasNextPage ? 'default' : 'secondary'}>
                Has Next: {data.meta.hasNextPage ? 'Yes' : 'No'}
              </Badge>
            </div>
          </div>
        )}

        {/* Manual Pagination Controls */}
        <div className="space-y-2">
          <h4 className="font-medium">Manual Pagination Test:</h4>
          <div className="flex gap-2">
            <Button
              onClick={() => setTestQuery({ ...testQuery, page: Math.max(1, testQuery.page! - 1) })}
              disabled={!data?.meta.hasPreviousPage}
              size="sm"
            >
              Previous
            </Button>
            <Button
              onClick={() => setTestQuery({ ...testQuery, page: (testQuery.page || 1) + 1 })}
              disabled={!data?.meta.hasNextPage}
              size="sm"
            >
              Next
            </Button>
            <Button
              onClick={() => setTestQuery({ ...testQuery, page: 1 })}
              size="sm"
              variant="outline"
            >
              First Page
            </Button>
            {data?.meta.totalPages && (
              <Button
                onClick={() => setTestQuery({ ...testQuery, page: data.meta.totalPages })}
                size="sm"
                variant="outline"
              >
                Last Page ({data.meta.totalPages})
              </Button>
            )}
          </div>
        </div>

        {/* Limit Test */}
        <div className="space-y-2">
          <h4 className="font-medium">Page Size Test:</h4>
          <div className="flex gap-2">
            {[5, 10, 20, 50].map(limit => (
              <Button
                key={limit}
                onClick={() => setTestQuery({ ...testQuery, limit, page: 1 })}
                size="sm"
                variant={testQuery.limit === limit ? 'default' : 'outline'}
              >
                {limit}
              </Button>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
