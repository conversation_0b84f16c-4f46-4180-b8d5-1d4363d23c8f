'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, Clock } from 'lucide-react';

interface TestResult {
  name: string;
  status: 'pending' | 'running' | 'passed' | 'failed';
  message?: string;
}

interface PaginationTestSuiteProps {
  onTestPagination: (page: number) => void;
  onTestFilter: (key: string, value: any) => void;
  currentPage: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export function PaginationTestSuite({
  onTestPagination,
  onTestFilter,
  currentPage,
  totalPages,
  hasNextPage,
  hasPreviousPage,
}: PaginationTestSuiteProps) {
  const [tests, setTests] = useState<TestResult[]>([
    { name: 'Page Navigation Forward', status: 'pending' },
    { name: 'Page Navigation Backward', status: 'pending' },
    { name: 'First Page Navigation', status: 'pending' },
    { name: 'Last Page Navigation', status: 'pending' },
    { name: 'Filter Reset Behavior', status: 'pending' },
    { name: 'URL State Persistence', status: 'pending' },
  ]);

  const updateTestStatus = (testName: string, status: TestResult['status'], message?: string) => {
    setTests(prev => prev.map(test => 
      test.name === testName 
        ? { ...test, status, message }
        : test
    ));
  };

  const runAllTests = async () => {
    // Reset all tests
    setTests(prev => prev.map(test => ({ ...test, status: 'pending' })));

    // Test 1: Page Navigation Forward
    updateTestStatus('Page Navigation Forward', 'running');
    const initialPage = currentPage;
    if (hasNextPage) {
      onTestPagination(currentPage + 1);
      setTimeout(() => {
        updateTestStatus('Page Navigation Forward', 'passed', 'Successfully navigated to next page');
      }, 1000);
    } else {
      updateTestStatus('Page Navigation Forward', 'failed', 'No next page available');
    }

    // Test 2: Page Navigation Backward
    setTimeout(() => {
      updateTestStatus('Page Navigation Backward', 'running');
      if (currentPage > 1) {
        onTestPagination(currentPage - 1);
        setTimeout(() => {
          updateTestStatus('Page Navigation Backward', 'passed', 'Successfully navigated to previous page');
        }, 1000);
      } else {
        updateTestStatus('Page Navigation Backward', 'failed', 'Already on first page');
      }
    }, 2000);

    // Test 3: First Page Navigation
    setTimeout(() => {
      updateTestStatus('First Page Navigation', 'running');
      onTestPagination(1);
      setTimeout(() => {
        updateTestStatus('First Page Navigation', 'passed', 'Successfully navigated to first page');
      }, 1000);
    }, 4000);

    // Test 4: Last Page Navigation
    setTimeout(() => {
      updateTestStatus('Last Page Navigation', 'running');
      if (totalPages > 1) {
        onTestPagination(totalPages);
        setTimeout(() => {
          updateTestStatus('Last Page Navigation', 'passed', 'Successfully navigated to last page');
        }, 1000);
      } else {
        updateTestStatus('Last Page Navigation', 'failed', 'Only one page available');
      }
    }, 6000);

    // Test 5: Filter Reset Behavior
    setTimeout(() => {
      updateTestStatus('Filter Reset Behavior', 'running');
      // Apply a filter (should reset to page 1)
      onTestFilter('isActive', true);
      setTimeout(() => {
        updateTestStatus('Filter Reset Behavior', 'passed', 'Filter applied and page reset to 1');
      }, 1000);
    }, 8000);

    // Test 6: URL State Persistence
    setTimeout(() => {
      updateTestStatus('URL State Persistence', 'running');
      const urlParams = new URLSearchParams(window.location.search);
      const hasPageParam = urlParams.has('page');
      updateTestStatus(
        'URL State Persistence', 
        hasPageParam ? 'passed' : 'failed',
        hasPageParam ? 'URL contains page parameter' : 'URL missing page parameter'
      );
    }, 10000);
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'passed':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'running':
        return <Clock className="h-4 w-4 text-blue-600 animate-spin" />;
      default:
        return <div className="h-4 w-4 rounded-full bg-gray-300" />;
    }
  };

  const getStatusBadge = (status: TestResult['status']) => {
    switch (status) {
      case 'passed':
        return <Badge variant="default" className="bg-green-100 text-green-800">Passed</Badge>;
      case 'failed':
        return <Badge variant="destructive">Failed</Badge>;
      case 'running':
        return <Badge variant="secondary" className="bg-blue-100 text-blue-800">Running</Badge>;
      default:
        return <Badge variant="outline">Pending</Badge>;
    }
  };

  const passedTests = tests.filter(t => t.status === 'passed').length;
  const failedTests = tests.filter(t => t.status === 'failed').length;
  const totalTests = tests.length;

  return (
    <Card className="w-full max-w-4xl mx-auto mt-4 border-blue-200 bg-blue-50">
      <CardHeader>
        <CardTitle className="text-blue-800 text-sm flex items-center justify-between">
          🧪 Pagination Test Suite
          <div className="flex gap-2">
            <Badge variant="default" className="bg-green-100 text-green-800">
              {passedTests}/{totalTests} Passed
            </Badge>
            {failedTests > 0 && (
              <Badge variant="destructive">
                {failedTests} Failed
              </Badge>
            )}
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Test Controls */}
        <div className="flex gap-2">
          <Button onClick={runAllTests} size="sm">
            Run All Tests
          </Button>
          <Button 
            onClick={() => setTests(prev => prev.map(test => ({ ...test, status: 'pending' })))}
            variant="outline" 
            size="sm"
          >
            Reset Tests
          </Button>
        </div>

        {/* Current State */}
        <div className="bg-white p-3 rounded border">
          <h4 className="font-medium text-sm mb-2">Current State:</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
            <div>Page: <Badge variant="outline">{currentPage}</Badge></div>
            <div>Total Pages: <Badge variant="outline">{totalPages}</Badge></div>
            <div>Has Next: <Badge variant={hasNextPage ? 'default' : 'secondary'}>{hasNextPage ? 'Yes' : 'No'}</Badge></div>
            <div>Has Previous: <Badge variant={hasPreviousPage ? 'default' : 'secondary'}>{hasPreviousPage ? 'Yes' : 'No'}</Badge></div>
          </div>
        </div>

        {/* Test Results */}
        <div className="space-y-2">
          <h4 className="font-medium text-sm">Test Results:</h4>
          <div className="bg-white rounded border">
            {tests.map((test, index) => (
              <div key={index} className="flex items-center justify-between p-3 border-b last:border-b-0">
                <div className="flex items-center gap-2">
                  {getStatusIcon(test.status)}
                  <span className="text-sm font-medium">{test.name}</span>
                </div>
                <div className="flex items-center gap-2">
                  {test.message && (
                    <span className="text-xs text-muted-foreground">{test.message}</span>
                  )}
                  {getStatusBadge(test.status)}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Manual Test Controls */}
        <div className="space-y-2">
          <h4 className="font-medium text-sm">Manual Tests:</h4>
          <div className="bg-white p-3 rounded border">
            <div className="flex flex-wrap gap-2">
              <Button
                size="sm"
                variant="outline"
                onClick={() => onTestPagination(1)}
                disabled={currentPage === 1}
              >
                Go to Page 1
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => onTestPagination(currentPage + 1)}
                disabled={!hasNextPage}
              >
                Next Page
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => onTestPagination(currentPage - 1)}
                disabled={!hasPreviousPage}
              >
                Previous Page
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => onTestPagination(totalPages)}
                disabled={currentPage === totalPages || totalPages <= 1}
              >
                Last Page
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => onTestFilter('isActive', true)}
              >
                Test Filter (Active)
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => onTestFilter('search', 'test')}
              >
                Test Search
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
