'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { format } from 'date-fns';
import { id as localeId } from 'date-fns/locale';
import {
  ArrowLeft,
  Download,
  Filter,
  RefreshCw,
  Search,
  Calendar,
  Package,
  User,
  TrendingDown,
  Clock,
  MapPin,
  DollarSign,
  Info,
  ChevronDown,
  Settings2,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Separator } from '@/components/ui/separator';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

import { useAllocationHistory } from '@/hooks/useInventory';
import { AllocationHistoryQueryParams, StockMovementWithRelations } from '@/types/inventory';
import { formatCurrency } from '@/lib/utils';
import { navigateBackToInventory } from '@/lib/utils/navigation';
import { ColumnDef, VisibilityState, flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table';

// Define the columns for the data table
const createAllocationColumns = (): ColumnDef<StockMovementWithRelations>[] => {
  return [
    {
      accessorKey: 'product',
      header: 'Produk',
      cell: ({ row }) => {
        const movement = row.original;
        return (
          <div className="space-y-1">
            <div className="font-medium text-sm">
              {movement.inventoryItem?.product?.name || 'N/A'}
            </div>
            <div className="text-xs text-muted-foreground">
              {movement.inventoryItem?.product?.code || 'N/A'}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'batchNumber',
      header: 'Batch',
      cell: ({ row }) => {
        const movement = row.original;
        return (
          <div className="text-sm">
            {movement.inventoryItem?.batchNumber || (
              <span className="text-muted-foreground italic">Tidak ada</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'quantity',
      header: 'Jumlah',
      cell: ({ row }) => {
        const movement = row.original;
        return (
          <div className="flex items-center gap-1">
            <TrendingDown className="h-4 w-4 text-orange-500" />
            <span className="font-medium">{movement.quantity}</span>
            <span className="text-xs text-muted-foreground">
              {movement.inventoryItem?.unit?.abbreviation || ''}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: 'method',
      header: 'Metode',
      cell: ({ row }) => {
        const movement = row.original;
        // Extract allocation method from notes
        const notes = movement.notes || '';
        const methodMatch = notes.match(/Metode\s+(FEFO|FIFO)/i);
        const isFefo = methodMatch ? methodMatch[1].toUpperCase() === 'FEFO' : notes.includes('FEFO');
        
        return (
          <Badge variant={isFefo ? 'default' : 'secondary'}>
            {isFefo ? 'FEFO' : 'FIFO'}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'unitPrice',
      header: 'Biaya',
      cell: ({ row }) => {
        const movement = row.original;
        return (
          <div className="font-medium">
            {formatCurrency(Number(movement.unitPrice || 0))}
          </div>
        );
      },
    },
    {
      accessorKey: 'movementDate',
      header: 'Tanggal',
      cell: ({ row }) => {
        const movement = row.original;
        return (
          <div className="text-sm">
            {format(new Date(movement.movementDate), 'dd MMM yyyy HH:mm', { locale: localeId })}
          </div>
        );
      },
    },
    {
      accessorKey: 'reason',
      header: 'Alasan',
      cell: ({ row }) => {
        const movement = row.original;
        return (
          <div className="text-sm max-w-xs truncate">
            {movement.reason || (
              <span className="text-muted-foreground italic">Tidak ada</span>
            )}
          </div>
        );
      },
    },
  ];
};

interface AllocationHistoryFiltersProps {
  filters: AllocationHistoryQueryParams;
  onFilterChange: (key: keyof AllocationHistoryQueryParams, value: any) => void;
  onClearFilters: () => void;
  loading: boolean;
}

function AllocationHistoryFilters({
  filters,
  onFilterChange,
  onClearFilters,
  loading,
}: AllocationHistoryFiltersProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Filter Riwayat Alokasi</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="space-y-2">
            <Label htmlFor="method">Metode</Label>
            <Select
              value={filters.method || 'all'}
              onValueChange={(value) => onFilterChange('method', value === 'all' ? undefined : value)}
              disabled={loading}
            >
              <SelectTrigger>
                <SelectValue placeholder="Semua metode" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Semua metode</SelectItem>
                <SelectItem value="FIFO">FIFO</SelectItem>
                <SelectItem value="FEFO">FEFO</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="startDate">Tanggal Mulai</Label>
            <Input
              id="startDate"
              type="date"
              value={filters.startDate || ''}
              onChange={(e) => onFilterChange('startDate', e.target.value || undefined)}
              disabled={loading}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="endDate">Tanggal Akhir</Label>
            <Input
              id="endDate"
              type="date"
              value={filters.endDate || ''}
              onChange={(e) => onFilterChange('endDate', e.target.value || undefined)}
              disabled={loading}
            />
          </div>

          <div className="space-y-2">
            <Label>&nbsp;</Label>
            <div className="flex gap-2">
              <Button variant="outline" onClick={onClearFilters} className="flex-1" disabled={loading}>
                Reset
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

interface DataTableProps {
  data: StockMovementWithRelations[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
  query: AllocationHistoryQueryParams;
  onQueryChange: (query: AllocationHistoryQueryParams) => void;
  loading: boolean;
}

function DataTable({
  data,
  meta,
  query,
  onQueryChange,
  loading,
}: DataTableProps) {
  const columns = createAllocationColumns();
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});

  // Handle pagination
  const handlePageChange = (newPage: number) => {
    onQueryChange({
      ...query,
      page: newPage,
    });
  };

  const handleLimitChange = (newLimit: string) => {
    onQueryChange({
      ...query,
      limit: parseInt(newLimit),
      page: 1, // Reset to first page
    });
  };

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    state: {
      columnVisibility,
    },
    manualPagination: true,
  });

  return (
    <div className="w-full min-w-0 max-w-full space-y-4">
      {/* Table Controls */}
      <div className="flex flex-wrap items-center justify-between gap-2">
        <div className="flex flex-wrap items-center gap-2">
          {/* Column Visibility */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="h-9 px-3">
                <Settings2 className="h-4 w-4 mr-1" />
                Kolom
                <ChevronDown className="h-4 w-4 ml-1" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              {table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => {
                  return (
                    <DropdownMenuCheckboxItem
                      key={column.id}
                      className="capitalize"
                      checked={column.getIsVisible()}
                      onCheckedChange={(value) =>
                        column.toggleVisibility(!!value)
                      }
                    >
                      {column.id}
                    </DropdownMenuCheckboxItem>
                  );
                })}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Table Container */}
      <div className="w-full min-w-0 max-w-full overflow-hidden rounded-md border">
        <div className="w-full overflow-x-auto">
          <Table className="min-w-[800px]">
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {loading ? (
                // Loading state - maintain table structure with skeleton rows
                Array.from({ length: query.limit || 10 }).map((_, index) => (
                  <TableRow key={`loading-${index}`}>
                    {columns.map((_, colIndex) => (
                      <TableCell key={colIndex}>
                        <div className="h-4 bg-muted animate-pulse rounded"></div>
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow key={row.id}>
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    className="h-24 text-center"
                  >
                    Tidak ada data alokasi.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Pagination */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between p-4">
        <div className="flex items-center gap-4">
          <div className="text-sm text-muted-foreground">
            Menampilkan {((query.page || 1) - 1) * (query.limit || 20) + 1} - {Math.min((query.page || 1) * (query.limit || 20), meta.total)} dari {meta.total} data
          </div>
          <Select
            value={String(query.limit || 20)}
            onValueChange={handleLimitChange}
            disabled={loading}
          >
            <SelectTrigger className="w-20">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="10">10</SelectItem>
              <SelectItem value="20">20</SelectItem>
              <SelectItem value="30">30</SelectItem>
              <SelectItem value="50">50</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(1)}
            disabled={loading || !meta.hasPreviousPage}
          >
            <span className="sr-only">Halaman pertama</span>
            <span aria-hidden="true">«</span>
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange((query.page || 1) - 1)}
            disabled={loading || !meta.hasPreviousPage}
          >
            <span className="sr-only">Halaman sebelumnya</span>
            <span aria-hidden="true">‹</span>
          </Button>
          <div className="flex items-center gap-1">
            <span className="text-sm">Halaman</span>
            <span className="text-sm font-medium">{query.page || 1}</span>
            <span className="text-sm">dari</span>
            <span className="text-sm font-medium">{meta.totalPages}</span>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange((query.page || 1) + 1)}
            disabled={loading || !meta.hasNextPage}
          >
            <span className="sr-only">Halaman berikutnya</span>
            <span aria-hidden="true">›</span>
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(meta.totalPages)}
            disabled={loading || !meta.hasNextPage}
          >
            <span className="sr-only">Halaman terakhir</span>
            <span aria-hidden="true">»</span>
          </Button>
        </div>
      </div>
    </div>
  );
}

export function AllocationHistoryClient() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [filters, setFilters] = useState<AllocationHistoryQueryParams>({
    page: 1,
    limit: 20,
    productId: searchParams.get('productId') || undefined,
  });

  const [showFilters, setShowFilters] = useState(false);

  const {
    data: allocationHistory,
    isLoading,
    error,
    refetch,
  } = useAllocationHistory(filters);

  // Update filters when URL params change
  useEffect(() => {
    const productId = searchParams.get('productId');
    if (productId && productId !== filters.productId) {
      setFilters(prev => ({ ...prev, productId, page: 1 }));
    }
  }, [searchParams, filters.productId]);

  const handleFilterChange = (key: keyof AllocationHistoryQueryParams, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: 1, // Reset to first page when filters change
    }));
  };

  const clearFilters = () => {
    const clearedFilters: AllocationHistoryQueryParams = {
      page: 1,
      limit: 20,
      productId: filters.productId, // Keep the product ID filter
    };
    setFilters(clearedFilters);
  };

  if (error) {
    return (
      <div className="w-full min-w-0 max-w-full space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={() => navigateBackToInventory(router)}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Kembali
          </Button>
          <h1 className="text-2xl font-bold">Riwayat Alokasi Stok</h1>
        </div>

        <Card>
          <CardContent className="flex flex-col items-center justify-center py-8">
            <p className="text-muted-foreground mb-4">Gagal memuat riwayat alokasi</p>
            <Button variant="outline" onClick={() => refetch()}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Coba Lagi
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="w-full min-w-0 max-w-full space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={() => navigateBackToInventory(router)}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Kembali
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Riwayat Alokasi Stok</h1>
            <p className="text-muted-foreground">
              Riwayat lengkap alokasi stok dengan metode FIFO/FEFO
            </p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
          >
            <Filter className="h-4 w-4 mr-2" />
            Filter
          </Button>
          <Button variant="outline" onClick={() => refetch()}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <AllocationHistoryFilters
          filters={filters}
          onFilterChange={handleFilterChange}
          onClearFilters={clearFilters}
          loading={isLoading}
        />
      )}

      {/* Results */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Riwayat Alokasi</CardTitle>
            {allocationHistory?.meta && (
              <div className="text-sm text-muted-foreground">
                Menampilkan {((allocationHistory.meta.page - 1) * allocationHistory.meta.limit) + 1} - {Math.min(allocationHistory.meta.page * allocationHistory.meta.limit, allocationHistory.meta.total)} dari {allocationHistory.meta.total} data
              </div>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {isLoading && !allocationHistory ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Memuat riwayat alokasi...</p>
            </div>
          ) : !allocationHistory?.data?.length ? (
            <div className="text-center py-8">
              <TrendingDown className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <p className="text-muted-foreground">Belum ada riwayat alokasi</p>
            </div>
          ) : (
            <DataTable
              data={allocationHistory.data}
              meta={allocationHistory.meta}
              query={filters}
              onQueryChange={setFilters}
              loading={isLoading}
            />
          )}
        </CardContent>
      </Card>
    </div>
  );
}
