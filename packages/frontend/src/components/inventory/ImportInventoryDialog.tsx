'use client';

import { useState, useRef } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import {
  Upload,
  Download,
  FileSpreadsheet,
  Database,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Loader2,
  FileText,
} from 'lucide-react';
import { useImportInventoryData, useDownloadImportTemplate } from '@/hooks/useInventoryReports';
import { cn } from '@/lib/utils';

interface ImportInventoryDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function ImportInventoryDialog({
  open,
  onOpenChange,
}: ImportInventoryDialogProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [dragActive, setDragActive] = useState(false);
  const [importStep, setImportStep] = useState<'select' | 'validate' | 'import' | 'complete'>('select');
  const [validationResult, setValidationResult] = useState<any>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const importMutation = useImportInventoryData();
  const downloadTemplateMutation = useDownloadImportTemplate();

  const handleFileSelect = (file: File) => {
    // Validate file type
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
      'application/vnd.ms-excel', // .xls
      'text/csv', // .csv
    ];

    if (!allowedTypes.includes(file.type)) {
      alert('Format file tidak didukung. Gunakan file Excel (.xlsx, .xls) atau CSV (.csv)');
      return;
    }

    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      alert('Ukuran file terlalu besar. Maksimal 10MB');
      return;
    }

    setSelectedFile(file);
    setImportStep('select');
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelect(e.dataTransfer.files[0]);
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFileSelect(e.target.files[0]);
    }
  };

  const handleValidate = async () => {
    if (!selectedFile) return;

    setImportStep('validate');
    try {
      const result = await importMutation.mutateAsync({
        file: selectedFile,
        validateOnly: true,
      });
      setValidationResult(result);
      
      if (result.success) {
        setImportStep('import');
      } else {
        setImportStep('select');
      }
    } catch (error) {
      setImportStep('select');
    }
  };

  const handleImport = async () => {
    if (!selectedFile) return;

    setImportStep('import');
    try {
      const result = await importMutation.mutateAsync({
        file: selectedFile,
        validateOnly: false,
      });
      
      if (result.success) {
        setImportStep('complete');
      } else {
        setImportStep('select');
      }
    } catch (error) {
      setImportStep('select');
    }
  };

  const handleDownloadTemplate = async (format: 'xlsx' | 'csv') => {
    try {
      await downloadTemplateMutation.mutateAsync(format);
    } catch (error) {
      console.error('Error downloading template:', error);
    }
  };

  const resetDialog = () => {
    setSelectedFile(null);
    setImportStep('select');
    setValidationResult(null);
    setDragActive(false);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleClose = () => {
    resetDialog();
    onOpenChange(false);
  };

  const getFileIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'xlsx':
      case 'xls':
        return <FileSpreadsheet className="h-8 w-8 text-green-600" />;
      case 'csv':
        return <Database className="h-8 w-8 text-blue-600" />;
      default:
        return <FileText className="h-8 w-8 text-gray-600" />;
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Import Data Inventori
          </DialogTitle>
          <DialogDescription>
            Import data inventori dari file Excel atau CSV. Pastikan format file sesuai dengan template yang disediakan.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Download Template Section */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm flex items-center gap-2">
                <Download className="h-4 w-4" />
                Download Template
              </CardTitle>
              <CardDescription>
                Download template untuk memastikan format data yang benar
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleDownloadTemplate('xlsx')}
                  disabled={downloadTemplateMutation.isPending}
                >
                  {downloadTemplateMutation.isPending ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <FileSpreadsheet className="mr-2 h-4 w-4" />
                  )}
                  Template Excel
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleDownloadTemplate('csv')}
                  disabled={downloadTemplateMutation.isPending}
                >
                  {downloadTemplateMutation.isPending ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <Database className="mr-2 h-4 w-4" />
                  )}
                  Template CSV
                </Button>
              </div>
            </CardContent>
          </Card>

          <Separator />

          {/* File Upload Section */}
          {importStep === 'select' && (
            <div className="space-y-4">
              <h4 className="text-sm font-medium">Pilih File untuk Import</h4>
              
              {/* Drag and Drop Area */}
              <div
                className={cn(
                  "border-2 border-dashed rounded-lg p-8 text-center transition-colors",
                  dragActive
                    ? "border-primary bg-primary/5"
                    : "border-muted-foreground/25 hover:border-muted-foreground/50"
                )}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
              >
                <Upload className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                <div className="space-y-2">
                  <p className="text-sm font-medium">
                    Drag & drop file di sini, atau klik untuk memilih
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Mendukung file Excel (.xlsx, .xls) dan CSV (.csv) hingga 10MB
                  </p>
                </div>
                <Button
                  variant="outline"
                  className="mt-4"
                  onClick={() => fileInputRef.current?.click()}
                >
                  Pilih File
                </Button>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".xlsx,.xls,.csv"
                  onChange={handleFileInputChange}
                  className="hidden"
                />
              </div>

              {/* Selected File Display */}
              {selectedFile && (
                <Card>
                  <CardContent className="pt-6">
                    <div className="flex items-center gap-4">
                      {getFileIcon(selectedFile.name)}
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">
                          {selectedFile.name}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {formatFileSize(selectedFile.size)}
                        </p>
                      </div>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setSelectedFile(null)}
                        >
                          Hapus
                        </Button>
                        <Button
                          size="sm"
                          onClick={handleValidate}
                          disabled={importMutation.isPending}
                        >
                          {importMutation.isPending ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Validasi...
                            </>
                          ) : (
                            'Validasi File'
                          )}
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          )}

          {/* Validation Step */}
          {importStep === 'validate' && (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span className="text-sm font-medium">Memvalidasi file...</span>
              </div>
              <Progress value={50} className="w-full" />
            </div>
          )}

          {/* Import Step */}
          {importStep === 'import' && (
            <div className="space-y-4">
              {validationResult && (
                <Alert>
                  <CheckCircle className="h-4 w-4" />
                  <AlertDescription>
                    File valid dan siap untuk diimport. 
                    {validationResult.warnings && validationResult.warnings.length > 0 && (
                      <span className="block mt-2 text-sm">
                        Ditemukan {validationResult.warnings.length} peringatan yang akan diabaikan.
                      </span>
                    )}
                  </AlertDescription>
                </Alert>
              )}

              <div className="flex justify-end gap-4">
                <Button
                  variant="outline"
                  onClick={() => setImportStep('select')}
                  disabled={importMutation.isPending}
                >
                  Kembali
                </Button>
                <Button
                  onClick={handleImport}
                  disabled={importMutation.isPending}
                >
                  {importMutation.isPending ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Mengimpor...
                    </>
                  ) : (
                    'Import Data'
                  )}
                </Button>
              </div>
            </div>
          )}

          {/* Complete Step */}
          {importStep === 'complete' && (
            <div className="space-y-4 text-center">
              <CheckCircle className="mx-auto h-12 w-12 text-green-600" />
              <div>
                <h4 className="text-lg font-medium">Import Berhasil!</h4>
                <p className="text-sm text-muted-foreground mt-2">
                  Data inventori berhasil diimport ke sistem.
                </p>
              </div>
              <Button onClick={handleClose}>
                Tutup
              </Button>
            </div>
          )}

          {/* Error Display */}
          {validationResult && !validationResult.success && (
            <Alert variant="destructive">
              <XCircle className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-2">
                  <p>{validationResult.message}</p>
                  {validationResult.errors && validationResult.errors.length > 0 && (
                    <div>
                      <p className="font-medium">Error yang ditemukan:</p>
                      <ul className="list-disc list-inside text-sm">
                        {validationResult.errors.map((error: string, index: number) => (
                          <li key={index}>{error}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </AlertDescription>
            </Alert>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
