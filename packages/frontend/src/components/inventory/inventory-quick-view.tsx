'use client';


import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { InventoryItemWithRelations } from '@/types/inventory';
import {
  formatCurrency,
  formatDate,
  formatDateTime,
  getStockLevelStatus,
  getStockLevelColor,
  getExpiryStatus,
  getExpiryStatusColor,
  STOCK_THRESHOLDS
} from '@/lib/constants/inventory';
import { getProductCategoryLabel } from '@/lib/constants/product';
import {
  Package,
  Calendar,
  MapPin,
  Building2,
  DollarSign,
  Hash,
  Clock,
  User,
  AlertTriangle,
  Eye,
  Edit,
} from 'lucide-react';

interface InventoryQuickViewProps {
  item: InventoryItemWithRelations | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onEdit?: (item: InventoryItemWithRelations) => void;
  onViewFull?: (item: InventoryItemWithRelations) => void;
}

export function InventoryQuickView({
  item,
  open,
  onOpenChange,
  onEdit,
  onViewFull,
}: InventoryQuickViewProps) {
  if (!item) return null;

  const stockStatus = getStockLevelStatus(item.quantityOnHand, STOCK_THRESHOLDS.LOW_STOCK);
  const stockColorClass = getStockLevelColor(stockStatus);

  const expiryStatus = item.expiryDate ? getExpiryStatus(item.expiryDate) : 'no-expiry';
  const expiryColorClass = getExpiryStatusColor(expiryStatus);

  const isLowStock = item.quantityOnHand <= STOCK_THRESHOLDS.LOW_STOCK;
  const isCriticalStock = item.quantityOnHand <= STOCK_THRESHOLDS.CRITICAL_STOCK;
  const isExpired = expiryStatus === 'expired';
  const isExpiringSoon = expiryStatus === 'expiring-soon';

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Detail Inventori
          </DialogTitle>
          <DialogDescription>
            Informasi lengkap item inventori
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Alerts */}
          {(isLowStock || isExpired || isExpiringSoon) && (
            <Card className="border-orange-200 bg-orange-50 gap-2">
              <CardHeader>
                <CardTitle className="text-sm flex items-center gap-2 text-orange-800">
                  <AlertTriangle className="h-4 w-4" />
                  Peringatan
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {isCriticalStock && (
                  <div className="text-sm text-red-700 bg-red-100 p-2 rounded">
                    ⚠️ Stok kritis: Hanya tersisa {item.quantityOnHand} unit
                  </div>
                )}
                {isLowStock && !isCriticalStock && (
                  <div className="text-sm text-orange-700 bg-orange-100 p-2 rounded">
                    ⚠️ Stok rendah: Perlu restok segera
                  </div>
                )}
                {isExpired && (
                  <div className="text-sm text-red-700 bg-red-100 p-2 rounded">
                    ⚠️ Produk sudah kedaluwarsa
                  </div>
                )}
                {isExpiringSoon && !isExpired && (
                  <div className="text-sm text-orange-700 bg-orange-100 p-2 rounded">
                    ⚠️ Produk akan kedaluwarsa dalam 30 hari
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Product Information */}
          <Card className="gap-2">
            <CardHeader>
              <CardTitle className="text-lg">Informasi Produk</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Package className="h-4 w-4" />
                    Nama Produk
                  </div>
                  <div className="font-medium">{item.product?.name || 'N/A'}</div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Hash className="h-4 w-4" />
                    Kode Produk
                  </div>
                  <div className="font-medium">{item.product?.code || 'N/A'}</div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Package className="h-4 w-4" />
                    Kategori
                  </div>
                  <div>
                    {item.product?.category ? (
                      <Badge variant="outline">{getProductCategoryLabel(item.product.category)}</Badge>
                    ) : (
                      <span className="text-muted-foreground italic">Tidak ada</span>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Hash className="h-4 w-4" />
                    Nomor Batch
                  </div>
                  <div className="font-medium">
                    {item.batchNumber || (
                      <span className="text-muted-foreground italic">Tidak ada</span>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Stock Information */}
          <Card className="gap-2">
            <CardHeader>
              <CardTitle className="text-lg">Informasi Stok</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="text-sm text-muted-foreground">Jumlah Tersedia</div>
                  <div className="flex items-center gap-2">
                    <span className="text-2xl font-bold">{item.quantityOnHand.toLocaleString('id-ID')}</span>
                    <span className="text-muted-foreground">{item.unit?.name || 'unit'}</span>
                  </div>
                  <Badge variant="outline" className={`${stockColorClass} w-fit`}>
                    {stockStatus === 'out-of-stock' && 'Habis'}
                    {stockStatus === 'low' && 'Stok Rendah'}
                    {stockStatus === 'normal' && 'Normal'}
                    {stockStatus === 'overstock' && 'Berlebih'}
                  </Badge>
                </div>

                <div className="space-y-2">
                  <div className="text-sm text-muted-foreground">Satuan</div>
                  <div className="font-medium">
                    {item.unit?.name || 'N/A'}
                    {item.unit?.abbreviation && (
                      <span className="text-muted-foreground ml-1">
                        ({item.unit.abbreviation})
                      </span>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <MapPin className="h-4 w-4" />
                    Lokasi
                  </div>
                  <div className="font-medium">
                    {item.location || (
                      <span className="text-muted-foreground italic">Tidak diset</span>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Building2 className="h-4 w-4" />
                    Supplier
                  </div>
                  <div className="font-medium">
                    {item.supplier?.name || (
                      <span className="text-muted-foreground italic">Tidak ada</span>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Price Information */}
          <Card className="gap-2">
            <CardHeader>
              <CardTitle className="text-lg">Informasi Harga</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <DollarSign className="h-4 w-4" />
                    Harga Beli
                  </div>
                  <div className="text-xl font-bold text-green-600">
                    {formatCurrency(item.costPrice)}
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <DollarSign className="h-4 w-4" />
                    Harga Jual
                  </div>
                  <div className="text-xl font-bold text-blue-600">
                    {item.sellingPrice ? formatCurrency(item.sellingPrice) : (
                      <span className="text-muted-foreground italic text-base">Belum diset</span>
                    )}
                  </div>
                </div>

                {item.sellingPrice && (
                  <div className="space-y-2 md:col-span-2">
                    <div className="text-sm text-muted-foreground">Margin</div>
                    <div className="text-lg font-medium">
                      {formatCurrency(item.sellingPrice - item.costPrice)}
                      <span className="text-sm text-muted-foreground ml-2">
                        ({(((item.sellingPrice - item.costPrice) / item.costPrice) * 100).toFixed(1)}%)
                      </span>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Date Information */}
          <Card className="gap-2">
            <CardHeader>
              <CardTitle className="text-lg">Informasi Tanggal</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Calendar className="h-4 w-4" />
                    Tanggal Diterima
                  </div>
                  <div className="font-medium">{formatDate(item.receivedDate)}</div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Clock className="h-4 w-4" />
                    Tanggal Kedaluwarsa
                  </div>
                  <div className="flex items-center gap-2">
                    {item.expiryDate ? (
                      <>
                        <span className="font-medium">{formatDate(item.expiryDate)}</span>
                        <Badge variant="outline" className={`${expiryColorClass} text-xs`}>
                          {expiryStatus === 'expired' && 'Kedaluwarsa'}
                          {expiryStatus === 'expiring-soon' && 'Akan Kedaluwarsa'}
                          {expiryStatus === 'good' && 'Masih Valid'}
                        </Badge>
                      </>
                    ) : (
                      <span className="text-muted-foreground italic">Tidak ada</span>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <User className="h-4 w-4" />
                    Dibuat
                  </div>
                  <div className="text-sm">{formatDateTime(item.createdAt)}</div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <User className="h-4 w-4" />
                    Diperbarui
                  </div>
                  <div className="text-sm">{formatDateTime(item.updatedAt)}</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Status */}
          <Card className="gap-2">
            <CardHeader>
              <CardTitle className="text-lg">Status</CardTitle>
            </CardHeader>
            <CardContent>
              <Badge variant={item.isActive ? 'default' : 'secondary'} className="text-sm">
                {item.isActive ? 'Aktif' : 'Tidak Aktif'}
              </Badge>
            </CardContent>
          </Card>

          {/* Notes */}
          {item.notes && (
            <Card className="gap-2">
              <CardHeader>
                <CardTitle className="text-lg">Catatan</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground whitespace-pre-wrap">
                  {item.notes}
                </p>
              </CardContent>
            </Card>
          )}
        </div>

        <Separator />

        {/* Action Buttons */}
        <div className="flex justify-end gap-2">
          {onViewFull && (
            <Button variant="outline" onClick={() => onViewFull(item)}>
              <Eye className="h-4 w-4 mr-2" />
              Lihat Detail Lengkap
            </Button>
          )}
          {onEdit && (
            <Button onClick={() => onEdit(item)}>
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Button>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
