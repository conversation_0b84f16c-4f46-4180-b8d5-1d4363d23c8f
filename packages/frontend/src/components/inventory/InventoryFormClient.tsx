'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { ArrowLeft, Save, CalendarIcon } from 'lucide-react';
import { format } from 'date-fns';
import { id } from 'date-fns/locale';
import { cn } from '@/lib/utils';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { ProductSelector } from '@/components/ui/product-selector';
import { SupplierSelector } from '@/components/ui/supplier-selector';
import { LiveCurrencyInput } from '@/components/ui/currency-input';

import { InventoryItem, CreateInventoryItemData, UpdateInventoryItemData } from '@/types/inventory';
import { useCreateInventoryItem, useUpdateInventoryItem } from '@/hooks/useInventory';
import { useProduct } from '@/hooks/useProducts';
import { navigateBackToInventory } from '@/lib/utils/navigation';

// Form validation schema
const inventoryFormSchema = z.object({
  productId: z.string().min(1, 'Produk wajib dipilih'),
  unitId: z.string().min(1, 'Unit wajib dipilih'),
  batchNumber: z.string().optional(),
  expiryDate: z.date().optional(),
  quantityOnHand: z.number().min(0, 'Jumlah tidak boleh negatif'),
  costPrice: z.number().min(0, 'Harga beli tidak boleh negatif'),
  sellingPrice: z.number().min(0, 'Harga jual tidak boleh negatif').optional(),
  location: z.string().optional(),
  receivedDate: z.date().optional(),
  supplierId: z.string().optional(),
  notes: z.string().optional(),
});

type InventoryFormData = z.infer<typeof inventoryFormSchema>;

interface InventoryFormClientProps {
  inventoryItem?: InventoryItem;
  mode?: 'create' | 'edit';
}

export function InventoryFormClient({ inventoryItem, mode = 'create' }: InventoryFormClientProps) {
  const router = useRouter();
  const [selectedProductId, setSelectedProductId] = useState<string>(inventoryItem?.productId || '');

  // API hooks
  const createInventoryItemMutation = useCreateInventoryItem();
  const updateInventoryItemMutation = useUpdateInventoryItem();

  // Data fetching hooks
  const { data: selectedProduct } = useProduct(selectedProductId);

  const isLoading = createInventoryItemMutation.isPending || updateInventoryItemMutation.isPending;

  // Form setup
  const form = useForm<InventoryFormData>({
    resolver: zodResolver(inventoryFormSchema),
    defaultValues: {
      productId: inventoryItem?.productId || '',
      unitId: inventoryItem?.unitId || '',
      batchNumber: inventoryItem?.batchNumber || '',
      expiryDate: inventoryItem?.expiryDate ? new Date(inventoryItem.expiryDate) : undefined,
      quantityOnHand: inventoryItem?.quantityOnHand || 0,
      costPrice: inventoryItem?.costPrice || 0,
      sellingPrice: inventoryItem?.sellingPrice || undefined,
      location: inventoryItem?.location || '',
      receivedDate: inventoryItem?.receivedDate ? new Date(inventoryItem.receivedDate) : new Date(),
      supplierId: inventoryItem?.supplierId || 'none',
      notes: inventoryItem?.notes || '',
    },
  });

  // Update form when inventory item changes (for edit mode)
  useEffect(() => {
    if (inventoryItem && mode === 'edit') {
      const formData = {
        productId: inventoryItem.productId || '',
        unitId: inventoryItem.unitId || '',
        batchNumber: inventoryItem.batchNumber || '',
        expiryDate: inventoryItem.expiryDate ? new Date(inventoryItem.expiryDate) : undefined,
        quantityOnHand: inventoryItem.quantityOnHand || 0,
        costPrice: inventoryItem.costPrice || 0,
        sellingPrice: inventoryItem.sellingPrice || undefined,
        location: inventoryItem.location || '',
        receivedDate: inventoryItem.receivedDate ? new Date(inventoryItem.receivedDate) : new Date(),
        supplierId: inventoryItem.supplierId || 'none',
        notes: inventoryItem.notes || '',
      };



      form.reset(formData);

      // Set the selected product ID for unit loading
      if (inventoryItem.productId) {
        setSelectedProductId(inventoryItem.productId);
      }
    }
  }, [inventoryItem, mode, form]);

  // Watch for product changes to update available units
  const watchedProductId = form.watch('productId');

  useEffect(() => {
    if (watchedProductId && watchedProductId !== selectedProductId) {
      setSelectedProductId(watchedProductId);
      // Only reset unit selection when product changes in create mode or when manually changing product in edit mode
      if (mode === 'create' || (mode === 'edit' && watchedProductId !== inventoryItem?.productId)) {
        form.setValue('unitId', '');
      }
    }
  }, [watchedProductId, selectedProductId, form, mode, inventoryItem?.productId]);

  const onSubmit = async (data: InventoryFormData) => {
    try {
      const formData = {
        ...data,
        expiryDate: data.expiryDate ? data.expiryDate.toISOString() : undefined,
        receivedDate: data.receivedDate ? data.receivedDate.toISOString() : undefined,
        sellingPrice: data.sellingPrice || undefined,
        supplierId: data.supplierId === 'none' || !data.supplierId ? undefined : data.supplierId,
        isActive: true,
      };

      if (mode === 'edit' && inventoryItem) {
        const updateData: UpdateInventoryItemData = formData;
        await updateInventoryItemMutation.mutateAsync({ id: inventoryItem.id, data: updateData });
      } else {
        const createData: CreateInventoryItemData = formData;
        await createInventoryItemMutation.mutateAsync(createData);
        router.push('/dashboard/inventory');
      }
    } catch (error) {
      // Error handling is done in the mutation hook
      console.error(`Error ${mode === 'edit' ? 'updating' : 'creating'} inventory item:`, error);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* Header */}
        <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
          <Button
            type="button"
            variant="outline"
            onClick={() => navigateBackToInventory(router)}
            className="flex items-center gap-2 w-full sm:w-auto"
          >
            <ArrowLeft className="h-4 w-4" />
            Kembali
          </Button>

          <Button type="submit" disabled={isLoading} className="w-full sm:w-auto">
            <Save className="mr-2 h-4 w-4" />
            {isLoading
              ? (mode === 'edit' ? 'Memperbarui...' : 'Menyimpan...')
              : (mode === 'edit' ? 'Perbarui' : 'Simpan')
            }
          </Button>
        </div>

        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Informasi Dasar</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Product Selection */}
              <FormField
                control={form.control}
                name="productId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Produk *</FormLabel>
                    <FormControl>
                      <ProductSelector
                        value={field.value}
                        onValueChange={field.onChange}
                        placeholder="Pilih produk"
                        searchPlaceholder="Cari produk berdasarkan nama atau kode..."
                        emptyMessage="Tidak ada produk ditemukan. Coba kata kunci lain."
                        disabled={false}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Unit Selection */}
              <FormField
                control={form.control}
                name="unitId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Unit *</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      value={field.value || undefined}
                      disabled={!selectedProduct}
                    >
                      <FormControl>
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Pilih unit" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {!selectedProduct ? (
                          <SelectItem value="no-product" disabled>
                            Pilih produk terlebih dahulu
                          </SelectItem>
                        ) : selectedProduct.unitHierarchies && selectedProduct.unitHierarchies.length > 0 ? (
                          selectedProduct.unitHierarchies.map((hierarchy) => (
                            <SelectItem key={hierarchy.unit.id} value={hierarchy.unit.id}>
                              {hierarchy.unit.name} ({hierarchy.unit.abbreviation})
                            </SelectItem>
                          ))
                        ) : (
                          <SelectItem value="no-units" disabled>
                            Tidak ada unit tersedia untuk produk ini
                          </SelectItem>
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        {/* Batch and Expiry Information */}
        <Card>
          <CardHeader>
            <CardTitle>Informasi Batch & Kedaluwarsa</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Batch Number */}
              <FormField
                control={form.control}
                name="batchNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nomor Batch</FormLabel>
                    <FormControl>
                      <Input placeholder="Masukkan nomor batch" {...field} />
                    </FormControl>
                    <FormDescription>
                      Nomor batch dari supplier (opsional)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Expiry Date */}
              <FormField
                control={form.control}
                name="expiryDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tanggal Kedaluwarsa</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              'w-full pl-3 text-left font-normal',
                              !field.value && 'text-muted-foreground'
                            )}
                          >
                            {field.value ? (
                              format(field.value, 'dd MMMM yyyy', { locale: id })
                            ) : (
                              <span>Pilih tanggal kedaluwarsa</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) => date < new Date()}
                        />
                      </PopoverContent>
                    </Popover>
                    <FormDescription>
                      Tanggal kedaluwarsa produk (opsional)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        {/* Quantity and Pricing */}
        <Card>
          <CardHeader>
            <CardTitle>Jumlah & Harga</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Quantity on Hand */}
              <FormField
                control={form.control}
                name="quantityOnHand"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Jumlah Stok *</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="0"
                        step="1"
                        placeholder="0"
                        {...field}
                        onChange={(e) => {
                          const value = e.target.value;
                          if (value === '') {
                            field.onChange(0); // Keep as 0 for required field
                          } else {
                            field.onChange(parseInt(value) || 0);
                          }
                        }}
                      />
                    </FormControl>
                    <FormDescription>
                      Jumlah stok yang tersedia
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Cost Price */}
              <FormField
                control={form.control}
                name="costPrice"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Harga Beli *</FormLabel>
                    <FormControl>
                      <LiveCurrencyInput
                        placeholder="Masukkan harga beli"
                        value={field.value}
                        onChange={(value) => field.onChange(value || 0)}
                      />
                    </FormControl>
                    <FormDescription>
                      Harga beli per unit
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Selling Price */}
              <FormField
                control={form.control}
                name="sellingPrice"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Harga Jual</FormLabel>
                    <FormControl>
                      <LiveCurrencyInput
                        placeholder="Masukkan harga jual (opsional)"
                        value={field.value}
                        onChange={field.onChange}
                      />
                    </FormControl>
                    <FormDescription>
                      Harga jual per unit (opsional)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        {/* Location and Supplier Information */}
        <Card>
          <CardHeader>
            <CardTitle>Lokasi & Supplier</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Location */}
              <FormField
                control={form.control}
                name="location"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Lokasi Penyimpanan</FormLabel>
                    <FormControl>
                      <Input placeholder="Contoh: Rak A-1, Lemari Es" {...field} />
                    </FormControl>
                    <FormDescription>
                      Lokasi penyimpanan di apotek (opsional)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Supplier Selection */}
              <FormField
                control={form.control}
                name="supplierId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Supplier</FormLabel>
                    <FormControl>
                      <SupplierSelector
                        value={field.value}
                        onValueChange={field.onChange}
                        placeholder="Pilih supplier"
                        searchPlaceholder="Cari supplier berdasarkan nama atau kode..."
                        emptyMessage="Tidak ada supplier ditemukan. Coba kata kunci lain."
                        allowNone={true}
                        noneLabel="Tidak ada supplier"
                        disabled={false}
                      />
                    </FormControl>
                    <FormDescription>
                      Supplier yang menyediakan produk ini (opsional)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Received Date */}
            <FormField
              control={form.control}
              name="receivedDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tanggal Diterima</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          className={cn(
                            'w-full pl-3 text-left font-normal',
                            !field.value && 'text-muted-foreground'
                          )}
                        >
                          {field.value ? (
                            format(field.value, 'dd MMMM yyyy', { locale: id })
                          ) : (
                            <span>Pilih tanggal diterima</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={field.onChange}
                        disabled={(date) => date > new Date()}
                      />
                    </PopoverContent>
                  </Popover>
                  <FormDescription>
                    Tanggal produk diterima di apotek (default: hari ini)
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Notes */}
        <Card>
          <CardHeader>
            <CardTitle>Catatan Tambahan</CardTitle>
          </CardHeader>
          <CardContent>
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Catatan</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Catatan tambahan tentang item inventori ini..."
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Catatan tambahan atau informasi khusus (opsional)
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Submit Button (Mobile) */}
        <div className="flex justify-end sm:hidden">
          <Button type="submit" disabled={isLoading} className="w-full">
            <Save className="mr-2 h-4 w-4" />
            {isLoading
              ? (mode === 'edit' ? 'Memperbarui...' : 'Menyimpan...')
              : (mode === 'edit' ? 'Perbarui' : 'Simpan')
            }
          </Button>
        </div>
      </form>
    </Form>
  );
}
