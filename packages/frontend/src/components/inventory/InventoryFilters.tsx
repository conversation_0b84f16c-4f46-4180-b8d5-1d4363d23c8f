'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Badge } from '@/components/ui/badge';
import { InventoryQueryParams } from '@/types/inventory';
import {
  INVENTORY_STATUS_OPTIONS,
  STOCK_STATUS_OPTIONS,
} from '@/lib/constants/inventory';
import {
  PRODUCT_TYPE_OPTIONS,
  PRODUCT_CATEGORY_FILTER_OPTIONS,
} from '@/lib/constants/product';
import { Filter, X } from 'lucide-react';

interface InventoryFiltersProps {
  filters: InventoryQueryParams;
  onFilterChange: (key: keyof InventoryQueryParams, value: any) => void;
  onBatchFilterChange?: (updates: Partial<InventoryQueryParams>) => void;
  onClearFilters: () => void;
  activeFiltersCount: number;
  loading?: boolean;
}

export function InventoryFilters({
  filters,
  onFilterChange,
  onBatchFilterChange,
  onClearFilters,
  activeFiltersCount,
  loading = false,
}: InventoryFiltersProps) {
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);

  return (
    <div className="flex flex-wrap items-center gap-2">
      {/* Status Filter */}
      <Select
        value={filters.isActive?.toString() || 'all'}
        onValueChange={(value) =>
          onFilterChange('isActive', value === 'all' ? undefined : value === 'true')
        }
        disabled={loading}
      >
        <SelectTrigger className="w-[140px] h-9">
          <SelectValue placeholder="Status" />
          {loading && (
            <div className="absolute right-8 top-1/2 transform -translate-y-1/2">
              <div className="w-3 h-3 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
            </div>
          )}
        </SelectTrigger>
        <SelectContent>
          {INVENTORY_STATUS_OPTIONS.map((option) => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {/* Stock Status Filter */}
      <Select
        value={
          filters.lowStock ? 'low_stock' :
            filters.expired ? 'expired' :
              filters.expiringSoon ? 'expiring_soon' :
                filters.quantityMin === 1 ? 'available' :
                  filters.quantityMax === 0 ? 'out_of_stock' : 'all'
        }
        onValueChange={(value) => {
          // Use batch update if available, otherwise fall back to individual updates
          if (onBatchFilterChange) {
            // Batch update - clear all stock status filters and set new one
            const updates: Partial<InventoryQueryParams> = {
              lowStock: undefined,
              expired: undefined,
              expiringSoon: undefined,
              quantityMin: undefined,
              quantityMax: undefined,
            };

            // Set the selected filter based on backend capabilities
            if (value === 'low_stock') {
              updates.lowStock = true;
            } else if (value === 'expired') {
              updates.expired = true;
            } else if (value === 'expiring_soon') {
              updates.expiringSoon = true;
            } else if (value === 'available') {
              updates.quantityMin = 1;
            } else if (value === 'out_of_stock') {
              updates.quantityMax = 0;
            }

            onBatchFilterChange(updates);
          } else {
            // Fallback to individual updates
            onFilterChange('lowStock', undefined);
            onFilterChange('expired', undefined);
            onFilterChange('expiringSoon', undefined);
            onFilterChange('quantityMin', undefined);
            onFilterChange('quantityMax', undefined);

            if (value === 'low_stock') {
              onFilterChange('lowStock', true);
            } else if (value === 'expired') {
              onFilterChange('expired', true);
            } else if (value === 'expiring_soon') {
              onFilterChange('expiringSoon', true);
            } else if (value === 'available') {
              onFilterChange('quantityMin', 1);
            } else if (value === 'out_of_stock') {
              onFilterChange('quantityMax', 0);
            }
          }
        }}
        disabled={loading}
      >
        <SelectTrigger className="w-[160px] h-9">
          <SelectValue placeholder="Status Stok" />
          {loading && (
            <div className="absolute right-8 top-1/2 transform -translate-y-1/2">
              <div className="w-3 h-3 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
            </div>
          )}
        </SelectTrigger>
        <SelectContent>
          {STOCK_STATUS_OPTIONS.map((option) => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {/* Product Type Filter */}
      <Select
        value={filters.productType || 'all'}
        onValueChange={(value) =>
          onFilterChange('productType', value === 'all' ? undefined : value)
        }
        disabled={loading}
      >
        <SelectTrigger className="w-[140px] h-9">
          <SelectValue placeholder="Jenis Produk" />
          {loading && (
            <div className="absolute right-8 top-1/2 transform -translate-y-1/2">
              <div className="w-3 h-3 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
            </div>
          )}
        </SelectTrigger>
        <SelectContent>
          {PRODUCT_TYPE_OPTIONS.map((option) => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {/* Advanced Filters Toggle */}
      <Popover>
        <PopoverTrigger asChild>
          <Button variant="outline" size="sm" className="h-9 px-3">
            <Filter className="h-4 w-4 mr-1" />
            Filter Lanjutan
            {activeFiltersCount > 3 && (
              <Badge variant="secondary" className="ml-2 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs">
                {activeFiltersCount - 3}
              </Badge>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-96 p-4" align="start">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="font-medium">Filter Lanjutan</h4>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
              >
                {showAdvancedFilters ? 'Sembunyikan' : 'Tampilkan Semua'}
              </Button>
            </div>

            {/* Product Category */}
            <div className="space-y-2">
              <Label>Kategori Produk</Label>
              <Select
                value={filters.productCategory || 'all'}
                onValueChange={(value) =>
                  onFilterChange('productCategory', value === 'all' ? undefined : value)
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Pilih kategori" />
                </SelectTrigger>
                <SelectContent>
                  {PRODUCT_CATEGORY_FILTER_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Location Filter */}
            <div className="space-y-2">
              <Label>Lokasi</Label>
              <Input
                placeholder="Masukkan lokasi..."
                value={filters.location || ''}
                onChange={(e) => onFilterChange('location', e.target.value || undefined)}
              />
            </div>

            {/* Batch Number Filter */}
            <div className="space-y-2">
              <Label>Nomor Batch</Label>
              <Input
                placeholder="Masukkan nomor batch..."
                value={filters.batchNumber || ''}
                onChange={(e) => onFilterChange('batchNumber', e.target.value || undefined)}
              />
            </div>

            {showAdvancedFilters && (
              <>
                {/* Quantity Range */}
                <div className="space-y-2">
                  <Label>Rentang Jumlah Stok</Label>
                  <div className="grid grid-cols-2 gap-2">
                    <Input
                      type="number"
                      placeholder="Min"
                      value={filters.quantityMin || ''}
                      onChange={(e) => onFilterChange('quantityMin', e.target.value ? parseInt(e.target.value) : undefined)}
                    />
                    <Input
                      type="number"
                      placeholder="Max"
                      value={filters.quantityMax || ''}
                      onChange={(e) => onFilterChange('quantityMax', e.target.value ? parseInt(e.target.value) : undefined)}
                    />
                  </div>
                </div>

                {/* Price Range */}
                <div className="space-y-2">
                  <Label>Rentang Harga Beli</Label>
                  <div className="grid grid-cols-2 gap-2">
                    <Input
                      type="number"
                      placeholder="Min"
                      value={filters.costPriceMin || ''}
                      onChange={(e) => onFilterChange('costPriceMin', e.target.value ? parseFloat(e.target.value) : undefined)}
                    />
                    <Input
                      type="number"
                      placeholder="Max"
                      value={filters.costPriceMax || ''}
                      onChange={(e) => onFilterChange('costPriceMax', e.target.value ? parseFloat(e.target.value) : undefined)}
                    />
                  </div>
                </div>

                {/* Low Stock Threshold */}
                <div className="space-y-2">
                  <Label>Ambang Batas Stok Rendah</Label>
                  <Input
                    type="number"
                    placeholder="Masukkan ambang batas..."
                    value={filters.lowStockThreshold || ''}
                    onChange={(e) => onFilterChange('lowStockThreshold', e.target.value ? parseInt(e.target.value) : undefined)}
                  />
                </div>

                {/* Expiring Soon Days */}
                <div className="space-y-2">
                  <Label>Hari Akan Kedaluwarsa</Label>
                  <Input
                    type="number"
                    placeholder="30"
                    value={filters.expiringSoonDays || ''}
                    onChange={(e) => onFilterChange('expiringSoonDays', e.target.value ? parseInt(e.target.value) : undefined)}
                  />
                </div>
              </>
            )}
          </div>
        </PopoverContent>
      </Popover>

      {/* Clear Filters Button */}
      {activeFiltersCount > 0 && (
        <Button
          variant="outline"
          size="sm"
          onClick={onClearFilters}
          className="h-9 px-3"
        >
          <X className="h-4 w-4 mr-1" />
          Hapus Filter
          <Badge variant="secondary" className="ml-2 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs">
            {activeFiltersCount}
          </Badge>
        </Button>
      )}
    </div>
  );
}
