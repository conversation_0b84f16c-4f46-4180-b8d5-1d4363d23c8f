import { useState, useEffect } from "react";
import { ReferenceType } from "@/types/inventory";
import { Button } from "@/components/ui/button";
import { Check, ChevronsUpDown, X } from "lucide-react";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { REFERENCE_TYPE_OPTIONS } from "@/lib/constants/inventory";

interface ReferenceTypeFilterProps {
  value?: ReferenceType[];
  onChange?: (value: ReferenceType[]) => void;
  className?: string;
}

// Group options by category
const salesTypes: ReferenceType[] = [ReferenceType.SALE, ReferenceType.SALE_CANCELLATION, ReferenceType.SALE_REFUND, ReferenceType.SALE_DELETION];
const purchaseTypes: ReferenceType[] = [ReferenceType.PURCHASE, ReferenceType.PURCHASE_RETURN];
const inventoryTypes: ReferenceType[] = [ReferenceType.INITIAL_STOCK, ReferenceType.ADJUSTMENT, ReferenceType.TRANSFER];
const allocationTypes: ReferenceType[] = [ReferenceType.ALLOCATION, ReferenceType.DEALLOCATION];
const statusTypes: ReferenceType[] = [ReferenceType.ACTIVATION, ReferenceType.DEACTIVATION];
const customerTypes: ReferenceType[] = [ReferenceType.CUSTOMER_RESERVATION, ReferenceType.CUSTOMER_ORDER];
const expiryTypes: ReferenceType[] = [ReferenceType.EXPIRY_WRITE_OFF];
const otherTypes: ReferenceType[] = [ReferenceType.OTHER];

const GROUPED_OPTIONS = {
  sales: REFERENCE_TYPE_OPTIONS.filter(opt => (salesTypes as readonly ReferenceType[]).includes(opt.value as ReferenceType)),
  purchase: REFERENCE_TYPE_OPTIONS.filter(opt => (purchaseTypes as readonly ReferenceType[]).includes(opt.value as ReferenceType)),
  inventory: REFERENCE_TYPE_OPTIONS.filter(opt => (inventoryTypes as readonly ReferenceType[]).includes(opt.value as ReferenceType)),
  allocation: REFERENCE_TYPE_OPTIONS.filter(opt => (allocationTypes as readonly ReferenceType[]).includes(opt.value as ReferenceType)),
  status: REFERENCE_TYPE_OPTIONS.filter(opt => (statusTypes as readonly ReferenceType[]).includes(opt.value as ReferenceType)),
  customer: REFERENCE_TYPE_OPTIONS.filter(opt => (customerTypes as readonly ReferenceType[]).includes(opt.value as ReferenceType)),
  expiry: REFERENCE_TYPE_OPTIONS.filter(opt => (expiryTypes as readonly ReferenceType[]).includes(opt.value as ReferenceType)),
  other: REFERENCE_TYPE_OPTIONS.filter(opt => (otherTypes as readonly ReferenceType[]).includes(opt.value as ReferenceType)),
};

export function ReferenceTypeFilter({
  value = [],
  onChange,
  className,
}: ReferenceTypeFilterProps) {
  const [open, setOpen] = useState(false);
  const [selectedValues, setSelectedValues] = useState<ReferenceType[]>(value);

  useEffect(() => {
    setSelectedValues(value);
  }, [value]);

  const handleSelect = (referenceType: ReferenceType) => {
    const newValues = selectedValues.includes(referenceType)
      ? selectedValues.filter(v => v !== referenceType)
      : [...selectedValues, referenceType];
    
    setSelectedValues(newValues);
    onChange?.(newValues);
  };

  const handleClear = () => {
    setSelectedValues([]);
    onChange?.([]);
  };

  const selectedLabels = selectedValues.map(value => {
    const option = REFERENCE_TYPE_OPTIONS.find(opt => opt.value === value);
    return option?.label || value;
  });

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn("justify-between", className)}
        >
          {selectedValues.length > 0 ? (
            <div className="flex items-center gap-1 flex-wrap">
              <span className="mr-1">Filter:</span>
              {selectedValues.length <= 2 ? (
                selectedLabels.map((label, i) => (
                  <Badge key={i} variant="secondary" className="mr-1">
                    {label}
                  </Badge>
                ))
              ) : (
                <Badge variant="secondary">
                  {selectedValues.length} jenis dipilih
                </Badge>
              )}
            </div>
          ) : (
            "Filter Jenis Referensi"
          )}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[300px] p-0">
        <Command>
          <CommandInput placeholder="Cari jenis referensi..." />
          <CommandList>
            <CommandEmpty>Tidak ada hasil</CommandEmpty>
            
            {selectedValues.length > 0 && (
              <>
                <div className="p-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-full justify-start text-sm"
                    onClick={handleClear}
                  >
                    <X className="mr-2 h-4 w-4" />
                    Hapus filter
                  </Button>
                </div>
                <CommandSeparator />
              </>
            )}
            
            <CommandGroup heading="Penjualan">
              {GROUPED_OPTIONS.sales.map((option) => (
                <CommandItem
                  key={option.value}
                  value={option.value}
                  onSelect={() => handleSelect(option.value)}
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      selectedValues.includes(option.value)
                        ? "opacity-100"
                        : "opacity-0"
                    )}
                  />
                  {option.label}
                </CommandItem>
              ))}
            </CommandGroup>
            
            <CommandGroup heading="Pembelian">
              {GROUPED_OPTIONS.purchase.map((option) => (
                <CommandItem
                  key={option.value}
                  value={option.value}
                  onSelect={() => handleSelect(option.value)}
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      selectedValues.includes(option.value)
                        ? "opacity-100"
                        : "opacity-0"
                    )}
                  />
                  {option.label}
                </CommandItem>
              ))}
            </CommandGroup>
            
            <CommandGroup heading="Inventaris">
              {GROUPED_OPTIONS.inventory.map((option) => (
                <CommandItem
                  key={option.value}
                  value={option.value}
                  onSelect={() => handleSelect(option.value)}
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      selectedValues.includes(option.value)
                        ? "opacity-100"
                        : "opacity-0"
                    )}
                  />
                  {option.label}
                </CommandItem>
              ))}
            </CommandGroup>
            
            <CommandGroup heading="Alokasi">
              {GROUPED_OPTIONS.allocation.map((option) => (
                <CommandItem
                  key={option.value}
                  value={option.value}
                  onSelect={() => handleSelect(option.value)}
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      selectedValues.includes(option.value)
                        ? "opacity-100"
                        : "opacity-0"
                    )}
                  />
                  {option.label}
                </CommandItem>
              ))}
            </CommandGroup>
            
            <CommandGroup heading="Status">
              {GROUPED_OPTIONS.status.map((option) => (
                <CommandItem
                  key={option.value}
                  value={option.value}
                  onSelect={() => handleSelect(option.value)}
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      selectedValues.includes(option.value)
                        ? "opacity-100"
                        : "opacity-0"
                    )}
                  />
                  {option.label}
                </CommandItem>
              ))}
            </CommandGroup>
            
            <CommandGroup heading="Pelanggan">
              {GROUPED_OPTIONS.customer.map((option) => (
                <CommandItem
                  key={option.value}
                  value={option.value}
                  onSelect={() => handleSelect(option.value)}
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      selectedValues.includes(option.value)
                        ? "opacity-100"
                        : "opacity-0"
                    )}
                  />
                  {option.label}
                </CommandItem>
              ))}
            </CommandGroup>
            
            <CommandGroup heading="Kedaluwarsa">
              {GROUPED_OPTIONS.expiry.map((option) => (
                <CommandItem
                  key={option.value}
                  value={option.value}
                  onSelect={() => handleSelect(option.value)}
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      selectedValues.includes(option.value)
                        ? "opacity-100"
                        : "opacity-0"
                    )}
                  />
                  {option.label}
                </CommandItem>
              ))}
            </CommandGroup>
            
            <CommandGroup heading="Lainnya">
              {GROUPED_OPTIONS.other.map((option) => (
                <CommandItem
                  key={option.value}
                  value={option.value}
                  onSelect={() => handleSelect(option.value)}
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      selectedValues.includes(option.value)
                        ? "opacity-100"
                        : "opacity-0"
                    )}
                  />
                  {option.label}
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
} 