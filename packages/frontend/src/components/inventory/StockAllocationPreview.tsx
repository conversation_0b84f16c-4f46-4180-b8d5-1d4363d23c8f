'use client';

import { format } from 'date-fns';
import { id as localeId } from 'date-fns/locale';
import {
  ArrowLeft,
  Play,
  AlertTriangle,
  CheckCircle,
  Clock,
  Package,
  MapPin,
  Calendar,
  DollarSign,
  Activity,
} from 'lucide-react';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

import { AllocationResult, AllocationMethod } from '@/types/stock-allocation';
import { formatCurrency } from '@/lib/utils';

interface StockAllocationPreviewProps {
  previewData: AllocationResult;
  formData: {
    productId: string;
    requestedQuantity: number;
    method: AllocationMethod;
    allowPartialAllocation: boolean;
    nearExpiryWarningDays: number;
    reason: string;
    notes?: string;
  };
  onBack: () => void;
  onExecute: () => void;
  isExecuting: boolean;
}

export function StockAllocationPreview({
  previewData,
  formData,
  onBack,
  onExecute,
  isExecuting,
}: StockAllocationPreviewProps) {
  const hasWarnings = previewData.warnings.length > 0;
  const hasErrors = previewData.errors.length > 0;
  const hasShortfall = previewData.shortfall && previewData.shortfall > 0;
  const isPartialAllocation = previewData.allocatedQuantity < previewData.requestedQuantity;

  const getExpiryBadge = (batch: any) => {
    if (!batch.expiryDate) return null;

    const expiryDate = new Date(batch.expiryDate);
    const today = new Date();
    const daysUntilExpiry = Math.ceil((expiryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

    if (daysUntilExpiry < 0) {
      return (
        <Badge variant="destructive" className="text-xs">
          Kedaluwarsa
        </Badge>
      );
    } else if (daysUntilExpiry <= formData.nearExpiryWarningDays) {
      return (
        <Badge variant="outline" className="text-xs bg-yellow-50 text-yellow-700 border-yellow-200">
          {daysUntilExpiry} hari lagi
        </Badge>
      );
    }

    return (
      <Badge variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200">
        {daysUntilExpiry} hari lagi
      </Badge>
    );
  };

  const getMethodBadge = () => {
    if (formData.method === AllocationMethod.FIFO) {
      return (
        <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
          FIFO (First In First Out)
        </Badge>
      );
    } else {
      return (
        <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
          FEFO (First Expired First Out)
        </Badge>
      );
    }
  };

  return (
    <div className="space-y-6">
      {/* Preview Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Preview Alokasi Stok
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">Jumlah Diminta</label>
              <p className="text-lg font-semibold">{previewData.requestedQuantity}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Jumlah Dialokasikan</label>
              <p className={`text-lg font-semibold ${
                isPartialAllocation ? 'text-orange-600' : 'text-green-600'
              }`}>
                {previewData.allocatedQuantity}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Metode Alokasi</label>
              <div className="mt-1">
                {getMethodBadge()}
              </div>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Total Biaya</label>
              <p className="text-lg font-semibold">{formatCurrency(previewData.totalCost)}</p>
            </div>
          </div>

          {/* Status Indicators */}
          <div className="flex flex-wrap gap-2">
            {previewData.success ? (
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                <CheckCircle className="mr-1 h-3 w-3" />
                Alokasi Berhasil
              </Badge>
            ) : (
              <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                <AlertTriangle className="mr-1 h-3 w-3" />
                Alokasi Gagal
              </Badge>
            )}

            {isPartialAllocation && (
              <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">
                <Clock className="mr-1 h-3 w-3" />
                Alokasi Sebagian
              </Badge>
            )}

            {hasShortfall && (
              <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                Kekurangan: {previewData.shortfall}
              </Badge>
            )}
          </div>

          {/* Allocation Details */}
          <div className="grid gap-4 sm:grid-cols-2">
            <div>
              <label className="text-sm font-medium text-muted-foreground">Alasan</label>
              <p className="text-sm">{formData.reason}</p>
            </div>
            {formData.notes && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">Catatan</label>
                <p className="text-sm">{formData.notes}</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Warnings and Errors */}
      {(hasWarnings || hasErrors) && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-orange-600">
              <AlertTriangle className="h-5 w-5" />
              Peringatan dan Error
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {hasWarnings && (
              <div>
                <h4 className="font-medium text-orange-600 mb-2">Peringatan:</h4>
                <ul className="space-y-1">
                  {previewData.warnings.map((warning, index) => (
                    <li key={index} className="text-sm text-orange-700 flex items-start gap-2">
                      <AlertTriangle className="h-3 w-3 mt-0.5 flex-shrink-0" />
                      {warning}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {hasErrors && (
              <div>
                <h4 className="font-medium text-red-600 mb-2">Error:</h4>
                <ul className="space-y-1">
                  {previewData.errors.map((error, index) => (
                    <li key={index} className="text-sm text-red-700 flex items-start gap-2">
                      <AlertTriangle className="h-3 w-3 mt-0.5 flex-shrink-0" />
                      {error}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Batch Details */}
      {previewData.batches.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Detail Batch yang Akan Dialokasikan
            </CardTitle>
          </CardHeader>
          <CardContent>
            {/* Mobile View */}
            <div className="block sm:hidden space-y-4">
              {previewData.batches.map((batch, index) => (
                <Card key={batch.inventoryItemId} className="p-4">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">Batch #{index + 1}</span>
                      <span className="font-semibold text-lg">
                        {batch.allocatedQuantity} unit
                      </span>
                    </div>
                    
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Nomor Batch:</span>
                        <span className="font-mono">{batch.batchNumber || 'N/A'}</span>
                      </div>
                      
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Tersedia:</span>
                        <span>{batch.availableQuantity} unit</span>
                      </div>
                      
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Harga Satuan:</span>
                        <span>{formatCurrency(batch.costPrice)}</span>
                      </div>
                      
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Total Biaya:</span>
                        <span className="font-medium">
                          {formatCurrency(batch.costPrice * batch.allocatedQuantity)}
                        </span>
                      </div>
                      
                      {batch.expiryDate && (
                        <div className="flex justify-between items-center">
                          <span className="text-muted-foreground">Kedaluwarsa:</span>
                          <div className="flex items-center gap-2">
                            <span className="text-xs">
                              {format(new Date(batch.expiryDate), 'dd MMM yyyy', { locale: localeId })}
                            </span>
                            {getExpiryBadge(batch)}
                          </div>
                        </div>
                      )}
                      
                      {batch.location && (
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Lokasi:</span>
                          <span>{batch.location}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </Card>
              ))}
            </div>

            {/* Desktop View */}
            <div className="hidden sm:block">
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Batch</TableHead>
                      <TableHead>Tersedia</TableHead>
                      <TableHead>Dialokasikan</TableHead>
                      <TableHead>Harga Satuan</TableHead>
                      <TableHead>Total Biaya</TableHead>
                      <TableHead>Kedaluwarsa</TableHead>
                      <TableHead>Lokasi</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {previewData.batches.map((batch, index) => (
                      <TableRow key={batch.inventoryItemId}>
                        <TableCell>
                          <div>
                            <div className="font-medium">#{index + 1}</div>
                            <div className="text-xs text-muted-foreground font-mono">
                              {batch.batchNumber || 'N/A'}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>{batch.availableQuantity}</TableCell>
                        <TableCell>
                          <span className="font-medium text-blue-600">
                            {batch.allocatedQuantity}
                          </span>
                        </TableCell>
                        <TableCell>{formatCurrency(batch.costPrice)}</TableCell>
                        <TableCell>
                          <span className="font-medium">
                            {formatCurrency(batch.costPrice * batch.allocatedQuantity)}
                          </span>
                        </TableCell>
                        <TableCell>
                          {batch.expiryDate ? (
                            <div className="space-y-1">
                              <div className="text-sm">
                                {format(new Date(batch.expiryDate), 'dd MMM yyyy', { locale: localeId })}
                              </div>
                              {getExpiryBadge(batch)}
                            </div>
                          ) : (
                            '-'
                          )}
                        </TableCell>
                        <TableCell>
                          {batch.location ? (
                            <div className="flex items-center gap-1">
                              <MapPin className="h-3 w-3 text-muted-foreground" />
                              <span className="text-sm">{batch.location}</span>
                            </div>
                          ) : (
                            '-'
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>

            {/* Summary */}
            <Separator className="my-4" />
            <div className="flex justify-between items-center">
              <span className="font-medium">Total Alokasi:</span>
              <div className="text-right">
                <div className="font-semibold text-lg">
                  {previewData.allocatedQuantity} unit
                </div>
                <div className="text-sm text-muted-foreground">
                  Rata-rata: {formatCurrency(previewData.averageCostPrice)}/unit
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Actions */}
      <div className="flex justify-between">
        <Button variant="outline" onClick={onBack}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Kembali ke Formulir
        </Button>
        
        <Button
          onClick={onExecute}
          disabled={isExecuting || !previewData.success || previewData.allocatedQuantity === 0}
          className="bg-green-600 hover:bg-green-700"
        >
          {isExecuting ? (
            <>
              <Activity className="mr-2 h-4 w-4 animate-spin" />
              Mengeksekusi...
            </>
          ) : (
            <>
              <Play className="mr-2 h-4 w-4" />
              Eksekusi Alokasi
            </>
          )}
        </Button>
      </div>
    </div>
  );
}
