import { Badge } from "@/components/ui/badge";
import { ReferenceType } from "@/types/inventory";
import { getReferenceTypeLabel } from "@/lib/constants/inventory";
import { cn } from "@/lib/utils";

interface ReferenceTypeBadgeProps {
  type: ReferenceType;
  className?: string;
}

export function ReferenceTypeBadge({ type, className }: ReferenceTypeBadgeProps) {
  const getVariant = () => {
    switch (type) {
      // Sales related
      case ReferenceType.SALE:
        return "bg-green-100 text-green-800 border-green-200";
      case ReferenceType.SALE_CANCELLATION:
      case ReferenceType.SALE_REFUND:
      case ReferenceType.SALE_DELETION:
        return "bg-red-100 text-red-800 border-red-200";
      
      // Purchase related
      case ReferenceType.PURCHASE:
        return "bg-blue-100 text-blue-800 border-blue-200";
      case ReferenceType.PURCHASE_RETURN:
        return "bg-orange-100 text-orange-800 border-orange-200";
      
      // Inventory management
      case ReferenceType.INITIAL_STOCK:
        return "bg-purple-100 text-purple-800 border-purple-200";
      case ReferenceType.ADJUSTMENT:
        return "bg-amber-100 text-amber-800 border-amber-200";
      case ReferenceType.TRANSFER:
        return "bg-indigo-100 text-indigo-800 border-indigo-200";
      
      // Allocation related
      case ReferenceType.ALLOCATION:
        return "bg-cyan-100 text-cyan-800 border-cyan-200";
      case ReferenceType.DEALLOCATION:
        return "bg-sky-100 text-sky-800 border-sky-200";
      
      // Status changes
      case ReferenceType.ACTIVATION:
        return "bg-emerald-100 text-emerald-800 border-emerald-200";
      case ReferenceType.DEACTIVATION:
        return "bg-rose-100 text-rose-800 border-rose-200";
      
      // Customer related
      case ReferenceType.CUSTOMER_RESERVATION:
      case ReferenceType.CUSTOMER_ORDER:
        return "bg-violet-100 text-violet-800 border-violet-200";
      
      // Expiry related
      case ReferenceType.EXPIRY_WRITE_OFF:
        return "bg-pink-100 text-pink-800 border-pink-200";
      
      // Other
      case ReferenceType.OTHER:
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  return (
    <Badge 
      variant="outline" 
      className={cn("font-medium", getVariant(), className)}
    >
      {getReferenceTypeLabel(type)}
    </Badge>
  );
} 