'use client';

import { useState } from 'react';
import { format } from 'date-fns';
import { id as localeId } from 'date-fns/locale';
import {
  CheckCircle,
  AlertTriangle,
  Package,
  RotateCcw,
  ArrowLeft,
  Download,
  Clock,
  TrendingUp,
  TrendingDown,
  MapPin,
  Calendar,
  DollarSign,
  FileText,
  FileSpreadsheet,
  Database,
  Loader2,
  ChevronDown,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

import {
  AllocationResult,
  AllocationMethod,
  AllocationReportRequest,
} from '@/types/stock-allocation';
import { formatCurrency } from '@/lib/utils';
import { useGenerateAllocationReport } from '@/hooks/useStockAllocation';
import { stockAllocationApi } from '@/lib/api/stock-allocation';
import { toast } from 'sonner';

interface StockAllocationResultsProps {
  results: AllocationResult;
  onStartOver: () => void;
  onBackToInventory: () => void;
}

export function StockAllocationResults({
  results,
  onStartOver,
  onBackToInventory,
}: StockAllocationResultsProps) {
  const [isGeneratingReport, setIsGeneratingReport] = useState(false);

  const isSuccess = results.success && results.allocatedQuantity > 0;
  const isPartialSuccess = results.allocatedQuantity > 0 && results.allocatedQuantity < results.requestedQuantity;
  const hasWarnings = results.warnings.length > 0;
  const hasErrors = results.errors.length > 0;

  const generateReportMutation = useGenerateAllocationReport();

  // Report generation handler
  const handleDownloadReport = async (format: 'pdf' | 'excel' | 'csv' = 'pdf') => {
    try {
      setIsGeneratingReport(true);

      // Prepare report request
      const reportRequest: AllocationReportRequest = {
        allocationResult: results,
        format,
        includeDetails: true,
        language: 'id',
        userInfo: {
          id: 'current-user', // This should come from auth context
          name: 'Current User', // This should come from auth context
          role: 'Pharmacist', // This should come from auth context
        },
        pharmacyInfo: {
          name: 'Apotek Sehat Bersama', // This should come from pharmacy settings
          address: 'Jl. Kesehatan No. 123, Jakarta', // This should come from pharmacy settings
          licenseNumber: 'SIA/001/2024', // This should come from pharmacy settings
          pharmacistName: 'Dr. Ahmad Setiawan, S.Farm., Apt.', // This should come from pharmacy settings
        },
      };

      // Generate report
      console.log('Generating report with request:', reportRequest);
      const response = await generateReportMutation.mutateAsync(reportRequest);
      console.log('Report generation response:', response);

      // Construct full download URL
      const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';
      // The reportUrl already includes /api prefix, so we need to construct properly
      const downloadUrl = baseUrl.endsWith('/api')
        ? `${baseUrl.replace('/api', '')}${response.reportUrl}`
        : `${baseUrl}${response.reportUrl.replace('/api', '')}`;
      console.log('Download URL:', downloadUrl);

      // Download the file using fetch
      const downloadResponse = await fetch(downloadUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token') || ''}`, // Add auth if needed
        },
      });

      console.log('Download response status:', downloadResponse.status);
      console.log('Download response headers:', downloadResponse.headers);

      if (!downloadResponse.ok) {
        throw new Error(`HTTP error! status: ${downloadResponse.status}`);
      }

      // Get the blob data
      const blob = await downloadResponse.blob();

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = response.fileName;
      document.body.appendChild(link);
      link.click();

      // Cleanup
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success(`Laporan ${format.toUpperCase()} berhasil diunduh`);
    } catch (error: any) {
      console.error('Error generating/downloading report:', error);
      const message = error?.response?.data?.message || error?.message || `Gagal mengunduh laporan ${format.toUpperCase()}`;
      toast.error(message);
    } finally {
      setIsGeneratingReport(false);
    }
  };

  const getStatusIcon = () => {
    if (isSuccess && !isPartialSuccess) {
      return <CheckCircle className="h-8 w-8 text-green-600" />;
    } else if (isPartialSuccess) {
      return <Clock className="h-8 w-8 text-orange-600" />;
    } else {
      return <AlertTriangle className="h-8 w-8 text-red-600" />;
    }
  };

  const getStatusMessage = () => {
    if (isSuccess && !isPartialSuccess) {
      return {
        title: 'Alokasi Stok Berhasil!',
        description: `Semua ${results.requestedQuantity} unit berhasil dialokasikan menggunakan metode ${results.method}.`,
        color: 'text-green-600',
      };
    } else if (isPartialSuccess) {
      return {
        title: 'Alokasi Stok Sebagian Berhasil',
        description: `${results.allocatedQuantity} dari ${results.requestedQuantity} unit berhasil dialokasikan. Kekurangan: ${results.shortfall} unit.`,
        color: 'text-orange-600',
      };
    } else {
      return {
        title: 'Alokasi Stok Gagal',
        description: 'Tidak ada stok yang dapat dialokasikan. Silakan periksa ketersediaan stok.',
        color: 'text-red-600',
      };
    }
  };

  const statusInfo = getStatusMessage();

  const getMethodBadge = () => {
    if (results.method === AllocationMethod.FIFO) {
      return (
        <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
          FIFO (First In First Out)
        </Badge>
      );
    } else {
      return (
        <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
          FEFO (First Expired First Out)
        </Badge>
      );
    }
  };

  const getExpiryBadge = (batch: any) => {
    if (!batch.expiryDate) return null;

    const expiryDate = new Date(batch.expiryDate);
    const today = new Date();
    const daysUntilExpiry = Math.ceil((expiryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

    if (daysUntilExpiry < 0) {
      return (
        <Badge variant="destructive" className="text-xs">
          Kedaluwarsa
        </Badge>
      );
    } else if (daysUntilExpiry <= 30) {
      return (
        <Badge variant="outline" className="text-xs bg-yellow-50 text-yellow-700 border-yellow-200">
          {daysUntilExpiry} hari lagi
        </Badge>
      );
    }

    return (
      <Badge variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200">
        {daysUntilExpiry} hari lagi
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      {/* Results Header */}
      <Card>
        <CardContent className="pt-6">
          <div className="text-center flex flex-col items-center space-y-4">
            {getStatusIcon()}
            <div>
              <h2 className={`text-2xl font-bold ${statusInfo.color}`}>
                {statusInfo.title}
              </h2>
              <p className="text-muted-foreground mt-2">
                {statusInfo.description}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Allocation Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Ringkasan Alokasi
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{results.requestedQuantity}</div>
              <div className="text-sm text-muted-foreground">Diminta</div>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className={`text-2xl font-bold ${isPartialSuccess ? 'text-orange-600' : 'text-green-600'
                }`}>
                {results.allocatedQuantity}
              </div>
              <div className="text-sm text-muted-foreground">Dialokasikan</div>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-purple-600">
                {formatCurrency(results.totalCost)}
              </div>
              <div className="text-sm text-muted-foreground">Total Biaya</div>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-gray-600">
                {formatCurrency(results.averageCostPrice)}
              </div>
              <div className="text-sm text-muted-foreground">Rata-rata/Unit</div>
            </div>
          </div>

          <div className="flex flex-wrap gap-2 justify-center">
            {getMethodBadge()}
            {isSuccess && (
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                <CheckCircle className="mr-1 h-3 w-3" />
                Berhasil
              </Badge>
            )}
            {isPartialSuccess && (
              <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">
                <Clock className="mr-1 h-3 w-3" />
                Sebagian
              </Badge>
            )}
            {results.shortfall && results.shortfall > 0 && (
              <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                Kekurangan: {results.shortfall} unit
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Warnings and Errors */}
      {(hasWarnings || hasErrors) && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-orange-600">
              <AlertTriangle className="h-5 w-5" />
              Peringatan dan Informasi
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {hasWarnings && (
              <div>
                <h4 className="font-medium text-orange-600 mb-2">Peringatan:</h4>
                <ul className="space-y-1">
                  {results.warnings.map((warning, index) => (
                    <li key={index} className="text-sm text-orange-700 flex items-start gap-2">
                      <AlertTriangle className="h-3 w-3 mt-0.5 flex-shrink-0" />
                      {warning}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {hasErrors && (
              <div>
                <h4 className="font-medium text-red-600 mb-2">Error:</h4>
                <ul className="space-y-1">
                  {results.errors.map((error, index) => (
                    <li key={index} className="text-sm text-red-700 flex items-start gap-2">
                      <AlertTriangle className="h-3 w-3 mt-0.5 flex-shrink-0" />
                      {error}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Allocated Batches */}
      {results.batches.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Batch yang Dialokasikan
            </CardTitle>
          </CardHeader>
          <CardContent>
            {/* Mobile View */}
            <div className="block sm:hidden space-y-4">
              {results.batches.map((batch, index) => (
                <Card key={batch.inventoryItemId} className="p-4 bg-green-50 border-green-200">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">Batch #{index + 1}</span>
                      <Badge variant="outline" className="bg-green-100 text-green-700 border-green-300">
                        <TrendingDown className="mr-1 h-3 w-3" />
                        -{batch.allocatedQuantity} unit
                      </Badge>
                    </div>

                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Nomor Batch:</span>
                        <span className="font-mono">{batch.batchNumber || 'N/A'}</span>
                      </div>

                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Sisa Stok:</span>
                        <span>{batch.availableQuantity - batch.allocatedQuantity} unit</span>
                      </div>

                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Biaya Alokasi:</span>
                        <span className="font-medium">
                          {formatCurrency(batch.costPrice * batch.allocatedQuantity)}
                        </span>
                      </div>

                      {batch.expiryDate && (
                        <div className="flex justify-between items-center">
                          <span className="text-muted-foreground">Kedaluwarsa:</span>
                          <div className="flex items-center gap-2">
                            <span className="text-xs">
                              {format(new Date(batch.expiryDate), 'dd MMM yyyy', { locale: localeId })}
                            </span>
                            {getExpiryBadge(batch)}
                          </div>
                        </div>
                      )}

                      {batch.location && (
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Lokasi:</span>
                          <span>{batch.location}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </Card>
              ))}
            </div>

            {/* Desktop View */}
            <div className="hidden sm:block">
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Batch</TableHead>
                      <TableHead>Dialokasikan</TableHead>
                      <TableHead>Sisa Stok</TableHead>
                      <TableHead>Biaya Alokasi</TableHead>
                      <TableHead>Kedaluwarsa</TableHead>
                      <TableHead>Lokasi</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {results.batches.map((batch, index) => (
                      <TableRow key={batch.inventoryItemId} className="bg-green-50">
                        <TableCell>
                          <div>
                            <div className="font-medium">#{index + 1}</div>
                            <div className="text-xs text-muted-foreground font-mono">
                              {batch.batchNumber || 'N/A'}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline" className="bg-green-100 text-green-700 border-green-300">
                            <TrendingDown className="mr-1 h-3 w-3" />
                            {batch.allocatedQuantity}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <span className="font-medium">
                            {batch.availableQuantity - batch.allocatedQuantity}
                          </span>
                        </TableCell>
                        <TableCell>
                          <span className="font-medium">
                            {formatCurrency(batch.costPrice * batch.allocatedQuantity)}
                          </span>
                        </TableCell>
                        <TableCell>
                          {batch.expiryDate ? (
                            <div className="space-y-1">
                              <div className="text-sm">
                                {format(new Date(batch.expiryDate), 'dd MMM yyyy', { locale: localeId })}
                              </div>
                              {getExpiryBadge(batch)}
                            </div>
                          ) : (
                            '-'
                          )}
                        </TableCell>
                        <TableCell>
                          {batch.location ? (
                            <div className="flex items-center gap-1">
                              <MapPin className="h-3 w-3 text-muted-foreground" />
                              <span className="text-sm">{batch.location}</span>
                            </div>
                          ) : (
                            '-'
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Actions */}
      <div className="flex flex-col gap-4 sm:flex-row sm:justify-between">
        <div className="flex gap-2">
          <Button variant="outline" onClick={onBackToInventory}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Kembali ke Inventori
          </Button>
          <Button variant="outline" onClick={onStartOver}>
            <RotateCcw className="mr-2 h-4 w-4" />
            Alokasi Baru
          </Button>
        </div>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" disabled={isGeneratingReport}>
              {isGeneratingReport ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Membuat Laporan...
                </>
              ) : (
                <>
                  <Download className="mr-2 h-4 w-4" />
                  Unduh Laporan
                  <ChevronDown className="ml-2 h-4 w-4" />
                </>
              )}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuItem
              onClick={() => handleDownloadReport('pdf')}
              disabled={isGeneratingReport}
            >
              <FileText className="mr-2 h-4 w-4" />
              <div className="flex flex-col">
                <span>PDF</span>
                <span className="text-xs text-muted-foreground">Direkomendasikan</span>
              </div>
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => handleDownloadReport('excel')}
              disabled={isGeneratingReport}
            >
              <FileSpreadsheet className="mr-2 h-4 w-4" />
              <div className="flex flex-col">
                <span>Excel</span>
                <span className="text-xs text-muted-foreground">Untuk analisis data</span>
              </div>
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => handleDownloadReport('csv')}
              disabled={isGeneratingReport}
            >
              <Database className="mr-2 h-4 w-4" />
              <div className="flex flex-col">
                <span>CSV</span>
                <span className="text-xs text-muted-foreground">Untuk integrasi sistem</span>
              </div>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
}
