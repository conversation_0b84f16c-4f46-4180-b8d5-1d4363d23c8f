'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import {
  FileText,
  FileSpreadsheet,
  Database,
  Download,
  Loader2,
  Filter,
  Settings,
} from 'lucide-react';
import {
  ReportType,
  ReportFormat,
  ReportLanguage,
  InventoryReportRequest,
  QUICK_REPORT_PRESETS,
  REPORT_TYPE_CONFIG,
  FORMAT_CONFIG,
} from '@/types/inventory-reports';
import { useGenerateInventoryReport, useReportValidation, useDownloadReport, useReportProgress } from '@/hooks/useInventoryReports';
import { ProductSelector } from '@/components/ui/product-selector';
import { SupplierSelector } from '@/components/ui/supplier-selector';

// Form validation schema
const reportFormSchema = z.object({
  type: z.nativeEnum(ReportType, {
    required_error: 'Jenis laporan wajib dipilih',
  }),
  format: z.nativeEnum(ReportFormat, {
    required_error: 'Format laporan wajib dipilih',
  }),
  language: z.nativeEnum(ReportLanguage),
  includeDetails: z.boolean(),
  includeSummary: z.boolean(),
  includeCharts: z.boolean(),

  // Filters
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  productId: z.string().optional(),
  supplierId: z.string().optional(),
  location: z.string().optional(),
  lowStockOnly: z.boolean(),
  expiredOnly: z.boolean(),
  expiringSoon: z.boolean(),
  expiringSoonDays: z.number().min(1).max(365),
  isActive: z.boolean().optional(),
  minQuantity: z.number().min(0).optional(),
  maxQuantity: z.number().min(0).optional(),
});

type ReportFormData = FormData;

interface ReportGenerationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  defaultType?: ReportType;
  defaultFilters?: Partial<ReportFormData>;
  defaultPresetId?: string;
}

// Create a proper form type that matches the schema
type FormData = {
  type: ReportType;
  format: ReportFormat;
  language: ReportLanguage;
  includeDetails: boolean;
  includeSummary: boolean;
  includeCharts: boolean;
  startDate?: string;
  endDate?: string;
  productId?: string;
  supplierId?: string;
  location?: string;
  lowStockOnly: boolean;
  expiredOnly: boolean;
  expiringSoon: boolean;
  expiringSoonDays: number;
  isActive?: boolean;
  minQuantity?: number;
  maxQuantity?: number;
};

export function ReportGenerationDialog({
  open,
  onOpenChange,
  defaultType,
  defaultFilters,
  defaultPresetId,
}: ReportGenerationDialogProps) {
  const [selectedPreset, setSelectedPreset] = useState<string | null>(null);
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [reportId, setReportId] = useState<string | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationStep, setGenerationStep] = useState<'idle' | 'generating' | 'downloading' | 'complete'>('idle');
  const [retryCount, setRetryCount] = useState(0);
  const [lastError, setLastError] = useState<string | null>(null);

  const generateReportMutation = useGenerateInventoryReport();
  const downloadMutation = useDownloadReport();
  const { validateFilters } = useReportValidation();

  // Progress tracking for long-running reports
  const { data: progress } = useReportProgress(reportId, isGenerating && !!reportId);

  // Get default values from preset
  const getDefaultValuesFromPreset = (presetId?: string): ReportFormData => {
    const baseDefaults: ReportFormData = {
      type: defaultType || ReportType.STOCK_LEVELS,
      format: ReportFormat.PDF,
      language: ReportLanguage.ID,
      includeDetails: true,
      includeSummary: true,
      includeCharts: false,
      lowStockOnly: false,
      expiredOnly: false,
      expiringSoon: false,
      expiringSoonDays: 30,
      startDate: '',
      endDate: '',
      productId: '',
      supplierId: '',
      location: '',
      isActive: undefined,
      minQuantity: undefined,
      maxQuantity: undefined,
      ...defaultFilters,
    };

    if (!presetId) {
      return baseDefaults;
    }

    const preset = QUICK_REPORT_PRESETS.find(p => p.id === presetId);
    if (!preset) {
      return baseDefaults; // Fallback to default
    }

    return {
      ...baseDefaults,
      type: preset.type,
      format: preset.format,
      // Apply preset filters with proper defaults
      lowStockOnly: preset.filters.lowStockOnly || false,
      expiredOnly: preset.filters.expiredOnly || false,
      expiringSoon: preset.filters.expiringSoon || false,
      expiringSoonDays: preset.filters.expiringSoonDays || 30,
      startDate: preset.filters.startDate || '',
      endDate: preset.filters.endDate || '',
      productId: preset.filters.productId || '',
      supplierId: preset.filters.supplierId || '',
      location: preset.filters.location || '',
      isActive: preset.filters.isActive,
      minQuantity: preset.filters.minQuantity,
      maxQuantity: preset.filters.maxQuantity,
      ...defaultFilters, // Allow override from props
    };
  };

  // Prevent dialog close during generation
  const handleOpenChange = (open: boolean) => {
    if (!open && isGenerating) {
      // Don't close dialog during generation
      return;
    }
    onOpenChange(open);
    if (!open) {
      resetState();
    }
  };

  const form = useForm<ReportFormData>({
    resolver: zodResolver(reportFormSchema),
    defaultValues: getDefaultValuesFromPreset(defaultPresetId),
  });

  const watchedType = form.watch('type');
  const watchedFormat = form.watch('format');

  // Handle preset changes and form reset when dialog opens
  useEffect(() => {
    if (open) {
      // Reset form with new values when dialog opens
      const newValues = getDefaultValuesFromPreset(defaultPresetId);
      form.reset(newValues);

      // Set selected preset if provided
      if (defaultPresetId) {
        setSelectedPreset(defaultPresetId);
      } else {
        setSelectedPreset(null);
      }

      // Reset generation state
      resetState();
    }
  }, [open, defaultPresetId, defaultType, defaultFilters]);

  // Auto-select preset based on defaultPresetId
  useEffect(() => {
    if (defaultPresetId && open) {
      setSelectedPreset(defaultPresetId);
    }
  }, [defaultPresetId, open]);

  // Clear preset selection when report type changes to non-preset type
  useEffect(() => {
    if (selectedPreset && watchedType) {
      // Check if current type matches the selected preset
      const currentPreset = QUICK_REPORT_PRESETS.find(p => p.id === selectedPreset);
      if (currentPreset && currentPreset.type !== watchedType) {
        // Current type doesn't match the selected preset, clear selection
        setSelectedPreset(null);
      }
    }
  }, [watchedType, selectedPreset]);

  const handlePresetSelect = (presetId: string) => {
    const preset = QUICK_REPORT_PRESETS.find(p => p.id === presetId);
    if (preset) {
      setSelectedPreset(presetId);

      // Update form with preset values
      form.setValue('type', preset.type);
      form.setValue('format', preset.format);

      // Apply preset filters with proper defaults
      Object.entries(preset.filters).forEach(([key, value]) => {
        if (key in form.getValues()) {
          // Ensure string fields get empty string instead of undefined
          let safeValue = value;
          if (value === undefined || value === null) {
            if (key === 'startDate' || key === 'endDate' || key === 'productId' || key === 'supplierId' || key === 'location') {
              safeValue = '';
            }
          }
          form.setValue(key as keyof ReportFormData, safeValue as any);
        }
      });
    }
  };

  // Reset state function
  const resetState = () => {
    setReportId(null);
    setIsGenerating(false);
    setGenerationStep('idle');
    setRetryCount(0);
    setLastError(null);
    form.clearErrors('root');
  };

  // Retry mechanism for failed downloads
  const retryDownload = async (fileName: string) => {
    const maxRetries = 3;
    if (retryCount >= maxRetries) {
      throw new Error(`Gagal mengunduh setelah ${maxRetries} percobaan. Silakan coba lagi nanti.`);
    }

    setRetryCount(prev => prev + 1);
    setLastError(null);

    try {
      await downloadMutation.mutateAsync({
        fileName,
        reportName: fileName,
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Gagal mengunduh laporan';
      setLastError(errorMessage);

      if (retryCount < maxRetries - 1) {
        // Wait before retry (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, retryCount) * 1000));
        return retryDownload(fileName);
      } else {
        throw error;
      }
    }
  };

  const onSubmit = async (data: ReportFormData) => {
    try {
      setIsGenerating(true);
      setGenerationStep('generating');

      // Clear any previous errors
      form.clearErrors('root');

      // Prepare filters
      const filters = {
        startDate: data.startDate,
        endDate: data.endDate,
        productId: data.productId,
        supplierId: data.supplierId,
        location: data.location,
        lowStockOnly: data.lowStockOnly,
        expiredOnly: data.expiredOnly,
        expiringSoon: data.expiringSoon,
        expiringSoonDays: data.expiringSoonDays,
        isActive: data.isActive,
        minQuantity: data.minQuantity,
        maxQuantity: data.maxQuantity,
      };

      // Validate filters
      const validation = validateFilters(filters);
      if (!validation.isValid) {
        form.setError('root', {
          message: validation.errors.join(', ')
        });
        setIsGenerating(false);
        setGenerationStep('idle');
        return;
      }

      // Prepare request
      const request: InventoryReportRequest = {
        type: data.type,
        format: data.format,
        language: data.language,
        filters,
        includeDetails: data.includeDetails,
        includeSummary: data.includeSummary,
        includeCharts: data.includeCharts,
      };

      const result = await generateReportMutation.mutateAsync(request);

      // Set report ID for progress tracking
      if (result.reportId) {
        setReportId(result.reportId);
      }

      // Auto-download using authenticated API with retry mechanism
      if (result.fileName) {
        setGenerationStep('downloading');
        try {
          await retryDownload(result.fileName);
          setGenerationStep('complete');
        } catch (downloadError) {
          console.error('Download failed after retries:', downloadError);
          setGenerationStep('idle');

          // Provide specific error messages based on error type
          let errorMessage = 'Laporan berhasil dibuat, tetapi gagal diunduh.';

          if (downloadError instanceof Error) {
            if (downloadError.message.includes('network') || downloadError.message.includes('fetch')) {
              errorMessage += ' Periksa koneksi internet Anda dan coba lagi.';
            } else if (downloadError.message.includes('authentication') || downloadError.message.includes('unauthorized')) {
              errorMessage += ' Sesi Anda mungkin telah berakhir. Silakan login ulang.';
            } else if (downloadError.message.includes('not found')) {
              errorMessage += ' File laporan tidak ditemukan. Silakan buat laporan baru.';
            } else {
              errorMessage += ` Error: ${downloadError.message}`;
            }
          }

          form.setError('root', { message: errorMessage });
        }
      }

      // Close dialog on success after a brief delay
      setTimeout(() => {
        onOpenChange(false);
        resetState();
      }, 1000);

    } catch (error) {
      console.error('Error generating report:', error);
      form.setError('root', {
        message: error instanceof Error ? error.message : 'Terjadi kesalahan saat membuat laporan'
      });
      setIsGenerating(false);
      setGenerationStep('idle');
    }
  };

  const getFormatIcon = (format: ReportFormat) => {
    switch (format) {
      case ReportFormat.PDF:
        return <FileText className="h-4 w-4" />;
      case ReportFormat.EXCEL:
        return <FileSpreadsheet className="h-4 w-4" />;
      case ReportFormat.CSV:
        return <Database className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  const typeConfig = REPORT_TYPE_CONFIG[watchedType];
  const formatConfig = FORMAT_CONFIG[watchedFormat];

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Buat Laporan Inventori
          </DialogTitle>
          <DialogDescription>
            Pilih jenis laporan dan konfigurasi yang diinginkan untuk menghasilkan laporan inventori.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Error Display */}
            {form.formState.errors.root && (
              <div className="p-4 border border-destructive/50 bg-destructive/10 rounded-lg">
                <p className="text-sm text-destructive font-medium">
                  {form.formState.errors.root.message}
                </p>
              </div>
            )}
            {/* Quick Presets */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium">Laporan Cepat</h4>
                {selectedPreset && (
                  <Badge variant="outline" className="text-xs">
                    {QUICK_REPORT_PRESETS.find(p => p.id === selectedPreset)?.name} dipilih
                  </Badge>
                )}
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {QUICK_REPORT_PRESETS.slice(0, 4).map((preset) => {
                  const isSelected = selectedPreset === preset.id;
                  const matchesCurrentType = preset.type === watchedType;

                  return (
                    <Card
                      key={preset.id}
                      className={`cursor-pointer transition-colors hover:bg-muted/50 ${isSelected
                          ? 'ring-2 ring-primary bg-primary/5'
                          : matchesCurrentType
                            ? 'ring-1 ring-primary/30 bg-primary/5'
                            : ''
                        }`}
                      onClick={() => handlePresetSelect(preset.id)}
                    >
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm flex items-center gap-2">
                          {preset.name}
                          {matchesCurrentType && !isSelected && (
                            <Badge variant="outline" className="text-xs">
                              Cocok
                            </Badge>
                          )}
                        </CardTitle>
                        <CardDescription className="text-xs">
                          {preset.description}
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <div className="flex items-center gap-2">
                          <Badge variant="secondary" className="text-xs">
                            {FORMAT_CONFIG[preset.format].name}
                          </Badge>
                          {preset.isDefault && (
                            <Badge variant="default" className="text-xs">
                              Default
                            </Badge>
                          )}
                          {isSelected && (
                            <Badge variant="default" className="text-xs bg-primary">
                              Aktif
                            </Badge>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </div>

            <Separator />

            {/* Report Configuration */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Report Type */}
              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Jenis Laporan</FormLabel>
                    <Select
                      onValueChange={(value) => {
                        field.onChange(value);
                        // Check if the new type matches any preset
                        const matchingPreset = QUICK_REPORT_PRESETS.find(p => p.type === value);
                        if (!matchingPreset && selectedPreset) {
                          // Clear preset selection if new type doesn't match any preset
                          setSelectedPreset(null);
                        }
                      }}
                      value={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Pilih jenis laporan" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Object.entries(REPORT_TYPE_CONFIG).map(([type, config]) => (
                          <SelectItem key={type} value={type}>
                            <div className="flex items-center gap-2">
                              <span>{config.name}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {typeConfig && (
                      <FormDescription>{typeConfig.description}</FormDescription>
                    )}
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Report Format */}
              <FormField
                control={form.control}
                name="format"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Format Laporan</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Pilih format" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {typeConfig?.supportedFormats.map((format) => (
                          <SelectItem key={format} value={format}>
                            <div className="flex items-center gap-2">
                              {getFormatIcon(format)}
                              <span>{FORMAT_CONFIG[format].name}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {formatConfig && (
                      <FormDescription>{formatConfig.description}</FormDescription>
                    )}
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Report Options */}
            <div className="space-y-4">
              <h4 className="text-sm font-medium flex items-center gap-2">
                <Settings className="h-4 w-4" />
                Opsi Laporan
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="includeSummary"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Sertakan Ringkasan</FormLabel>
                        <FormDescription>
                          Tambahkan ringkasan statistik
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="includeDetails"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Sertakan Detail</FormLabel>
                        <FormDescription>
                          Tampilkan data detail lengkap
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="includeCharts"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Sertakan Grafik</FormLabel>
                        <FormDescription>
                          Tambahkan visualisasi data
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Advanced Filters Toggle */}
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium flex items-center gap-2">
                <Filter className="h-4 w-4" />
                Filter Laporan
              </h4>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
              >
                {showAdvancedFilters ? 'Sembunyikan' : 'Tampilkan'} Filter Lanjutan
              </Button>
            </div>

            {/* Basic Filters */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="startDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tanggal Mulai</FormLabel>
                    <FormControl>
                      <Input
                        type="date"
                        {...field}
                        value={field.value || ''}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="endDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tanggal Akhir</FormLabel>
                    <FormControl>
                      <Input
                        type="date"
                        {...field}
                        value={field.value || ''}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Advanced Filters */}
            {showAdvancedFilters && (
              <div className="space-y-4 p-4 border rounded-lg bg-muted/20">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="productId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Produk Spesifik</FormLabel>
                        <FormControl>
                          <ProductSelector
                            value={field.value}
                            onValueChange={field.onChange}
                            placeholder="Pilih produk (opsional)"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="supplierId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Supplier Spesifik</FormLabel>
                        <FormControl>
                          <SupplierSelector
                            value={field.value}
                            onValueChange={field.onChange}
                            placeholder="Pilih supplier (opsional)"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="location"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Lokasi</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Lokasi penyimpanan"
                            {...field}
                            value={field.value || ''}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="expiringSoonDays"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Hari Kedaluwarsa</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min="1"
                            max="365"
                            placeholder="30"
                            {...field}
                            value={field.value || ''}
                            onChange={(e) => field.onChange(Number.parseInt(e.target.value) || 30)}
                          />
                        </FormControl>
                        <FormDescription>
                          Produk yang akan kedaluwarsa dalam X hari
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Filter Checkboxes */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <FormField
                    control={form.control}
                    name="lowStockOnly"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>Hanya Stok Rendah</FormLabel>
                        </div>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="expiredOnly"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>Hanya Kedaluwarsa</FormLabel>
                        </div>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="expiringSoon"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>Akan Kedaluwarsa</FormLabel>
                        </div>
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            )}

            {/* Progress Tracking */}
            {isGenerating && (
              <div className="space-y-4 p-4 border rounded-lg bg-muted/50">
                <div className="flex items-center justify-between">
                  <h4 className="text-sm font-medium">Status Pembuatan Laporan</h4>
                  <div className="flex items-center gap-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span className="text-sm text-muted-foreground">
                      {generationStep === 'generating' && 'Membuat laporan...'}
                      {generationStep === 'downloading' && (
                        retryCount > 0 ? `Mengunduh laporan... (Percobaan ${retryCount + 1}/3)` : 'Mengunduh laporan...'
                      )}
                      {generationStep === 'complete' && 'Selesai!'}
                    </span>
                  </div>
                </div>

                {progress && (
                  <div className="space-y-2">
                    <Progress value={progress.progress || 0} className="w-full" />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>{progress.status || 'Memproses...'}</span>
                      {progress.estimatedTimeRemaining && (
                        <span>Estimasi: {progress.estimatedTimeRemaining}s</span>
                      )}
                    </div>
                  </div>
                )}

                {!progress && (
                  <div className="space-y-2">
                    <Progress value={generationStep === 'generating' ? 30 : generationStep === 'downloading' ? 80 : 100} className="w-full" />
                    <div className="text-xs text-muted-foreground">
                      {generationStep === 'generating' && 'Memproses data dan membuat laporan...'}
                      {generationStep === 'downloading' && (
                        retryCount > 0 ? 'Mencoba mengunduh ulang...' : 'Menyiapkan unduhan...'
                      )}
                      {generationStep === 'complete' && 'Laporan berhasil dibuat dan diunduh!'}
                    </div>
                  </div>
                )}

                {/* Show last error if retry is in progress */}
                {lastError && retryCount > 0 && (
                  <div className="text-xs text-orange-600 bg-orange-50 p-2 rounded">
                    Percobaan sebelumnya gagal: {lastError}
                  </div>
                )}
              </div>
            )}

            {/* Form Actions */}
            <div className="flex justify-end gap-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  if (!isGenerating) {
                    onOpenChange(false);
                    resetState();
                  }
                }}
                disabled={isGenerating}
              >
                {isGenerating ? 'Sedang Memproses...' : 'Batal'}
              </Button>
              <Button
                type="submit"
                disabled={isGenerating || generateReportMutation.isPending}
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {generationStep === 'generating' && 'Membuat Laporan...'}
                    {generationStep === 'downloading' && 'Mengunduh...'}
                    {generationStep === 'complete' && 'Selesai!'}
                  </>
                ) : (
                  <>
                    <Download className="mr-2 h-4 w-4" />
                    Buat Laporan
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
