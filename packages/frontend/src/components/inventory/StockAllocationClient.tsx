'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  ArrowLeft,
  Package,
  Search,
  AlertTriangle,
  CheckCircle,
  Clock,
  TrendingUp,
  TrendingDown,
  Activity,
  Eye,
  Play,
  RefreshCw,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';

import { AllocationMethod, StockAllocationData } from '@/types/stock-allocation';
import { ProductSelector } from '@/components/ui/product-selector';
import { StockAllocationPreview } from './StockAllocationPreview';
import { StockAllocationResults } from './StockAllocationResults';
import {
  usePreviewAllocation,
  useAllocateStock,
  useStockSummary,
  useValidateStockAvailability,
} from '@/hooks/useStockAllocation';
import { navigateBackToInventory } from '@/lib/utils/navigation';


// Form validation schema
const stockAllocationSchema = z.object({
  productId: z.string().min(1, 'Produk wajib dipilih'),
  requestedQuantity: z.number().min(1, 'Jumlah harus lebih dari 0'),
  method: z.nativeEnum(AllocationMethod, {
    required_error: 'Metode alokasi wajib dipilih',
  }),
  allowPartialAllocation: z.boolean(),
  nearExpiryWarningDays: z.number().min(0).max(365),
  reason: z.string().min(1, 'Alasan alokasi wajib diisi'),
  notes: z.string().optional(),
});

// Define the form data type explicitly to match our needs
type StockAllocationFormData = {
  productId: string;
  requestedQuantity: number;
  method: AllocationMethod;
  allowPartialAllocation: boolean;
  nearExpiryWarningDays: number;
  reason: string;
  notes?: string;
};

type AllocationStep = 'form' | 'preview' | 'results';

export function StockAllocationClient() {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState<AllocationStep>('form');
  const [selectedProductId, setSelectedProductId] = useState<string>('');
  const [previewData, setPreviewData] = useState<any>(null);
  const [allocationResults, setAllocationResults] = useState<any>(null);

  // Use the robust navigation utility
  const handleNavigateBack = () => {
    navigateBackToInventory(router);
  };

  // Form setup with proper type alignment
  const form = useForm<StockAllocationFormData>({
    resolver: zodResolver(stockAllocationSchema),
    defaultValues: {
      productId: '',
      requestedQuantity: 0, // Allow starting with 0 so user can clear field
      method: AllocationMethod.FEFO,
      allowPartialAllocation: true,
      nearExpiryWarningDays: 30,
      reason: '',
      notes: '',
    },
    mode: 'onChange',
  });

  // Mutations and queries
  const previewAllocationMutation = usePreviewAllocation();
  const allocateStockMutation = useAllocateStock();
  const validateStockMutation = useValidateStockAvailability();
  const { data: stockSummary, refetch: refetchStockSummary } = useStockSummary(selectedProductId);

  // Watch form values for real-time updates
  const watchedProductId = form.watch('productId');
  const watchedQuantity = form.watch('requestedQuantity');

  // Update selected product when form changes
  React.useEffect(() => {
    if (watchedProductId !== selectedProductId) {
      setSelectedProductId(watchedProductId);
    }
  }, [watchedProductId, selectedProductId]);

  // Validate stock availability when quantity or product changes
  React.useEffect(() => {
    if (watchedProductId && watchedQuantity > 0) {
      validateStockMutation.mutate({
        productId: watchedProductId,
        requestedQuantity: watchedQuantity,
      });
    }
  }, [watchedProductId, watchedQuantity]);

  // Transform form data to API format
  const transformFormDataToAPI = (formData: StockAllocationFormData): StockAllocationData => {
    return {
      productId: formData.productId,
      requestedQuantity: formData.requestedQuantity,
      method: formData.method,
      allowPartialAllocation: formData.allowPartialAllocation,
      nearExpiryWarningDays: formData.nearExpiryWarningDays,
      reason: formData.reason,
      notes: formData.notes,
      previewOnly: true, // For preview calls
    };
  };

  const handlePreview = async (data: StockAllocationFormData) => {
    try {
      const apiData = transformFormDataToAPI(data);
      const result = await previewAllocationMutation.mutateAsync(apiData);
      setPreviewData(result);
      setCurrentStep('preview');
    } catch (error) {
      // Error handling is done in the mutation hook
    }
  };

  const handleExecuteAllocation = async () => {
    if (!previewData) return;

    try {
      const formData = form.getValues();
      const apiData = transformFormDataToAPI(formData);
      // Remove previewOnly for actual execution
      const { previewOnly, ...executionData } = apiData;

      const result = await allocateStockMutation.mutateAsync(executionData);
      setAllocationResults(result);
      setCurrentStep('results');
    } catch (error) {
      // Error handling is done in the mutation hook
    }
  };

  const handleStartOver = () => {
    setCurrentStep('form');
    setPreviewData(null);
    setAllocationResults(null);
    form.reset();
    setSelectedProductId('');
  };

  const getStepIndicator = () => {
    const steps = [
      { key: 'form', label: 'Formulir', icon: Package },
      { key: 'preview', label: 'Preview', icon: Eye },
      { key: 'results', label: 'Hasil', icon: CheckCircle },
    ];

    return (
      <div className="flex items-center justify-center mb-8">
        {steps.map((step, index) => {
          const isActive = currentStep === step.key;
          const isCompleted =
            (step.key === 'form' && (currentStep === 'preview' || currentStep === 'results')) ||
            (step.key === 'preview' && currentStep === 'results');
          const Icon = step.icon;

          return (
            <div key={step.key} className="flex items-center">
              <div className={`flex items-center gap-2 px-4 py-2 rounded-lg ${isActive
                ? 'bg-primary text-primary-foreground'
                : isCompleted
                  ? 'bg-green-100 text-green-700'
                  : 'bg-muted text-muted-foreground'
                }`}>
                <Icon className="h-4 w-4" />
                <span className="text-sm font-medium">{step.label}</span>
              </div>
              {index < steps.length - 1 && (
                <div className={`w-8 h-0.5 mx-2 ${isCompleted ? 'bg-green-300' : 'bg-muted'
                  }`} />
              )}
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          onClick={handleNavigateBack}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Kembali
        </Button>
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Alokasi Stok</h1>
          <p className="text-muted-foreground">
            Alokasi stok menggunakan metode FIFO (First In First Out) atau FEFO (First Expired First Out)
          </p>
        </div>
      </div>

      {/* Step Indicator */}
      {getStepIndicator()}

      {/* Form Step */}
      {currentStep === 'form' && (
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handlePreview)} className="space-y-6">
            <div className="grid gap-6 lg:grid-cols-3">
              {/* Main Form */}
              <div className="lg:col-span-2 space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Package className="h-5 w-5" />
                      Informasi Alokasi
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* Product Selection */}
                    <FormField
                      control={form.control}
                      name="productId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Produk</FormLabel>
                          <FormControl>
                            <ProductSelector
                              value={field.value}
                              onValueChange={field.onChange}
                              placeholder="Pilih produk untuk dialokasikan..."
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Quantity Input */}
                    <FormField
                      control={form.control}
                      name="requestedQuantity"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Jumlah yang Diminta</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              placeholder="Masukkan jumlah..."
                              value={field.value === 0 ? '' : field.value}
                              onChange={(e) => {
                                const value = e.target.value;
                                if (value === '') {
                                  // Allow empty state - set to 0 but display as empty
                                  field.onChange(0);
                                } else {
                                  const numValue = parseInt(value);
                                  if (!isNaN(numValue) && numValue >= 0) {
                                    field.onChange(numValue);
                                  }
                                }
                              }}
                              onBlur={() => {
                                // On blur, if still empty (0), keep it as 0 for validation
                                field.onBlur();
                              }}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Allocation Method */}
                    <FormField
                      control={form.control}
                      name="method"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Metode Alokasi</FormLabel>
                          <FormControl>
                            <RadioGroup
                              onValueChange={field.onChange}
                              value={field.value}
                              className="grid grid-cols-1 sm:grid-cols-2 gap-4"
                            >
                              <Label htmlFor="fifo" className="cursor-pointer flex items-center space-x-2 border rounded-lg p-4">
                                <RadioGroupItem value={AllocationMethod.FIFO} id="fifo" />
                                <div className="flex-1">
                                  <div className="font-medium">
                                    FIFO (First In First Out)
                                  </div>
                                  <p className="text-sm font-normal text-muted-foreground">
                                    Menggunakan stok yang diterima lebih dulu
                                  </p>
                                </div>
                              </Label>
                              <Label htmlFor="fefo" className="cursor-pointer flex items-center space-x-2 border rounded-lg p-4">
                                <RadioGroupItem value={AllocationMethod.FEFO} id="fefo" />
                                <div className="flex-1">
                                  <div className="font-medium">
                                    FEFO (First Expired First Out)
                                  </div>
                                  <p className="text-sm font-normal text-muted-foreground">
                                    Menggunakan stok yang akan kedaluwarsa lebih dulu
                                  </p>
                                </div>
                              </Label>
                            </RadioGroup>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <Separator />

                    {/* Reason */}
                    <FormField
                      control={form.control}
                      name="reason"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Alasan Alokasi</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Contoh: Penjualan, Transfer, Konsumsi internal..."
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Notes */}
                    <FormField
                      control={form.control}
                      name="notes"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Catatan (Opsional)</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Catatan tambahan..."
                              className="min-h-[80px]"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>
              </div>

              {/* Sidebar - Stock Information */}
              <div className="space-y-6">
                {/* Stock Summary */}
                {selectedProductId && stockSummary && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center justify-between">
                        <span>Ringkasan Stok</span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => refetchStockSummary()}
                        >
                          <RefreshCw className="h-4 w-4" />
                        </Button>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid gap-3">
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">Total Tersedia:</span>
                          <span className="font-medium">
                            {(stockSummary.totalAvailable || 0).toLocaleString('id-ID')} {stockSummary.unit?.abbreviation || stockSummary.unit?.name || 'unit'}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">Jumlah Batch:</span>
                          <span className="font-medium">
                            {stockSummary.batchCount || 0} ({(stockSummary.totalAvailable || 0).toLocaleString('id-ID')} {stockSummary.unit?.abbreviation || stockSummary.unit?.name || 'unit'})
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">Hampir Kedaluwarsa:</span>
                          <span className="font-medium text-orange-600">
                            {stockSummary.nearExpiryCount || 0} ({(stockSummary.nearExpiryQuantity || 0).toLocaleString('id-ID')} {stockSummary.unit?.abbreviation || stockSummary.unit?.name || 'unit'})
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">Kedaluwarsa:</span>
                          <span className="font-medium text-red-600">
                            {stockSummary.expiredCount || 0} ({(stockSummary.expiredQuantity || 0).toLocaleString('id-ID')} {stockSummary.unit?.abbreviation || stockSummary.unit?.name || 'unit'})
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Stock Availability Status */}
                {validateStockMutation.data && (
                  <Card>
                    <CardHeader>
                      <CardTitle>Status Ketersediaan</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="flex items-center gap-2">
                          {validateStockMutation.data.isAvailable ? (
                            <>
                              <CheckCircle className="h-4 w-4 text-green-600" />
                              <span className="text-sm text-green-600 font-medium">Stok Tersedia</span>
                            </>
                          ) : (
                            <>
                              <AlertTriangle className="h-4 w-4 text-red-600" />
                              <span className="text-sm text-red-600 font-medium">Stok Tidak Mencukupi</span>
                            </>
                          )}
                        </div>

                        <div className="text-sm space-y-1">
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Diminta:</span>
                            <span>{validateStockMutation.data.requestedQuantity}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Tersedia:</span>
                            <span>{validateStockMutation.data.totalAvailable}</span>
                          </div>
                          {validateStockMutation.data.shortfall > 0 && (
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">Kekurangan:</span>
                              <span className="text-red-600">{validateStockMutation.data.shortfall}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>

            {/* Form Actions */}
            <div className="flex justify-end gap-4">
              <Button
                type="button"
                variant="outline"
                onClick={handleNavigateBack}
              >
                Batal
              </Button>
              <Button
                type="submit"
                disabled={previewAllocationMutation.isPending || !form.formState.isValid}
              >
                {previewAllocationMutation.isPending ? (
                  <>
                    <Activity className="mr-2 h-4 w-4 animate-spin" />
                    Memuat Preview...
                  </>
                ) : (
                  <>
                    <Eye className="mr-2 h-4 w-4" />
                    Preview Alokasi
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      )}

      {/* Preview Step */}
      {currentStep === 'preview' && previewData && (
        <StockAllocationPreview
          previewData={previewData}
          formData={form.getValues()}
          onBack={() => setCurrentStep('form')}
          onExecute={handleExecuteAllocation}
          isExecuting={allocateStockMutation.isPending}
        />
      )}

      {/* Results Step */}
      {currentStep === 'results' && allocationResults && (
        <StockAllocationResults
          results={allocationResults}
          onStartOver={handleStartOver}
          onBackToInventory={() => router.push('/dashboard/inventory')}
        />
      )}
    </div>
  );
}
