import { Skeleton } from '@/components/ui/skeleton';
import { Separator } from '@/components/ui/separator';

export function SiteHeaderSkeleton() {
  return (
    <header className="flex h-(--header-height) shrink-0 items-center gap-2 border-b transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-(--header-height)">
      <div className="flex w-full items-center gap-1 px-4 lg:gap-2 lg:px-6">
        {/* Sidebar trigger skeleton */}
        <Skeleton className="h-6 w-6 -ml-1" />
        
        <Separator
          orientation="vertical"
          className="mx-2 data-[orientation=vertical]:h-4"
        />
        
        {/* Time display skeleton */}
        <div className="flex-1">
          <Skeleton className="h-4 w-32" />
        </div>
        
        <div className="ml-auto flex items-center gap-2">
          {/* Theme toggle skeleton */}
          <Skeleton className="h-10 w-10 rounded-md" />
          
          {/* Notifications skeleton */}
          <Skeleton className="h-10 w-10 rounded-md" />
          
          {/* User avatar skeleton */}
          <Skeleton className="h-8 w-8 rounded-full" />
        </div>
      </div>
    </header>
  );
}
