'use client';

import React from 'react';
import {
  ColumnDef,
  flexRender,
  useReactTable,
  getCoreRowModel,
  SortingState,
  VisibilityState
} from '@tanstack/react-table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Skeleton } from '@/components/ui/skeleton';
import { AlertTriangle, Clock, Eye, Package } from 'lucide-react';
import { ExpiryTrackingItem, BatchManagementQueryParams } from '@/types/batch-management';
import { useExpiryTracking } from '@/hooks/useBatchManagement';
import { formatDate } from '@/lib/utils';

interface ExpiryTrackingTableProps {
  query: BatchManagementQueryParams;
  onQueryChange: (query: BatchManagementQueryParams) => void;
  onView?: (item: ExpiryTrackingItem) => void;
  onAction?: (item: ExpiryTrackingItem, action: 'dispose' | 'extend' | 'transfer') => void;
}

export function ExpiryTrackingTable({
  query,
  onQueryChange,
  onView,
  onAction,
}: ExpiryTrackingTableProps) {
  const { data, isLoading, error } = useExpiryTracking(query);

  const getStatusBadge = (status: string, daysUntilExpiry: number) => {
    const variants: Record<string, any> = {
      ACTIVE: 'default',
      EXPIRING_SOON: 'secondary',
      EXPIRED: 'destructive',
    };

    const icons: Record<string, any> = {
      ACTIVE: <Package className="h-3 w-3" />,
      EXPIRING_SOON: <Clock className="h-3 w-3" />,
      EXPIRED: <AlertTriangle className="h-3 w-3" />,
    };

    const labels: Record<string, string> = {
      ACTIVE: 'Aktif',
      EXPIRING_SOON: 'Akan Kedaluwarsa',
      EXPIRED: 'Kedaluwarsa',
    };

    return (
      <Badge variant={variants[status] || 'secondary'} className="gap-1">
        {icons[status]}
        {labels[status] || status}
      </Badge>
    );
  };

  const getPriorityBadge = (priority: string) => {
    const variants: Record<string, any> = {
      HIGH: 'destructive',
      MEDIUM: 'secondary',
      LOW: 'outline',
    };

    const labels: Record<string, string> = {
      HIGH: 'Tinggi',
      MEDIUM: 'Sedang',
      LOW: 'Rendah',
    };

    return (
      <Badge variant={variants[priority] || 'secondary'}>
        {labels[priority] || priority}
      </Badge>
    );
  };

  const getDaysUntilExpiryDisplay = (days: number) => {
    if (days < 0) {
      return (
        <span className="text-red-600 font-medium">
          Kedaluwarsa {Math.abs(days)} hari lalu
        </span>
      );
    } else if (days === 0) {
      return (
        <span className="text-red-600 font-medium">
          Kedaluwarsa hari ini
        </span>
      );
    } else if (days <= 7) {
      return (
        <span className="text-red-500 font-medium">
          {days} hari lagi
        </span>
      );
    } else if (days <= 30) {
      return (
        <span className="text-yellow-600 font-medium">
          {days} hari lagi
        </span>
      );
    } else {
      return (
        <span className="text-green-600">
          {days} hari lagi
        </span>
      );
    }
  };

  const columns: ColumnDef<ExpiryTrackingItem>[] = [
    {
      accessorKey: 'batchNumber',
      header: 'Batch Number',
      cell: ({ row }) => (
        <div className="font-medium">
          {row.getValue('batchNumber')}
        </div>
      ),
    },
    {
      accessorKey: 'productName',
      header: 'Produk',
      cell: ({ row }) => (
        <div className="max-w-[200px] truncate">
          {row.getValue('productName')}
        </div>
      ),
    },
    {
      accessorKey: 'supplierName',
      header: 'Supplier',
      cell: ({ row }) => (
        <div className="max-w-[150px] truncate">
          {row.getValue('supplierName') || '-'}
        </div>
      ),
    },
    {
      accessorKey: 'expiryDate',
      header: 'Tanggal Kedaluwarsa',
      cell: ({ row }) => formatDate(row.getValue('expiryDate')),
    },
    {
      accessorKey: 'daysUntilExpiry',
      header: 'Sisa Waktu',
      cell: ({ row }) => getDaysUntilExpiryDisplay(row.getValue('daysUntilExpiry')),
    },
    {
      accessorKey: 'quantityOnHand',
      header: 'Stok',
      cell: ({ row }) => (
        <div className="text-right font-medium">
          {row.getValue('quantityOnHand')}
        </div>
      ),
    },
    {
      accessorKey: 'location',
      header: 'Lokasi',
      cell: ({ row }) => (
        <div className="max-w-[100px] truncate">
          {row.getValue('location') || '-'}
        </div>
      ),
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }) => getStatusBadge(
        row.getValue('status'),
        row.getValue('daysUntilExpiry')
      ),
    },
    {
      accessorKey: 'priority',
      header: 'Prioritas',
      cell: ({ row }) => getPriorityBadge(row.getValue('priority')),
    },
    {
      id: 'actions',
      header: 'Aksi',
      cell: ({ row }) => (
        <div className="flex items-center gap-1">
          {onView && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onView(row.original)}
            >
              <Eye className="h-4 w-4" />
            </Button>
          )}
          {onAction && row.original.status === 'EXPIRING_SOON' && (
            <>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onAction(row.original, 'extend')}
                title="Perpanjang"
              >
                <Clock className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onAction(row.original, 'transfer')}
                title="Transfer"
              >
                <Package className="h-4 w-4" />
              </Button>
            </>
          )}
          {onAction && row.original.status === 'EXPIRED' && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onAction(row.original, 'dispose')}
              title="Buang"
            >
              <AlertTriangle className="h-4 w-4" />
            </Button>
          )}
        </div>
      ),
    },
  ];

  if (error) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <p className="text-muted-foreground">Gagal memuat data tracking kedaluwarsa</p>
            <Button
              onClick={() => window.location.reload()}
              variant="outline"
              className="mt-2"
            >
              Coba Lagi
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});

  const table = useReactTable({
    data: data?.data || [],
    columns,
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    state: {
      sorting,
      columnVisibility,
    },
    manualSorting: true,
    manualFiltering: true,
    manualPagination: true,
  });

  return (
    <Card>
      <CardHeader>
        <CardTitle>Tracking Kedaluwarsa Batch</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {isLoading ? (
                Array.from({ length: query.limit || 10 }).map((_, index) => (
                  <TableRow key={index}>
                    {columns.map((_, colIndex) => (
                      <TableCell key={colIndex}>
                        <Skeleton className="h-4 w-full" />
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    key={row.id}
                    className={onView ? 'cursor-pointer hover:bg-muted/50' : ''}
                    onClick={() => onView?.(row.original)}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24 text-center">
                    Tidak ada data tracking kedaluwarsa.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
}
