'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Package,
  CheckCircle,
  Clock,
  Shield,
  AlertTriangle,
  TrendingUp,
  TrendingDown,
  Activity
} from 'lucide-react';
import { useBatchManagementStats } from '@/hooks/useBatchManagement';

interface BatchManagementStatsCardsProps {
  period?: string;
}

export function BatchManagementStatsCards({ period }: BatchManagementStatsCardsProps) {
  const { data: stats, isLoading, error } = useBatchManagementStats(period);

  if (error) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i}>
            <CardContent className="flex items-center justify-center py-8">
              <div className="text-center">
                <AlertTriangle className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-sm text-muted-foreground">Gagal memuat data</p>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (isLoading || !stats) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-4" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-16 mb-2" />
              <Skeleton className="h-3 w-32" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const formatNumber = (num: number) => num.toLocaleString('id-ID');
  const formatPercentage = (num: number) => `${num.toFixed(1)}%`;

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {/* Total Batches */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Batch</CardTitle>
          <Package className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatNumber(stats.totalBatches)}</div>
          <p className="text-xs text-muted-foreground">
            <span className="text-green-600">+{stats.recentActivity}</span> dalam {period || '30'} hari terakhir
          </p>
        </CardContent>
      </Card>

      {/* Active Batches */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Batch Aktif</CardTitle>
          <CheckCircle className="h-4 w-4 text-green-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-600">{formatNumber(stats.activeBatches)}</div>
          <p className="text-xs text-muted-foreground">
            {formatPercentage((stats.activeBatches / stats.totalBatches) * 100)} dari total
          </p>
        </CardContent>
      </Card>

      {/* Expiring Soon */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Akan Kedaluwarsa</CardTitle>
          <Clock className="h-4 w-4 text-orange-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-orange-600">{formatNumber(stats.expiringSoon)}</div>
          <p className="text-xs text-muted-foreground">
            Dalam 90 hari ke depan
          </p>
        </CardContent>
      </Card>

      {/* BPOM Compliant */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">BPOM Compliant</CardTitle>
          <Shield className="h-4 w-4 text-blue-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-blue-600">{formatNumber(stats.bpomCompliant)}</div>
          <p className="text-xs text-muted-foreground">
            {formatPercentage(stats.validationSuccessRate)} validation success rate
          </p>
        </CardContent>
      </Card>

      {/* Validation Success Rate */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Tingkat Validasi</CardTitle>
          <Activity className="h-4 w-4 text-purple-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-purple-600">
            {formatPercentage(stats.validationSuccessRate)}
          </div>
          <p className="text-xs text-muted-foreground">
            Validasi berhasil
          </p>
        </CardContent>
      </Card>

      {/* Validation Errors */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Error Validasi</CardTitle>
          <AlertTriangle className="h-4 w-4 text-red-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-red-600">{formatNumber(stats.validationErrors)}</div>
          <p className="text-xs text-muted-foreground">
            Memerlukan perhatian
          </p>
        </CardContent>
      </Card>

      {/* Expired Batches */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Batch Kedaluwarsa</CardTitle>
          <Clock className="h-4 w-4 text-red-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-red-600">{formatNumber(stats.expired)}</div>
          <p className="text-xs text-muted-foreground">
            Perlu ditangani segera
          </p>
        </CardContent>
      </Card>

      {/* Compliance Trend */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Tren Compliance</CardTitle>
          {stats.validationSuccessRate >= 90 ? (
            <TrendingUp className="h-4 w-4 text-green-500" />
          ) : (
            <TrendingDown className="h-4 w-4 text-red-500" />
          )}
        </CardHeader>
        <CardContent>
          <div className={`text-2xl font-bold ${stats.validationSuccessRate >= 90 ? 'text-green-600' : 'text-red-600'}`}>
            {stats.validationSuccessRate >= 90 ? '+' : '-'}{Math.abs(stats.validationSuccessRate - 90).toFixed(1)}%
          </div>
          <p className="text-xs text-muted-foreground">
            Dari target 90%
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
