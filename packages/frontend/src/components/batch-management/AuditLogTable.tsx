'use client';

import React from 'react';
import {
  ColumnDef,
  flexRender,
  useReactTable,
  getCoreRowModel,
  SortingState,
  VisibilityState
} from '@tanstack/react-table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Skeleton } from '@/components/ui/skeleton';
import {
  CheckCircle,
  XCircle,
  AlertTriangle,
  Eye,
  FileText,
  User,
  Calendar,
  Activity
} from 'lucide-react';
import { BatchAuditLogItem, BatchManagementQueryParams } from '@/types/batch-management';
import { useBatchAuditLogs } from '@/hooks/useBatchManagement';
import { formatDate, formatDateTime } from '@/lib/utils';

interface AuditLogTableProps {
  query: BatchManagementQueryParams;
  onQueryChange: (query: BatchManagementQueryParams) => void;
  onView?: (log: BatchAuditLogItem) => void;
  onViewDetails?: (log: BatchAuditLogItem) => void;
}

export function AuditLogTable({
  query,
  onQueryChange,
  onView,
  onViewDetails,
}: AuditLogTableProps) {
  const { data, isLoading, error } = useBatchAuditLogs(query);

  const getStatusBadge = (status: string) => {
    const variants: Record<string, any> = {
      SUCCESS: 'default',
      FAILED: 'destructive',
      WARNING: 'secondary',
    };

    const icons: Record<string, any> = {
      SUCCESS: <CheckCircle className="h-3 w-3" />,
      FAILED: <XCircle className="h-3 w-3" />,
      WARNING: <AlertTriangle className="h-3 w-3" />,
    };

    const labels: Record<string, string> = {
      SUCCESS: 'Berhasil',
      FAILED: 'Gagal',
      WARNING: 'Peringatan',
    };

    return (
      <Badge variant={variants[status] || 'secondary'} className="gap-1">
        {icons[status]}
        {labels[status] || status}
      </Badge>
    );
  };

  const getActionBadge = (action: string) => {
    const labels: Record<string, string> = {
      CREATED: 'Dibuat',
      UPDATED: 'Diperbarui',
      VALIDATED: 'Divalidasi',
      REJECTED: 'Ditolak',
      SUBSTITUTED: 'Disubstitusi',
      EXPIRED: 'Kedaluwarsa',
      RECALLED: 'Ditarik',
      FORMAT_VALIDATION: 'Validasi Format',
      UNIQUENESS_CHECK: 'Cek Keunikan',
      GOODS_RECEIPT: 'Penerimaan Barang',
      INVENTORY_CREATED: 'Inventori Dibuat',
      STOCK_MOVEMENT: 'Pergerakan Stok',
    };

    const variants: Record<string, any> = {
      CREATED: 'default',
      UPDATED: 'secondary',
      VALIDATED: 'default',
      REJECTED: 'destructive',
      SUBSTITUTED: 'secondary',
      EXPIRED: 'destructive',
      RECALLED: 'destructive',
      COMPLIANCE_CHECK: 'outline',
      FORMAT_VALIDATION: 'outline',
      UNIQUENESS_CHECK: 'outline',
      GOODS_RECEIPT: 'default',
      INVENTORY_CREATED: 'default',
      STOCK_MOVEMENT: 'secondary',
    };

    return (
      <Badge variant={variants[action] || 'secondary'}>
        {labels[action] || action}
      </Badge>
    );
  };

  const columns: ColumnDef<BatchAuditLogItem>[] = [
    {
      accessorKey: 'createdAt',
      header: 'Waktu',
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <Calendar className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm">
            {formatDateTime(row.getValue('createdAt'))}
          </span>
        </div>
      ),
    },
    {
      accessorKey: 'batchNumber',
      header: 'Batch Number',
      cell: ({ row }) => (
        <div className="font-medium">
          {row.getValue('batchNumber')}
        </div>
      ),
    },
    {
      accessorKey: 'action',
      header: 'Aksi',
      cell: ({ row }) => getActionBadge(row.getValue('action')),
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }) => getStatusBadge(row.getValue('status')),
    },
    {
      accessorKey: 'productName',
      header: 'Produk',
      cell: ({ row }) => (
        <div className="max-w-[150px] truncate">
          {row.getValue('productName') || '-'}
        </div>
      ),
    },
    {
      accessorKey: 'userName',
      header: 'User',
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <User className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm">
            {row.getValue('userName') || 'System'}
          </span>
        </div>
      ),
    },
    {
      accessorKey: 'referenceNumber',
      header: 'Referensi',
      cell: ({ row }) => {
        const refNumber = row.getValue('referenceNumber') as string;
        const refType = row.original.referenceType;

        if (!refNumber) return '-';

        return (
          <div className="text-sm">
            <div className="font-medium">{refNumber}</div>
            {refType && (
              <div className="text-muted-foreground text-xs">
                {refType}
              </div>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'bpomCompliant',
      header: 'BPOM',
      cell: ({ row }) => {
        const compliant = row.getValue('bpomCompliant') as boolean;
        if (compliant === null || compliant === undefined) return '-';

        return compliant ? (
          <Badge variant="default" className="gap-1">
            <CheckCircle className="h-3 w-3" />
            Ya
          </Badge>
        ) : (
          <Badge variant="destructive" className="gap-1">
            <XCircle className="h-3 w-3" />
            Tidak
          </Badge>
        );
      },
    },
    {
      accessorKey: 'message',
      header: 'Pesan',
      cell: ({ row }) => {
        const message = row.getValue('message') as string;
        const errorMessage = row.original.errorMessage;

        const displayMessage = errorMessage || message;

        if (!displayMessage) return '-';

        return (
          <div className="max-w-[200px] truncate text-sm" title={displayMessage}>
            {displayMessage}
          </div>
        );
      },
    },
    {
      id: 'actions',
      header: 'Aksi',
      cell: ({ row }) => (
        <div className="flex items-center gap-1">
          {onView && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onView(row.original)}
              title="Lihat Detail"
            >
              <Eye className="h-4 w-4" />
            </Button>
          )}
          {onViewDetails && row.original.details && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onViewDetails(row.original)}
              title="Lihat Detail Lengkap"
            >
              <FileText className="h-4 w-4" />
            </Button>
          )}
        </div>
      ),
    },
  ];

  if (error) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <p className="text-muted-foreground">Gagal memuat audit log</p>
            <Button
              onClick={() => window.location.reload()}
              variant="outline"
              className="mt-2"
            >
              Coba Lagi
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});

  const table = useReactTable({
    data: data?.data || [],
    columns,
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    state: {
      sorting,
      columnVisibility,
    },
    manualSorting: true,
    manualFiltering: true,
    manualPagination: true,
  });

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Activity className="h-5 w-5" />
          Audit Log Batch Management
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {isLoading ? (
                Array.from({ length: query.limit || 10 }).map((_, index) => (
                  <TableRow key={index}>
                    {columns.map((_, colIndex) => (
                      <TableCell key={colIndex}>
                        <Skeleton className="h-4 w-full" />
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    key={row.id}
                    className={onView ? 'cursor-pointer hover:bg-muted/50' : ''}
                    onClick={() => onView?.(row.original)}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24 text-center">
                    Tidak ada audit log.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
}
