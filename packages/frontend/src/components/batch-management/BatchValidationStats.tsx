'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import {
  CheckCircle,
  XCircle,
  AlertTriangle,
  Shield,
  FileCheck,
  Zap,
  Clock,
  TrendingUp,
  TrendingDown
} from 'lucide-react';
import { useBatchManagementStats } from '@/hooks/useBatchManagement';

interface ValidationStatsData {
  totalValidations: number;
  successfulValidations: number;
  failedValidations: number;
  warningValidations: number;
  formatValidationRate: number;
  uniquenessValidationRate: number;
  bpomComplianceRate: number;
  dateAlignmentRate: number;
  trend: {
    direction: 'up' | 'down' | 'stable';
    percentage: number;
  };
}

interface BatchValidationStatsProps {
  detailed?: boolean;
  className?: string;
}

export function BatchValidationStats({ detailed = false, className }: BatchValidationStatsProps) {
  const { data: batchStats, isLoading, error } = useBatchManagementStats();

  // Calculate validation stats from batch management stats
  const stats: ValidationStatsData = batchStats ? {
    totalValidations: batchStats.totalBatches || 0,
    successfulValidations: batchStats.activeBatches || 0,
    failedValidations: batchStats.validationErrors || 0,
    warningValidations: batchStats.expiringSoon || 0,
    formatValidationRate: batchStats.validationSuccessRate || 0,
    uniquenessValidationRate: 98.7, // This would come from a separate API endpoint
    bpomComplianceRate: batchStats.validationSuccessRate || 0,
    dateAlignmentRate: 96.1, // This would come from a separate API endpoint
    trend: {
      direction: (batchStats.validationSuccessRate || 0) >= 90 ? 'up' : 'down',
      percentage: Math.abs((batchStats.validationSuccessRate || 0) - 90),
    },
  } : {
    totalValidations: 0,
    successfulValidations: 0,
    failedValidations: 0,
    warningValidations: 0,
    formatValidationRate: 0,
    uniquenessValidationRate: 0,
    bpomComplianceRate: 0,
    dateAlignmentRate: 0,
    trend: { direction: 'stable', percentage: 0 },
  };

  const successRate = stats.totalValidations > 0 ? (stats.successfulValidations / stats.totalValidations) * 100 : 0;
  const failureRate = stats.totalValidations > 0 ? (stats.failedValidations / stats.totalValidations) * 100 : 0;
  const warningRate = stats.totalValidations > 0 ? (stats.warningValidations / stats.totalValidations) * 100 : 0;

  const validationRules = [
    {
      name: 'Format Validation',
      rate: stats.formatValidationRate,
      icon: FileCheck,
      description: 'Validasi format batch number sesuai standar',
    },
    {
      name: 'Uniqueness Check',
      rate: stats.uniquenessValidationRate,
      icon: Zap,
      description: 'Pengecekan keunikan batch number',
    },
    {
      name: 'BPOM Batch Validation',
      rate: stats.bpomComplianceRate,
      icon: Shield,
      description: 'Validasi batch number sesuai standar BPOM',
    },
    {
      name: 'Date Alignment',
      rate: stats.dateAlignmentRate,
      icon: Clock,
      description: 'Kesesuaian tanggal produksi dan kedaluwarsa',
    },
  ];

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <AlertTriangle className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
            <p className="text-sm text-muted-foreground">Gagal memuat statistik validasi</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <Skeleton className="h-6 w-48" />
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-3 gap-4">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="space-y-2 text-center">
                <Skeleton className="h-8 w-16 mx-auto" />
                <Skeleton className="h-3 w-12 mx-auto" />
                <Skeleton className="h-4 w-10 mx-auto" />
              </div>
            ))}
          </div>
          {detailed && (
            <div className="space-y-3">
              {Array.from({ length: 4 }).map((_, i) => (
                <div key={i} className="space-y-2">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-2 w-full" />
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">Statistik Validasi Batch</CardTitle>
          <div className="flex items-center gap-1">
            {stats.trend.direction === 'up' ? (
              <TrendingUp className="h-4 w-4 text-green-500" />
            ) : stats.trend.direction === 'down' ? (
              <TrendingDown className="h-4 w-4 text-red-500" />
            ) : null}
            <span className={`text-sm font-medium ${stats.trend.direction === 'up' ? 'text-green-600' :
              stats.trend.direction === 'down' ? 'text-red-600' :
                'text-muted-foreground'
              }`}>
              {stats.trend.direction === 'up' ? '+' : stats.trend.direction === 'down' ? '-' : ''}
              {stats.trend.percentage}%
            </span>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Overall Stats */}
        <div className="grid grid-cols-3 gap-4 text-center">
          <div className="space-y-2">
            <div className="flex items-center justify-center">
              <CheckCircle className="h-5 w-5 text-green-500 mr-1" />
              <span className="text-2xl font-bold text-green-600">
                {stats.successfulValidations.toLocaleString('id-ID')}
              </span>
            </div>
            <div className="text-xs text-muted-foreground">Berhasil</div>
            <div className="text-sm font-medium text-green-600">
              {successRate.toFixed(1)}%
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-center">
              <AlertTriangle className="h-5 w-5 text-yellow-500 mr-1" />
              <span className="text-2xl font-bold text-yellow-600">
                {stats.warningValidations.toLocaleString('id-ID')}
              </span>
            </div>
            <div className="text-xs text-muted-foreground">Warning</div>
            <div className="text-sm font-medium text-yellow-600">
              {warningRate.toFixed(1)}%
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-center">
              <XCircle className="h-5 w-5 text-red-500 mr-1" />
              <span className="text-2xl font-bold text-red-600">
                {stats.failedValidations.toLocaleString('id-ID')}
              </span>
            </div>
            <div className="text-xs text-muted-foreground">Gagal</div>
            <div className="text-sm font-medium text-red-600">
              {failureRate.toFixed(1)}%
            </div>
          </div>
        </div>

        {/* Validation Rules Performance */}
        {detailed && (
          <div className="space-y-4">
            <h4 className="font-medium text-sm">Performa Aturan Validasi</h4>
            {validationRules.map((rule, index) => {
              const IconComponent = rule.icon;
              return (
                <div key={index} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <IconComponent className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium">{rule.name}</span>
                    </div>
                    <Badge variant={rule.rate >= 95 ? 'default' : rule.rate >= 90 ? 'secondary' : 'destructive'}>
                      {rule.rate.toFixed(1)}%
                    </Badge>
                  </div>
                  <Progress value={rule.rate} className="h-2" />
                  {detailed && (
                    <p className="text-xs text-muted-foreground">{rule.description}</p>
                  )}
                </div>
              );
            })}
          </div>
        )}

        {/* Quick Summary for non-detailed view */}
        {!detailed && (
          <div className="space-y-3">
            <div className="flex items-center justify-between text-sm">
              <span>Success Rate</span>
              <span className="font-medium text-green-600">{successRate.toFixed(1)}%</span>
            </div>
            <Progress value={successRate} className="h-2" />

            <div className="grid grid-cols-2 gap-4 text-xs text-muted-foreground">
              <div>Total Validasi: {stats.totalValidations.toLocaleString('id-ID')}</div>
              <div>BPOM Compliance: {stats.bpomComplianceRate.toFixed(1)}%</div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
