import type React from 'react';
import { useState, useEffect } from 'react';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';

// Props for trigger-based usage (existing pattern)
type TriggerBasedProps = {
  children: React.ReactNode;
  variant?: 'destructive' | 'primary' | 'informative' | 'warning';
  disabled?: boolean;
  title?: string;
  description?: string;
  confirmText?: string;
  cancelText?: string;
  pendingText?: string;
  requireConfirmationText?: string;
  confirmationPlaceholder?: string;
  handler: () => void | Promise<void>;
  // Controlled props should not be present
  open?: never;
  onOpenChange?: never;
  onConfirm?: never;
  requiresInput?: never;
  inputPlaceholder?: never;
  multilineInput?: never;
};

// Props for controlled usage (new pattern)
type ControlledProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  description: string;
  confirmText?: string;
  cancelText?: string;
  onConfirm: (input?: string) => void | Promise<void>;
  variant?: 'destructive' | 'primary' | 'informative' | 'warning';
  requiresInput?: boolean;
  inputPlaceholder?: string;
  multilineInput?: boolean;
  // Trigger-based props should not be present
  children?: never;
  disabled?: never;
  pendingText?: never;
  requireConfirmationText?: never;
  confirmationPlaceholder?: never;
  handler?: never;
};

type AlertDialogWrapperProps = TriggerBasedProps | ControlledProps;

export function AlertDialogWrapper(props: AlertDialogWrapperProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [internalOpen, setInternalOpen] = useState(false);
  const [confirmationInput, setConfirmationInput] = useState('');

  // Determine if this is controlled or trigger-based usage
  const isControlled = 'open' in props;

  // Extract props based on usage pattern
  const {
    variant = 'destructive',
    title = isControlled ? props.title : 'Hapus',
    description = isControlled ? props.description : 'Apakah Anda yakin ingin menghapus ini? Tindakan ini tidak dapat dibatalkan.',
    confirmText = isControlled ? (props.confirmText || 'Konfirmasi') : 'Hapus',
    cancelText = isControlled ? (props.cancelText || 'Batal') : 'Batal',
  } = props;

  // For controlled usage
  const open = isControlled ? props.open : internalOpen;
  const onOpenChange = isControlled ? props.onOpenChange : setInternalOpen;

  // For trigger-based usage
  const children = isControlled ? undefined : props.children;
  const disabled = isControlled ? false : (props.disabled || false);
  const pendingText = isControlled ? 'Memproses...' : (props.pendingText || 'Menghapus...');
  const requireConfirmationText = isControlled ? undefined : props.requireConfirmationText;
  const confirmationPlaceholder = isControlled ? (props.inputPlaceholder || 'Masukkan input...') : (props.confirmationPlaceholder || 'Ketik untuk mengonfirmasi');
  const handler = isControlled ? undefined : props.handler;

  // For controlled input usage
  const requiresInput = isControlled ? props.requiresInput : false;
  const multilineInput = isControlled ? props.multilineInput : false;
  const onConfirm = isControlled ? props.onConfirm : undefined;

  const variantClass = {
    'destructive': {
      action: 'bg-red-600 hover:bg-red-700',
    },
    'primary': {
      action: 'bg-primary hover:bg-primary/90',
    },
    'informative': {
      action: 'bg-blue-600 hover:bg-blue-700',
    },
    'warning': {
      action: 'bg-orange-600 hover:bg-orange-700',
    },
  };

  // Reset confirmation input when dialog opens/closes
  useEffect(() => {
    if (open) {
      setConfirmationInput('');
    }
  }, [open]);

  const handleConfirm = async () => {
    try {
      setIsLoading(true);

      if (isControlled && onConfirm) {
        // For controlled usage, pass input if required
        await onConfirm(requiresInput ? confirmationInput : undefined);
      } else if (!isControlled && handler) {
        // For trigger-based usage
        await handler();
      }

      // Close dialog
      if (isControlled && onOpenChange) {
        onOpenChange(false);
      } else {
        setInternalOpen(false);
      }
    } catch (error) {
      console.error('Error in AlertDialogWrapper handler:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Check if confirmation text is required and matches (for trigger-based)
  // or if input is required and provided (for controlled)
  const isConfirmationValid = isControlled
    ? (!requiresInput || confirmationInput.trim().length > 0)
    : (!requireConfirmationText || confirmationInput === requireConfirmationText);
  const isDisabled = disabled || isLoading || !isConfirmationValid;

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      {!isControlled && children && (
        <AlertDialogTrigger asChild>
          {children}
        </AlertDialogTrigger>
      )}
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{title}</AlertDialogTitle>
          <AlertDialogDescription asChild>
            <div className="space-y-3">
              <div className="text-sm text-muted-foreground whitespace-pre-line">
                {description}
              </div>
              {!isControlled && requireConfirmationText && (
                <div className="text-sm text-muted-foreground">
                  Ketik <span className="font-mono font-semibold text-destructive">&apos;{requireConfirmationText}&apos;</span> untuk mengonfirmasi
                </div>
              )}
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>

        {((isControlled && requiresInput) || (!isControlled && requireConfirmationText)) && (
          <div className="px-6 pb-4">
            {isControlled && multilineInput ? (
              <Textarea
                placeholder={confirmationPlaceholder}
                value={confirmationInput}
                onChange={(e) => setConfirmationInput(e.target.value)}
                disabled={isLoading}
                className="min-h-[100px]"
              />
            ) : (
              <Input
                type="text"
                placeholder={confirmationPlaceholder}
                value={confirmationInput}
                onChange={(e) => setConfirmationInput(e.target.value)}
                className={!isControlled ? "border-destructive focus:border-destructive focus:ring-destructive" : ""}
                disabled={isLoading}
                autoComplete="off"
              />
            )}
          </div>
        )}

        <AlertDialogFooter>
          <AlertDialogCancel disabled={isLoading}>{cancelText}</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            className={variantClass[variant].action}
            disabled={isDisabled}
          >
            {isLoading ? pendingText : confirmText}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
