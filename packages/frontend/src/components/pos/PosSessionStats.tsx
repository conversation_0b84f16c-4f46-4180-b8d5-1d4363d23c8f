'use client';

import { Session } from 'next-auth';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Loader2, TrendingUp, DollarSign, CreditCard, Clock, Target } from 'lucide-react';
import { formatCurrency } from '@/lib/utils';
import { useSessionStatsById } from '@/hooks/useSession';
import { PaymentMethod } from '@/types/sales';
import { SessionData } from '@/types/session';

interface PosSessionStatsProps {
  user: Session['user'];
  sessionData: SessionData | null;
}

export function PosSessionStats({ user, sessionData }: PosSessionStatsProps) {
  const sessionStartTime = sessionData?.openedAt ? new Date(sessionData.openedAt).toISOString() : '';
  const sessionEndTime = sessionData?.closedAt ? new Date(sessionData.closedAt).toISOString() : undefined;

  // Use cashier ID from session data if available, fallback to user.id
  const cashierId = sessionData?.cashierId || user.id;

  const { data: stats, isLoading, error } = useSessionStatsById(cashierId, sessionStartTime, sessionEndTime);



  const getPaymentMethodLabel = (method: PaymentMethod) => {
    switch (method) {
      case PaymentMethod.CASH:
        return 'Tunai';
      case PaymentMethod.TRANSFER:
        return 'Transfer';
      case PaymentMethod.CREDIT:
        return 'Kredit';
      case PaymentMethod.GIRO:
        return 'Giro';
      default:
        return method;
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-6 w-6 animate-spin mr-2" />
        <span>Memuat statistik sesi...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64 text-muted-foreground">
        <span>Gagal memuat statistik sesi</span>
      </div>
    );
  }

  if (!stats) {
    return (
      <div className="flex items-center justify-center h-64 text-muted-foreground">
        <span>Tidak ada data statistik</span>
      </div>
    );
  }

  const sessionDuration = stats.firstSaleTime && stats.lastSaleTime
    ? Math.round((stats.lastSaleTime - stats.firstSaleTime) / (1000 * 60))
    : 0;

  const paymentMethods = Object.entries(stats.paymentMethodBreakdown);
  const totalPaymentAmount = paymentMethods.reduce((sum, [, data]) => sum + data.amount, 0);

  return (
    <div className="h-full overflow-auto p-4 space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Statistik Sesi</h3>
          <p className="text-sm text-muted-foreground">
            {sessionData ? (
              `Sesi dimulai: ${new Date(sessionData.openedAt).toLocaleString('id-ID')}`
            ) : (
              'Tidak ada sesi aktif'
            )}
          </p>
        </div>
        <Badge variant="outline" className="text-sm">
          {stats.totalSalesCount} transaksi
        </Badge>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Penjualan</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats.totalRevenue)}</div>
            <p className="text-xs text-muted-foreground">
              {stats.totalSalesCount} transaksi
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Rata-rata Transaksi</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats.averageTransactionValue)}</div>
            <p className="text-xs text-muted-foreground">
              per transaksi
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Durasi Aktif</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {sessionDuration > 60
                ? `${Math.floor(sessionDuration / 60)}j ${sessionDuration % 60}m`
                : `${sessionDuration}m`
              }
            </div>
            <p className="text-xs text-muted-foreground">
              waktu aktif
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Payment Method Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <CreditCard className="h-4 w-4" />
            Metode Pembayaran
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {paymentMethods.length === 0 ? (
            <p className="text-sm text-muted-foreground">Belum ada transaksi</p>
          ) : (
            paymentMethods.map(([method, data]) => {
              const percentage = totalPaymentAmount > 0 ? (data.amount / totalPaymentAmount) * 100 : 0;
              return (
                <div key={method} className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="font-medium">{getPaymentMethodLabel(method as PaymentMethod)}</span>
                    <div className="text-right">
                      <div className="font-medium">{formatCurrency(data.amount)}</div>
                      <div className="text-xs text-muted-foreground">{data.count} transaksi</div>
                    </div>
                  </div>
                  <Progress value={percentage} className="h-2" />
                  <div className="text-xs text-muted-foreground text-right">
                    {percentage.toFixed(1)}% dari total
                  </div>
                </div>
              );
            })
          )}
        </CardContent>
      </Card>

      {/* Hourly Activity */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Aktivitas per Jam
          </CardTitle>
        </CardHeader>
        <CardContent>
          {Object.keys(stats.hourlyBreakdown).length === 0 ? (
            <p className="text-sm text-muted-foreground">Belum ada aktivitas</p>
          ) : (
            <div className="space-y-2">
              {Object.entries(stats.hourlyBreakdown)
                .sort(([a], [b]) => parseInt(a) - parseInt(b))
                .map(([hour, data]) => {
                  const maxAmount = Math.max(...Object.values(stats.hourlyBreakdown).map(h => h.amount));
                  const percentage = maxAmount > 0 ? (data.amount / maxAmount) * 100 : 0;

                  return (
                    <div key={hour} className="flex items-center gap-3">
                      <div className="w-12 text-xs font-mono">
                        {hour.padStart(2, '0')}:00
                      </div>
                      <div className="flex-1">
                        <Progress value={percentage} className="h-2" />
                      </div>
                      <div className="w-20 text-right text-xs">
                        <div className="font-medium">{formatCurrency(data.amount)}</div>
                        <div className="text-muted-foreground">{data.count}x</div>
                      </div>
                    </div>
                  );
                })}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
