'use client';

import { Session } from 'next-auth';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Loader2, Receipt, User, Clock } from 'lucide-react';
import { formatCurrency } from '@/lib/utils';
import { useSessionSalesById } from '@/hooks/useSession';
import { PaymentMethod } from '@/types/sales';
import { SessionData } from '@/types/session';

interface PosSessionHistoryProps {
  user: Session['user'];
  sessionData: SessionData | null;
}

export function PosSessionHistory({ user, sessionData }: PosSessionHistoryProps) {
  const sessionStartTime = sessionData?.openedAt ? new Date(sessionData.openedAt).toISOString() : '';
  const sessionEndTime = sessionData?.closedAt ? new Date(sessionData.closedAt).toISOString() : undefined;

  // Use cashier ID from session data if available, fallback to user.id
  const cashierId = sessionData?.cashierId || user.id;

  const { data: salesData, isLoading, error } = useSessionSalesById(cashierId, sessionStartTime, sessionEndTime);



  const getPaymentMethodLabel = (method: PaymentMethod) => {
    switch (method) {
      case PaymentMethod.CASH:
        return 'Tunai';
      case PaymentMethod.TRANSFER:
        return 'Transfer';
      case PaymentMethod.CREDIT:
        return 'Kredit';
      case PaymentMethod.GIRO:
        return 'Giro';
      default:
        return method;
    }
  };

  const getPaymentMethodVariant = (method: PaymentMethod) => {
    switch (method) {
      case PaymentMethod.CASH:
        return 'default';
      case PaymentMethod.TRANSFER:
        return 'secondary';
      case PaymentMethod.CREDIT:
        return 'outline';
      case PaymentMethod.GIRO:
        return 'destructive';
      default:
        return 'outline';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-6 w-6 animate-spin mr-2" />
        <span>Memuat riwayat penjualan...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64 text-muted-foreground">
        <span>Gagal memuat riwayat penjualan</span>
      </div>
    );
  }

  const sales = salesData?.data || [];

  return (
    <div className="h-full flex flex-col">
      <div className="flex-none p-4 border-b">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold">Riwayat Penjualan</h3>
            <p className="text-sm text-muted-foreground">
              {sessionData ? (
                `Sesi dimulai: ${new Date(sessionData.openedAt).toLocaleString('id-ID')}`
              ) : (
                'Tidak ada sesi aktif'
              )}
            </p>
          </div>
          <Badge variant="outline" className="text-sm">
            {sales.length} transaksi
          </Badge>
        </div>
      </div>

      <div className="flex-1 min-h-0">
        <ScrollArea className="h-full">
          {sales.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-64 text-muted-foreground">
              <Receipt className="h-12 w-12 mb-4 opacity-50" />
              <p className="text-lg font-medium">Belum ada transaksi</p>
              <p className="text-sm">Transaksi akan muncul di sini setelah Anda melakukan penjualan</p>
            </div>
          ) : (
            <Table>
              <TableHeader className="sticky top-0 bg-background">
                <TableRow>
                  <TableHead>No. Transaksi</TableHead>
                  <TableHead>Waktu</TableHead>
                  <TableHead>Pelanggan</TableHead>
                  <TableHead>Metode</TableHead>
                  <TableHead className="text-right">Total</TableHead>
                  <TableHead className="text-center">Item</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {sales.map((sale) => (
                  <TableRow key={sale.id}>
                    <TableCell className="font-medium">
                      {sale.saleNumber}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1 text-sm">
                        <Clock className="h-3 w-3" />
                        {new Date(sale.saleDate).toLocaleTimeString('id-ID', {
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <User className="h-3 w-3" />
                        <span className="text-sm">
                          {sale.customer?.fullName || sale.customerName || 'Pelanggan Umum'}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={getPaymentMethodVariant(sale.paymentMethod)} className="text-xs">
                        {getPaymentMethodLabel(sale.paymentMethod)}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right font-medium">
                      {formatCurrency(sale.totalAmount)}
                    </TableCell>
                    <TableCell className="text-center">
                      <Badge variant="outline" className="text-xs">
                        {sale.saleItems.length}
                      </Badge>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </ScrollArea>
      </div>
    </div>
  );
}
