'use client';

import { useState, useEffect } from 'react';
import { signOut } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useTheme } from 'next-themes';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Sun,
  Moon,
  User,
  LogOut,
  Home,
  HelpCircle,
  Settings,
  Clock,
  Timer,
  Store,
  Menu,
  ShoppingCart,
  History,
  BarChart3,
} from 'lucide-react';
import Link from 'next/link';
import { PosTab } from './PosClient';

interface SessionData {
  openedAt: Date;
  cashierId: string;
  cashierName: string;
  initialAmount: number;
}

interface PosHeaderProps {
  user?: any;
  sessionData?: SessionData | null;
  onEndSession?: () => void;
  activeTab?: PosTab;
  onTabChange?: (tab: PosTab) => void;
  onShowHelp?: () => void;
}

export function PosHeader({
  user,
  sessionData,
  onEndSession,
  activeTab = 'cart',
  onTabChange,
  onShowHelp
}: PosHeaderProps) {
  const router = useRouter();
  const { setTheme, resolvedTheme } = useTheme();
  const [currentTime, setCurrentTime] = useState(new Date());
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000); // Update every second for session duration

    return () => clearInterval(timer);
  }, []);

  const getSessionDuration = () => {
    if (!sessionData?.openedAt) return '';

    const now = new Date();
    const start = new Date(sessionData.openedAt);
    const diffMs = now.getTime() - start.getTime();

    const hours = Math.floor(diffMs / (1000 * 60 * 60));
    const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

    if (hours > 0) {
      return `${hours}j ${minutes}m`;
    }
    return `${minutes}m`;
  };

  const handleEndSession = () => {
    if (confirm('Anda yakin ingin menutup sesi kasir ini?')) {
      onEndSession?.();
    }
  };

  const handleSignOut = async () => {
    await signOut({ redirect: false });
    router.push('/auth/login');
  };

  const toggleTheme = () => {
    if (resolvedTheme === 'dark') {
      setTheme('light');
    } else {
      setTheme('dark');
    }
  };

  const getUserDisplayName = () => {
    if (user?.firstName && user?.lastName) {
      return `${user.firstName} ${user.lastName}`;
    }
    return user?.name || user?.email || 'Kasir';
  };

  const getUserInitials = () => {
    if (user?.firstName && user?.lastName) {
      return `${user.firstName[0]}${user.lastName[0]}`.toUpperCase();
    }
    if (user?.name) {
      const names = user.name.split(' ');
      return names.length > 1
        ? `${names[0][0]}${names[1][0]}`.toUpperCase()
        : names[0][0].toUpperCase();
    }
    return user?.email?.[0]?.toUpperCase() || 'K';
  };

  return (
    <header className="flex h-16 shrink-0 items-center border-b bg-background px-4 lg:px-6">
      <div className="flex w-full items-center justify-between gap-4">
        {/* Left side - Logo, Time, and Dashboard */}
        <div className="flex items-center gap-4">
          {/* Logo and pharmacy name */}
          <div className="flex items-center gap-3">
            <div className="h-8 w-8 bg-primary rounded-lg flex items-center justify-center">
              <Store className="h-5 w-5 text-primary-foreground" />
            </div>
            <div className="font-bold text-lg">Apotek POS</div>
          </div>

          {/* Dashboard Button */}
          <Button
            variant="outline"
            size="sm"
            className="h-8 px-3 text-xs"
            asChild
          >
            <Link href="/dashboard">
              <Home className="h-3 w-3 mr-1" />
              Dashboard
            </Link>
          </Button>

          {/* Current Time & Date - Enhanced */}
          <div className="hidden md:flex flex-col items-start">
            <div className="text-lg font-bold leading-none text-primary">
              {currentTime.toLocaleTimeString('id-ID', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
              })}
            </div>
            <div className="text-xs text-muted-foreground">
              {currentTime.toLocaleDateString('id-ID', {
                weekday: 'short',
                day: 'numeric',
                month: 'short'
              })}
            </div>
          </div>


        </div>

        {/* Right side - Session Information and User Menu */}
        <div className="flex items-center gap-4">
          {/* POS Navigation Dropdown - only show when session is active */}
          {sessionData && onTabChange && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="h-8 px-3 text-xs">
                  <Menu className="h-3 w-3 mr-1" />
                  {activeTab === 'cart' && 'Keranjang Belanja'}
                  {activeTab === 'history' && 'Riwayat Penjualan'}
                  {activeTab === 'stats' && 'Statistik Sesi'}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuLabel>Navigasi POS</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => onTabChange('cart')}
                  className={activeTab === 'cart' ? 'bg-muted' : ''}
                >
                  <ShoppingCart className="mr-2 h-4 w-4" />
                  <span>Keranjang Belanja</span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => onTabChange('history')}
                  className={activeTab === 'history' ? 'bg-muted' : ''}
                >
                  <History className="mr-2 h-4 w-4" />
                  <span>Riwayat Penjualan</span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => onTabChange('stats')}
                  className={activeTab === 'stats' ? 'bg-muted' : ''}
                >
                  <BarChart3 className="mr-2 h-4 w-4" />
                  <span>Statistik Sesi</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}

          {/* Help Button */}
          {onShowHelp && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onShowHelp}
              className="h-8 w-8 p-0"
            >
              <HelpCircle className="h-4 w-4" />
            </Button>
          )}
          {/* Session Information with Tooltips */}
          {sessionData && (
            <TooltipProvider>
              <div className="flex items-center gap-3 px-3 py-2 bg-muted/50 rounded-lg border">
                <div className="flex items-center gap-2">
                  <Badge variant="secondary" className="text-xs px-2 py-0.5">
                    AKTIF
                  </Badge>

                  <Tooltip>
                    <TooltipTrigger asChild>
                      <span className="text-sm font-medium cursor-help">
                        {sessionData.cashierName}
                      </span>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Nama kasir yang sedang bertugas</p>
                    </TooltipContent>
                  </Tooltip>
                </div>

                <div className="flex items-center gap-3 text-xs text-muted-foreground">
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className="flex items-center gap-1 cursor-help">
                        <Clock className="h-3 w-3" />
                        <span>{new Date(sessionData.openedAt).toLocaleTimeString('id-ID', { hour: '2-digit', minute: '2-digit' })}</span>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Waktu sesi kasir dibuka</p>
                    </TooltipContent>
                  </Tooltip>

                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className="flex items-center gap-1 cursor-help">
                        <Timer className="h-3 w-3" />
                        <span>{getSessionDuration()}</span>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Durasi sesi kasir berjalan</p>
                    </TooltipContent>
                  </Tooltip>
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleEndSession}
                  className="h-7 text-xs px-3"
                >
                  Tutup Kasir
                </Button>
              </div>
            </TooltipProvider>
          )}

          {/* Theme Toggle */}
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleTheme}
            disabled={!mounted}
          >
            {!mounted ? (
              <div className="h-4 w-4" />
            ) : resolvedTheme === 'dark' ? (
              <Sun className="h-4 w-4" />
            ) : (
              <Moon className="h-4 w-4" />
            )}
          </Button>

          {/* User Profile */}
          {user && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-auto p-2 hover:bg-muted/50">
                  <div className="flex items-center gap-2">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={user?.avatar} />
                      <AvatarFallback className="text-xs">{getUserInitials()}</AvatarFallback>
                    </Avatar>
                    <div className="text-left hidden lg:block">
                      <p className="text-sm font-medium leading-none">{getUserDisplayName()}</p>
                      <p className="text-xs text-muted-foreground mt-0.5">{user.role}</p>
                    </div>
                  </div>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56" align="end" forceMount>
                <DropdownMenuLabel className="font-normal">
                  <div className="flex flex-col space-y-1">
                    <p className="text-sm font-medium leading-none">
                      {getUserDisplayName()}
                    </p>
                    <p className="text-xs leading-none text-muted-foreground">
                      {user?.email}
                    </p>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => router.push('/dashboard/profil')}>
                  <User className="mr-2 h-4 w-4" />
                  <span>Profil</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => router.push('/dashboard/pengaturan')}>
                  <Settings className="mr-2 h-4 w-4" />
                  <span>Pengaturan</span>
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <HelpCircle className="mr-2 h-4 w-4" />
                  <span>Bantuan</span>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleSignOut}>
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>Keluar</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      </div>
    </header>
  );
}