'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Trash2, 
  Plus, 
  Minus, 
  Percent,
  Package
} from 'lucide-react';
import { formatCurrency } from '@/lib/utils';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';

interface ProductUnit {
  id: string;
  name: string;
  sellingPrice: number;
}

interface CartItem {
  productId: string;
  productName: string;
  unitId: string;
  unitName: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  discountAmount: number;
  discountPercent: number;
  availableUnits: ProductUnit[];
}

interface PosCartItemProps {
  item: CartItem;
  index: number;
  onUpdateQuantity: (index: number, quantity: number) => void;
  onUpdateDiscount: (index: number, discountPercent: number) => void;
  onChangeUnit: (index: number, unitId: string) => void;
  onRemove: (index: number) => void;
}

export function PosCartItem({ 
  item, 
  index, 
  onUpdateQuantity, 
  onUpdateDiscount,
  onChangeUnit,
  onRemove 
}: PosCartItemProps) {
  const [isDiscountOpen, setIsDiscountOpen] = useState(false);
  const [isUnitOpen, setIsUnitOpen] = useState(false);
  
  const handleQuantityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newQuantity = parseInt(e.target.value, 10);
    if (!isNaN(newQuantity) && newQuantity >= 0) {
      onUpdateQuantity(index, newQuantity);
    }
  };

  const handleIncrement = () => {
    onUpdateQuantity(index, item.quantity + 1);
  };

  const handleDecrement = () => {
    if (item.quantity > 1) {
      onUpdateQuantity(index, item.quantity - 1);
    } else {
      onRemove(index);
    }
  };

  const handleDiscountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const discountPercent = parseFloat(e.target.value);
    if (!isNaN(discountPercent) && discountPercent >= 0 && discountPercent <= 100) {
      onUpdateDiscount(index, discountPercent);
    }
  };

  return (
    <div className="py-2 px-3 flex items-center gap-2 border-b last:border-b-0">
      <div className="flex-1 min-w-0">
        <div className="font-medium truncate">{item.productName}</div>
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <span>{formatCurrency(item.unitPrice)}</span>
          
          {/* Unit selector */}
          <Popover open={isUnitOpen} onOpenChange={setIsUnitOpen}>
            <PopoverTrigger asChild>
              <Button 
                variant="ghost" 
                size="sm" 
                className="h-6 px-1 text-xs"
              >
                <Package className="h-3 w-3 mr-1" />
                {item.unitName}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-48 p-2" align="start">
              <div className="space-y-2">
                <Label>Pilih Satuan</Label>
                <Select 
                  value={item.unitId} 
                  onValueChange={(value) => {
                    onChangeUnit(index, value);
                    setIsUnitOpen(false);
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Pilih satuan" />
                  </SelectTrigger>
                  <SelectContent>
                    {item.availableUnits?.map((unit) => (
                      <SelectItem key={unit.id} value={unit.id}>
                        {unit.name} - {formatCurrency(unit.sellingPrice)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </PopoverContent>
          </Popover>
          
          {/* Discount indicator */}
          {item.discountPercent > 0 && (
            <span className="text-green-600 dark:text-green-500">
              -{item.discountPercent}%
            </span>
          )}
        </div>
      </div>
      
      <div className="flex items-center gap-1">
        <Button
          variant="outline"
          size="icon"
          className="h-7 w-7"
          onClick={handleDecrement}
        >
          <Minus className="h-3 w-3" />
        </Button>
        
        <Input
          type="number"
          value={item.quantity}
          onChange={handleQuantityChange}
          className="w-12 h-7 text-center [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
          min="1"
        />
        
        <Button
          variant="outline"
          size="icon"
          className="h-7 w-7"
          onClick={handleIncrement}
        >
          <Plus className="h-3 w-3" />
        </Button>
      </div>
      
      {/* Discount button */}
      <Popover open={isDiscountOpen} onOpenChange={setIsDiscountOpen}>
        <PopoverTrigger asChild>
          <Button
            variant={item.discountPercent > 0 ? "default" : "outline"}
            size="icon"
            className="h-7 w-7"
          >
            <Percent className="h-3 w-3" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-48 p-2" align="end">
          <div className="space-y-2">
            <Label>Diskon (%)</Label>
            <div className="flex items-center gap-2">
              <Input
                type="number"
                value={item.discountPercent}
                onChange={handleDiscountChange}
                className="w-full"
                min="0"
                max="100"
              />
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => {
                  onUpdateDiscount(index, 0);
                  setIsDiscountOpen(false);
                }}
              >
                Reset
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>
      
      <div className="w-20 text-right font-medium">
        {formatCurrency(item.totalPrice)}
      </div>
      
      <Button
        variant="ghost"
        size="icon"
        className="h-7 w-7 text-destructive"
        onClick={() => onRemove(index)}
      >
        <Trash2 className="h-3 w-3" />
      </Button>
    </div>
  );
} 