'use client';

import { useState } from 'react';
import { Sale } from '@/types/sales';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { CheckCircle2, Printer, FileText, RotateCw } from 'lucide-react';
import { formatCurrency } from '@/lib/utils';
import { format } from 'date-fns';
import { id } from 'date-fns/locale';

interface PosPaymentCompleteProps {
  sale: Sale;
  onNewTransaction: () => void;
}

export function PosPaymentComplete({ sale, onNewTransaction }: PosPaymentCompleteProps) {
  const [isPrinting, setIsPrinting] = useState(false);

  const handlePrintReceipt = () => {
    setIsPrinting(true);
    
    // Open receipt in new window
    window.open(`/sales/${sale.id}/receipt/print`, '_blank');
    
    // Reset printing state after a delay
    setTimeout(() => {
      setIsPrinting(false);
    }, 2000);
  };

  const handleViewReceipt = () => {
    window.open(`/sales/${sale.id}/receipt`, '_blank');
  };

  return (
    <div className="flex flex-col items-center justify-center h-full max-w-md mx-auto">
      <Card className="w-full">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <CheckCircle2 className="h-16 w-16 text-success" />
          </div>
          <CardTitle className="text-2xl">Pembayaran Berhasil</CardTitle>
          <CardDescription>
            Transaksi telah selesai dan struk siap dicetak
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          <div className="bg-muted p-4 rounded-md">
            <div className="flex justify-between mb-2">
              <span className="font-medium">No. Transaksi:</span>
              <span>{sale.saleNumber}</span>
            </div>
            <div className="flex justify-between mb-2">
              <span className="font-medium">Tanggal:</span>
              <span>{format(new Date(sale.createdAt), 'dd MMMM yyyy, HH:mm', { locale: id })}</span>
            </div>
            <div className="flex justify-between mb-2">
              <span className="font-medium">Pelanggan:</span>
              <span>{sale.customerName || 'Pelanggan Umum'}</span>
            </div>
            <div className="flex justify-between mb-2">
              <span className="font-medium">Metode Pembayaran:</span>
              <span>{getPaymentMethodLabel(sale.paymentMethod)}</span>
            </div>
            
            <Separator className="my-2" />
            
            <div className="flex justify-between mb-2">
              <span className="font-medium">Subtotal:</span>
              <span>{formatCurrency(sale.subtotal)}</span>
            </div>
            {sale.discountAmount > 0 && (
              <div className="flex justify-between mb-2">
                <span className="font-medium">Diskon:</span>
                <span>-{formatCurrency(sale.discountAmount)}</span>
              </div>
            )}
            {sale.taxAmount > 0 && (
              <div className="flex justify-between mb-2">
                <span className="font-medium">Pajak:</span>
                <span>{formatCurrency(sale.taxAmount)}</span>
              </div>
            )}
            
            <div className="flex justify-between text-lg font-bold mt-2">
              <span>Total:</span>
              <span>{formatCurrency(sale.totalAmount)}</span>
            </div>
            
            {sale.paymentMethod === 'CASH' && (
              <>
                <div className="flex justify-between mt-2">
                  <span className="font-medium">Dibayar:</span>
                  <span>{formatCurrency(sale.amountPaid)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">Kembalian:</span>
                  <span>{formatCurrency(sale.changeAmount)}</span>
                </div>
              </>
            )}
          </div>
        </CardContent>
        
        <CardFooter className="flex flex-col gap-2">
          <Button 
            className="w-full" 
            onClick={handlePrintReceipt}
            disabled={isPrinting}
          >
            <Printer className="mr-2 h-4 w-4" />
            {isPrinting ? 'Mencetak...' : 'Cetak Struk'}
          </Button>
          
          <div className="flex gap-2 w-full">
            <Button 
              variant="outline" 
              className="flex-1"
              onClick={handleViewReceipt}
            >
              <FileText className="mr-2 h-4 w-4" />
              Lihat Struk
            </Button>
            
            <Button 
              variant="outline" 
              className="flex-1"
              onClick={onNewTransaction}
            >
              <RotateCw className="mr-2 h-4 w-4" />
              Transaksi Baru
            </Button>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}

function getPaymentMethodLabel(method: string): string {
  switch (method) {
    case 'CASH':
      return 'Tunai';
    case 'TRANSFER':
      return 'Transfer Bank';
    case 'CREDIT':
      return 'Kartu Kredit';
    case 'GIRO':
      return 'QRIS';
    default:
      return method;
  }
} 