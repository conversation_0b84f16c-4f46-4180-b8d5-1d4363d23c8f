'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';

interface PosSessionManagerProps {
  onStartSession: (initialAmount: number) => void;
}

const formSchema = z.object({
  initialAmount: z.coerce.number().min(0, 'Jumlah kas awal tidak boleh negatif'),
});

type FormValues = z.infer<typeof formSchema>;

export function PosSessionManager({ onStartSession }: PosSessionManagerProps) {
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      initialAmount: 0,
    },
  });

  const onSubmit = async (values: FormValues) => {
    setIsLoading(true);
    try {
      // In a real implementation, you might want to send this to the server
      // For now, we'll just call the onStartSession callback
      onStartSession(values.initialAmount);
    } catch (error) {
      console.error('Failed to start session:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex items-center justify-center h-full">
      <Card className="w-[400px]">
        <CardHeader>
          <CardTitle>Buka Kasir</CardTitle>
          <CardDescription>
            Masukkan jumlah kas awal untuk memulai sesi kasir baru
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="initialAmount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Jumlah Kas Awal</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="0"
                        {...field}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormDescription>
                      Masukkan jumlah uang yang ada di kasir saat ini
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? 'Memproses...' : 'Buka Kasir'}
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
} 