/**
 * Test suite for cart stock validation consistency
 *
 * This test verifies that the stock validation inconsistency has been fixed:
 * 1. Unit selector popover shows correct cart-aware stock levels
 * 2. Cart quantity controls respect the same stock limitations
 * 3. Users cannot add more items than actually available across all unit levels
 */

import { describe, it, expect } from 'vitest'
import {
  calculateAvailableStockWithCartImpact,
  convertPosCartItemsToCalculatorFormat,
  extractUnitHierarchies
} from '@/lib/utils/cart-stock-calculator';

// Mock cart item structure from PosCartPanel
interface MockCartItem {
  productId: string;
  productName: string;
  productCode: string;
  quantity: number;
  price: number;
  unitId: string;
  unitName: string;
  discount: number;
  subtotal: number;
  availableUnits: {
    id: string;
    name: string;
    price: number;
    stock: number;
    conversionFactor: number;
  }[];
}

describe('Cart Stock Validation Consistency', () => {
  // Mock medicine with unit hierarchy: Box (100) → Strip (10) → Tablet (1)
  const mockMedicine: MockCartItem = {
    productId: 'medicine-123',
    productName: 'Test Medicine',
    productCode: 'MED-001',
    quantity: 1,
    price: 90000,
    unitId: 'box-id',
    unitName: 'Box',
    discount: 0,
    subtotal: 90000,
    availableUnits: [
      {
        id: 'tablet-id',
        name: 'Tablet',
        price: 1000,
        stock: 1000, // 1000 tablets = 100 strips = 10 boxes
        conversionFactor: 1
      },
      {
        id: 'strip-id',
        name: 'Strip',
        price: 9500,
        stock: 100, // 100 strips = 10 boxes
        conversionFactor: 10
      },
      {
        id: 'box-id',
        name: 'Box',
        price: 90000,
        stock: 10, // 10 boxes
        conversionFactor: 100
      }
    ]
  };

  // Helper function to create product data for cart-aware stock calculation
  const createProductDataForCartItem = (item: MockCartItem) => {
    const sortedUnits = [...item.availableUnits].sort((a, b) => a.conversionFactor - b.conversionFactor);

    return {
      id: item.productId,
      unitHierarchies: sortedUnits.map((unit, index) => ({
        unitId: unit.id,
        conversionFactor: unit.conversionFactor,
        level: index
      }))
    };
  };

  // Helper function to get cart-aware stock for a specific unit
  const getCartAwareStock = (item: MockCartItem, unitId: string, cartItems: MockCartItem[], excludeItemIndex?: number): number => {
    const productData = createProductDataForCartItem(item);
    const unitHierarchies = extractUnitHierarchies(productData);

    // Filter cart items to exclude the current item being evaluated (if specified)
    const filteredCartItems = excludeItemIndex !== undefined
      ? cartItems.filter((_, index) => index !== excludeItemIndex)
      : cartItems;

    // Convert cart items to calculator format
    const cartStockItems = convertPosCartItemsToCalculatorFormat(filteredCartItems);

    // Create original stock data from item's available units
    const originalStockData: Record<string, any> = {};
    item.availableUnits.forEach(unit => {
      originalStockData[unit.id] = {
        quantityOnHand: unit.stock,
        quantityAllocated: 0,
        availableStock: unit.stock
      };
    });

    // Calculate updated stock considering cart impact
    const updatedStockData = calculateAvailableStockWithCartImpact(
      originalStockData,
      cartStockItems,
      item.productId,
      unitHierarchies
    );

    return updatedStockData[unitId]?.availableStock || 0;
  };

  describe('Scenario: Maximum Box Stock Consumption', () => {
    it('should show correct stock levels after adding maximum boxes to cart', () => {
      // Add 10 boxes to cart (all available boxes)
      const cartItems: MockCartItem[] = [
        {
          ...mockMedicine,
          quantity: 10, // All 10 boxes
          unitId: 'box-id',
          unitName: 'Box'
        }
      ];

      // Check cart-aware stock for each unit
      const boxStock = getCartAwareStock(mockMedicine, 'box-id', cartItems);
      const stripStock = getCartAwareStock(mockMedicine, 'strip-id', cartItems);
      const tabletStock = getCartAwareStock(mockMedicine, 'tablet-id', cartItems);

      // After consuming 10 boxes (1000 tablets), no stock should remain
      expect(boxStock).toBe(0)
      expect(stripStock).toBe(0)
      expect(tabletStock).toBe(0)
    });

    it('should prevent adding more items when all boxes are consumed', () => {
      // Cart with 10 boxes (all available)
      const cartItems: MockCartItem[] = [
        {
          ...mockMedicine,
          quantity: 10,
          unitId: 'box-id',
          unitName: 'Box'
        }
      ];

      // Try to add strips - should show 0 available
      const stripStock = getCartAwareStock(mockMedicine, 'strip-id', cartItems);
      expect(stripStock).toBe(0)

      // Try to add tablets - should show 0 available
      const tabletStock = getCartAwareStock(mockMedicine, 'tablet-id', cartItems);
      expect(tabletStock).toBe(0)
    });
  });

  describe('Scenario: Partial Stock Consumption', () => {
    it('should show correct remaining stock after partial consumption', () => {
      // Add 5 boxes to cart (half of available stock)
      const cartItems: MockCartItem[] = [
        {
          ...mockMedicine,
          quantity: 5, // 5 boxes = 500 tablets
          unitId: 'box-id',
          unitName: 'Box'
        }
      ];

      // Check remaining stock
      const boxStock = getCartAwareStock(mockMedicine, 'box-id', cartItems);
      const stripStock = getCartAwareStock(mockMedicine, 'strip-id', cartItems);
      const tabletStock = getCartAwareStock(mockMedicine, 'tablet-id', cartItems);

      // Remaining: 500 tablets = 50 strips = 5 boxes
      expect(boxStock).toBe(5)
      expect(stripStock).toBe(50)
      expect(tabletStock).toBe(500)
    });

    it('should handle mixed unit cart items correctly', () => {
      // Cart with mixed units: 3 boxes + 2 strips
      const cartItems: MockCartItem[] = [
        {
          ...mockMedicine,
          quantity: 3, // 3 boxes = 300 tablets
          unitId: 'box-id',
          unitName: 'Box'
        },
        {
          ...mockMedicine,
          quantity: 2, // 2 strips = 20 tablets
          unitId: 'strip-id',
          unitName: 'Strip'
        }
      ];

      // Total consumed: 320 tablets, Remaining: 680 tablets
      const boxStock = getCartAwareStock(mockMedicine, 'box-id', cartItems);
      const stripStock = getCartAwareStock(mockMedicine, 'strip-id', cartItems);
      const tabletStock = getCartAwareStock(mockMedicine, 'tablet-id', cartItems);

      expect(boxStock).toBe(6) // 680 / 100 = 6 boxes
      expect(stripStock).toBe(68) // 680 / 10 = 68 strips
      expect(tabletStock).toBe(680) // 680 tablets
    });
  });

  describe('Scenario: Quantity Validation with Exclusion', () => {
    it('should exclude current item when validating quantity changes', () => {
      // Cart with 1 box
      const cartItems: MockCartItem[] = [
        {
          ...mockMedicine,
          quantity: 1,
          unitId: 'box-id',
          unitName: 'Box'
        }
      ];

      // When validating quantity change for the same item, exclude it from calculation
      const availableForCurrentItem = getCartAwareStock(mockMedicine, 'box-id', cartItems, 0);

      // Should show full stock since we excluded the current item
      expect(availableForCurrentItem).toBe(10)
    });

    it('should handle unit change validation correctly', () => {
      // Cart with 5 boxes
      const cartItems: MockCartItem[] = [
        {
          ...mockMedicine,
          quantity: 5,
          unitId: 'box-id',
          unitName: 'Box'
        }
      ];

      // When changing unit from box to strip, exclude current item
      const availableStripsForChange = getCartAwareStock(mockMedicine, 'strip-id', cartItems, 0);

      // Should show full stock in strips since we excluded the current item
      expect(availableStripsForChange).toBe(100)
    });
  });

  describe('Scenario: Multiple Items Same Product Different Units', () => {
    it('should handle multiple cart items of same product with different units', () => {
      // Cart with same product in different units
      const cartItems: MockCartItem[] = [
        {
          ...mockMedicine,
          quantity: 2, // 2 boxes = 200 tablets
          unitId: 'box-id',
          unitName: 'Box'
        },
        {
          ...mockMedicine,
          quantity: 5, // 5 strips = 50 tablets
          unitId: 'strip-id',
          unitName: 'Strip'
        },
        {
          ...mockMedicine,
          quantity: 50, // 50 tablets
          unitId: 'tablet-id',
          unitName: 'Tablet'
        }
      ];

      // Total consumed: 300 tablets, Remaining: 700 tablets
      const boxStock = getCartAwareStock(mockMedicine, 'box-id', cartItems);
      const stripStock = getCartAwareStock(mockMedicine, 'strip-id', cartItems);
      const tabletStock = getCartAwareStock(mockMedicine, 'tablet-id', cartItems);

      expect(boxStock).toBe(7); // 700 / 100 = 7 boxes
      expect(stripStock).toBe(70); // 700 / 10 = 70 strips
      expect(tabletStock).toBe(700); // 700 tablets
    });
  });

  describe('Scenario: Amoxicillin Real-World Bug Fix', () => {
    // Mock Amoxicillin with actual stock levels from the bug report
    const mockAmoxicillin: MockCartItem = {
      productId: 'amoxicillin-500mg',
      productName: 'Amoxicillin 500mg',
      productCode: 'AMX-500',
      quantity: 1,
      price: 9500,
      unitId: 'strip-id',
      unitName: 'Strip',
      discount: 0,
      subtotal: 9500,
      availableUnits: [
        {
          id: 'capsule-id',
          name: 'Kapsul',
          price: 1000,
          stock: 49, // Total: 2 strips (20 capsules) + 29 individual = 49 capsules
          conversionFactor: 1 // Base unit
        },
        {
          id: 'strip-id',
          name: 'Strip',
          price: 9500,
          stock: 4, // 49 capsules / 10 = 4.9, so 4 complete strips available
          conversionFactor: 10 // 1 strip = 10 capsules
        }
      ]
    };

    // Helper function to validate entire cart (simulating handleProcessSale logic)
    const validateCartForSale = (cartItems: MockCartItem[]): { isValid: boolean; errorMessage?: string } => {
      const productGroups = new Map<string, MockCartItem[]>();

      // Group cart items by product
      cartItems.forEach(item => {
        if (!productGroups.has(item.productId)) {
          productGroups.set(item.productId, []);
        }
        productGroups.get(item.productId)!.push(item);
      });

      // Validate each product group
      for (const [, productItems] of productGroups) {
        const firstItem = productItems[0];
        const productData = createProductDataForCartItem(firstItem);
        const unitHierarchies = extractUnitHierarchies(productData);

        // Calculate total demand in base units for this product
        let totalDemandInBaseUnits = 0;
        for (const item of productItems) {
          const unitHierarchy = unitHierarchies.find(uh => uh.unitId === item.unitId);
          if (unitHierarchy) {
            totalDemandInBaseUnits += item.quantity * unitHierarchy.conversionFactor;
          }
        }

        // Get original base stock
        const baseUnitHierarchy = unitHierarchies.find(uh => uh.level === 0);
        if (baseUnitHierarchy) {
          const baseUnit = firstItem.availableUnits.find(u => u.id === baseUnitHierarchy.unitId);
          const availableBaseStock = baseUnit?.stock || 0;

          if (totalDemandInBaseUnits > availableBaseStock) {
            const errorUnit = firstItem.availableUnits.find(u => u.id === firstItem.unitId);
            const availableInErrorUnit = Math.floor(availableBaseStock / (unitHierarchies.find(uh => uh.unitId === firstItem.unitId)?.conversionFactor || 1));

            return {
              isValid: false,
              errorMessage: `Stok ${firstItem.productName} tidak cukup. Tersedia: ${availableInErrorUnit} ${errorUnit?.name || 'unit'}`
            };
          }
        }
      }

      return { isValid: true };
    };

    it('should allow valid mixed unit cart (1 strip + 19 capsules)', () => {
      // The exact scenario from the bug report
      const cartItems: MockCartItem[] = [
        {
          ...mockAmoxicillin,
          quantity: 1, // 1 strip = 10 capsules
          unitId: 'strip-id',
          unitName: 'Strip'
        },
        {
          ...mockAmoxicillin,
          quantity: 19, // 19 capsules
          unitId: 'capsule-id',
          unitName: 'Kapsul'
        }
      ];

      // Total demand: 10 + 19 = 29 capsules
      // Available: 49 capsules total (2 strips worth + 29 individual)

      const validation = validateCartForSale(cartItems);

      // This should be valid since 1 strip (10 capsules) + 19 capsules = 29 capsules total
      // and we have 49 capsules available
      expect(validation.isValid).toBe(true);
    });

    it('should reject cart that exceeds available stock', () => {
      const cartItems: MockCartItem[] = [
        {
          ...mockAmoxicillin,
          quantity: 2, // 2 strips = 20 capsules
          unitId: 'strip-id',
          unitName: 'Strip'
        },
        {
          ...mockAmoxicillin,
          quantity: 15, // 15 capsules
          unitId: 'capsule-id',
          unitName: 'Kapsul'
        }
      ];

      // Total demand: 20 + 15 = 35 capsules
      // Available: 49 capsules

      const validation = validateCartForSale(cartItems);

      // This should be valid since 35 < 49
      expect(validation.isValid).toBe(true);
    });

    it('should reject cart that actually exceeds available stock', () => {
      const cartItems: MockCartItem[] = [
        {
          ...mockAmoxicillin,
          quantity: 4, // 4 strips = 40 capsules
          unitId: 'strip-id',
          unitName: 'Strip'
        },
        {
          ...mockAmoxicillin,
          quantity: 15, // 15 capsules
          unitId: 'capsule-id',
          unitName: 'Kapsul'
        }
      ];

      // Total demand: 40 + 15 = 55 capsules
      // Available: 49 capsules

      const validation = validateCartForSale(cartItems);

      expect(validation.isValid).toBe(false);
      expect(validation.errorMessage).toContain('tidak cukup');
    });

    it('should correctly calculate available stock for mixed units', () => {
      // Available: 49 total capsules = 4 complete strips + 9 individual capsules

      const cartItems: MockCartItem[] = [];

      // Check individual unit availability
      const stripStock = getCartAwareStock(mockAmoxicillin, 'strip-id', cartItems);
      const capsuleStock = getCartAwareStock(mockAmoxicillin, 'capsule-id', cartItems);

      expect(stripStock).toBe(4); // 4 strips available (49 / 10 = 4.9, floor to 4)
      expect(capsuleStock).toBe(49); // 49 capsules available
    });
  });

  describe('Scenario: Product Selector Stock Validation', () => {
    // Test the product selector add-to-cart logic with cart-aware stock validation

    // Helper function to simulate product selector add-to-cart logic
    const simulateProductSelectorAddToCart = (
      product: MockCartItem,
      currentCartItems: MockCartItem[]
    ): { success: boolean; errorMessage?: string; updatedCart?: MockCartItem[] } => {
      const baseUnit = product.availableUnits.find(u => u.conversionFactor === 1);
      if (!baseUnit) {
        return { success: false, errorMessage: 'Base unit not found' };
      }

      const existingItemIndex = currentCartItems.findIndex(
        (item) => item.productId === product.productId && item.unitId === baseUnit.id
      );

      if (existingItemIndex >= 0) {
        // Update quantity if product already in cart - check stock first
        const existingItem = currentCartItems[existingItemIndex];
        const newQuantity = existingItem.quantity + 1;

        // Create a temporary item for stock calculation
        const tempItem: MockCartItem = {
          ...product,
          quantity: existingItem.quantity,
          unitId: baseUnit.id,
          unitName: baseUnit.name
        };

        // Get cart-aware stock for this unit (excluding the existing item)
        const cartAwareStock = getCartAwareStock(tempItem, baseUnit.id, currentCartItems, existingItemIndex);

        // Check if we have enough stock for the new quantity
        if (newQuantity > cartAwareStock) {
          return {
            success: false,
            errorMessage: `Stok tidak cukup. Tersedia: ${cartAwareStock} ${baseUnit.name} (setelah dikurangi keranjang)`
          };
        }

        const updatedCart = [...currentCartItems];
        updatedCart[existingItemIndex].quantity = newQuantity;
        return { success: true, updatedCart };
      } else {
        // Add new product to cart - check stock first
        const newItem: MockCartItem = {
          ...product,
          quantity: 1,
          unitId: baseUnit.id,
          unitName: baseUnit.name
        };

        // Get cart-aware stock for this unit (no exclusion needed for new item)
        const cartAwareStock = getCartAwareStock(newItem, baseUnit.id, currentCartItems);

        // Check if we have enough stock for 1 unit
        if (1 > cartAwareStock) {
          return {
            success: false,
            errorMessage: `Stok tidak cukup. Tersedia: ${cartAwareStock} ${baseUnit.name} (setelah dikurangi keranjang)`
          };
        }

        return { success: true, updatedCart: [...currentCartItems, newItem] };
      }
    };

    it('should prevent adding product when cart already has maximum stock', () => {
      // Cart with maximum stock (10 boxes = 1000 tablets)
      const cartItems: MockCartItem[] = [
        {
          ...mockMedicine,
          quantity: 10, // All 10 boxes
          unitId: 'box-id',
          unitName: 'Box'
        }
      ];

      // Try to add the same product through product selector
      const result = simulateProductSelectorAddToCart(mockMedicine, cartItems);

      expect(result.success).toBe(false);
      expect(result.errorMessage).toContain('Stok tidak cukup');
      expect(result.errorMessage).toContain('0 Tablet'); // Should show 0 tablets available
    });

    it('should allow adding product when cart has partial stock', () => {
      // Cart with partial stock (5 boxes = 500 tablets)
      const cartItems: MockCartItem[] = [
        {
          ...mockMedicine,
          quantity: 5, // 5 boxes
          unitId: 'box-id',
          unitName: 'Box'
        }
      ];

      // Try to add the same product through product selector (should add 1 tablet)
      const result = simulateProductSelectorAddToCart(mockMedicine, cartItems);

      expect(result.success).toBe(true);
      expect(result.updatedCart).toHaveLength(2); // Original box item + new tablet item

      // Check that the new item is added with base unit (tablet)
      const newItem = result.updatedCart?.find(item => item.unitId === 'tablet-id');
      expect(newItem).toBeDefined();
      expect(newItem?.quantity).toBe(1);
    });

    it('should increment existing item quantity when adding same product-unit combination', () => {
      // Cart with 1 tablet
      const cartItems: MockCartItem[] = [
        {
          ...mockMedicine,
          quantity: 1, // 1 tablet
          unitId: 'tablet-id',
          unitName: 'Tablet'
        }
      ];

      // Try to add the same product through product selector (should increment tablet quantity)
      const result = simulateProductSelectorAddToCart(mockMedicine, cartItems);

      expect(result.success).toBe(true);
      expect(result.updatedCart).toHaveLength(1); // Same item, quantity incremented

      // Check that the quantity was incremented
      const updatedItem = result.updatedCart?.[0];
      expect(updatedItem?.quantity).toBe(2);
      expect(updatedItem?.unitId).toBe('tablet-id');
    });

    it('should prevent incrementing when it would exceed available stock', () => {
      // Cart with 999 tablets (1 less than maximum)
      const cartItems: MockCartItem[] = [
        {
          ...mockMedicine,
          quantity: 999, // 999 tablets
          unitId: 'tablet-id',
          unitName: 'Tablet'
        }
      ];

      // Try to add one more tablet through product selector
      const result = simulateProductSelectorAddToCart(mockMedicine, cartItems);

      expect(result.success).toBe(true); // Should succeed since 999 + 1 = 1000 (exactly available)
      expect(result.updatedCart?.[0]?.quantity).toBe(1000);
    });

    it('should prevent adding when incrementing would exceed stock', () => {
      // Cart with 1000 tablets (maximum)
      const cartItems: MockCartItem[] = [
        {
          ...mockMedicine,
          quantity: 1000, // All 1000 tablets
          unitId: 'tablet-id',
          unitName: 'Tablet'
        }
      ];

      // Try to add one more tablet through product selector
      const result = simulateProductSelectorAddToCart(mockMedicine, cartItems);

      expect(result.success).toBe(false);
      expect(result.errorMessage).toContain('Stok tidak cukup');
      expect(result.errorMessage).toContain('0 Tablet'); // Should show 0 tablets available
    });

    it('should handle mixed units correctly when adding through product selector', () => {
      // Cart with mixed units: 5 boxes + 50 strips
      const cartItems: MockCartItem[] = [
        {
          ...mockMedicine,
          quantity: 5, // 5 boxes = 500 tablets
          unitId: 'box-id',
          unitName: 'Box'
        },
        {
          ...mockMedicine,
          quantity: 50, // 50 strips = 500 tablets
          unitId: 'strip-id',
          unitName: 'Strip'
        }
      ];

      // Total in cart: 1000 tablets (maximum), try to add more
      const result = simulateProductSelectorAddToCart(mockMedicine, cartItems);

      expect(result.success).toBe(false);
      expect(result.errorMessage).toContain('Stok tidak cukup');
      expect(result.errorMessage).toContain('0 Tablet'); // Should show 0 tablets available
    });
  });

  describe('Scenario: Unit Conversion and Cart Merging Bug Fix', () => {
    // Test the specific scenario described in the bug report

    // Mock medicine with the exact stock levels from the bug report
    const mockMedicineForMerging: MockCartItem = {
      productId: 'medicine-merge-test',
      productName: 'Test Medicine for Merging',
      productCode: 'MED-MERGE',
      quantity: 1,
      price: 9500,
      unitId: 'strip-id',
      unitName: 'Strip',
      discount: 0,
      subtotal: 9500,
      availableUnits: [
        {
          id: 'capsule-id',
          name: 'Kapsul',
          price: 1000,
          stock: 49, // Total: 2 strips (20 capsules) + 29 individual = 49 capsules
          conversionFactor: 1 // Base unit
        },
        {
          id: 'strip-id',
          name: 'Strip',
          price: 9500,
          stock: 4, // 49 capsules / 10 = 4.9, so 4 complete strips available
          conversionFactor: 10 // 1 strip = 10 capsules
        }
      ]
    };

    // Helper function to simulate unit change with merging (from handleUnitChange logic)
    const simulateUnitChangeWithMerging = (
      cartItems: MockCartItem[],
      itemIndex: number,
      newUnitId: string
    ): { success: boolean; errorMessage?: string; updatedCart?: MockCartItem[] } => {
      const updatedItems = [...cartItems];
      const item = updatedItems[itemIndex];
      const selectedUnit = item.availableUnits.find(u => u.id === newUnitId);

      if (!selectedUnit) {
        return { success: false, errorMessage: 'Unit tidak ditemukan' };
      }

      // Check if changing to this unit would create a duplicate product-unit combination
      const existingItemIndex = updatedItems.findIndex(
        (cartItem, cartIndex) =>
          cartIndex !== itemIndex &&
          cartItem.productId === item.productId &&
          cartItem.unitId === newUnitId
      );

      if (existingItemIndex >= 0) {
        // Merge with existing item
        const existingItem = updatedItems[existingItemIndex];

        // Convert current item quantity to target unit for merging calculation
        const currentItemUnit = item.availableUnits.find(u => u.id === item.unitId);
        const targetUnit = item.availableUnits.find(u => u.id === newUnitId);

        if (!currentItemUnit || !targetUnit) {
          return { success: false, errorMessage: 'Unit tidak ditemukan' };
        }

        // Convert current item quantity to target unit
        const currentItemQuantityInTargetUnit = Math.round(
          (item.quantity * currentItemUnit.conversionFactor) / targetUnit.conversionFactor
        );

        const newQuantity = existingItem.quantity + currentItemQuantityInTargetUnit;

        // Get cart-aware stock for the target unit (excluding BOTH items being merged)
        const availableForMerge = getCartAwareStock(item, newUnitId, cartItems, undefined, [itemIndex, existingItemIndex]);

        // Check if we have enough stock for the merged quantity
        if (newQuantity > availableForMerge) {
          return {
            success: false,
            errorMessage: `Stok tidak cukup untuk menggabungkan. Tersedia: ${availableForMerge} ${selectedUnit.name} (setelah dikurangi keranjang)`
          };
        }

        // Update existing item quantity and remove current item
        existingItem.quantity = newQuantity;
        updatedItems.splice(itemIndex, 1);

        return { success: true, updatedCart: updatedItems };
      } else {
        // No merging needed, just unit change
        const availableForChange = getCartAwareStock(item, newUnitId, cartItems, itemIndex);

        if (item.quantity > availableForChange) {
          return {
            success: false,
            errorMessage: `Stok tidak cukup. Tersedia: ${availableForChange} ${selectedUnit.name} (setelah dikurangi keranjang)`
          };
        }

        // Update current item unit
        item.unitId = selectedUnit.id;
        item.unitName = selectedUnit.name;
        item.price = selectedUnit.price;

        return { success: true, updatedCart: updatedItems };
      }
    };

    // Updated getCartAwareStock function to support multiple exclusions
    const getCartAwareStockWithMultipleExclusions = (
      item: MockCartItem,
      unitId: string,
      cartItems: MockCartItem[],
      excludeItemIndex?: number,
      excludeItemIndices?: number[]
    ): number => {
      const productData = createProductDataForCartItem(item);
      const unitHierarchies = extractUnitHierarchies(productData);

      // Filter cart items to exclude specified items
      let filteredCartItems = cartItems;

      if (excludeItemIndices && excludeItemIndices.length > 0) {
        // Exclude multiple items (for merging scenarios)
        filteredCartItems = cartItems.filter((_, index) => !excludeItemIndices.includes(index));
      } else if (excludeItemIndex !== undefined) {
        // Exclude single item (for quantity validation)
        filteredCartItems = cartItems.filter((_, index) => index !== excludeItemIndex);
      }

      // Convert cart items to calculator format
      const cartStockItems = convertPosCartItemsToCalculatorFormat(filteredCartItems);

      // Create original stock data from item's available units
      const originalStockData: Record<string, any> = {};
      item.availableUnits.forEach(unit => {
        originalStockData[unit.id] = {
          quantityOnHand: unit.stock,
          quantityAllocated: 0,
          availableStock: unit.stock
        };
      });

      // Calculate updated stock considering cart impact
      const updatedStockData = calculateAvailableStockWithCartImpact(
        originalStockData,
        cartStockItems,
        item.productId,
        unitHierarchies
      );

      return updatedStockData[unitId]?.availableStock || 0;
    };

    // Update the helper function reference
    const getCartAwareStock = getCartAwareStockWithMultipleExclusions;

    it('should successfully merge 1 Strip + 9 Capsules when changing Strip to Capsules', () => {
      // The exact scenario from the bug report
      const cartItems: MockCartItem[] = [
        {
          ...mockMedicineForMerging,
          quantity: 1, // 1 strip = 10 capsules
          unitId: 'strip-id',
          unitName: 'Strip'
        },
        {
          ...mockMedicineForMerging,
          quantity: 9, // 9 capsules
          unitId: 'capsule-id',
          unitName: 'Kapsul'
        }
      ];

      // Try to change Item 1 from Strip to Capsules (should merge with Item 2)
      const result = simulateUnitChangeWithMerging(cartItems, 0, 'capsule-id');

      expect(result.success).toBe(true);
      expect(result.updatedCart).toHaveLength(1); // Should merge into single item

      // Check that the merged item has correct quantity
      const mergedItem = result.updatedCart?.[0];
      expect(mergedItem?.unitId).toBe('capsule-id');
      expect(mergedItem?.quantity).toBe(19); // 10 (from strip) + 9 (existing) = 19 capsules
      expect(mergedItem?.unitName).toBe('Kapsul');
    });

    it('should correctly calculate available stock when excluding both items for merging', () => {
      const cartItems: MockCartItem[] = [
        {
          ...mockMedicineForMerging,
          quantity: 1, // 1 strip = 10 capsules
          unitId: 'strip-id',
          unitName: 'Strip'
        },
        {
          ...mockMedicineForMerging,
          quantity: 9, // 9 capsules
          unitId: 'capsule-id',
          unitName: 'Kapsul'
        }
      ];

      // Calculate available stock excluding both items (for merging validation)
      const availableForMerge = getCartAwareStock(
        mockMedicineForMerging,
        'capsule-id',
        cartItems,
        undefined,
        [0, 1] // Exclude both items
      );

      // Should show full stock since both items are excluded
      expect(availableForMerge).toBe(49); // Full stock available
    });

    it('should prevent merging when total would exceed available stock', () => {
      // Cart that would exceed stock when merged
      const cartItems: MockCartItem[] = [
        {
          ...mockMedicineForMerging,
          quantity: 4, // 4 strips = 40 capsules
          unitId: 'strip-id',
          unitName: 'Strip'
        },
        {
          ...mockMedicineForMerging,
          quantity: 15, // 15 capsules
          unitId: 'capsule-id',
          unitName: 'Kapsul'
        }
      ];

      // Try to change 4 strips to capsules (would be 40 + 15 = 55 capsules, exceeds 49 available)
      const result = simulateUnitChangeWithMerging(cartItems, 0, 'capsule-id');

      expect(result.success).toBe(false);
      expect(result.errorMessage).toContain('Stok tidak cukup untuk menggabungkan');
    });

    it('should handle unit conversion correctly during merging', () => {
      const cartItems: MockCartItem[] = [
        {
          ...mockMedicineForMerging,
          quantity: 2, // 2 strips = 20 capsules
          unitId: 'strip-id',
          unitName: 'Strip'
        },
        {
          ...mockMedicineForMerging,
          quantity: 5, // 5 capsules
          unitId: 'capsule-id',
          unitName: 'Kapsul'
        }
      ];

      // Change 2 strips to capsules (should become 20 capsules + 5 existing = 25 total)
      const result = simulateUnitChangeWithMerging(cartItems, 0, 'capsule-id');

      expect(result.success).toBe(true);
      expect(result.updatedCart).toHaveLength(1);

      const mergedItem = result.updatedCart?.[0];
      expect(mergedItem?.quantity).toBe(25); // 20 (from 2 strips) + 5 (existing) = 25 capsules
    });

    it('should allow merging at exact stock limit', () => {
      const cartItems: MockCartItem[] = [
        {
          ...mockMedicineForMerging,
          quantity: 4, // 4 strips = 40 capsules
          unitId: 'strip-id',
          unitName: 'Strip'
        },
        {
          ...mockMedicineForMerging,
          quantity: 9, // 9 capsules
          unitId: 'capsule-id',
          unitName: 'Kapsul'
        }
      ];

      // Change 4 strips to capsules (would be 40 + 9 = 49 capsules, exactly at limit)
      const result = simulateUnitChangeWithMerging(cartItems, 0, 'capsule-id');

      expect(result.success).toBe(true);
      expect(result.updatedCart).toHaveLength(1);

      const mergedItem = result.updatedCart?.[0];
      expect(mergedItem?.quantity).toBe(49); // Exactly at stock limit
    });
  });

  describe('Edge Cases', () => {
    it('should handle zero stock correctly', () => {
      // Cart that consumes all stock
      const cartItems: MockCartItem[] = [
        {
          ...mockMedicine,
          quantity: 10, // All 10 boxes
          unitId: 'box-id',
          unitName: 'Box'
        }
      ];

      const boxStock = getCartAwareStock(mockMedicine, 'box-id', cartItems);
      const stripStock = getCartAwareStock(mockMedicine, 'strip-id', cartItems);
      const tabletStock = getCartAwareStock(mockMedicine, 'tablet-id', cartItems);

      expect(boxStock).toBe(0);
      expect(stripStock).toBe(0);
      expect(tabletStock).toBe(0);
    });

    it('should handle empty cart correctly', () => {
      const cartItems: MockCartItem[] = [];

      const boxStock = getCartAwareStock(mockMedicine, 'box-id', cartItems);
      const stripStock = getCartAwareStock(mockMedicine, 'strip-id', cartItems);
      const tabletStock = getCartAwareStock(mockMedicine, 'tablet-id', cartItems);

      // Should show original stock levels
      expect(boxStock).toBe(10);
      expect(stripStock).toBe(100);
      expect(tabletStock).toBe(1000);
    });
  });
});
