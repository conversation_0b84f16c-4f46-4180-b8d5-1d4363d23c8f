'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { Session } from 'next-auth';
import { PosSessionManager } from './PosSessionManager';
import { ProductSelector, ProductSelectorRef } from '@/components/ui/product-selector';
import { CustomerSelector } from '@/components/ui/customer-selector';
import { PosCartPanel } from './PosCartPanel';
import { PosSessionHistory } from './PosSessionHistory';
import { PosSessionStats } from './PosSessionStats';
import { useProduct } from '@/hooks/useProducts';
import { Customer } from '@/types/customer';
import { Input } from '@/components/ui/input';
import { SessionData } from '@/types/session';

export type PosTab = 'cart' | 'history' | 'stats';

interface PosClientProps {
  user: Session['user'];
  onSessionChange?: (sessionData: SessionData | null) => void;
  onEndSessionRequest?: (handler: () => void) => void;
  activeTab?: PosTab;
  onTabChange?: (tab: PosTab) => void;
}

export function PosClient({
  user,
  onSessionChange,
  onEndSessionRequest,
  activeTab = 'cart',
  onTabChange
}: PosClientProps) {
  const [isSessionActive, setIsSessionActive] = useState(false);
  const [sessionData, setSessionData] = useState<SessionData | null>(null);

  const [selectedProductId, setSelectedProductId] = useState<string>('');
  const [selectedCustomerId, setSelectedCustomerId] = useState<string>('walk-in');
  const [customerName, setCustomerName] = useState<string>('');
  const [customerPhone, setCustomerPhone] = useState<string>('');
  const { data: selectedProduct } = useProduct(selectedProductId);

  // Ref for product selector to enable keyboard shortcut
  const productSelectorRef = useRef<ProductSelectorRef>(null);

  // Check for existing session in localStorage
  useEffect(() => {
    const savedSession = localStorage.getItem('pos_session');
    if (savedSession) {
      try {
        const parsedSession = JSON.parse(savedSession);
        setSessionData(parsedSession);
        setIsSessionActive(true);
      } catch (error) {
        console.error('Failed to parse session data:', error);
        localStorage.removeItem('pos_session');
      }
    }
  }, []);

  const handleEndSession = useCallback(() => {
    if (confirm('Anda yakin ingin menutup sesi kasir ini?')) {
      // Update session data with close time before clearing
      if (sessionData) {
        const closedSession = {
          ...sessionData,
          closedAt: new Date(),
        };
        // Optionally save closed session to history or send to backend
        console.log('Session closed:', closedSession);
      }

      setSessionData(null);
      setIsSessionActive(false);
      localStorage.removeItem('pos_session');
    }
  }, [sessionData]);

  // Notify parent about session changes
  useEffect(() => {
    onSessionChange?.(sessionData);
  }, [sessionData, onSessionChange]);

  // Register end session handler with parent
  useEffect(() => {
    if (onEndSessionRequest) {
      onEndSessionRequest(handleEndSession);
    }
  }, [onEndSessionRequest, handleEndSession]);

  // Keyboard shortcut for product selector (F2)
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'F2' && !event.ctrlKey && !event.altKey && !event.shiftKey) {
        event.preventDefault();
        productSelectorRef.current?.focus();
      }
    };

    if (isSessionActive) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [isSessionActive]);

  const handleStartSession = (initialAmount: number) => {
    const sessionId = `${user.id}-${Date.now()}`;
    const newSession = {
      sessionId,
      openedAt: new Date(),
      cashierId: user.id,
      cashierName: user.name,
      initialAmount,
    };

    setSessionData(newSession);
    setIsSessionActive(true);
    localStorage.setItem('pos_session', JSON.stringify(newSession));
  };

  const handleProductSelected = (productId: string) => {
    setSelectedProductId(productId);
  };

  const handleCustomerSelected = (customerId: string | undefined) => {
    const newCustomerId = customerId || 'walk-in';
    setSelectedCustomerId(newCustomerId);

    // Clear walk-in customer fields when switching away from walk-in
    if (newCustomerId !== 'walk-in') {
      setCustomerName('');
      setCustomerPhone('');
    }
  };

  const handleCustomerCreated = (customer: Customer) => {
    // Auto-select the newly created customer
    setSelectedCustomerId(customer.id);
  };

  if (!isSessionActive) {
    return <PosSessionManager onStartSession={handleStartSession} />;
  }

  return (
    <div className="h-full flex flex-col overflow-hidden p-4">
      <div className="flex-none mb-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="text-sm font-medium mb-1 block">
              Pilih Produk <span className="text-xs text-muted-foreground font-normal">(F2)</span>
            </label>
            <ProductSelector
              ref={productSelectorRef}
              value={selectedProductId}
              onValueChange={handleProductSelected}
              placeholder="Cari produk untuk ditambahkan ke keranjang..."
              className="w-full"
            />
          </div>
          <div>
            <label className="text-sm font-medium mb-1 block">
              Pelanggan
            </label>
            <CustomerSelector
              value={selectedCustomerId}
              onValueChange={handleCustomerSelected}
              placeholder="Pilih pelanggan..."
              enableCustomerCreation={true}
              onCustomerCreated={handleCustomerCreated}
              className="w-full"
            />

            {/* Walk-in customer optional fields - compact horizontal layout */}
            {selectedCustomerId === 'walk-in' && (
              <div className="mt-2 grid grid-cols-2 gap-2">
                <div>
                  <Input
                    value={customerName}
                    onChange={(e) => setCustomerName(e.target.value)}
                    placeholder="Nama (opsional)"
                    className="h-8 text-xs"
                  />
                </div>
                <div>
                  <Input
                    value={customerPhone}
                    onChange={(e) => setCustomerPhone(e.target.value)}
                    placeholder="Telepon (opsional)"
                    className="h-8 text-xs"
                  />
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="flex-1 min-h-0 overflow-hidden">
        {/* Cart Panel - only visible when cart tab is active */}
        <div className={activeTab === 'cart' ? 'block h-full' : 'hidden'}>
          <PosCartPanel
            user={user}
            selectedProduct={selectedProduct}
            customerId={selectedCustomerId}
            customerName={customerName}
            customerPhone={customerPhone}
            onProductSelected={() => setSelectedProductId('')}
          />
        </div>

        {/* Session History - always mounted but only visible when history tab is active */}
        <div className={activeTab === 'history' ? 'block h-full' : 'hidden'}>
          <PosSessionHistory
            user={user}
            sessionData={sessionData}
          />
        </div>

        {/* Session Stats - always mounted but only visible when stats tab is active */}
        <div className={activeTab === 'stats' ? 'block h-full' : 'hidden'}>
          <PosSessionStats
            user={user}
            sessionData={sessionData}
          />
        </div>
      </div>
    </div>
  );
}