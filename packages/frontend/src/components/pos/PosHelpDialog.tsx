'use client';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Keyboard,
  BookOpen,
  Lightbulb,
  Phone,
  Mail,
  MessageCircle,
  ShoppingCart,
  CreditCard,
  Package,
  Users,
  BarChart3,
  Search,
} from 'lucide-react';

interface PosHelpDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function PosHelpDialog({ open, onOpenChange }: PosHelpDialogProps) {
  const keyboardShortcuts = [
    { key: 'F2', description: 'Fokus ke pencarian produk' },
    { key: 'Esc', description: 'Tutup dialog atau popover yang terbuka' },
    { key: 'Enter', description: 'Konfirmasi aksi atau pilihan' },
    { key: 'Tab', description: 'Navigasi antar elemen form' },
  ];

  const usageSteps = [
    {
      icon: <Users className="h-5 w-5" />,
      title: 'Pilih Pelanggan',
      description: 'Pilih pelanggan terdaftar atau gunakan "Pelanggan Umum" untuk walk-in customer. Untuk pelanggan umum, Anda dapat mengisi nama dan nomor telepon secara opsional.',
    },
    {
      icon: <Search className="h-5 w-5" />,
      title: 'Cari Produk',
      description: 'Gunakan pencarian produk untuk menemukan item yang ingin dijual. Tekan F2 untuk fokus langsung ke kolom pencarian.',
    },
    {
      icon: <ShoppingCart className="h-5 w-5" />,
      title: 'Kelola Keranjang',
      description: 'Tambah, edit, atau hapus item di keranjang. Anda dapat mengubah jumlah, satuan, memberikan diskon, dan menyesuaikan harga sesuai kebutuhan.',
    },
    {
      icon: <CreditCard className="h-5 w-5" />,
      title: 'Proses Pembayaran',
      description: 'Pilih metode pembayaran (Tunai, Transfer, Kredit, atau Giro) dan proses transaksi. Untuk pembayaran tunai, masukkan jumlah uang yang diterima.',
    },
  ];

  const tips = [
    'Gunakan shortcut F2 untuk pencarian produk yang lebih cepat',
    'Periksa stok produk sebelum menambahkan ke keranjang dalam jumlah besar',
    'Manfaatkan fitur diskon untuk memberikan potongan harga kepada pelanggan',
    'Gunakan tab "Riwayat Penjualan" untuk melihat transaksi yang sudah dilakukan',
    'Pantau statistik sesi untuk memantau performa penjualan harian',
    'Pastikan sesi kasir ditutup dengan benar di akhir shift',
    'Simpan informasi pelanggan walk-in untuk memudahkan pelayanan di masa depan',
  ];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            Bantuan POS System
          </DialogTitle>
          <DialogDescription>
            Panduan lengkap penggunaan sistem Point of Sale (POS) apotek
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="guide" className="flex-1 overflow-hidden">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="guide" className="text-xs">
              <BookOpen className="h-3 w-3 mr-1" />
              Panduan
            </TabsTrigger>
            <TabsTrigger value="shortcuts" className="text-xs">
              <Keyboard className="h-3 w-3 mr-1" />
              Shortcut
            </TabsTrigger>
            <TabsTrigger value="tips" className="text-xs">
              <Lightbulb className="h-3 w-3 mr-1" />
              Tips
            </TabsTrigger>
            <TabsTrigger value="support" className="text-xs">
              <Phone className="h-3 w-3 mr-1" />
              Dukungan
            </TabsTrigger>
          </TabsList>

          <div className="mt-4 overflow-auto max-h-[60vh]">
            <TabsContent value="guide" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Cara Menggunakan POS</CardTitle>
                  <CardDescription>
                    Ikuti langkah-langkah berikut untuk melakukan transaksi penjualan
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {usageSteps.map((step, index) => (
                    <div key={index} className="flex gap-4">
                      <div className="flex-shrink-0">
                        <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center text-primary-foreground text-sm font-bold">
                          {index + 1}
                        </div>
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          {step.icon}
                          <h4 className="font-semibold">{step.title}</h4>
                        </div>
                        <p className="text-sm text-muted-foreground">{step.description}</p>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <BarChart3 className="h-5 w-5" />
                    Fitur Navigasi
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div>
                    <h4 className="font-medium mb-1">Keranjang Belanja</h4>
                    <p className="text-sm text-muted-foreground">
                      Tampilan utama untuk mengelola item yang akan dijual, mengatur jumlah, harga, dan diskon.
                    </p>
                  </div>
                  <div>
                    <h4 className="font-medium mb-1">Riwayat Penjualan</h4>
                    <p className="text-sm text-muted-foreground">
                      Melihat semua transaksi yang telah dilakukan dalam sesi kasir saat ini.
                    </p>
                  </div>
                  <div>
                    <h4 className="font-medium mb-1">Statistik Sesi</h4>
                    <p className="text-sm text-muted-foreground">
                      Memantau performa penjualan, total revenue, dan breakdown metode pembayaran.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="shortcuts" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Keyboard Shortcuts</CardTitle>
                  <CardDescription>
                    Gunakan shortcut berikut untuk navigasi yang lebih cepat
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {keyboardShortcuts.map((shortcut, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <span className="text-sm">{shortcut.description}</span>
                        <Badge variant="outline" className="font-mono">
                          {shortcut.key}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="tips" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Tips Penggunaan Efisien</CardTitle>
                  <CardDescription>
                    Saran untuk memaksimalkan produktivitas dalam menggunakan POS
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {tips.map((tip, index) => (
                      <div key={index} className="flex items-start gap-3">
                        <div className="w-6 h-6 bg-yellow-100 dark:bg-yellow-900 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                          <Lightbulb className="h-3 w-3 text-yellow-600 dark:text-yellow-400" />
                        </div>
                        <p className="text-sm">{tip}</p>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="support" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Informasi Dukungan</CardTitle>
                  <CardDescription>
                    Hubungi tim dukungan jika Anda memerlukan bantuan
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center gap-3 p-3 border rounded-lg">
                      <Phone className="h-5 w-5 text-blue-600" />
                      <div>
                        <p className="font-medium">Telepon</p>
                        <p className="text-sm text-muted-foreground">+62 21 1234 5678</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3 p-3 border rounded-lg">
                      <Mail className="h-5 w-5 text-green-600" />
                      <div>
                        <p className="font-medium">Email</p>
                        <p className="text-sm text-muted-foreground"><EMAIL></p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3 p-3 border rounded-lg">
                      <MessageCircle className="h-5 w-5 text-purple-600" />
                      <div>
                        <p className="font-medium">WhatsApp</p>
                        <p className="text-sm text-muted-foreground">+62 812 3456 7890</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3 p-3 border rounded-lg">
                      <BookOpen className="h-5 w-5 text-orange-600" />
                      <div>
                        <p className="font-medium">Dokumentasi</p>
                        <p className="text-sm text-muted-foreground">docs.apotek.com</p>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <div className="text-center text-sm text-muted-foreground">
                    <p>Jam operasional dukungan: Senin - Jumat, 08:00 - 17:00 WIB</p>
                    <p className="mt-1">Untuk masalah urgent di luar jam kerja, hubungi WhatsApp</p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </div>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
