'use client';

import { Building, CreditCard } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useSupplierStats } from '@/hooks/useSuppliers';
import { Skeleton } from '@/components/ui/skeleton';

export function SupplierStatsCards() {
  const { data: stats, isLoading, error } = useSupplierStats();

  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 4 }).map((_, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-4" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-16 mb-2" />
              <Skeleton className="h-3 w-32" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error || !stats) {
    return null; // Don't show stats if there's an error
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card className="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 border-blue-200 dark:border-blue-800">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-blue-700 dark:text-blue-300">
            Total Supplier
          </CardTitle>
          <Building className="h-4 w-4 text-blue-600 dark:text-blue-400" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-blue-900 dark:text-blue-100">{stats.total}</div>
          <p className="text-xs text-blue-600 dark:text-blue-400">
            Semua supplier terdaftar
          </p>
        </CardContent>
      </Card>
      <Card className="bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 border-green-200 dark:border-green-800">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-green-700 dark:text-green-300">
            Supplier Aktif
          </CardTitle>
          <div className="h-2 w-2 bg-green-500 rounded-full"></div>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-900 dark:text-green-100">{stats.active}</div>
          <p className="text-xs text-green-600 dark:text-green-400">
            Siap untuk transaksi
          </p>
        </CardContent>
      </Card>
      <Card className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-900/20 dark:to-gray-800/20 border-gray-200 dark:border-gray-800">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Tidak Aktif
          </CardTitle>
          <div className="h-2 w-2 bg-gray-500 rounded-full"></div>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">{stats.inactive}</div>
          <p className="text-xs text-gray-600 dark:text-gray-400">
            Perlu ditinjau ulang
          </p>
        </CardContent>
      </Card>
      <Card className="bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 border-purple-200 dark:border-purple-800">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-purple-700 dark:text-purple-300">
            PBF (Pedagang Besar Farmasi)
          </CardTitle>
          <CreditCard className="h-4 w-4 text-purple-600 dark:text-purple-400" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-purple-900 dark:text-purple-100">
            {stats.byType?.PBF || 0}
          </div>
          <p className="text-xs text-purple-600 dark:text-purple-400">
            Distributor farmasi resmi
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
