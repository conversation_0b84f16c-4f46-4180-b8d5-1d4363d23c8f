'use client';

import { useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { Plus, Download, Upload, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { DataTable } from '@/components/suppliers/data-table';
import { createSupplierColumns } from '@/components/suppliers/columns';
import { SupplierQuickView } from '@/components/suppliers/supplier-quick-view';
import { SupplierImportModal } from '@/components/suppliers/SupplierImportModal';
import { SupplierExportModal } from '@/components/suppliers/SupplierExportModal';
import { SupplierWithRelations, SupplierQueryParams } from '@/types/supplier';
import {
  useSuppliers,
  useSuppliersInvalidate,
  useActivateSupplier,
  useDeactivateSupplier,
  useHardDeleteSupplier,
} from '@/hooks/useSuppliers';
import {
  SUPPLIER_TYPE_OPTIONS,
  SUPPLIER_STATUS_OPTIONS,
  INDONESIAN_PROVINCES,
  DEFAULT_PAGINATION,
} from '@/lib/constants/supplier';
import { toast } from 'sonner';

interface SuppliersPageClientProps {
  initialQuery: SupplierQueryParams;
}

export function SuppliersPageClient({
  initialQuery,
}: SuppliersPageClientProps) {
  const router = useRouter();

  // Query state - this will trigger the TanStack Query
  const [query, setQuery] = useState<SupplierQueryParams>(initialQuery);

  // Use TanStack Query for data fetching
  const { data, isLoading, error, refetch } = useSuppliers(query);

  // Use invalidation hook for stats refresh
  const { invalidateStats } = useSuppliersInvalidate();

  // Use mutation hooks for supplier operations
  const activateSupplierMutation = useActivateSupplier();
  const deactivateSupplierMutation = useDeactivateSupplier();
  const hardDeleteSupplierMutation = useHardDeleteSupplier();

  // Quick View Modal State
  const [quickViewSupplier, setQuickViewSupplier] = useState<SupplierWithRelations | null>(null);
  const [quickViewOpen, setQuickViewOpen] = useState(false);

  // Import/Export Modal States
  const [importModalOpen, setImportModalOpen] = useState(false);
  const [exportModalOpen, setExportModalOpen] = useState(false);

  // Filters state (separate from query for UI)
  const [filters, setFilters] = useState<SupplierQueryParams>(initialQuery);

  // Handle query changes from DataTable - TanStack Query will automatically refetch
  const handleQueryChange = useCallback((newQuery: SupplierQueryParams) => {
    setQuery(newQuery);

    // Update URL without navigation to preserve scroll position
    const params = new URLSearchParams();
    Object.entries(newQuery).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.set(key, String(value));
      }
    });

    const newUrl = `/dashboard/suppliers?${params.toString()}`;
    const currentUrl = window.location.pathname + window.location.search;

    if (newUrl !== currentUrl) {
      window.history.replaceState(null, '', newUrl);
    }
  }, []);



  // Handlers
  const handleViewSupplier = (supplier: SupplierWithRelations) => {
    router.push(`/dashboard/suppliers/${supplier.id}`);
  };

  const handleEditSupplier = (supplier: SupplierWithRelations) => {
    router.push(`/dashboard/suppliers/${supplier.id}/edit`);
  };



  const handleActivateSupplier = async (supplier: SupplierWithRelations) => {
    try {
      await activateSupplierMutation.mutateAsync(supplier.id);
      toast.success('Supplier berhasil diaktifkan');
    } catch (err: any) {
      toast.error(err?.response?.data?.message || 'Gagal mengaktifkan supplier');
      console.error('Error activating supplier:', err);
    }
  };

  const handleDeactivateSupplier = async (supplier: SupplierWithRelations) => {
    try {
      await deactivateSupplierMutation.mutateAsync(supplier.id);
      toast.success('Supplier berhasil dinonaktifkan');
    } catch (err: any) {
      toast.error(err?.response?.data?.message || 'Gagal menonaktifkan supplier');
      console.error('Error deactivating supplier:', err);
    }
  };

  const handleHardDeleteSupplier = async (supplier: SupplierWithRelations) => {
    try {
      await hardDeleteSupplierMutation.mutateAsync(supplier.id);
      toast.success('Supplier berhasil dihapus secara permanen');
    } catch (err: any) {
      const errorMessage = err?.response?.data?.message || 'Gagal menghapus supplier secara permanen';
      toast.error(errorMessage);
      console.error('Error hard deleting supplier:', err);
    }
  };

  const handleQuickView = (supplier: SupplierWithRelations) => {
    setQuickViewSupplier(supplier);
    setQuickViewOpen(true);
  };

  const handleQuickViewEdit = (supplier: SupplierWithRelations) => {
    setQuickViewOpen(false);
    router.push(`/dashboard/suppliers/${supplier.id}/edit`);
  };

  const handleQuickViewFull = (supplier: SupplierWithRelations) => {
    setQuickViewOpen(false);
    router.push(`/dashboard/suppliers/${supplier.id}`);
  };

  const handleFilterChange = useCallback((key: keyof SupplierQueryParams, value: any) => {
    const newFilters = {
      ...filters,
      [key]: value,
      page: 1, // Reset to first page when filtering
    };

    // Only update if the value actually changed
    if (filters[key] !== value) {
      setFilters(newFilters);
      handleQueryChange(newFilters);
    }
  }, [filters, handleQueryChange]);

  const clearFilters = useCallback(() => {
    const clearedFilters = { ...DEFAULT_PAGINATION };
    setFilters(clearedFilters);
    handleQueryChange(clearedFilters);
  }, [handleQueryChange]);

  const handleRefresh = useCallback(() => {
    refetch(); // TanStack Query refetch
    invalidateStats(); // Refresh stats
  }, [refetch, invalidateStats]);

  // Import/Export handlers
  const handleImportComplete = useCallback(() => {
    refetch(); // Refresh data after import
    invalidateStats(); // Refresh stats
  }, [refetch, invalidateStats]);

  const columns = createSupplierColumns(
    handleViewSupplier,
    handleEditSupplier,
    handleQuickView,
    handleActivateSupplier,
    handleDeactivateSupplier,
    handleHardDeleteSupplier,
    {
      isActivating: activateSupplierMutation.isPending,
      isDeactivating: deactivateSupplierMutation.isPending,
      isHardDeleting: hardDeleteSupplierMutation.isPending,
    }
  );

  return (
    <div className="w-full min-w-0 max-w-full space-y-6 overflow-hidden">
      {/* Header */}
      <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
        <div className="min-w-0 flex-1">
          <h1 className="text-2xl lg:text-3xl font-bold tracking-tight text-gray-900 dark:text-gray-100">
            Manajemen Supplier
          </h1>
          <p className="text-muted-foreground mt-1 text-sm lg:text-base">
            Kelola data supplier dan vendor apotek dengan mudah dan efisien
          </p>
        </div>
        <div className="flex flex-col gap-2 sm:flex-row sm:flex-wrap">
          <Button
            variant="outline"
            className="w-full sm:w-auto"
            onClick={() => setExportModalOpen(true)}
          >
            <Download className="mr-2 h-4 w-4" />
            Export Data
          </Button>
          <Button
            variant="outline"
            className="w-full sm:w-auto"
            onClick={() => setImportModalOpen(true)}
          >
            <Upload className="mr-2 h-4 w-4" />
            Import Data
          </Button>
          <Button
            onClick={() => router.push('/dashboard/suppliers/new')}
            variant="default"
            className="w-full sm:w-auto"
          >
            <Plus className="mr-2 h-4 w-4" />
            Tambah Supplier
          </Button>
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Card className="border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20">
          <CardContent>
            <div className="flex items-center gap-3">
              <div className="flex-1">
                <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                  Terjadi Kesalahan
                </h3>
                <p className="text-sm text-red-700 dark:text-red-300 mt-1">
                  {error?.message || 'Gagal memuat data supplier'}
                </p>
              </div>
              <Button
                variant="outline"
                onClick={handleRefresh}
                className="border-red-300 text-red-700 hover:bg-red-100 dark:border-red-700 dark:text-red-300 dark:hover:bg-red-900/30"
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                Coba Lagi
              </Button>
            </div>
          </CardContent>
        </Card>
      )}



      {/* Data Table */}
      <Card className="w-full min-w-0 max-w-full overflow-hidden">
        <CardContent className="p-0 sm:p-6">
          <div className="w-full min-w-0 max-w-full">
            <DataTable
              columns={columns}
              data={data?.data || []}
              meta={data?.meta || { total: 0, page: 1, limit: 10, totalPages: 0, hasNextPage: false, hasPreviousPage: false }}
              query={query}
              onQueryChange={handleQueryChange}
              loading={isLoading}
              searchPlaceholder="Cari supplier..."
              onRowClick={handleViewSupplier}
              filters={filters}
              onFilterChange={handleFilterChange}
              onClearFilters={clearFilters}
              filterOptions={{
                supplierTypes: SUPPLIER_TYPE_OPTIONS,
                supplierStatuses: SUPPLIER_STATUS_OPTIONS,
                provinces: INDONESIAN_PROVINCES,
              }}
            />
          </div>
        </CardContent>
      </Card>

      {/* Quick View Modal */}
      <SupplierQuickView
        supplier={quickViewSupplier}
        open={quickViewOpen}
        onOpenChange={setQuickViewOpen}
        onEdit={handleQuickViewEdit}
        onViewFull={handleQuickViewFull}
      />

      {/* Import Modal */}
      <SupplierImportModal
        open={importModalOpen}
        onOpenChange={setImportModalOpen}
        onImportComplete={handleImportComplete}
      />

      {/* Export Modal */}
      <SupplierExportModal
        open={exportModalOpen}
        onOpenChange={setExportModalOpen}
        currentFilters={filters}
      />
    </div>
  );
}
