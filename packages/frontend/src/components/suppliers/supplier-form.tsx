'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { LiveCurrencyInput } from '@/components/ui/currency-input';
import { CreateSupplierDto, SupplierType, PaymentMethod } from '@/types/supplier';
import {
  SUPPLIER_TYPE_OPTIONS,
  PAYMENT_METHOD_OPTIONS,
  INDONESIAN_PROVINCES,
} from '@/lib/constants/supplier';
import { useGenerateSupplierCode, useValidateSupplierCode } from '@/hooks/useSuppliers';
import { toast } from 'sonner';
import { Loader2 } from 'lucide-react';

const supplierSchema = z.object({
  code: z.string().min(1, 'Kode supplier wajib diisi'),
  name: z.string().min(1, 'Nama supplier wajib diisi'),
  type: z.nativeEnum(SupplierType, { required_error: 'Jenis supplier wajib dipilih' }),
  address: z.string().optional(),
  city: z.string().optional(),
  province: z.string().optional(),
  postalCode: z.string().optional(),
  phone: z.string().optional(),
  email: z.string().email('Format email tidak valid').optional().or(z.literal('')),
  website: z.string().url('Format website tidak valid').optional().or(z.literal('')),
  npwp: z.string().optional(),
  licenseNumber: z.string().optional(),
  pharmacyLicense: z.string().optional(),
  creditLimit: z.preprocess(
    (val) => {
      if (val === '' || val === null || val === undefined) return undefined;
      const num = Number(val);
      return isNaN(num) ? undefined : num;
    },
    z.number().min(0, 'Limit kredit tidak boleh negatif').optional()
  ),
  paymentTerms: z.preprocess(
    (val) => {
      if (val === '' || val === null || val === undefined) return undefined;
      const num = Number(val);
      return isNaN(num) ? undefined : num;
    },
    z.number().min(0, 'Termin pembayaran tidak boleh negatif').optional()
  ),
  preferredPayment: z.nativeEnum(PaymentMethod).optional(),
  bankName: z.string().optional(),
  bankAccount: z.string().optional(),
  bankAccountName: z.string().optional(),
  notes: z.string().optional(),
});

type SupplierFormData = z.infer<typeof supplierSchema>;

interface SupplierFormProps {
  initialData?: Partial<CreateSupplierDto>;
  onSubmit: (data: CreateSupplierDto) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
  mode?: 'create' | 'edit';
}

export function SupplierForm({
  initialData,
  onSubmit,
  onCancel,
  isLoading = false,
  mode = 'create',
}: SupplierFormProps) {
  const [isGeneratingCode, setIsGeneratingCode] = useState(false);
  const [isValidatingCode, setIsValidatingCode] = useState(false);
  const [codeValidationMessage, setCodeValidationMessage] = useState<string>('');

  const generateCodeMutation = useGenerateSupplierCode();
  const validateCodeMutation = useValidateSupplierCode();

  const form = useForm<SupplierFormData>({
    resolver: zodResolver(supplierSchema) as any,
    defaultValues: {
      code: initialData?.code || '',
      name: initialData?.name || '',
      type: initialData?.type || SupplierType.LOCAL,
      address: initialData?.address || '',
      city: initialData?.city || '',
      province: initialData?.province || '',
      postalCode: initialData?.postalCode || '',
      phone: initialData?.phone || '',
      email: initialData?.email || '',
      website: initialData?.website || '',
      npwp: initialData?.npwp || '',
      licenseNumber: initialData?.licenseNumber || '',
      pharmacyLicense: initialData?.pharmacyLicense || '',
      creditLimit: initialData?.creditLimit || undefined,
      paymentTerms: initialData?.paymentTerms || undefined,
      preferredPayment: initialData?.preferredPayment || undefined,
      bankName: initialData?.bankName || '',
      bankAccount: initialData?.bankAccount || '',
      bankAccountName: initialData?.bankAccountName || '',
      notes: initialData?.notes || '',
    },
  });

  // Auto-generate supplier code when type changes (only in create mode)
  const generateSupplierCode = async (type: SupplierType) => {
    if (mode !== 'create') return;

    try {
      setIsGeneratingCode(true);
      const result = await generateCodeMutation.mutateAsync(type);
      form.setValue('code', result.code);
      setCodeValidationMessage('');
      toast.success('Kode supplier berhasil dibuat');
    } catch (error: any) {
      console.error('Error generating supplier code:', error);
      toast.error('Gagal membuat kode supplier');
    } finally {
      setIsGeneratingCode(false);
    }
  };

  // Validate supplier code uniqueness
  const validateSupplierCode = async (code: string) => {
    if (!code || code.length < 3) {
      setCodeValidationMessage('');
      return;
    }

    try {
      setIsValidatingCode(true);
      const result = await validateCodeMutation.mutateAsync(code);
      if (result.isUnique) {
        setCodeValidationMessage('✓ Kode tersedia');
      } else {
        setCodeValidationMessage('✗ Kode sudah digunakan');
      }
    } catch (error: any) {
      console.error('Error validating supplier code:', error);
      setCodeValidationMessage('Gagal memvalidasi kode');
    } finally {
      setIsValidatingCode(false);
    }
  };

  // Watch for supplier type changes to auto-generate code
  const watchedType = form.watch('type');
  const watchedCode = form.watch('code');

  useEffect(() => {
    if (mode === 'create' && watchedType && !initialData?.code) {
      generateSupplierCode(watchedType);
    }
  }, [watchedType, mode]);

  // Debounced code validation
  useEffect(() => {
    if (mode === 'create' && watchedCode) {
      const timeoutId = setTimeout(() => {
        validateSupplierCode(watchedCode);
      }, 500);

      return () => clearTimeout(timeoutId);
    }
  }, [watchedCode, mode]);

  const handleSubmit = async (data: SupplierFormData) => {
    try {
      await onSubmit(data as CreateSupplierDto);
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Informasi Dasar</CardTitle>
            <CardDescription>
              Informasi dasar tentang supplier
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Kode Supplier *</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          placeholder={mode === 'create' ? 'Akan dibuat otomatis...' : 'SUP001'}
                          {...field}
                          disabled={isGeneratingCode}
                        />
                        {(isGeneratingCode || isValidatingCode) && (
                          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                            <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
                          </div>
                        )}
                      </div>
                    </FormControl>
                    {mode === 'create' && codeValidationMessage && (
                      <FormDescription className={
                        codeValidationMessage.startsWith('✓')
                          ? 'text-green-600'
                          : codeValidationMessage.startsWith('✗')
                            ? 'text-red-600'
                            : 'text-muted-foreground'
                      }>
                        {codeValidationMessage}
                      </FormDescription>
                    )}
                    {mode === 'create' && (
                      <FormDescription>
                        Kode akan dibuat otomatis berdasarkan jenis supplier. Anda dapat mengubahnya jika diperlukan.
                      </FormDescription>
                    )}
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div>
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nama Supplier *</FormLabel>
                      <FormControl>
                        <Input placeholder="PT. Supplier Farmasi" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Jenis Supplier *</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Pilih jenis supplier" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {SUPPLIER_TYPE_OPTIONS.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input type="email" placeholder="<EMAIL>" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Telepon</FormLabel>
                    <FormControl>
                      <Input placeholder="021-1234567" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="website"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Website</FormLabel>
                    <FormControl>
                      <Input placeholder="https://supplier.com" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        {/* Address Information */}
        <Card>
          <CardHeader>
            <CardTitle>Informasi Alamat</CardTitle>
            <CardDescription>
              Alamat lengkap supplier
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <FormField
              control={form.control}
              name="address"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Alamat</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Jl. Contoh No. 123, Kelurahan, Kecamatan"
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="city"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Kota</FormLabel>
                    <FormControl>
                      <Input placeholder="Jakarta" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="province"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Provinsi</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Pilih provinsi" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {INDONESIAN_PROVINCES.map((province) => (
                          <SelectItem key={province} value={province}>
                            {province}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="postalCode"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Kode Pos</FormLabel>
                    <FormControl>
                      <Input placeholder="12345" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        {/* Business Information */}
        <Card>
          <CardHeader>
            <CardTitle>Informasi Bisnis</CardTitle>
            <CardDescription>
              Informasi legal dan bisnis supplier
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="npwp"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>NPWP</FormLabel>
                    <FormControl>
                      <Input placeholder="12.345.678.9-012.345" {...field} />
                    </FormControl>
                    <FormDescription>
                      Nomor Pokok Wajib Pajak
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="licenseNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nomor Izin Usaha</FormLabel>
                    <FormControl>
                      <Input placeholder="IU.123456789" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <FormField
              control={form.control}
              name="pharmacyLicense"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Izin Apotek/PBF</FormLabel>
                  <FormControl>
                    <Input placeholder="PBF.123456789" {...field} />
                  </FormControl>
                  <FormDescription>
                    Nomor izin khusus untuk farmasi
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Financial Information */}
        <Card>
          <CardHeader>
            <CardTitle>Informasi Keuangan</CardTitle>
            <CardDescription>
              Pengaturan kredit dan pembayaran
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="creditLimit"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Limit Kredit</FormLabel>
                    <FormControl>
                      <LiveCurrencyInput
                        placeholder="Masukkan limit kredit"
                        value={field.value}
                        onChange={field.onChange}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="paymentTerms"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Termin Pembayaran (Hari)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="Masukkan termin pembayaran"
                        {...field}
                        value={field.value ?? ''}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="preferredPayment"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Metode Pembayaran Utama</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Pilih metode pembayaran" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {PAYMENT_METHOD_OPTIONS.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        {/* Notes */}
        <Card>
          <CardHeader>
            <CardTitle>Catatan</CardTitle>
          </CardHeader>
          <CardContent>
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Catatan Tambahan</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Catatan khusus tentang supplier..."
                      className="resize-none"
                      rows={3}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Form Actions */}
        <div className="flex items-center justify-end space-x-2">
          <Button type="button" variant="outline" onClick={onCancel}>
            Batal
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading ? 'Menyimpan...' : mode === 'create' ? 'Tambah Supplier' : 'Simpan Perubahan'}
          </Button>
        </div>
      </form>
    </Form>
  );
}
