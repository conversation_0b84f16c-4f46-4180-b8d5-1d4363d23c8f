'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import {
  Phone,
  Mail,
  Globe,
  MapPin,
  CreditCard,
  FileText,
  DollarSign,
  Calendar,
  User,
  Edit,
  ExternalLink
} from 'lucide-react';
import { SupplierWithRelations, SupplierPayment } from '@/types/supplier';
import { suppliersApi } from '@/lib/api/suppliers';
import { useSupplierPaymentSummary } from '@/hooks/useSuppliers';
import {
  getSupplierTypeLabel,
  getSupplierStatusLabel,
  getSupplierStatusColor,
  getSupplierTypeColor,
  getPaymentMethodLabel,
} from '@/lib/constants/supplier';
import { formatCurrency } from '@/lib/utils';

interface SupplierQuickViewProps {
  supplier: SupplierWithRelations | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onEdit?: (supplier: SupplierWithRelations) => void;
  onViewFull?: (supplier: SupplierWithRelations) => void;
}

export function SupplierQuickView({
  supplier,
  open,
  onOpenChange,
  onEdit,
  onViewFull,
}: SupplierQuickViewProps) {
  const { status } = useSession();
  const [recentPayments, setRecentPayments] = useState<SupplierPayment[]>([]);
  const [loadingPayments, setLoadingPayments] = useState(false);
  const [paymentsError, setPaymentsError] = useState<string | null>(null);

  // Use payment summary hook for accurate outstanding balance
  const {
    data: paymentSummary,
    isLoading: isLoadingPaymentSummary,
    error: paymentSummaryError
  } = useSupplierPaymentSummary(supplier?.id || '');

  useEffect(() => {
    if (supplier && open) {
      // Clear previous error state when opening modal
      setPaymentsError(null);
      setRecentPayments([]);
      fetchRecentPayments();
    }
  }, [supplier, open]);

  const fetchRecentPayments = async () => {
    if (!supplier) return;

    // Check if user is authenticated
    if (status === 'unauthenticated') {
      setPaymentsError('Sesi Anda telah berakhir. Silakan login kembali.');
      return;
    }

    if (status === 'loading') {
      return; // Wait for session to load
    }

    try {
      setLoadingPayments(true);
      setPaymentsError(null);
      const response = await suppliersApi.getSupplierPayments(supplier.id, 1, 5);
      setRecentPayments(response.data);
    } catch (error: any) {
      console.error('Error fetching payments:', error);

      // Handle different types of errors
      if (error.response?.status === 401) {
        setPaymentsError('Sesi Anda telah berakhir. Silakan login kembali.');
      } else if (error.response?.status === 403) {
        setPaymentsError('Anda tidak memiliki akses untuk melihat data pembayaran.');
      } else if (error.response?.status === 404) {
        setPaymentsError('Data supplier tidak ditemukan.');
      } else if (error.code === 'NETWORK_ERROR' || !error.response) {
        setPaymentsError('Tidak dapat terhubung ke server. Periksa koneksi internet Anda.');
      } else {
        setPaymentsError('Terjadi kesalahan saat memuat data pembayaran.');
      }

      setRecentPayments([]);
    } finally {
      setLoadingPayments(false);
    }
  };

  if (!supplier) return null;

  // Calculate outstanding balance from payment summary (PENDING + OVERDUE)
  const totalOutstanding = paymentSummary
    ? (paymentSummary.summary.totalPending || 0) + (paymentSummary.summary.totalOverdue || 0)
    : 0;

  const primaryContact = supplier.contacts?.find(contact => contact.isPrimary) || supplier.contacts?.[0];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[95vh] overflow-y-auto p-4 sm:p-6 lg:p-8 sm:max-w-3xl">
        <DialogHeader>
          <div className="space-y-4">
            <div className="flex-1 min-w-0">
              <DialogTitle className="text-xl sm:text-2xl font-bold break-words">
                {supplier.name}
              </DialogTitle>
              <DialogDescription className="text-sm sm:text-base mt-1">
                Kode: {supplier.code} • {getSupplierTypeLabel(supplier.type)}
              </DialogDescription>
            </div>
            <div className="flex flex-wrap items-center gap-2 shrink-0">
              <Badge variant="outline" className={getSupplierStatusColor(supplier.status)}>
                {getSupplierStatusLabel(supplier.status)}
              </Badge>
              <Badge variant="outline" className={getSupplierTypeColor(supplier.type)}>
                {getSupplierTypeLabel(supplier.type)}
              </Badge>
            </div>
          </div>
        </DialogHeader>

        <div className="grid gap-4 sm:gap-6 mt-4 sm:mt-6">
          {/* Quick Stats */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
            <Card className="border-l-4 border-l-orange-500">
              <CardContent>
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-orange-50 rounded-lg">
                    <DollarSign className="h-5 w-5 text-orange-600" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-xs sm:text-sm font-medium text-muted-foreground">
                      Saldo Tertunggak
                    </p>
                    {isLoadingPaymentSummary ? (
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 border-2 border-orange-500 border-t-transparent rounded-full animate-spin"></div>
                        <p className="text-xs text-muted-foreground">Memuat...</p>
                      </div>
                    ) : paymentSummaryError ? (
                      <p className="text-xs text-red-600">Gagal memuat</p>
                    ) : (
                      <p className="text-sm sm:text-lg font-bold text-orange-600 truncate">
                        {formatCurrency(totalOutstanding)}
                      </p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-l-4 border-l-blue-500">
              <CardContent>
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-50 rounded-lg">
                    <FileText className="h-5 w-5 text-blue-600" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-xs sm:text-sm font-medium text-muted-foreground">
                      Dokumen
                    </p>
                    <p className="text-sm sm:text-lg font-bold text-blue-600">
                      {supplier._count?.documents || 0}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-l-4 border-l-green-500 sm:col-span-2 lg:col-span-1">
              <CardContent>
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-green-50 rounded-lg">
                    <Calendar className="h-5 w-5 text-green-600" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-xs sm:text-sm font-medium text-muted-foreground">
                      Total Transaksi
                    </p>
                    <p className="text-sm sm:text-lg font-bold text-green-600">
                      {supplier._count?.payments || 0}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 sm:gap-6">
            {/* Contact Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base sm:text-lg flex items-center gap-2">
                  <User className="h-4 w-4 sm:h-5 sm:w-5" />
                  Informasi Kontak
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {supplier.phone && (
                  <div className="flex items-center gap-3">
                    <Phone className="h-4 w-4 text-muted-foreground shrink-0" />
                    <span className="text-sm break-all">{supplier.phone}</span>
                  </div>
                )}
                {supplier.email && (
                  <div className="flex items-center gap-3">
                    <Mail className="h-4 w-4 text-muted-foreground shrink-0" />
                    <span className="text-sm break-all">{supplier.email}</span>
                  </div>
                )}
                {supplier.website && (
                  <div className="flex items-center gap-3">
                    <Globe className="h-4 w-4 text-muted-foreground shrink-0" />
                    <a
                      href={supplier.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-sm text-blue-600 hover:underline break-all"
                    >
                      {supplier.website}
                    </a>
                  </div>
                )}
                {(supplier.address || supplier.city) && (
                  <div className="flex items-start gap-3">
                    <MapPin className="h-4 w-4 text-muted-foreground mt-0.5 shrink-0" />
                    <div className="text-sm min-w-0">
                      {supplier.address && <div className="break-words">{supplier.address}</div>}
                      <div className="break-words">
                        {supplier.city && supplier.province
                          ? `${supplier.city}, ${supplier.province}`
                          : supplier.city || supplier.province
                        }
                        {supplier.postalCode && ` ${supplier.postalCode}`}
                      </div>
                    </div>
                  </div>
                )}

                {primaryContact && (
                  <>
                    <Separator className="my-4" />
                    <div className="bg-muted/30 p-3 rounded-lg">
                      <p className="text-sm font-medium mb-2">Kontak Utama</p>
                      <p className="text-sm font-medium break-words">{primaryContact.name}</p>
                      {primaryContact.position && (
                        <p className="text-xs text-muted-foreground mt-1 break-words">{primaryContact.position}</p>
                      )}
                      {primaryContact.phone && (
                        <p className="text-xs text-muted-foreground break-all">{primaryContact.phone}</p>
                      )}
                    </div>
                  </>
                )}
              </CardContent>
            </Card>

            {/* Financial Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base sm:text-lg flex items-center gap-2">
                  <CreditCard className="h-4 w-4 sm:h-5 sm:w-5" />
                  Informasi Keuangan
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {supplier.creditLimit && (
                  <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2">
                    <span className="text-sm font-medium shrink-0">Limit Kredit:</span>
                    <span className="text-sm break-words">
                      {formatCurrency(supplier.creditLimit)}
                    </span>
                  </div>
                )}
                {supplier.paymentTerms && (
                  <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2">
                    <span className="text-sm font-medium shrink-0">Termin Pembayaran:</span>
                    <span className="text-sm">{supplier.paymentTerms} hari</span>
                  </div>
                )}
                {supplier.preferredPayment && (
                  <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2">
                    <span className="text-sm font-medium shrink-0">Metode Pembayaran:</span>
                    <span className="text-sm break-words">
                      {getPaymentMethodLabel(supplier.preferredPayment)}
                    </span>
                  </div>
                )}
                {supplier.bankName && (
                  <div className="space-y-1">
                    <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2">
                      <span className="text-sm font-medium shrink-0">Bank:</span>
                      <span className="text-sm break-words">{supplier.bankName}</span>
                    </div>
                    {supplier.bankAccount && (
                      <div className="text-sm text-muted-foreground pl-0 sm:pl-2 break-all">
                        {supplier.bankAccount}
                        {supplier.bankAccountName && ` (${supplier.bankAccountName})`}
                      </div>
                    )}
                  </div>
                )}

                {/* Business Info */}
                <Separator className="my-4" />
                {supplier.npwp && (
                  <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2">
                    <span className="text-sm font-medium shrink-0">NPWP:</span>
                    <span className="text-sm break-all">{supplier.npwp}</span>
                  </div>
                )}
                {supplier.licenseNumber && (
                  <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2">
                    <span className="text-sm font-medium shrink-0">Izin Usaha:</span>
                    <span className="text-sm break-words">{supplier.licenseNumber}</span>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Recent Payments */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base sm:text-lg">Transaksi Terbaru</CardTitle>
            </CardHeader>
            <CardContent>
              {loadingPayments ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                  <p className="text-sm text-muted-foreground">Memuat transaksi...</p>
                </div>
              ) : paymentsError ? (
                <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                  <div className="flex items-start gap-3">
                    <div className="w-5 h-5 text-red-500 mt-0.5">
                      <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium text-red-800 dark:text-red-200">
                        Gagal memuat data pembayaran
                      </p>
                      <p className="text-xs text-red-600 dark:text-red-300 mt-1">
                        {paymentsError}
                      </p>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={fetchRecentPayments}
                        className="mt-2 text-red-600 border-red-300 hover:bg-red-50"
                      >
                        Coba Lagi
                      </Button>
                    </div>
                  </div>
                </div>
              ) : recentPayments.length > 0 ? (
                <div className="space-y-3">
                  {recentPayments.slice(0, 3).map((payment) => (
                    <div key={payment.id} className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 p-3 border rounded-lg">
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium break-words">
                          {payment.invoiceNumber || 'Tanpa Nomor Invoice'}
                        </p>
                        <p className="text-xs text-muted-foreground break-words">
                          {new Date(payment.paymentDate).toLocaleDateString('id-ID')} •{' '}
                          {getPaymentMethodLabel(payment.paymentMethod)}
                        </p>
                      </div>
                      <div className="flex items-center justify-between sm:flex-col sm:items-end gap-2">
                        <p className="text-sm font-medium break-words">
                          {formatCurrency(payment.amount)}
                        </p>
                        <Badge
                          variant="outline"
                          className={payment.status === 'PAID' ? 'text-green-600' : 'text-orange-600'}
                        >
                          {payment.status}
                        </Badge>
                      </div>
                    </div>
                  ))}
                  {recentPayments.length > 3 && (
                    <p className="text-xs text-muted-foreground text-center pt-2">
                      +{recentPayments.length - 3} transaksi lainnya
                    </p>
                  )}
                </div>
              ) : (
                <div className="text-center py-4">
                  <div className="w-12 h-12 mx-auto mb-3 text-gray-400">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <p className="text-sm text-muted-foreground">Belum ada transaksi</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row items-stretch sm:items-center justify-end gap-2 sm:gap-3 pt-4 sm:pt-6 border-t">
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
              className="order-3 sm:order-1"
            >
              Tutup
            </Button>
            {onEdit && (
              <Button
                variant="outline"
                onClick={() => onEdit(supplier)}
                className="order-2 sm:order-2"
              >
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </Button>
            )}
            {onViewFull && (
              <Button
                onClick={() => onViewFull(supplier)}
                className="order-1 sm:order-3"
              >
                <ExternalLink className="mr-2 h-4 w-4" />
                Lihat Detail Lengkap
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
