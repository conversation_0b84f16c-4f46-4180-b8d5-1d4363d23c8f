"use client"

import { <PERSON><PERSON> } from "lucide-react";
import { But<PERSON> } from "../ui/button";

export function CopyCodeButton({ code = '' }) {
  const handleCopyCode = () => {
    if (code) {
      navigator.clipboard.writeText(code);
    }
  };

  return <Button
    title="Salin Kode Supplier"
    onClick={handleCopyCode}
    size="sm"
    variant="ghost"
  >
    <Copy className="h-3 w-3" />
  </Button>
}