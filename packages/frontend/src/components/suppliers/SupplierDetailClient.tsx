'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  ArrowLeft,
  Edit,
  Trash2,
  Download,
  RefreshCw,
  MoreHorizontal,
  FileText,
  DollarSign,
  UserX,
  UserCheck,
  AlertTriangle,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { Supplier, SupplierStatus } from '@/types/supplier';
import { DocumentManagementModal } from './DocumentManagementModal';
import { PaymentManagementModal } from './PaymentManagementModal';
import { AlertDialogWrapper } from '@/components/alert-dialog-wrapper';
import {
  useActivateSupplier,
  useDeactivateSupplier,
  useHardDeleteSupplier
} from '@/hooks/useSuppliers';
import { toast } from 'sonner';
import { navigateBackToSuppliers } from '@/lib/utils/navigation';

interface SupplierDetailClientProps {
  supplier: Supplier;
  onRefresh: () => void;
}

export function SupplierDetailClient({ supplier, onRefresh }: SupplierDetailClientProps) {
  const router = useRouter();
  const [showDocumentModal, setShowDocumentModal] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);

  // Mutation hooks for supplier operations
  const activateSupplierMutation = useActivateSupplier();
  const deactivateSupplierMutation = useDeactivateSupplier();
  const hardDeleteSupplierMutation = useHardDeleteSupplier();

  // Status checks
  const isActive = supplier.status === SupplierStatus.ACTIVE;
  const isInactive = supplier.status === SupplierStatus.INACTIVE;

  const handleEdit = () => {
    router.push(`/dashboard/suppliers/${supplier.id}/edit`);
  };



  const handleActivate = async () => {
    try {
      await activateSupplierMutation.mutateAsync(supplier.id);
      toast.success('Supplier berhasil diaktifkan');
      onRefresh();
    } catch (err: any) {
      toast.error(err?.response?.data?.message || 'Gagal mengaktifkan supplier');
      console.error('Error activating supplier:', err);
    }
  };

  const handleDeactivate = async () => {
    try {
      await deactivateSupplierMutation.mutateAsync(supplier.id);
      toast.success('Supplier berhasil dinonaktifkan');
      onRefresh();
    } catch (err: any) {
      toast.error(err?.response?.data?.message || 'Gagal menonaktifkan supplier');
      console.error('Error deactivating supplier:', err);
    }
  };

  const handleHardDelete = async () => {
    try {
      await hardDeleteSupplierMutation.mutateAsync(supplier.id);
      toast.success('Supplier berhasil dihapus secara permanen');
      router.push('/dashboard/suppliers');
    } catch (err: any) {
      const errorMessage = err?.response?.data?.message || 'Gagal menghapus supplier secara permanen';
      toast.error(errorMessage);
      console.error('Error hard deleting supplier:', err);
    }
  };

  const handleExport = () => {
    // Implementation for exporting supplier data
    console.log('Export supplier data');
  };

  return (
    <>
      <div className="flex flex-wrap gap-2">
        <Button variant="outline" onClick={() => navigateBackToSuppliers(router)}>
          <ArrowLeft className="h-4 w-4" /> Kembali
        </Button>

        <Button variant="outline" onClick={() => setShowDocumentModal(true)}>
          <FileText className="mr-2 h-4 w-4" />
          Kelola Dokumen
        </Button>

        <Button variant="outline" onClick={() => setShowPaymentModal(true)}>
          <DollarSign className="mr-2 h-4 w-4" />
          Manajemen Pembayaran
        </Button>

        <Button variant="outline" onClick={handleExport}>
          <Download className="mr-2 h-4 w-4" />
          Export
        </Button>

        <Button variant="outline" onClick={onRefresh}>
          <RefreshCw className="mr-2 h-4 w-4" />
          Refresh
        </Button>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={handleEdit}>
              <Edit className="mr-2 h-4 w-4" />
              Edit Supplier
            </DropdownMenuItem>
            <DropdownMenuSeparator />

            {/* Status-based actions */}
            {isActive && (
              <AlertDialogWrapper
                variant="warning"
                title="Nonaktifkan Supplier"
                description="Apakah Anda yakin ingin menonaktifkan supplier ini?"
                confirmText="Nonaktifkan"
                cancelText="Batal"
                pendingText="Menonaktifkan..."
                disabled={deactivateSupplierMutation.isPending}
                handler={handleDeactivate}
              >
                <DropdownMenuItem
                  className="text-orange-600 focus:text-orange-600"
                  onSelect={(event) => {
                    event.preventDefault();
                  }}
                >
                  <UserX className="mr-2 h-4 w-4" />
                  Nonaktifkan
                </DropdownMenuItem>
              </AlertDialogWrapper>
            )}

            {isInactive && (
              <AlertDialogWrapper
                variant="primary"
                title="Aktifkan Supplier"
                description="Apakah Anda yakin ingin mengaktifkan supplier ini?"
                confirmText="Aktifkan"
                cancelText="Batal"
                pendingText="Mengaktifkan..."
                disabled={activateSupplierMutation.isPending}
                handler={handleActivate}
              >
                <DropdownMenuItem
                  className="text-green-600 focus:text-green-600"
                  onSelect={(event) => {
                    event.preventDefault();
                  }}
                >
                  <UserCheck className="mr-2 h-4 w-4" />
                  Aktifkan
                </DropdownMenuItem>
              </AlertDialogWrapper>
            )}

            {isInactive && (
              <AlertDialogWrapper
                variant="destructive"
                title="Hapus Supplier Permanen"
                description={`Anda akan menghapus permanen supplier "${supplier.name}". Tindakan ini akan:

• Menghapus semua kontak supplier yang terkait secara permanen
• Menghilangkan semua dokumen supplier dan catatan pembayaran
• Menyebabkan inkonsistensi pada riwayat purchase order yang mereferensikan supplier ini
• Mempengaruhi pelaporan keuangan dan analisis vendor
• Berdampak pada operasional bisnis dan hubungan supplier

Tindakan ini TIDAK DAPAT DIBATALKAN dan dapat berdampak serius pada integritas data sistem.`}
                confirmText="Hapus Permanen"
                cancelText="Batal"
                pendingText="Menghapus..."
                requireConfirmationText="HAPUS"
                confirmationPlaceholder="Ketik 'HAPUS' untuk mengonfirmasi"
                disabled={hardDeleteSupplierMutation.isPending}
                handler={handleHardDelete}
              >
                <DropdownMenuItem
                  className="text-red-600 focus:text-red-600"
                  onSelect={(event) => {
                    event.preventDefault();
                  }}
                >
                  <AlertTriangle className="mr-2 h-4 w-4" />
                  Hapus Permanen
                </DropdownMenuItem>
              </AlertDialogWrapper>
            )}


          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Document Management Modal */}
      <DocumentManagementModal
        open={showDocumentModal}
        onOpenChange={setShowDocumentModal}
        supplierId={supplier.id}
        supplierName={supplier.name}
      />

      {/* Payment Management Modal */}
      <PaymentManagementModal
        open={showPaymentModal}
        onOpenChange={setShowPaymentModal}
        supplierId={supplier.id}
        supplierName={supplier.name}
      />
    </>
  );
}
