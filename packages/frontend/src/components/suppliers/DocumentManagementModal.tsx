'use client';

import { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Upload,
  FileText,
  Download,
  Trash2,
  Search,
  Filter,
  Plus,
  X,
  Eye,
  Edit,
  Calendar,
  User,
  FileIcon,
} from 'lucide-react';
import { Tabs, TabsContent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  useSupplierDocuments,
  useUploadSupplierDocument,
  useDeleteSupplierDocument
} from '@/hooks/useSuppliers';
import { DocumentQueryParams, SupplierDocument } from '@/types/supplier';
import { DOCUMENT_TYPE_OPTIONS } from '@/lib/constants/supplier';
import suppliersApi from '@/lib/api/suppliers';

interface DocumentManagementModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  supplierId: string;
  supplierName: string;
}

export function DocumentManagementModal({
  open,
  onOpenChange,
  supplierId,
  supplierName,
}: DocumentManagementModalProps) {
  const [query, setQuery] = useState<DocumentQueryParams>({
    page: 1,
    limit: 10,
  });
  const [selectedDocuments, setSelectedDocuments] = useState<string[]>([]);
  const [uploadFiles, setUploadFiles] = useState<File[]>([]);
  const [uploadMetadata, setUploadMetadata] = useState({
    type: '',
    name: '',
    description: '',
  });

  // Queries and mutations
  const { data: documentsData, isLoading } = useSupplierDocuments(supplierId, query);
  const uploadMutation = useUploadSupplierDocument();
  const deleteMutation = useDeleteSupplierDocument();

  // File upload handling
  const onDrop = useCallback((acceptedFiles: File[]) => {
    setUploadFiles(prev => [...prev, ...acceptedFiles]);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'application/vnd.ms-excel': ['.xls'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'image/*': ['.png', '.jpg', '.jpeg', '.gif'],
    },
    maxSize: 10 * 1024 * 1024, // 10MB
  });

  const handleUpload = async () => {
    if (uploadFiles.length === 0 || !uploadMetadata.type || !uploadMetadata.name) {
      toast.error('Harap pilih file dan lengkapi informasi dokumen');
      return;
    }

    const uploadPromise = async () => {
      const uploadedFiles = [];
      for (const file of uploadFiles) {
        const result = await uploadMutation.mutateAsync({
          supplierId,
          file,
          metadata: uploadMetadata,
        });
        uploadedFiles.push(result);
      }
      return uploadedFiles;
    };

    toast.promise(uploadPromise(), {
      loading: `Mengupload ${uploadFiles.length} dokumen...`,
      success: (data) => {
        setUploadFiles([]);
        setUploadMetadata({ type: '', name: '', description: '' });
        return `Berhasil mengupload ${data.length} dokumen`;
      },
      error: (error) => {
        console.error('Upload failed:', error);
        return error?.response?.data?.message || 'Gagal mengupload dokumen. Silakan coba lagi.';
      },
    });
  };

  const handleDelete = async (documentId: string) => {
    if (confirm('Apakah Anda yakin ingin menghapus dokumen ini?')) {
      const deletePromise = deleteMutation.mutateAsync({ supplierId, documentId });

      toast.promise(deletePromise, {
        loading: 'Menghapus dokumen...',
        success: 'Dokumen berhasil dihapus',
        error: (error) => {
          console.error('Delete failed:', error);
          return error?.response?.data?.message || 'Gagal menghapus dokumen. Silakan coba lagi.';
        },
      });
    }
  };

  const handleBulkDelete = async () => {
    if (selectedDocuments.length === 0) return;

    if (confirm(`Apakah Anda yakin ingin menghapus ${selectedDocuments.length} dokumen?`)) {
      const bulkDeletePromise = async () => {
        for (const documentId of selectedDocuments) {
          await deleteMutation.mutateAsync({ supplierId, documentId });
        }
        setSelectedDocuments([]);
        return selectedDocuments.length;
      };

      toast.promise(bulkDeletePromise(), {
        loading: `Menghapus ${selectedDocuments.length} dokumen...`,
        success: (count) => `Berhasil menghapus ${count} dokumen`,
        error: (error) => {
          console.error('Bulk delete failed:', error);
          return error?.response?.data?.message || 'Gagal menghapus dokumen. Silakan coba lagi.';
        },
      });
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getDocumentTypeLabel = (type: string) => {
    const option = DOCUMENT_TYPE_OPTIONS.find(opt => opt.value === type);
    return option?.label || type;
  };

  const handleDownload = async (doc: SupplierDocument) => {
    try {
      toast.loading('Mengunduh dokumen...', { id: `download-${doc.id}` });

      const blob = await suppliersApi.downloadSupplierDocument(supplierId, doc.id);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = doc.fileName || doc.name;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast.success('Dokumen berhasil diunduh', { id: `download-${doc.id}` });
    } catch (error) {
      console.error('Download failed:', error);
      toast.error('Gagal mengunduh dokumen', { id: `download-${doc.id}` });
    }
  };

  const handleView = async (doc: SupplierDocument) => {
    try {
      toast.loading('Membuka dokumen...', { id: `view-${doc.id}` });

      const blob = await suppliersApi.downloadSupplierDocument(supplierId, doc.id);
      const url = window.URL.createObjectURL(blob);
      window.open(url, '_blank');

      toast.success('Dokumen berhasil dibuka', { id: `view-${doc.id}` });
    } catch (error) {
      console.error('View failed:', error);
      toast.error('Gagal membuka dokumen', { id: `view-${doc.id}` });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[95vh] overflow-y-auto p-4 sm:p-6 lg:p-8 sm:max-w-4xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Manajemen Dokumen - {supplierName}
          </DialogTitle>
          <DialogDescription>
            Kelola dokumen supplier seperti kontrak, faktur, sertifikat, dan dokumen lainnya
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="documents" className="flex-1 overflow-hidden">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="documents">Daftar Dokumen</TabsTrigger>
            <TabsTrigger value="upload">Upload Dokumen</TabsTrigger>
          </TabsList>

          <TabsContent value="documents" className="space-y-4 overflow-auto max-h-[60vh]">
            {/* Search and Filter Controls */}
            <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
              <div className="flex gap-2">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Cari dokumen..."
                    value={query.search || ''}
                    onChange={(e) => setQuery(prev => ({ ...prev, search: e.target.value, page: 1 }))}
                    className="pl-10 w-64"
                  />
                </div>
                <Select
                  value={query.type || 'all'}
                  onValueChange={(value) => setQuery(prev => ({
                    ...prev,
                    type: value === 'all' ? undefined : value as any,
                    page: 1
                  }))}
                >
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Semua Jenis" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Semua Jenis</SelectItem>
                    {DOCUMENT_TYPE_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {selectedDocuments.length > 0 && (
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={handleBulkDelete}
                  disabled={deleteMutation.isPending}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Hapus Terpilih ({selectedDocuments.length})
                </Button>
              )}
            </div>

            {/* Documents Table */}
            <div className="border rounded-lg">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-12">
                      <Checkbox
                        checked={
                          (documentsData?.data.length || -1) > 0 &&
                          selectedDocuments.length === (documentsData?.data.length || -1)
                        }
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setSelectedDocuments(documentsData?.data.map(doc => doc.id) || []);
                          } else {
                            setSelectedDocuments([]);
                          }
                        }}
                      />
                    </TableHead>
                    <TableHead>Nama Dokumen</TableHead>
                    <TableHead>Jenis</TableHead>
                    <TableHead>Ukuran</TableHead>
                    <TableHead>Tanggal Upload</TableHead>
                    <TableHead>Aksi</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    Array.from({ length: 5 }).map((_, index) => (
                      <TableRow key={index}>
                        <TableCell colSpan={6}>
                          <div className="h-4 bg-muted animate-pulse rounded"></div>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : documentsData?.data.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                        Belum ada dokumen yang diupload
                      </TableCell>
                    </TableRow>
                  ) : (
                    documentsData?.data.map((document) => (
                      <TableRow key={document.id}>
                        <TableCell>
                          <Checkbox
                            checked={selectedDocuments.includes(document.id)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setSelectedDocuments(prev => [...prev, document.id]);
                              } else {
                                setSelectedDocuments(prev => prev.filter(id => id !== document.id));
                              }
                            }}
                          />
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <FileIcon className="h-4 w-4 text-muted-foreground" />
                            <div>
                              <p className="font-medium">{document.name}</p>
                              {document.description && (
                                <p className="text-sm text-muted-foreground">{document.description}</p>
                              )}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">
                            {getDocumentTypeLabel(document.type)}
                          </Badge>
                        </TableCell>
                        <TableCell>{formatFileSize(document.fileSize || 0)}</TableCell>
                        <TableCell>
                          {new Date(document.uploadedAt).toLocaleDateString('id-ID')}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleView(document)}
                              title="Lihat dokumen"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDownload(document)}
                              title="Unduh dokumen"
                            >
                              <Download className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDelete(document.id)}
                              disabled={deleteMutation.isPending}
                              title="Hapus dokumen"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>

            {/* Pagination */}
            {documentsData?.meta && documentsData.meta.totalPages > 1 && (
              <div className="flex items-center justify-between">
                <p className="text-sm text-muted-foreground">
                  Menampilkan {((documentsData.meta.page - 1) * documentsData.meta.limit) + 1} - {Math.min(documentsData.meta.page * documentsData.meta.limit, documentsData.meta.total)} dari {documentsData.meta.total} dokumen
                </p>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setQuery(prev => ({ ...prev, page: prev.page! - 1 }))}
                    disabled={!documentsData.meta.hasPreviousPage}
                  >
                    Sebelumnya
                  </Button>
                  <span className="text-sm">
                    Halaman {documentsData.meta.page} dari {documentsData.meta.totalPages}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setQuery(prev => ({ ...prev, page: prev.page! + 1 }))}
                    disabled={!documentsData.meta.hasNextPage}
                  >
                    Selanjutnya
                  </Button>
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="upload" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Upload Dokumen Baru</CardTitle>
                <CardDescription>
                  Drag & drop file atau klik untuk memilih. Maksimal 10MB per file.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* File Upload Area */}
                <div
                  {...getRootProps()}
                  className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${isDragActive ? 'border-primary bg-primary/5' : 'border-muted-foreground/25'
                    }`}
                >
                  <input {...getInputProps()} />
                  <Upload className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  {isDragActive ? (
                    <p>Drop file di sini...</p>
                  ) : (
                    <div>
                      <p className="text-lg font-medium">Drag & drop file di sini</p>
                      <p className="text-sm text-muted-foreground">atau klik untuk memilih file</p>
                      <p className="text-xs text-muted-foreground mt-2">
                        Mendukung: PDF, DOC, DOCX, XLS, XLSX, gambar (maks. 10MB)
                      </p>
                    </div>
                  )}
                </div>

                {/* Selected Files */}
                {uploadFiles.length > 0 && (
                  <div className="space-y-2">
                    <Label>File Terpilih:</Label>
                    {uploadFiles.map((file, index) => (
                      <div key={index} className="flex items-center justify-between p-2 border rounded">
                        <div className="flex items-center gap-2">
                          <FileIcon className="h-4 w-4" />
                          <span className="text-sm">{file.name}</span>
                          <span className="text-xs text-muted-foreground">
                            ({formatFileSize(file.size)})
                          </span>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setUploadFiles(prev => prev.filter((_, i) => i !== index))}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                )}

                {/* Upload Metadata */}
                <div className="grid gap-4 sm:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="document-type">Jenis Dokumen *</Label>
                    <Select
                      value={uploadMetadata.type}
                      onValueChange={(value) => setUploadMetadata(prev => ({ ...prev, type: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Pilih jenis dokumen" />
                      </SelectTrigger>
                      <SelectContent>
                        {DOCUMENT_TYPE_OPTIONS.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="document-name">Nama Dokumen *</Label>
                    <Input
                      id="document-name"
                      value={uploadMetadata.name}
                      onChange={(e) => setUploadMetadata(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="Masukkan nama dokumen"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="document-description">Deskripsi (Opsional)</Label>
                  <Textarea
                    id="document-description"
                    value={uploadMetadata.description}
                    onChange={(e) => setUploadMetadata(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Masukkan deskripsi dokumen"
                    rows={3}
                  />
                </div>

                <Button
                  onClick={handleUpload}
                  disabled={
                    uploadFiles.length === 0 ||
                    !uploadMetadata.type ||
                    !uploadMetadata.name ||
                    uploadMutation.isPending
                  }
                  className="w-full"
                >
                  {uploadMutation.isPending ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Mengupload...
                    </>
                  ) : (
                    <>
                      <Upload className="h-4 w-4 mr-2" />
                      Upload Dokumen
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
