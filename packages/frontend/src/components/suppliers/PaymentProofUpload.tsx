'use client';

import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { toast } from 'sonner';
import {
  Upload,
  FileText,
  Download,
  Trash2,
  Eye,
  X,
  CheckCircle,
  AlertCircle,
} from 'lucide-react';
import { usePaymentProof, useUploadPaymentProof, useDeletePaymentProof, handleDownload, handleView } from '@/hooks/useSuppliers';
import { SupplierDocument } from '@/types/supplier';
import { formatFileSize } from '@/lib/utils';
import { AlertDialogWrapper } from '@/components/alert-dialog-wrapper';

interface PaymentProofUploadProps {
  supplierId: string;
  paymentId: string;
  paymentReference?: string;
  onProofUploaded?: () => void;
}

export function PaymentProofUpload({
  supplierId,
  paymentId,
  paymentReference,
  onProofUploaded,
}: PaymentProofUploadProps) {
  const [uploadData, setUploadData] = useState({
    name: '',
    description: '',
  });
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [showUploadForm, setShowUploadForm] = useState(false);

  // Queries and mutations
  const { data: paymentProof, isLoading } = usePaymentProof(supplierId, paymentId);
  const uploadMutation = useUploadPaymentProof();
  const deleteMutation = useDeletePaymentProof();

  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles.length > 0) {
      const file = acceptedFiles[0];
      setSelectedFile(file);
      setUploadData(prev => ({
        ...prev,
        name: prev.name || `Bukti Pembayaran ${paymentReference || paymentId}`,
      }));
      setShowUploadForm(true);
    }
  }, [paymentReference, paymentId]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'image/jpeg': ['.jpg', '.jpeg'],
      'image/png': ['.png'],
    },
    maxFiles: 1,
    maxSize: 10 * 1024 * 1024, // 10MB
  });

  const handleUpload = async () => {
    if (!selectedFile) return;

    const uploadPromise = uploadMutation.mutateAsync({
      supplierId,
      paymentId,
      file: selectedFile,
      data: uploadData,
    });

    toast.promise(uploadPromise, {
      loading: 'Mengunggah bukti pembayaran...',
      success: () => {
        setSelectedFile(null);
        setUploadData({ name: '', description: '' });
        setShowUploadForm(false);
        onProofUploaded?.();
        return 'Bukti pembayaran berhasil diunggah';
      },
      error: (error) => {
        console.error('Upload failed:', error);
        return error?.response?.data?.message || 'Gagal mengunggah bukti pembayaran';
      },
    });
  };

  const handleDelete = async () => {
    const deletePromise = deleteMutation.mutateAsync({
      supplierId,
      paymentId,
    });

    toast.promise(deletePromise, {
      loading: 'Menghapus bukti pembayaran...',
      success: 'Bukti pembayaran berhasil dihapus',
      error: (error) => {
        console.error('Delete failed:', error);
        return error?.response?.data?.message || 'Gagal menghapus bukti pembayaran';
      },
    });
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
            <span className="ml-2 text-sm text-muted-foreground">Memuat bukti pembayaran...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          Bukti Pembayaran
        </CardTitle>
        <CardDescription>
          Unggah bukti pembayaran untuk transaksi ini (PDF, JPG, PNG - maksimal 10MB)
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {paymentProof ? (
          // Show existing payment proof
          <div className="border rounded-lg p-4 bg-green-50 dark:bg-green-900/20">
            <div className="flex items-start justify-between">
              <div className="flex items-start gap-3">
                <div className="p-2 bg-green-100 dark:bg-green-800 rounded-lg">
                  <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400" />
                </div>
                <div className="flex-1 min-w-0">
                  <h4 className="font-medium text-green-800 dark:text-green-200">
                    {paymentProof.name}
                  </h4>
                  {paymentProof.description && (
                    <p className="text-sm text-green-700 dark:text-green-300 mt-1">
                      {paymentProof.description}
                    </p>
                  )}
                  <div className="flex items-center gap-4 mt-2 text-xs text-green-600 dark:text-green-400">
                    {paymentProof.fileName && (
                      <span>File: {paymentProof.fileName}</span>
                    )}
                    {paymentProof.fileSize && (
                      <span>Ukuran: {formatFileSize(paymentProof.fileSize)}</span>
                    )}
                    <span>
                      Diunggah: {new Date(paymentProof.uploadedAt).toLocaleDateString('id-ID')}
                    </span>
                  </div>
                </div>
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleView(supplierId, paymentProof)}
                  className="text-green-700 border-green-200 hover:bg-green-100"
                >
                  <Eye className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleDownload(supplierId, paymentProof)}
                  className="text-green-700 border-green-200 hover:bg-green-100"
                >
                  <Download className="h-4 w-4" />
                </Button>
                <AlertDialogWrapper
                  variant="destructive"
                  title="Hapus Bukti Pembayaran"
                  description="Apakah Anda yakin ingin menghapus bukti pembayaran ini? Tindakan ini tidak dapat dibatalkan."
                  confirmText="Hapus"
                  cancelText="Batal"
                  pendingText="Menghapus..."
                  disabled={deleteMutation.isPending}
                  handler={handleDelete}
                >
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-red-600 border-red-200 hover:bg-red-50"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </AlertDialogWrapper>
              </div>
            </div>
          </div>
        ) : showUploadForm ? (
          // Show upload form
          <div className="space-y-4">
            <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-4 bg-muted/10">
              <div className="flex items-center gap-3">
                <FileText className="h-8 w-8 text-muted-foreground" />
                <div>
                  <p className="font-medium">{selectedFile?.name}</p>
                  <p className="text-sm text-muted-foreground">
                    {selectedFile && formatFileSize(selectedFile.size)}
                  </p>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setSelectedFile(null);
                    setShowUploadForm(false);
                  }}
                  className="ml-auto"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div className="space-y-3">
              <div>
                <Label htmlFor="proof-name">Nama Dokumen</Label>
                <Input
                  id="proof-name"
                  value={uploadData.name}
                  onChange={(e) => setUploadData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Masukkan nama dokumen"
                />
              </div>
              <div>
                <Label htmlFor="proof-description">Deskripsi (Opsional)</Label>
                <Textarea
                  id="proof-description"
                  value={uploadData.description}
                  onChange={(e) => setUploadData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Masukkan deskripsi dokumen"
                  rows={3}
                />
              </div>
            </div>

            <div className="flex gap-2">
              <Button
                onClick={handleUpload}
                disabled={!uploadData.name.trim() || uploadMutation.isPending}
                className="flex-1"
              >
                {uploadMutation.isPending ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Mengunggah...
                  </>
                ) : (
                  <>
                    <Upload className="h-4 w-4 mr-2" />
                    Unggah Bukti Pembayaran
                  </>
                )}
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  setSelectedFile(null);
                  setShowUploadForm(false);
                }}
              >
                Batal
              </Button>
            </div>
          </div>
        ) : (
          // Show upload dropzone
          <div
            {...getRootProps()}
            className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${isDragActive
              ? 'border-primary bg-primary/5'
              : 'border-muted-foreground/25 hover:border-primary/50 hover:bg-muted/50'
              }`}
          >
            <input {...getInputProps()} />
            <div className="flex flex-col items-center gap-3">
              <div className="p-3 bg-muted rounded-full">
                <Upload className="h-6 w-6 text-muted-foreground" />
              </div>
              <div>
                <p className="font-medium">
                  {isDragActive ? 'Lepaskan file di sini' : 'Seret file ke sini atau klik untuk memilih'}
                </p>
                <p className="text-sm text-muted-foreground mt-1">
                  PDF, JPG, PNG (maksimal 10MB)
                </p>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
