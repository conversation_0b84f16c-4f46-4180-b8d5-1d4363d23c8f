'use client';

import { useRouter } from 'next/navigation';
import { ArrowLeft, Package } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { GoodsReceiptForm } from '@/components/goods-receipts/goods-receipt-form';
import { useCreateGoodsReceipt } from '@/hooks/useGoodsReceipts';
import { CreateGoodsReceiptDto } from '@/types/goods-receipt';

export function NewGoodsReceiptPageClient() {
  const router = useRouter();
  const createGoodsReceiptMutation = useCreateGoodsReceipt();

  const handleSubmit = async (data: CreateGoodsReceiptDto) => {
    try {
      const result = await createGoodsReceiptMutation.mutateAsync(data);
      router.push(`/dashboard/goods-receipts/${result.id}`);
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  const handleBack = () => {
    router.back();
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col items-start md:items-center md:flex-row  gap-4">
        <Button variant="outline" size="sm" onClick={handleBack}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Kembali
        </Button>
        <div>
          <h1 className="text-2xl font-bold tracking-tight flex items-center gap-2">
            <Package className="h-6 w-6" />
            Penerimaan Barang Baru
          </h1>
          <p className="text-muted-foreground">
            Buat penerimaan barang baru dan lakukan kontrol kualitas
          </p>
        </div>
      </div>

      {/* Form */}
      <Card className="shadow-none rounded-none border-x-0 gap-3 md:gap-6 md:border md:rounded-xl md:shadow">
        <CardHeader className="px-0 md:px-6">
          <CardTitle>Form Penerimaan Barang</CardTitle>
        </CardHeader>
        <CardContent className="px-0 md:px-6">
          <GoodsReceiptForm
            onSubmit={handleSubmit}
            isSubmitting={createGoodsReceiptMutation.isPending}
            mode="create"
          />
        </CardContent>
      </Card>
    </div>
  );
}
