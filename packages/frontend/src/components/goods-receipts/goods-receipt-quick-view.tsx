'use client';

import { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import {
  Package,
  Building2,
  Calendar,
  DollarSign,
  ShoppingCart,
  Truck,
  User,
  Edit,
  ExternalLink,
  CheckCircle,
  XCircle,
  FileText,
  Clock
} from 'lucide-react';
import { GoodsReceiptWithRelations, GoodsReceiptStatus } from '@/types/goods-receipt';
import { formatCurrency, formatDate } from '@/lib/utils';

interface GoodsReceiptQuickViewProps {
  goodsReceipt: GoodsReceiptWithRelations | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onEdit?: (goodsReceipt: GoodsReceiptWithRelations) => void;
  onReject?: (goodsReceipt: GoodsReceiptWithRelations) => void;
  onComplete?: (goodsReceipt: GoodsReceiptWithRelations) => void;
  onDelete?: (goodsReceipt: GoodsReceiptWithRelations) => void;
  onViewDetails?: (goodsReceipt: GoodsReceiptWithRelations) => void;
}

function getStatusBadge(status: GoodsReceiptStatus) {
  const statusConfig = {
    [GoodsReceiptStatus.PENDING]: {
      label: 'Menunggu',
      variant: 'secondary' as const,
      icon: Clock,
    },
    [GoodsReceiptStatus.REJECTED]: {
      label: 'Ditolak',
      variant: 'destructive' as const,
      icon: XCircle,
    },
    [GoodsReceiptStatus.COMPLETED]: {
      label: 'Selesai',
      variant: 'default' as const,
      icon: CheckCircle,
    },
  };

  const config = statusConfig[status];
  const Icon = config.icon;

  return (
    <Badge variant={config.variant} className="flex items-center gap-1">
      <Icon className="h-3 w-3" />
      {config.label}
    </Badge>
  );
}

export function GoodsReceiptQuickView({
  goodsReceipt,
  open,
  onOpenChange,
  onEdit,
  onReject,
  onComplete,
  onDelete,
  onViewDetails,
}: GoodsReceiptQuickViewProps) {
  const [isLoading, setIsLoading] = useState(false);

  if (!goodsReceipt) return null;

  const canEdit = goodsReceipt.status === GoodsReceiptStatus.PENDING;
  const canReject = goodsReceipt.status === GoodsReceiptStatus.PENDING;
  const canComplete = goodsReceipt.status === GoodsReceiptStatus.PENDING;
  const canDelete = goodsReceipt.status === GoodsReceiptStatus.PENDING;

  const handleAction = async (action: () => void | Promise<void>) => {
    setIsLoading(true);
    try {
      await action();
      onOpenChange(false);
    } catch (error) {
      // Error is already handled by the mutation's onError callback
      if (process.env.NODE_ENV === 'development') {
        console.error('Action failed:', error);
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Detail Penerimaan Barang {goodsReceipt.receiptNumber}
          </DialogTitle>
          <DialogDescription>
            Informasi lengkap penerimaan barang dan status kontrol kualitas
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-6 py-4">
          {/* Status and Actions */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {getStatusBadge(goodsReceipt.status)}
            </div>

            <div className="flex items-center gap-2">
              {onEdit && canEdit && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleAction(() => onEdit(goodsReceipt))}
                  disabled={isLoading}
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </Button>
              )}

              {onComplete && canComplete && (
                <Button
                  variant="default"
                  size="sm"
                  onClick={() => handleAction(() => onComplete(goodsReceipt))}
                  disabled={isLoading}
                >
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Selesaikan
                </Button>
              )}

              {onViewDetails && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onViewDetails(goodsReceipt)}
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Detail Lengkap
                </Button>
              )}
            </div>
          </div>

          <div className="grid gap-6 md:grid-cols-2">
            {/* Goods Receipt Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  Informasi Penerimaan
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-muted-foreground">Nomor Penerimaan</p>
                    <p className="font-medium">{goodsReceipt.receiptNumber}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Tanggal Terima</p>
                    <p className="font-medium">{formatDate(goodsReceipt.receiptDate)}</p>
                  </div>
                  {goodsReceipt.deliveryDate && (
                    <div>
                      <p className="text-muted-foreground">Tanggal Kirim</p>
                      <p className="font-medium">{formatDate(goodsReceipt.deliveryDate)}</p>
                    </div>
                  )}
                  <div>
                    <p className="text-muted-foreground">Status</p>
                    {getStatusBadge(goodsReceipt.status)}
                  </div>
                  {goodsReceipt.invoiceNumber && (
                    <div>
                      <p className="text-muted-foreground">Nomor Invoice</p>
                      <p className="font-medium">{goodsReceipt.invoiceNumber}</p>
                    </div>
                  )}
                  {goodsReceipt.deliveryNote && (
                    <div>
                      <p className="text-muted-foreground">Surat Jalan</p>
                      <p className="font-medium">{goodsReceipt.deliveryNote}</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Supplier Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Building2 className="h-5 w-5" />
                  Informasi Supplier
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div>
                    <p className="font-medium text-lg">{goodsReceipt.supplier.name}</p>
                    <p className="text-sm text-muted-foreground">{goodsReceipt.supplier.code}</p>
                  </div>
                  
                  {goodsReceipt.supplier.phone && (
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">{goodsReceipt.supplier.phone}</span>
                    </div>
                  )}
                  
                  <Badge variant="secondary" className="text-xs">
                    {goodsReceipt.supplier.type}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Purchase Order Information */}
          {goodsReceipt.purchaseOrder && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <ShoppingCart className="h-5 w-5" />
                  Purchase Order Terkait
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <p className="text-muted-foreground">Nomor PO</p>
                    <p className="font-medium">{goodsReceipt.purchaseOrder.orderNumber}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Tanggal Order</p>
                    <p className="font-medium">{formatDate(goodsReceipt.purchaseOrder.orderDate)}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Total PO</p>
                    <p className="font-medium">{formatCurrency(goodsReceipt.purchaseOrder.totalAmount)}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Status PO</p>
                    <Badge variant="outline">
                      {goodsReceipt.purchaseOrder.status}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Financial Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                Ringkasan Keuangan
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-muted-foreground">Total Penerimaan</p>
                  <p className="font-bold text-lg">{formatCurrency(goodsReceipt.totalAmount)}</p>
                </div>
                <div>
                  <p className="text-muted-foreground">Jumlah Item</p>
                  <p className="font-medium">{goodsReceipt.items.length} item</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Goods Receipt Items */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Package className="h-5 w-5" />
                Item Penerimaan ({goodsReceipt.items.length} item)
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {goodsReceipt.items.map((item) => (
                  <div key={item.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex-1">
                      <div className="font-medium">{item.product.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {item.product.code} • {item.product.manufacturer}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Diterima: {item.quantityReceived} {item.unit.abbreviation} × {formatCurrency(item.unitPrice)}
                      </div>
                      {item.batchNumber && (
                        <div className="text-xs text-muted-foreground">
                          Batch: {item.batchNumber}
                          {item.expiryDate && ` • Exp: ${formatDate(item.expiryDate)}`}
                        </div>
                      )}
                      {item.notes && (
                        <div className="text-xs text-muted-foreground mt-1">
                          Catatan: {item.notes}
                        </div>
                      )}
                    </div>
                    <div className="text-right">
                      <div className="font-medium">{formatCurrency(item.totalPrice)}</div>

                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Delivery Information */}
          {(goodsReceipt.deliveryNote || goodsReceipt.receivedBy) && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Truck className="h-5 w-5" />
                  Informasi Pengiriman
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {goodsReceipt.receivedBy && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Diterima oleh</p>
                    <p className="text-sm">{goodsReceipt.receivedBy}</p>
                  </div>
                )}
                {goodsReceipt.deliveryNote && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Catatan Pengiriman</p>
                    <p className="text-sm">{goodsReceipt.deliveryNote}</p>
                  </div>
                )}
                {goodsReceipt.notes && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Catatan Penerimaan</p>
                    <p className="text-sm">{goodsReceipt.notes}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Action Buttons */}
          <div className="flex items-center justify-end gap-2 pt-4 border-t">
            {onReject && canReject && (
              <Button
                variant="outline"
                onClick={() => handleAction(() => onReject(goodsReceipt))}
                disabled={isLoading}
                className="text-red-600 hover:text-red-700"
              >
                <XCircle className="h-4 w-4 mr-2" />
                Tolak
              </Button>
            )}

            {onDelete && canDelete && (
              <Button
                variant="outline"
                onClick={() => handleAction(() => onDelete(goodsReceipt))}
                disabled={isLoading}
                className="text-red-600 hover:text-red-700"
              >
                <FileText className="h-4 w-4 mr-2" />
                Hapus
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
