import { z } from "zod";

export const NO_PURCHASE_ORDER = "__NO_PURCHASE_ORDER__";

export const goodsReceiptItemSchema = z
  .object({
    productId: z.string().min(1, "Produk harus dipilih"),
    unitId: z.string().min(1, "Unit harus dipilih"),
    quantityOrdered: z.number().optional(),
    quantityReceived: z.number().min(1, "Jumlah diterima harus lebih dari 0"),
    unitPrice: z.number().min(0, "Harga unit tidak boleh negatif"),
    batchNumber: z.string().optional(),
    expiryDate: z.string().optional(),
    manufacturingDate: z.string().optional(),
    storageLocation: z.string().optional(),
    storageCondition: z.string().optional(),
    conditionOnReceipt: z.string().optional(),
    damageNotes: z.string().optional(),
    notes: z.string().optional(),
    isSubstitution: z.boolean().optional(),
    originalProductId: z.string().optional(),
    substitutionReason: z.string().optional(),
    substitutionNotes: z.string().optional(),
  })
  .refine(
    (data) => {
      if (data.quantityOrdered && data.quantityOrdered > 0) {
        return data.quantityReceived <= data.quantityOrdered;
      }
      return true;
    },
    {
      message:
        "Kuantitas yang diterima tidak boleh melebihi kuantitas yang dipesan",
      path: ["quantityReceived"],
    },
  )
  .refine(
    (data) => {
      if (data.manufacturingDate) {
        const mfgDate = new Date(data.manufacturingDate);
        const today = new Date();
        today.setHours(23, 59, 59, 999);
        return mfgDate <= today;
      }
      return true;
    },
    {
      message: "Tanggal produksi tidak boleh di masa depan",
      path: ["manufacturingDate"],
    },
  )
  .refine(
    (data) => {
      if (data.manufacturingDate && data.expiryDate) {
        const mfgDate = new Date(data.manufacturingDate);
        const expDate = new Date(data.expiryDate);
        return expDate > mfgDate;
      }
      return true;
    },
    {
      message: "Tanggal kadaluarsa harus setelah tanggal produksi",
      path: ["expiryDate"],
    },
  );

export const goodsReceiptFormSchema = z.object({
  purchaseOrderId: z.string().optional(),
  supplierId: z.string().min(1, "Supplier harus dipilih"),
  receiptDate: z.string().min(1, "Tanggal penerimaan harus diisi"),
  deliveryDate: z.string().optional(),
  invoiceNumber: z.string().optional(),
  deliveryNote: z.string().optional(),
  deliveredBy: z.string().optional(),
  receivedBy: z.string().optional(),
  deliveryCondition: z.string().optional(),
  notes: z.string().optional(),
  internalNotes: z.string().optional(),
  items: z.array(goodsReceiptItemSchema).min(1, "Minimal harus ada satu item"),
});

export type GoodsReceiptFormValues = z.infer<typeof goodsReceiptFormSchema>;
