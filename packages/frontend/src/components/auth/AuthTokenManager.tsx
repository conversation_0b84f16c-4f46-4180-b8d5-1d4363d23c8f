'use client';

import { useSession } from 'next-auth/react';
import { useEffect } from 'react';
import { setAuthToken } from '@/lib/axios';

/**
 * Component that automatically manages authentication tokens for API requests.
 * This component should be included at the root level to ensure all API calls
 * have the proper authorization headers.
 */
export function AuthTokenManager() {
  const { data: session, status } = useSession();

  useEffect(() => {
    if (status === 'authenticated' && session?.accessToken) {
      // Set the authorization header for all API requests
      setAuthToken(session.accessToken);
    } else if (status === 'unauthenticated') {
      // Clear the authorization header
      setAuthToken(null);
    }
  }, [session, status]);

  // This component doesn't render anything
  return null;
}
