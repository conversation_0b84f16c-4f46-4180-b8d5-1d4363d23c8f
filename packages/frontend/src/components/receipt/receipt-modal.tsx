'use client';

import { useRef } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import {
  Printer,
  Download,
  X,
  Loader2,
  Receipt as ReceiptIcon
} from 'lucide-react';
import { ReceiptModalProps } from '@/types/receipt';
import { ReceiptDisplay } from './receipt-display';
import { useDownloadReceiptPdf, useDownloadReceiptWidePdf } from '@/hooks/useSales';
import { toast } from 'sonner';

export function ReceiptModal({
  open,
  onOpenChange,
  receiptData,
  isLoading = false,
  title = 'Struk Transaksi',
  saleId
}: ReceiptModalProps & { saleId?: string }) {
  const receiptRef = useRef<HTMLDivElement>(null);
  const downloadReceiptPdfMutation = useDownloadReceiptPdf();
  const downloadReceiptWidePdfMutation = useDownloadReceiptWidePdf();

  const handlePrint = () => {
    if (!receiptData) return;

    try {
      // Create a new window for printing
      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        toast.error('Gagal membuka jendela cetak. Pastikan popup tidak diblokir.');
        return;
      }

      // Get the receipt HTML content
      const receiptElement = receiptRef.current;
      if (!receiptElement) {
        toast.error('Gagal mengambil konten struk');
        return;
      }

      const receiptHtml = receiptElement.innerHTML;
      const isWide = receiptData.formatting.paperWidth === 80;

      // Create print HTML using exact backend template styling
      const printHtml = `
        <!DOCTYPE html>
        <html lang="id">
          <head>
            <title>Struk - ${receiptData.transaction.saleNumber}</title>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <style>
              /* Base styles for thermal printer compatibility - EXACT COPY FROM BACKEND TEMPLATE */
              * {
                  margin: 0;
                  padding: 0;
                  box-sizing: border-box;
              }

              body {
                  font-family: 'Courier New', 'Consolas', monospace;
                  font-size: 12px;
                  line-height: 1.3;
                  color: #000;
                  background: #fff;
                  padding: 4mm;
                  width: 100%;
              }

              /* Paper width configurations */
              .receipt-container {
                  width: 100%;
                  max-width: ${isWide ? '80' : '58'}mm;
                  margin: 0 auto;
                  background: white;
                  color: black;
              }

              /* Typography */
              .text-center { text-align: center; }
              .text-left { text-align: left; }
              .text-right { text-align: right; }
              .font-bold { font-weight: bold; }
              .font-large { font-size: 14px; font-weight: bold; }
              .font-small { font-size: 10px; }
              .font-xs { font-size: 9px; }

              /* Layout utilities */
              .mb-1 { margin-bottom: 2px; }
              .mb-2 { margin-bottom: 4px; }
              .mb-3 { margin-bottom: 6px; }
              .mt-1 { margin-top: 2px; }
              .mt-2 { margin-top: 4px; }
              .mt-3 { margin-top: 6px; }

              /* Flex utilities for alignment */
              .flex { display: flex; }
              .justify-between { justify-content: space-between; }
              .items-center { align-items: center; }

              /* Borders and separators */
              .border-dashed {
                  border-top: 1px dashed #333;
                  margin: 3px 0;
              }

              .border-solid {
                  border-top: 1px solid #333;
                  margin: 3px 0;
              }

              /* Header section */
              .header {
                  text-align: center;
                  margin-bottom: 6px;
              }

              .pharmacy-name {
                  font-size: ${isWide ? '16px' : '14px'};
                  font-weight: bold;
                  margin-bottom: 2px;
              }

              .pharmacy-info {
                  font-size: 11px;
                  line-height: 1.2;
              }

              /* Transaction info */
              .transaction-header {
                  text-align: center;
                  font-weight: bold;
                  margin: 6px 0 4px 0;
              }

              .info-row {
                  display: flex;
                  justify-content: space-between;
                  margin-bottom: 1px;
                  font-size: 11px;
              }

              /* Items section */
              .items-header {
                  text-align: center;
                  font-weight: bold;
                  margin: 4px 0;
              }

              .item {
                  margin-bottom: 4px;
                  font-size: 10px;
              }

              .item-name {
                  font-weight: bold;
                  margin-bottom: 1px;
              }

              .item-details {
                  font-size: 9px;
                  color: #333;
                  margin-bottom: 1px;
              }

              .item-calculation {
                  display: flex;
                  justify-content: space-between;
                  font-size: 10px;
              }

              .item-discount {
                  display: flex;
                  justify-content: space-between;
                  font-size: 9px;
                  color: #333;
              }

              /* Totals section */
              .totals {
                  margin-top: 6px;
              }

              .total-row {
                  display: flex;
                  justify-content: space-between;
                  margin-bottom: 1px;
                  font-size: 11px;
              }

              .grand-total {
                  display: flex;
                  justify-content: space-between;
                  font-weight: bold;
                  font-size: ${isWide ? '14px' : '12px'};
                  margin-top: 2px;
                  padding-top: 2px;
                  border-top: 1px solid #333;
              }

              /* Payment section */
              .payment-row {
                  display: flex;
                  justify-content: space-between;
                  margin-bottom: 1px;
                  font-size: 11px;
              }

              /* Footer section */
              .footer {
                  text-align: center;
                  margin-top: 8px;
                  font-size: 9px;
                  line-height: 1.3;
              }

              .thank-you {
                  font-weight: bold;
                  margin-bottom: 4px;
              }

              .return-policy {
                  margin: 4px 0;
                  white-space: pre-wrap;
              }

              .notes {
                  font-style: italic;
                  margin: 4px 0;
              }

              .print-time {
                  margin-top: 6px;
                  font-size: 8px;
                  color: #666;
              }

              /* Print-specific styles */
              @media print {
                  @page {
                      size: ${isWide ? '3.15in 11.69in' : '2.28in 8.27in'}; /* 80mm x 297mm or 58mm x 210mm in inches */
                      margin: 0.08in; /* 2mm in inches */
                  }

                  body {
                      margin: 0;
                      padding: 0;
                      font-size: ${isWide ? '11px' : '10px'};
                      width: 100%;
                      background: white;
                  }

                  .receipt-container {
                      width: 100%;
                      max-width: none;
                      margin: 0;
                      padding: 0.08in; /* 2mm */
                      background: white;
                      color: black;
                  }

                  /* Hide elements that shouldn't print */
                  .no-print {
                      display: none !important;
                  }

                  /* Ensure black text on white background */
                  * {
                      color: black !important;
                      background: white !important;
                      -webkit-print-color-adjust: exact !important;
                      color-adjust: exact !important;
                  }

                  /* Page break control */
                  .receipt-container {
                      page-break-inside: avoid;
                      break-inside: avoid;
                  }

                  /* Optimize for thermal printer */
                  html {
                      width: 100%;
                      height: 100%;
                  }
              }

              /* Responsive adjustments for different paper widths */
              @media (max-width: 60mm) {
                  body { font-size: 10px; }
                  .pharmacy-name { font-size: 12px; }
                  .item { font-size: 9px; }
                  .item-details { font-size: 8px; }
              }

              @media (min-width: 75mm) {
                  body { font-size: 13px; }
                  .pharmacy-name { font-size: 16px; }
                  .item { font-size: 11px; }
              }

              /* Line break utilities */
              .line-break-1::after { content: "\\A"; white-space: pre; }
              .line-break-2::after { content: "\\A\\A"; white-space: pre; }
              .line-break-3::after { content: "\\A\\A\\A"; white-space: pre; }

              /* Remove any conflicting styles from the display component */
              .border, .shadow-sm, .border-gray-200 {
                  border: none !important;
                  box-shadow: none !important;
              }
            </style>
            <script>
              // Help browsers recognize thermal printer paper size
              window.addEventListener('beforeprint', function() {
                console.log('Printing thermal receipt: ${isWide ? '80mm' : '58mm'} width');
                document.title = 'Thermal Receipt ${isWide ? '80mm' : '58mm'} - ${receiptData.transaction.saleNumber}';
              });

              // Show paper size guidance
              window.addEventListener('load', function() {
                const paperSize = '${isWide ? '80mm (3.15") wide' : '58mm (2.28") wide'}';
                console.log('Receipt paper size: ' + paperSize);

                // Add a small notice for user guidance (will be hidden in print)
                const notice = document.createElement('div');
                notice.className = 'no-print';
                notice.style.cssText = 'position: fixed; top: 10px; left: 10px; background: #f0f0f0; padding: 10px; border: 1px solid #ccc; font-size: 12px; z-index: 1000;';
                notice.innerHTML = 'Paper Size: ' + paperSize + '<br>Untuk thermal printer';
                document.body.appendChild(notice);
              });
            </script>
          </head>
          <body>
            <div class="receipt-container">
              ${receiptHtml.replace(/class="receipt-display[^"]*"/g, 'class="receipt-container"')}
            </div>
          </body>
        </html>
      `;

      // Write content and trigger print
      printWindow.document.write(printHtml);
      printWindow.document.close();

      // Wait for content to load then print
      printWindow.onload = () => {
        setTimeout(() => {
          printWindow.print();
          printWindow.close();
        }, 250);
      };

      toast.success('Jendela cetak dibuka');
    } catch (error) {
      console.error('Print error:', error);
      toast.error('Gagal mencetak struk');
    }
  };

  const handleDownloadPdf = async () => {
    if (!receiptData || !saleId) return;

    try {
      const isWide = receiptData.formatting.paperWidth === 80;
      const mutation = isWide ? downloadReceiptWidePdfMutation : downloadReceiptPdfMutation;

      const blob = await mutation.mutateAsync(saleId);

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `struk${isWide ? '-lebar' : ''}-${receiptData.transaction.saleNumber}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('PDF download error:', error);
      // Error is already handled by the mutation's onError callback
    }
  };

  if (!receiptData && !isLoading) {
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <ReceiptIcon className="h-5 w-5" />
            {title}
            {receiptData && (
              <span className="text-sm font-normal text-muted-foreground">
                - {receiptData.transaction.saleNumber}
              </span>
            )}
          </DialogTitle>
          <DialogDescription>
            {receiptData?.formatting.paperWidth === 80
              ? 'Format struk lebar (80mm) untuk thermal printer'
              : 'Format struk standar (58mm) untuk thermal printer'
            }
          </DialogDescription>
        </DialogHeader>

        <Separator />

        {/* Action Buttons */}
        <div className="flex items-center justify-between gap-2 py-2">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handlePrint}
              disabled={!receiptData || isLoading}
              className="flex items-center gap-2"
            >
              <Printer className="h-4 w-4" />
              Cetak
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={handleDownloadPdf}
              disabled={!receiptData || !saleId || isLoading || downloadReceiptPdfMutation.isPending || downloadReceiptWidePdfMutation.isPending}
              className="flex items-center gap-2"
            >
              {(downloadReceiptPdfMutation.isPending || downloadReceiptWidePdfMutation.isPending) ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Download className="h-4 w-4" />
              )}
              {(downloadReceiptPdfMutation.isPending || downloadReceiptWidePdfMutation.isPending) ? 'Mengunduh PDF...' : 'Unduh PDF'}
            </Button>
          </div>

          <Button
            variant="ghost"
            size="sm"
            onClick={() => onOpenChange(false)}
            className="flex items-center gap-2"
          >
            <X className="h-4 w-4" />
            Tutup
          </Button>
        </div>

        <Separator />

        {/* Receipt Content */}
        <div className="flex-1 overflow-auto py-4">
          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
                <p className="text-muted-foreground">Memuat struk...</p>
              </div>
            </div>
          ) : receiptData ? (
            <div className="flex justify-center">
              <div ref={receiptRef}>
                <ReceiptDisplay
                  receiptData={receiptData}
                  className="border border-gray-200 shadow-sm"
                />
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-center h-64">
              <p className="text-muted-foreground">Tidak ada data struk</p>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
