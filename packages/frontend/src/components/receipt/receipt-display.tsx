'use client';

import { ReceiptData, ReceiptDisplayOptions } from '@/types/receipt';
import { formatCurrency } from '@/lib/utils';
import { cn } from '@/lib/utils';

interface ReceiptDisplayProps {
  receiptData: ReceiptData;
  options?: ReceiptDisplayOptions;
  className?: string;
}

export function ReceiptDisplay({
  receiptData,
  options = {},
  className
}: ReceiptDisplayProps) {
  const {
    showHeader = true,
    showFooter = true,
    showLineBreaks = true,
    compactMode = false
  } = options;

  const { pharmacy, transaction, customer, items, totals, payment, footer, formatting } = receiptData;
  const isWide = formatting.paperWidth === 80;

  // Generate line breaks based on formatting hints
  const lineBreak = (count: number) => {
    if (!showLineBreaks) return null;
    return Array.from({ length: count }, (_, i) => (
      <div key={i} className="h-2" />
    ));
  };

  return (
    <div
      className={cn(
        "receipt-display font-mono text-sm bg-white text-black p-4",
        isWide ? "max-w-md" : "max-w-xs",
        compactMode && "text-xs p-2",
        className
      )}
      data-paper-width={formatting.paperWidth}
      style={{
        fontFamily: 'Courier New, monospace',
        lineHeight: compactMode ? '1.2' : '1.4',
        // maxWidth: isWide ? '320px' : '240px' // Consistent styling via inline styles
      }}
    >
      {/* Header Section */}
      {showHeader && (
        <div className="text-center receipt-header">
          <div className="font-bold text-lg pharmacy-name">{pharmacy.name}</div>
          <div className="text-sm pharmacy-address">{pharmacy.address}</div>
          <div className="text-sm pharmacy-phone">Tel: {pharmacy.phone}</div>
          {pharmacy.email && <div className="text-sm pharmacy-email">Email: {pharmacy.email}</div>}
          {pharmacy.license && <div className="text-sm pharmacy-license">SIPA: {pharmacy.license}</div>}
          {lineBreak(formatting.lineBreaks.afterHeader)}
        </div>
      )}

      {/* Transaction Information */}
      <div className="text-center receipt-transaction">
        <div className="font-bold transaction-header">STRUK PENJUALAN</div>
        <div className="border-t border-dashed border-gray-400 my-1"></div>
        <div className="flex justify-between info-row">
          <span>No. Transaksi:</span>
          <span>{transaction.saleNumber}</span>
        </div>
        <div className="flex justify-between info-row">
          <span>Tanggal:</span>
          <span>{transaction.date}</span>
        </div>
        <div className="flex justify-between info-row">
          <span>Waktu:</span>
          <span>{transaction.time}</span>
        </div>
        <div className="flex justify-between info-row">
          <span>Kasir:</span>
          <span>{transaction.cashier.name}</span>
        </div>
        {lineBreak(formatting.lineBreaks.afterTransaction)}
      </div>

      {/* Customer Information */}
      <div className="receipt-customer">
        <div className="border-t border-dashed border-gray-400 my-1"></div>
        <div className="flex justify-between info-row">
          <span>Pelanggan:</span>
          <span>{customer.name}</span>
        </div>
        {customer.phone && (
          <div className="flex justify-between info-row">
            <span>Telepon:</span>
            <span>{customer.phone}</span>
          </div>
        )}
        {customer.membershipNumber && (
          <div className="flex justify-between info-row">
            <span>No. Member:</span>
            <span>{customer.membershipNumber}</span>
          </div>
        )}
        {lineBreak(formatting.lineBreaks.afterCustomer)}
      </div>

      {/* Items Section */}
      <div className="receipt-items">
        <div className="border-t border-dashed border-gray-400 my-1"></div>
        <div className="font-bold text-center items-header">DETAIL PEMBELIAN</div>
        <div className="border-t border-dashed border-gray-400 my-1"></div>

        {items.map((item, index) => (
          <div key={index} className="mb-2 item">
            <div className="font-medium item-name">{item.name}</div>
            <div className="text-xs text-gray-600 item-details">{item.code}</div>
            {item.manufacturer && (
              <div className="text-xs text-gray-600 item-details">{item.manufacturer}</div>
            )}
            <div className="flex justify-between item-calculation">
              <span>{item.quantity} {item.unit} x {formatCurrency(item.unitPrice)}</span>
              <span>{formatCurrency(item.subtotal)}</span>
            </div>
            {item.discount > 0 && (
              <div className="flex justify-between text-xs item-discount">
                <span>Diskon:</span>
                <span>-{formatCurrency(item.discount)}</span>
              </div>
            )}
          </div>
        ))}
        {lineBreak(formatting.lineBreaks.afterItems)}
      </div>

      {/* Totals Section */}
      <div className="receipt-totals">
        <div className="border-t border-dashed border-gray-400 my-1"></div>
        <div className="flex justify-between total-row">
          <span>Subtotal:</span>
          <span>{formatCurrency(totals.subtotal)}</span>
        </div>
        {totals.discount > 0 && (
          <div className="flex justify-between total-row">
            <span>Total Diskon:</span>
            <span>-{formatCurrency(totals.discount)}</span>
          </div>
        )}
        {totals.tax > 0 && (
          <div className="flex justify-between total-row">
            <span>Pajak:</span>
            <span>{formatCurrency(totals.tax)}</span>
          </div>
        )}
        <div className="flex justify-between font-bold text-lg grand-total border-t border-solid border-gray-800 pt-1 mt-2">
          <span>TOTAL:</span>
          <span>{formatCurrency(totals.total)}</span>
        </div>
        {lineBreak(formatting.lineBreaks.afterTotals)}
      </div>

      {/* Payment Information */}
      <div className="receipt-payment">
        <div className="border-t border-dashed border-gray-400 my-1"></div>
        <div className="flex justify-between payment-row">
          <span>Metode Bayar:</span>
          <span>{payment.method}</span>
        </div>
        <div className="flex justify-between payment-row">
          <span>Jumlah Bayar:</span>
          <span>{formatCurrency(payment.amountPaid)}</span>
        </div>
        {payment.change > 0 && (
          <div className="flex justify-between payment-row">
            <span>Kembalian:</span>
            <span>{formatCurrency(payment.change)}</span>
          </div>
        )}
        {lineBreak(formatting.lineBreaks.afterPayment)}
      </div>

      {/* Footer Section */}
      {showFooter && (
        <div className="text-center receipt-footer">
          {lineBreak(formatting.lineBreaks.beforeFooter)}
          <div className="border-t border-dashed border-gray-400 my-1"></div>
          <div className="font-medium thank-you">{footer.thankYouMessage}</div>
          <div className="text-xs mt-2 whitespace-pre-wrap return-policy">{footer.returnPolicy}</div>
          {footer.notes && (
            <div className="text-xs mt-2 italic notes">Catatan: {footer.notes}</div>
          )}
          <div className="mt-2 text-xs print-time">
            Dicetak pada: {new Date().toLocaleString('id-ID')}
          </div>
        </div>
      )}
    </div>
  );
}
