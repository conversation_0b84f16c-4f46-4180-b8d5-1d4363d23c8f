'use client';

import { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import {
  ShoppingCart,
  User,
  Calendar,
  DollarSign,
  Receipt,
  Package,
  Phone,
  Edit,
  ExternalLink,
  RefreshCw,
  X,
  Undo,
  Printer
} from 'lucide-react';
import { SaleWithRelations, SaleStatus } from '@/types/sales';
import { formatCurrency, formatDate } from '@/lib/utils';
import { getSaleStatusColor, getPaymentMethodColor, getSaleStatusLabel, getPaymentMethodLabel } from '@/lib/constants/sales';

interface SalesQuickViewProps {
  sale: SaleWithRelations | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onEdit?: (sale: SaleWithRelations) => void;
  onComplete?: (sale: SaleWithRelations) => void;
  onCancel?: (sale: SaleWithRelations) => void;
  onRefund?: (sale: SaleWithRelations) => void;
  onGenerateReceipt?: (sale: SaleWithRelations) => void;
  onGenerateReceiptWide?: (sale: SaleWithRelations) => void;
  onViewDetails?: (sale: SaleWithRelations) => void;
}

export function SalesQuickView({
  sale,
  open,
  onOpenChange,
  onEdit,
  onComplete,
  onCancel,
  onRefund,
  onGenerateReceipt,
  onGenerateReceiptWide,
  onViewDetails,
}: SalesQuickViewProps) {
  const [isLoading, setIsLoading] = useState(false);

  if (!sale) return null;

  const customerName = sale.customer?.fullName || sale.customerName || 'Walk-in Customer';
  const customerPhone = sale.customer?.phoneNumber || sale.customerPhone;
  const cashierName = `${sale.cashier.firstName} ${sale.cashier.lastName}`;

  const handleAction = async (action: () => void | Promise<void>) => {
    setIsLoading(true);
    try {
      await action();
      onOpenChange(false);
    } catch (error) {
      // Error is already handled by the mutation's onError callback
      if (process.env.NODE_ENV === 'development') {
        console.error('Action failed:', error);
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <ShoppingCart className="h-5 w-5" />
            Detail Transaksi {sale.saleNumber}
          </DialogTitle>
          <DialogDescription>
            Informasi lengkap transaksi penjualan
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-6 py-4">
          {/* Status and Actions */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Badge variant="outline" className={getSaleStatusColor(sale.status)}>
                {getSaleStatusLabel(sale.status)}
              </Badge>
              <Badge variant="outline" className={getPaymentMethodColor(sale.paymentMethod)}>
                {getPaymentMethodLabel(sale.paymentMethod)}
              </Badge>
            </div>

            <div className="flex items-center gap-2">
              {onEdit && sale.status === SaleStatus.DRAFT && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleAction(() => onEdit(sale))}
                  disabled={isLoading}
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </Button>
              )}

              {onComplete && sale.status === SaleStatus.DRAFT && (
                <Button
                  variant="default"
                  size="sm"
                  onClick={() => handleAction(() => onComplete?.(sale))}
                  disabled={isLoading}
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Selesaikan
                </Button>
              )}

              {onGenerateReceipt && sale.status === SaleStatus.COMPLETED && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleAction(() => onGenerateReceipt(sale))}
                  disabled={isLoading}
                >
                  <Receipt className="h-4 w-4 mr-2" />
                  Cetak Struk
                </Button>
              )}

              {onGenerateReceiptWide && sale.status === SaleStatus.COMPLETED && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleAction(() => onGenerateReceiptWide(sale))}
                  disabled={isLoading}
                >
                  <Printer className="h-4 w-4 mr-2" />
                  Struk Lebar
                </Button>
              )}

              {onViewDetails && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onViewDetails(sale)}
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Detail Lengkap
                </Button>
              )}
            </div>
          </div>

          <div className="grid gap-6 md:grid-cols-2">
            {/* Sale Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  Informasi Transaksi
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-muted-foreground">Nomor Transaksi</p>
                    <p className="font-medium">{sale.saleNumber}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Tanggal</p>
                    <p className="font-medium">{formatDate(sale.saleDate)}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Kasir</p>
                    <p className="font-medium">{cashierName}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Status</p>
                    <Badge variant="outline" className={getSaleStatusColor(sale.status)}>
                      {getSaleStatusLabel(sale.status)}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Customer Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Informasi Pelanggan
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">{customerName}</span>
                  </div>
                  {customerPhone && (
                    <div className="flex items-center gap-2">
                      <Phone className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">{customerPhone}</span>
                    </div>
                  )}
                  {sale.customer && (
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary" className="text-xs">
                        {sale.customer.type === 'REGISTERED' ? 'Pelanggan Terdaftar' : 'Walk-in'}
                      </Badge>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Financial Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                Ringkasan Keuangan
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <p className="text-muted-foreground">Subtotal</p>
                  <p className="font-medium">{formatCurrency(sale.subtotal)}</p>
                </div>
                {sale.discountAmount > 0 && (
                  <div>
                    <p className="text-muted-foreground">Diskon</p>
                    <p className="font-medium text-green-600">-{formatCurrency(sale.discountAmount)}</p>
                  </div>
                )}
                {sale.taxAmount > 0 && (
                  <div>
                    <p className="text-muted-foreground">Pajak</p>
                    <p className="font-medium">{formatCurrency(sale.taxAmount)}</p>
                  </div>
                )}
                <div>
                  <p className="text-muted-foreground">Total</p>
                  <p className="font-bold text-lg">{formatCurrency(sale.totalAmount)}</p>
                </div>
              </div>

              <Separator className="my-4" />

              <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <p className="text-muted-foreground">Metode Pembayaran</p>
                  <Badge variant="outline" className={getPaymentMethodColor(sale.paymentMethod)}>
                    {getPaymentMethodLabel(sale.paymentMethod)}
                  </Badge>
                </div>
                <div>
                  <p className="text-muted-foreground">Jumlah Dibayar</p>
                  <p className="font-medium">{formatCurrency(sale.amountPaid)}</p>
                </div>
                {sale.changeAmount > 0 && (
                  <div>
                    <p className="text-muted-foreground">Kembalian</p>
                    <p className="font-medium">{formatCurrency(sale.changeAmount)}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Sale Items */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Package className="h-5 w-5" />
                Item Transaksi ({sale.saleItems.length} item)
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {sale.saleItems.map((item) => (
                  <div key={item.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex-1">
                      <div className="font-medium">{item.product.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {item.product.code} • {item.product.manufacturer}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {item.quantity} {item.unit.abbreviation} × {formatCurrency(item.unitPrice)}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">{formatCurrency(item.totalPrice)}</div>
                      {item.discountAmount > 0 && (
                        <div className="text-sm text-green-600">
                          Diskon: -{formatCurrency(item.discountAmount)}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Notes */}
          {sale.notes && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Catatan</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">{sale.notes}</p>
              </CardContent>
            </Card>
          )}

          {/* Action Buttons */}
          <div className="flex items-center justify-end gap-2 pt-4 border-t">
            {onCancel && (sale.status === SaleStatus.DRAFT || sale.status === SaleStatus.COMPLETED) && (
              <Button
                variant="outline"
                onClick={() => handleAction(() => onCancel(sale))}
                disabled={isLoading}
                className="text-red-600 hover:text-red-700"
              >
                <X className="h-4 w-4 mr-2" />
                Batalkan
              </Button>
            )}

            {onRefund && sale.status === SaleStatus.COMPLETED && (
              <Button
                variant="outline"
                onClick={() => handleAction(() => onRefund(sale))}
                disabled={isLoading}
                className="text-orange-600 hover:text-orange-700"
              >
                <Undo className="h-4 w-4 mr-2" />
                Refund
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
