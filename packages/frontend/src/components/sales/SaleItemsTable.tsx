'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { Trash2, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  TableCell,
  TableRow,
} from '@/components/ui/table';
import {
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form';
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';
import { ProductSelector } from '@/components/ui/product-selector';
import { UnitSelectorWithStock } from '@/components/ui/unit-selector-with-stock';
import { DiscountTypeSelector } from '@/components/ui/discount-type-selector';
import { LiveCurrencyInput } from '@/components/ui/currency-input';
import { useProduct } from '@/hooks/useProducts';
import { useProductStock } from '@/hooks/useInventory';
import { formatCurrency } from '@/lib/utils';
import {
  calculateAvailableStockWithCartImpact,
  convertPosCartItemsToCalculatorFormat,
  extractUnitHierarchies
} from '@/lib/utils/cart-stock-calculator';
import { toast } from 'sonner';

// Cart item interface for stock calculations
interface CartItem {
  productId: string;
  productName: string;
  productCode: string;
  quantity: number;
  price: number;
  unitId: string;
  unitName: string;
  discount: number;
  subtotal: number;
  availableUnits: {
    id: string;
    name: string;
    price: number;
    stock: number;
    conversionFactor: number;
  }[];
}

interface SaleItemsTableProps {
  form: UseFormReturn<any>;
  itemFields: any[];
  onAddItem: () => void;
  onRemoveItem: (index: number) => void;
}

export function SaleItemsTable({
  form,
  itemFields,
  onAddItem,
  onRemoveItem,
}: SaleItemsTableProps) {
  const headerScrollRef = useRef<HTMLDivElement>(null);
  const scrollAreaViewportRef = useRef<HTMLDivElement>(null);

  // Synchronize horizontal scroll between header and ScrollArea content
  const handleHeaderScroll = (e: React.UIEvent<HTMLDivElement>) => {
    if (scrollAreaViewportRef.current) {
      scrollAreaViewportRef.current.scrollLeft = e.currentTarget.scrollLeft;
    }
  };

  const handleContentScroll = (e: Event) => {
    const target = e.target as HTMLDivElement;
    if (headerScrollRef.current && target) {
      headerScrollRef.current.scrollLeft = target.scrollLeft;
    }
  };

  // Set up scroll synchronization with ScrollArea viewport
  useEffect(() => {
    const setupScrollSync = () => {
      const viewport = scrollAreaViewportRef.current;
      if (viewport) {
        viewport.addEventListener('scroll', handleContentScroll);
        return () => viewport.removeEventListener('scroll', handleContentScroll);
      }
    };

    // Try to set up immediately
    const cleanup = setupScrollSync();

    // If viewport not found, try again after a short delay
    if (!cleanup) {
      const timer = setTimeout(() => {
        setupScrollSync();
      }, 100);
      return () => clearTimeout(timer);
    }

    return cleanup;
  }, [itemFields.length]); // Re-run when items change

  // Show empty state if no items
  if (itemFields.length === 0) {
    return (
      <div className="border rounded-lg p-8 text-center">
        <p className="text-muted-foreground mb-4">Belum ada item yang ditambahkan</p>
        <Button onClick={onAddItem} size="sm" variant="outline">
          <Plus className="h-4 w-4 mr-2" />
          Tambah Item Pertama
        </Button>
      </div>
    );
  }

  return (
    <>
      <div className="relative">
        {/* Mobile: Show hint about horizontal scroll */}
        <div className="block sm:hidden bg-amber-50/50 border-b">
          <div className="px-3 py-2 text-xs text-amber-700 flex items-center gap-2">
            <span>📱</span>
            <span>Geser tabel ke kanan untuk melihat semua kolom</span>
          </div>
        </div>

        {/* Table Header */}
        <div className="border-b bg-muted/20">
          <div
            ref={headerScrollRef}
            className="w-full overflow-x-auto scrollbar-hide no-scrollbar"
            onScroll={handleHeaderScroll}
          >
            <table className="w-full min-w-[1200px] table-fixed caption-bottom text-sm">
              <colgroup>
                <col className="w-[160px]" />
                <col className="w-[120px]" />
                <col className="w-[70px]" />
                <col className="w-[90px]" />
                <col className="w-[60px]" />
                <col className="w-[80px]" />
                <col className="w-[90px]" />
                <col className="w-[40px]" />
              </colgroup>
              <thead>
                <tr className="border-b bg-gradient-to-r from-muted/40 to-muted/20">
                  <th className="h-8 px-3 py-2 text-left align-middle whitespace-nowrap text-xs font-semibold text-muted-foreground bg-muted/40">Produk</th>
                  <th className="h-8 px-2 py-2 text-left align-middle whitespace-nowrap text-xs font-semibold text-muted-foreground bg-muted/40">Satuan</th>
                  <th className="h-8 px-2 py-2 text-center align-middle whitespace-nowrap text-xs font-semibold text-muted-foreground bg-muted/40">Qty</th>
                  <th className="h-8 px-2 py-2 text-right align-middle whitespace-nowrap text-xs font-semibold text-muted-foreground bg-muted/40">Harga</th>
                  <th className="h-8 px-2 py-2 text-center align-middle whitespace-nowrap text-xs font-semibold text-muted-foreground bg-muted/40">Disc</th>
                  <th className="h-8 px-2 py-2 text-right align-middle whitespace-nowrap text-xs font-semibold text-muted-foreground bg-muted/40">Nilai</th>
                  <th className="h-8 px-2 py-2 text-right align-middle whitespace-nowrap text-xs font-semibold text-muted-foreground bg-muted/40">Subtotal</th>
                  <th className="h-8 px-1 py-2 text-center align-middle whitespace-nowrap text-xs font-semibold bg-muted/40 border-l sticky right-0 z-40">×</th>
                </tr>
              </thead>
            </table>
          </div>
        </div>

        {/* Table Content with ScrollArea - Height constrained */}
        <div
          className="w-full overflow-hidden"
          style={{
            height: '240px',
            maxHeight: '240px',
            minHeight: '240px',
            position: 'relative'
          }}
        >
          <ScrollArea
            className="w-full h-full"
            style={{
              height: '100%',
              maxHeight: '100%',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0
            }}
            ref={(el) => {
              if (el) {
                // Use the correct selector from shadcn/ui ScrollArea implementation
                const viewport = el.querySelector('[data-slot="scroll-area-viewport"]') as HTMLDivElement;
                if (viewport) {
                  // Force viewport to respect container constraints
                  viewport.style.height = '100%';
                  viewport.style.maxHeight = '100%';
                  viewport.style.overflow = 'auto';
                }
                scrollAreaViewportRef.current = viewport;
              }
            }}
          >
            <table className="w-full min-w-[1200px] table-fixed caption-bottom text-sm">
              <colgroup>
                <col className="w-[160px]" />
                <col className="w-[120px]" />
                <col className="w-[70px]" />
                <col className="w-[90px]" />
                <col className="w-[60px]" />
                <col className="w-[80px]" />
                <col className="w-[90px]" />
                <col className="w-[40px]" />
              </colgroup>
              <tbody className="[&_tr:last-child]:border-0">
                {itemFields.map((field, index) => (
                  <SaleItemRow
                    key={field.id}
                    index={index}
                    form={form}
                    onRemove={() => onRemoveItem(index)}
                  />
                ))}
              </tbody>
            </table>
            <ScrollBar orientation="horizontal" />
          </ScrollArea>
        </div>
      </div>
      {/* Add Item Button */}
      <div className="border-t bg-muted/5 px-4 py-3">
        <Button
          onClick={(e) => {
            e.preventDefault();
            onAddItem();
          }}
          size="sm"
          variant="secondary"
          className="w-full"
        >
          <Plus className="h-3 w-3 mr-2" />
          Tambah Item Lainnya
        </Button>
      </div>
    </>
  );
}

// Individual Sale Item Row Component
function SaleItemRow({
  index,
  form,
  onRemove,
}: {
  index: number;
  form: UseFormReturn<any>;
  onRemove: () => void;
}) {
  const [availableUnits, setAvailableUnits] = useState<any[]>([]);

  // Helper function to convert form items to cart items for stock calculation
  const convertFormItemsToCartItems = useCallback((): CartItem[] => {
    const formItems = form.getValues('items') || [];
    return formItems.map((item: any, itemIndex: number) => {
      const productData = form.getValues(`items.${itemIndex}.productData`);
      const availableUnitsData = form.getValues(`items.${itemIndex}.availableUnits`) || [];

      return {
        productId: item.productId || '',
        productName: productData?.name || '',
        productCode: productData?.code || '',
        quantity: item.quantity || 0,
        price: item.unitPrice || 0,
        unitId: item.unitId || '',
        unitName: availableUnitsData.find((u: any) => u.id === item.unitId)?.name || '',
        discount: 0, // Simplified for stock calculation
        subtotal: (item.quantity || 0) * (item.unitPrice || 0),
        availableUnits: availableUnitsData
      };
    }).filter((item: CartItem) => item.productId && item.unitId);
  }, [form]);

  // Helper function to create product data for cart-aware stock calculation
  const createProductDataForCartItem = useCallback((item: CartItem) => {
    const sortedUnits = [...item.availableUnits].sort((a, b) => a.conversionFactor - b.conversionFactor);

    return {
      id: item.productId,
      unitHierarchies: sortedUnits.map((unit, unitIndex) => ({
        unitId: unit.id,
        conversionFactor: unit.conversionFactor,
        level: unitIndex
      }))
    };
  }, []);

  // Helper function to get cart-aware stock for a specific unit
  const getCartAwareStock = useCallback((productId: string, unitId: string, excludeItemIndex?: number): number => {
    const cartItems = convertFormItemsToCartItems();

    // Find a cart item for this product to get unit structure
    const sampleItem = cartItems.find(item => item.productId === productId);
    if (!sampleItem) return 0;

    const productData = createProductDataForCartItem(sampleItem);
    const unitHierarchies = extractUnitHierarchies(productData);

    // Filter cart items to exclude specified item
    const filteredCartItems = excludeItemIndex !== undefined
      ? cartItems.filter((_, cartIndex) => cartIndex !== excludeItemIndex)
      : cartItems;

    // Convert cart items to calculator format
    const cartStockItems = convertPosCartItemsToCalculatorFormat(filteredCartItems);

    // Create original stock data from available units
    const originalStockData: Record<string, any> = {};
    sampleItem.availableUnits.forEach(unit => {
      originalStockData[unit.id] = {
        quantityOnHand: unit.stock,
        quantityAllocated: 0,
        availableStock: unit.stock
      };
    });

    // Calculate updated stock considering cart impact
    const updatedStockData = calculateAvailableStockWithCartImpact(
      originalStockData,
      cartStockItems,
      productId,
      unitHierarchies
    );

    return updatedStockData[unitId]?.availableStock || 0;
  }, [convertFormItemsToCartItems, createProductDataForCartItem]);

  // Watch form values
  const watchedProductId = form.watch(`items.${index}.productId`);
  const watchedUnitId = form.watch(`items.${index}.unitId`);
  const watchedQuantity = form.watch(`items.${index}.quantity`);
  const watchedUnitPrice = form.watch(`items.${index}.unitPrice`);
  const watchedDiscountType = form.watch(`items.${index}.discountType`);
  const watchedDiscountValue = form.watch(`items.${index}.discountValue`);

  // Fetch product details
  const { data: productData } = useProduct(watchedProductId);

  // Fetch product stock information
  const { data: stockData } = useProductStock(watchedProductId);

  // Helper function to get units with cart-aware stock for display
  const getUnitsWithCartAwareStock = useCallback(() => {
    if (!watchedProductId || availableUnits.length === 0) {
      return availableUnits;
    }

    return availableUnits.map(unit => {
      // Get cart-aware stock for this unit (excluding current item)
      const cartAwareStock = getCartAwareStock(watchedProductId, unit.id, index);

      return {
        ...unit,
        stockInfo: {
          ...unit.stockInfo,
          availableStock: cartAwareStock // Use cart-aware stock for display
        }
      };
    });
  }, [watchedProductId, availableUnits, getCartAwareStock, index]);

  // Update available units when product changes
  useEffect(() => {
    if (productData?.unitHierarchies) {
      const units = productData.unitHierarchies.map((hierarchy: any) => {
        // Get original stock information for this unit
        const unitStock = stockData?.[hierarchy.unitId] || {
          quantityOnHand: 0,
          quantityAllocated: 0,
          availableStock: 0
        };

        return {
          id: hierarchy.unitId,
          name: hierarchy.unit.name,
          abbreviation: hierarchy.unit.abbreviation,
          conversionFactor: hierarchy.conversionFactor,
          sellingPrice: hierarchy.sellingPrice,
          level: hierarchy.level,
          isBaseUnit: hierarchy.level === 0,
          stockInfo: {
            quantityOnHand: unitStock.quantityOnHand,
            quantityAllocated: unitStock.quantityAllocated,
            availableStock: unitStock.availableStock // Use original stock
          }
        };
      });

      setAvailableUnits(units);

      // Store available units in form for cart calculations
      form.setValue(`items.${index}.availableUnits`, units.map(unit => ({
        id: unit.id,
        name: unit.name,
        price: unit.sellingPrice || 0,
        stock: unit.stockInfo.availableStock,
        conversionFactor: unit.conversionFactor
      })));

      // Store product data in form for cart calculations
      form.setValue(`items.${index}.productData`, productData);

      // Auto-select base unit if no unit selected
      if (watchedProductId && !watchedUnitId) {
        const baseUnit = units.find((unit: any) => unit.isBaseUnit);
        if (baseUnit) {
          form.setValue(`items.${index}.unitId`, baseUnit.id);
          form.setValue(`items.${index}.unitPrice`, baseUnit.sellingPrice || 0);
        }
      }
    }
  }, [productData, stockData, watchedProductId, watchedUnitId, form, index]);

  // Update unit price when unit changes
  useEffect(() => {
    if (watchedUnitId && availableUnits.length > 0) {
      const selectedUnit = availableUnits.find((unit: any) => unit.id === watchedUnitId);
      if (selectedUnit?.sellingPrice) {
        form.setValue(`items.${index}.unitPrice`, selectedUnit.sellingPrice);
      }
    }
  }, [watchedUnitId, availableUnits, form, index]);

  // Calculate totals
  const itemSubtotal = (watchedQuantity || 0) * (watchedUnitPrice || 0);
  const itemDiscount = watchedDiscountType === 'PERCENTAGE'
    ? itemSubtotal * ((watchedDiscountValue || 0) / 100)
    : (watchedDiscountValue || 0);
  const itemTotal = itemSubtotal - itemDiscount;

  return (
    <TableRow className="hover:bg-muted/20 border-b!">
      {/* Product */}
      <TableCell className="px-2 py-2 max-w-0 overflow-hidden">
        <FormField
          control={form.control}
          name={`items.${index}.productId`}
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <ProductSelector
                  value={field.value}
                  onValueChange={(value) => {
                    // Product selection validation will be handled by unit and quantity validation

                    field.onChange(value);
                    form.setValue(`items.${index}.unitId`, '');
                    form.setValue(`items.${index}.unitPrice`, 0);
                  }}
                  placeholder="Pilih produk..."
                  className="h-8 text-xs w-full min-w-0"
                />
              </FormControl>
              <FormMessage className="text-xs" />
            </FormItem>
          )}
        />
      </TableCell>

      {/* Unit */}
      <TableCell className="px-2 py-1 max-w-0 overflow-hidden">
        <FormField
          control={form.control}
          name={`items.${index}.unitId`}
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <UnitSelectorWithStock
                  units={getUnitsWithCartAwareStock()}
                  value={field.value}
                  onValueChange={(unitId) => {
                    // Validate stock availability when unit is selected
                    if (watchedProductId && unitId) {
                      const cartAwareStock = getCartAwareStock(watchedProductId, unitId, index);
                      const currentQuantity = watchedQuantity || 1;

                      if (currentQuantity > cartAwareStock) {
                        const selectedUnit = availableUnits.find(unit => unit.id === unitId);
                        toast.error(`Stok tidak cukup. Tersedia: ${cartAwareStock} ${selectedUnit?.name || 'unit'} (setelah dikurangi keranjang)`);
                        return; // Don't change the unit
                      }
                    }

                    field.onChange(unitId);
                  }}
                  placeholder="Satuan..."
                  disabled={!watchedProductId}
                  showStockInfo={true}
                  className="h-8 text-xs w-full"
                  enableCartAwareStock={false} // We're handling cart-aware stock manually
                />
              </FormControl>
              <FormMessage className="text-xs" />
            </FormItem>
          )}
        />
      </TableCell>

      {/* Quantity */}
      <TableCell className="px-2 py-1 max-w-0">
        <FormField
          control={form.control}
          name={`items.${index}.quantity`}
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Input
                  {...form.register(`items.${index}.quantity`)}
                  type="number"
                  min="1"
                  step="1"
                  placeholder='1'
                  defaultValue={field.value || 1}
                  onChange={(e) => {
                    const newQuantity = parseInt(e.target.value) || 1;

                    // Validate stock availability if product and unit are selected
                    if (watchedProductId && watchedUnitId) {
                      const cartAwareStock = getCartAwareStock(watchedProductId, watchedUnitId, index);

                      if (newQuantity > cartAwareStock) {
                        const selectedUnit = availableUnits.find(unit => unit.id === watchedUnitId);
                        toast.error(`Stok tidak cukup. Tersedia: ${cartAwareStock} ${selectedUnit?.name || 'unit'} (setelah dikurangi keranjang)`);
                        return; // Don't update the value
                      }
                    }

                    field.onChange(newQuantity);
                  }}
                  className="h-8 text-xs text-center px-2 w-full"
                />
              </FormControl>
              <FormMessage className="text-xs" />
            </FormItem>
          )}
        />
      </TableCell>

      {/* Unit Price */}
      <TableCell className="px-2 py-1 max-w-0">
        <FormField
          control={form.control}
          name={`items.${index}.unitPrice`}
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <LiveCurrencyInput
                  value={field.value}
                  onValueChange={field.onChange}
                  placeholder="0"
                  className="h-8 text-xs text-right px-2 w-full"
                />
              </FormControl>
              <FormMessage className="text-xs" />
            </FormItem>
          )}
        />
      </TableCell>

      {/* Discount Type */}
      <TableCell className="px-2 py-1 max-w-0">
        <FormField
          control={form.control}
          name={`items.${index}.discountType`}
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <DiscountTypeSelector
                  value={field.value}
                  onValueChange={field.onChange}
                  size="sm"
                  className="justify-center w-full"
                />
              </FormControl>
              <FormMessage className="text-xs" />
            </FormItem>
          )}
        />
      </TableCell>

      {/* Discount Value */}
      <TableCell className="px-2 py-1 max-w-0">
        <FormField
          control={form.control}
          name={`items.${index}.discountValue`}
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Input
                  {...form.register(`items.${index}.discountValue`)}
                  type="number"
                  min="0"
                  step="0.01"
                  defaultValue={field.value || 0}
                  onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                  disabled={!watchedDiscountType}
                  className="h-8 text-xs text-right px-2 w-full"
                  placeholder="0"
                />
              </FormControl>
              <FormMessage className="text-xs" />
            </FormItem>
          )}
        />
      </TableCell>

      {/* Subtotal */}
      <TableCell className="px-2 py-1 max-w-0">
        <div className="text-right overflow-hidden">
          <div className="font-semibold text-sn text-primary truncate">
            {formatCurrency(itemTotal)}
          </div>
          {itemDiscount > 0 && (
            <div className="text-[10px] text-red-500 truncate">
              -{formatCurrency(itemDiscount)}
            </div>
          )}
        </div>
      </TableCell>

      {/* Actions */}
      <TableCell className="px-1 py-1 text-center sticky right-0 bg-background border-l max-w-0">
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={onRemove}
          className="text-destructive hover:text-destructive h-6 w-6 p-0"
        >
          <Trash2 className="h-3 w-3" />
        </Button>
      </TableCell>
    </TableRow>
  );
}