'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialog<PERSON>ooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { CustomerSelector } from '@/components/ui/customer-selector';
import { LiveCurrencyInput } from '@/components/ui/currency-input';
import { DiscountTypeSelector } from '@/components/ui/discount-type-selector';
import { SaleItemsTable } from '@/components/sales/SaleItemsTable';
import { CreateSaleDto, PaymentMethod } from '@/types/sales';
import { Customer } from '@/types/customer';
import { useCreateSale, useCreateDraft, useGenerateSaleNumber } from '@/hooks/useSales';
import { formatCurrency } from '@/lib/utils';
import { UseFormReturn } from 'react-hook-form';
import {
  Save,
  Receipt,
  ArrowLeft
} from 'lucide-react';
import { toast } from 'sonner';

// Form validation schema
const saleItemSchema = z.object({
  productId: z.string().min(1, 'Produk harus dipilih'),
  unitId: z.string().min(1, 'Satuan harus dipilih'),
  quantity: z.number().min(1, 'Jumlah minimal 1'),
  unitPrice: z.number().min(0, 'Harga tidak boleh negatif'),
  discountType: z.enum(['PERCENTAGE', 'FIXED_AMOUNT']).optional(),
  discountValue: z.number().transform((val) => val || 0).optional(),
  notes: z.string().optional(),
});

const createSaleSchema = z.object({
  saleNumber: z.string().optional(),
  customerId: z.string().optional(),
  customerName: z.string().optional(),
  customerPhone: z.string().optional(),
  paymentMethod: z.nativeEnum(PaymentMethod),
  amountPaid: z.number().min(0, 'Jumlah bayar tidak boleh negatif'),
  discountType: z.enum(['PERCENTAGE', 'FIXED_AMOUNT']).optional(),
  discountValue: z.number().min(0).optional(),
  taxAmount: z.number().min(0).optional(),
  notes: z.string().optional(),
  items: z.array(saleItemSchema).min(1, 'Minimal harus ada 1 item'),
});

type CreateSaleFormData = z.infer<typeof createSaleSchema>;

const PAYMENT_METHOD_OPTIONS = [
  { value: PaymentMethod.CASH, label: 'Tunai' },
  { value: PaymentMethod.TRANSFER, label: 'Transfer Bank' },
  { value: PaymentMethod.CREDIT, label: 'Kredit' },
  { value: PaymentMethod.GIRO, label: 'Giro' },
];

export function CreateSaleFormClient() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [submitAction, setSubmitAction] = useState<'draft' | 'complete'>('draft');

  // Mutations
  const createSaleMutation = useCreateSale();
  const createDraftMutation = useCreateDraft();
  const { refetch: generateSaleNumber } = useGenerateSaleNumber();

  // Form setup
  const form = useForm<CreateSaleFormData>({
    resolver: zodResolver(createSaleSchema),
    defaultValues: {
      saleNumber: '',
      customerId: '',
      customerName: '',
      customerPhone: '',
      paymentMethod: PaymentMethod.CASH,
      amountPaid: 0,
      discountType: 'FIXED_AMOUNT',
      discountValue: 0,
      taxAmount: 0,
      notes: '',
      items: [
        {
          productId: '',
          unitId: '',
          quantity: 1,
          unitPrice: 0,
          discountType: 'PERCENTAGE',
          discountValue: 0,
          notes: '',
        }
      ],
    },
  });

  const { fields: itemFields, append: appendItem, remove: removeItem } = useFieldArray({
    control: form.control,
    name: 'items',
  });

  // Watch form values for calculations
  const watchedItems = form.watch('items');
  const watchedDiscountType = form.watch('discountType');
  const watchedDiscountValue = form.watch('discountValue');
  const watchedTaxAmount = form.watch('taxAmount');
  const watchedAmountPaid = form.watch('amountPaid');

  // Calculate totals
  const calculations = useCallback(() => {
    const itemsSubtotal = watchedItems.reduce((sum, item) => {
      const itemTotal = (item.quantity || 0) * (item.unitPrice || 0);
      const itemDiscount = item.discountType === 'PERCENTAGE'
        ? itemTotal * ((item.discountValue || 0) / 100)
        : (item.discountValue || 0);
      return sum + itemTotal - itemDiscount;
    }, 0);

    const saleDiscount = watchedDiscountType === 'PERCENTAGE'
      ? itemsSubtotal * ((watchedDiscountValue || 0) / 100)
      : (watchedDiscountValue || 0);

    const subtotalAfterDiscount = itemsSubtotal - saleDiscount;
    const taxAmount = watchedTaxAmount || 0;
    const totalAmount = subtotalAfterDiscount + taxAmount;
    const changeAmount = Math.max(0, (watchedAmountPaid || 0) - totalAmount);

    return {
      itemsSubtotal,
      saleDiscount,
      subtotalAfterDiscount,
      taxAmount,
      totalAmount,
      changeAmount,
    };
  }, [watchedItems, watchedDiscountType, watchedDiscountValue, watchedTaxAmount, watchedAmountPaid]);

  const totals = calculations();

  // Generate sale number on component mount
  useEffect(() => {
    const generateNumber = async () => {
      try {
        const result = await generateSaleNumber();
        if (result.data?.saleNumber) {
          form.setValue('saleNumber', result.data.saleNumber);
        }
      } catch (error) {
        console.error('Failed to generate sale number:', error);
      }
    };

    generateNumber();
  }, [generateSaleNumber, form]);

  // Add new item
  const handleAddItem = () => {
    appendItem({
      productId: '',
      unitId: '',
      quantity: 1,
      unitPrice: 0,
      discountType: 'PERCENTAGE', // Set a default value instead of undefined
      discountValue: 0,
      notes: '',
    });
  };

  // Remove item
  const handleRemoveItem = (index: number) => {
    removeItem(index);
  };

  // Handle customer creation
  const handleCustomerCreated = (customer: Customer) => {
    // Customer is auto-selected by the CustomerSelector component
    toast.success(`Pelanggan ${customer.fullName} berhasil dibuat dan dipilih!`);
    console.log('New customer created and selected:', customer);
  };

  // Handle form submission
  const handleSubmit = async (action: 'draft' | 'complete') => {
    setSubmitAction(action);
    setShowConfirmDialog(true);
  };

  const confirmSubmit = async () => {
    setIsSubmitting(true);
    setShowConfirmDialog(false);

    try {
      const formData = form.getValues();

      // Prepare the sale data
      const saleData: CreateSaleDto = {
        ...formData,
        // Handle walk-in customer: convert 'walk-in' to undefined for backend
        customerId: formData.customerId === 'walk-in' ? undefined : formData.customerId,
        discountType: formData.discountType,
        items: formData.items.map(item => ({
          productId: item.productId,
          unitId: item.unitId,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          discountType: item.discountType,
          discountValue: item.discountValue,
          notes: item.notes,
        })),
      };

      // Create the sale using the appropriate mutation
      if (submitAction === 'draft') {
        await createDraftMutation.mutateAsync(saleData);
        toast.success('Draft transaksi berhasil disimpan');
      } else {
        // For complete action, create as completed sale directly
        await createSaleMutation.mutateAsync(saleData);
        toast.success('Transaksi berhasil diselesaikan');
      }

      // Redirect to sales list
      router.push('/dashboard/sales');
    } catch (error) {
      console.error('Failed to create sale:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="md:py-6 py-4 px-4 lg:px-6 flex-1 flex flex-col space-y-3 overflow-y-auto">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">Transaksi Baru</h1>
        <p className="text-muted-foreground">
          Buat transaksi penjualan baru dengan memilih pelanggan, produk, dan metode pembayaran
        </p>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-2 justify-between items-center">
        <Button
          type="button"
          variant="outline"
          onClick={() => router.push('/dashboard/sales')}
          className="flex items-center gap-2 h-8 text-sm"
        >
          <ArrowLeft className="h-4 w-4" />
          Kembali
        </Button>

        {itemFields.length > 0 && (
          <div className="flex gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => handleSubmit('draft')}
              disabled={isSubmitting}
              className="flex items-center gap-2 h-8 text-sm"
            >
              <Save className="h-4 w-4" />
              Simpan Draft
            </Button>

            <Button
              type="button"
              onClick={() => handleSubmit('complete')}
              disabled={isSubmitting || watchedAmountPaid < totals.totalAmount}
              className="flex items-center gap-2 h-8 text-sm"
            >
              <Receipt className="h-4 w-4" />
              Selesaikan Transaksi
            </Button>
          </div>
        )}
      </div>

      <Form {...form}>
        <div className="space-y-2">
          {/* Transaction Items */}
          <div className="bg-background border rounded-lg">
            {/* Simple Section Header */}
            <div className="px-4 py-2 bg-muted/5 border-b flex items-center justify-between">
              <div className="space-x-3">
                <span className="text-sm text-muted-foreground">No. Transaksi</span>
                <Badge variant="secondary" className="font-mono text-sm px-2 py-1">
                  {form.watch('saleNumber') || 'Loading...'}
                </Badge>
              </div>
              <div className="space-x-3">
                <span className="text-sm font-medium text-muted-foreground">Item Transaksi</span>
                {itemFields.length > 0 && (
                  <Badge variant="secondary" className="text-sm text-muted-foreground">
                    {itemFields.length} item{itemFields.length > 1 ? 's' : ''}
                  </Badge>
                )}
              </div>
            </div>
            {/* Items Table Content with native overflow */}
            <SaleItemsTable
              form={form}
              itemFields={itemFields}
              onAddItem={handleAddItem}
              onRemoveItem={handleRemoveItem}
            />
          </div>

          <div className="space-y-2">
            {/* Information and Payment Details */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-2">
              {/* Customer Information */}
              <CustomerInfoCard form={form} onCustomerCreated={handleCustomerCreated} />

              {/* Payment Summary */}
              {itemFields.length > 0 && (
                <>
                  <PaymentSummaryCard form={form} totals={totals} />
                  <PaymentDetailsCard form={form} totals={totals} watchedAmountPaid={watchedAmountPaid} />
                </>
              )}
            </div>
          </div>
        </div>
      </Form>

      {/* Confirmation Dialog */}
      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {submitAction === 'complete' ? 'Selesaikan Transaksi' : 'Simpan Draft'}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {submitAction === 'complete'
                ? 'Apakah Anda yakin ingin menyelesaikan transaksi ini? Stok akan dikurangi dan transaksi tidak dapat diubah.'
                : 'Apakah Anda yakin ingin menyimpan transaksi sebagai draft?'}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isSubmitting}>
              Batal
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmSubmit}
              disabled={isSubmitting}
              className={submitAction === 'complete' ? 'bg-primary hover:bg-primary/90' : 'bg-blue-600 hover:bg-blue-700'}
            >
              {isSubmitting
                ? (submitAction === 'complete' ? 'Menyelesaikan...' : 'Menyimpan...')
                : (submitAction === 'complete' ? 'Selesaikan' : 'Simpan')
              }
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}

// Customer Information Card Component
function CustomerInfoCard({
  form,
  onCustomerCreated
}: {
  form: UseFormReturn<any>;
  onCustomerCreated: (customer: Customer) => void;
}) {
  return (
    <div className="bg-background border rounded-lg">
      <div className="px-2 py-1.5 border-b bg-muted/5">
        <h3 className="text-xs font-medium text-muted-foreground">Informasi Pelanggan</h3>
      </div>
      <div className="p-2 space-y-2">
        <FormField
          control={form.control}
          name="customerId"
          render={({ field }) => {
            const handleChange = (customerId: string | undefined) => {
              field.onChange(customerId);
              if (customerId === 'walk-in') {
                form.setValue('customerName', '');
                form.setValue('customerPhone', '');
              } else if (!customerId) {
                form.setValue('customerName', '');
                form.setValue('customerPhone', '');
              } else {
                form.setValue('customerName', '');
                form.setValue('customerPhone', '');
              }
            };

            return (
              <FormItem className="block space-y-1">
                <FormLabel className="text-xs">Pelanggan</FormLabel>
                <FormControl>
                  <CustomerSelector
                    className="text-xs h-7"
                    value={field.value}
                    onValueChange={handleChange}
                    placeholder="Pilih pelanggan atau pelanggan umum"
                    enableCustomerCreation={true}
                    onCustomerCreated={onCustomerCreated}
                  />
                </FormControl>
                <FormMessage className="text-xs" />
              </FormItem>
            );
          }}
        />

        {/* Walk-in customer fields */}
        {(form.watch('customerId') === 'walk-in' || form.watch('customerId') === '') && (
          <div className="space-y-2">
            <FormField
              control={form.control}
              name="customerName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-xs">Nama Pelanggan</FormLabel>
                  <FormControl>
                    <Input
                      value={field.value || ''}
                      onChange={field.onChange}
                      placeholder="Nama pelanggan (opsional)"
                      className="h-7 text-xs"
                    />
                  </FormControl>
                  <FormMessage className="text-xs" />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="customerPhone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-xs">Nomor Telepon</FormLabel>
                  <FormControl>
                    <Input
                      value={field.value || ''}
                      onChange={field.onChange}
                      placeholder="Nomor telepon (opsional)"
                      className="h-7 text-xs"
                    />
                  </FormControl>
                  <FormMessage className="text-xs" />
                </FormItem>
              )}
            />
          </div>
        )}
      </div>
    </div>
  );
}

// Payment Summary Card Component
function PaymentSummaryCard({
  form,
  totals
}: {
  form: UseFormReturn<any>;
  totals: any;
}) {
  return (
    <div className="bg-background border rounded-lg">
      <div className="px-2 py-1.5 border-b bg-muted/5">
        <h3 className="text-xs font-medium text-muted-foreground">Ringkasan Pembayaran</h3>
      </div>
      <div className="p-2 space-y-2">
        {/* Sale-level discount */}
        <div className="space-y-2">
          <div className="grid grid-cols-2 gap-2">
            <FormField
              control={form.control}
              name="discountType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-xs">Jenis Diskon</FormLabel>
                  <FormControl>
                    <DiscountTypeSelector
                      value={field.value}
                      onValueChange={field.onChange}
                      size="sm"
                      className="h-7 text-xs"
                    />
                  </FormControl>
                  <FormMessage className="text-xs" />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="discountValue"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-xs">Nilai Diskon</FormLabel>
                  <FormControl>
                    <Input
                      {...form.register('discountValue')}
                      type="number"
                      min="0"
                      step="0.01"
                      defaultValue={field.value || 0}
                      onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      placeholder="0"
                      className="h-7 text-xs"
                    />
                  </FormControl>
                  <FormMessage className="text-xs" />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="taxAmount"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-xs">Pajak (PPN)</FormLabel>
                <FormControl>
                  <LiveCurrencyInput
                    value={field.value}
                    onValueChange={field.onChange}
                    placeholder="0"
                    className="h-7 text-xs"
                  />
                </FormControl>
                <FormMessage className="text-xs" />
              </FormItem>
            )}
          />
        </div>

        {/* Totals summary */}
        <Separator />
        <div className="space-y-1">
          <div className="flex justify-between text-xs">
            <span>Subtotal Item:</span>
            <span>{formatCurrency(totals.itemsSubtotal)}</span>
          </div>
          {totals.saleDiscount > 0 && (
            <div className="flex justify-between text-xs text-red-600">
              <span>Diskon Penjualan:</span>
              <span>-{formatCurrency(totals.saleDiscount)}</span>
            </div>
          )}
          {totals.taxAmount > 0 && (
            <div className="flex justify-between text-xs">
              <span>Pajak (PPN):</span>
              <span>{formatCurrency(totals.taxAmount)}</span>
            </div>
          )}
          <Separator />
          <div className="flex justify-between font-semibold text-sm">
            <span>Total:</span>
            <span>{formatCurrency(totals.totalAmount)}</span>
          </div>
        </div>
      </div>
    </div>
  );
}

// Payment Details Card Component
function PaymentDetailsCard({
  form,
  totals,
  watchedAmountPaid
}: {
  form: UseFormReturn<any>;
  totals: any;
  watchedAmountPaid: number;
}) {
  return (
    <div className="bg-background border rounded-lg">
      <div className="px-2 py-1.5 border-b bg-muted/5">
        <h3 className="text-xs font-medium text-muted-foreground">Pembayaran</h3>
      </div>
      <div className="p-2 space-y-2">
        <div className="space-y-2">
          <FormField
            control={form.control}
            name="paymentMethod"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-xs">Metode Pembayaran *</FormLabel>
                <Select onValueChange={field.onChange} value={field.value}>
                  <FormControl>
                    <SelectTrigger size="sm" className="text-xs w-full h-7">
                      <SelectValue placeholder="Pilih metode pembayaran" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {PAYMENT_METHOD_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value} className="text-xs">
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage className="text-xs" />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="amountPaid"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-xs">Jumlah Bayar *</FormLabel>
                <FormControl>
                  <LiveCurrencyInput
                    value={field.value}
                    onValueChange={field.onChange}
                    placeholder="0"
                    className="h-7 text-xs"
                  />
                </FormControl>
                <FormMessage className="text-xs" />
              </FormItem>
            )}
          />
        </div>

        {/* Change amount */}
        {totals.changeAmount > 0 && (
          <div className="p-2 bg-green-50 border border-green-200 rounded">
            <div className="flex justify-between items-center">
              <span className="font-medium text-green-800 text-xs">Kembalian:</span>
              <span className="font-bold text-green-800 text-sm">
                {formatCurrency(totals.changeAmount)}
              </span>
            </div>
          </div>
        )}

        {/* Insufficient payment warning */}
        {watchedAmountPaid > 0 && watchedAmountPaid < totals.totalAmount && (
          <div className="p-2 bg-red-50 border border-red-200 rounded">
            <div className="flex justify-between items-center">
              <span className="font-medium text-red-800 text-xs">Kurang Bayar:</span>
              <span className="font-bold text-red-800 text-sm">
                {formatCurrency(totals.totalAmount - watchedAmountPaid)}
              </span>
            </div>
          </div>
        )}

        <FormField
          control={form.control}
          name="notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-xs">Catatan</FormLabel>
              <FormControl>
                <Textarea
                  value={field.value || ''}
                  onChange={field.onChange}
                  placeholder="Catatan tambahan untuk transaksi ini..."
                  rows={2}
                  className="text-xs resize-none"
                />
              </FormControl>
              <FormMessage className="text-xs" />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
}
