'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { SaleQueryParams } from '@/types/sales';
import {
  SALE_STATUS_OPTIONS,
  PAYMENT_METHOD_OPTIONS,
  DATE_RANGE_PRESETS,
} from '@/lib/constants/sales';
import { Filter, X } from 'lucide-react';

interface SalesFiltersProps {
  filters: SaleQueryParams;
  onFilterChange: (key: keyof SaleQueryParams, value: any) => void;
  onBatchFilterChange?: (updates: Partial<SaleQueryParams>) => void;
  onClearFilters: () => void;
  activeFiltersCount: number;
  loading?: boolean;
}

export function SalesFilters({
  filters,
  onFilterChange,
  onBatchFilterChange,
  onClearFilters,
  activeFiltersCount,
  loading = false,
}: SalesFiltersProps) {
  const [selectedPreset, setSelectedPreset] = useState<string>('none');

  // Reset preset selection when filters are cleared externally
  useEffect(() => {
    if (!filters.startDate && !filters.endDate) {
      setSelectedPreset('none');
    }
  }, [filters.startDate, filters.endDate]);

  const handleDateRangePreset = (preset: string) => {
    if (preset === 'none') {
      setSelectedPreset('none');
      // Clear date filters when "Pilih Periode" is selected
      if (onBatchFilterChange) {
        onBatchFilterChange({
          startDate: undefined,
          endDate: undefined,
        });
      } else {
        onFilterChange('startDate', undefined);
        onFilterChange('endDate', undefined);
      }
      return;
    }

    const presetConfig = DATE_RANGE_PRESETS.find(p => p.value === preset);
    if (presetConfig) {
      const { startDate, endDate } = presetConfig.getDateRange();
      setSelectedPreset(preset);
      if (onBatchFilterChange) {
        onBatchFilterChange({
          startDate,
          endDate,
        });
      } else {
        onFilterChange('startDate', startDate);
        onFilterChange('endDate', endDate);
      }
    }
  };

  return (
    <div className="flex flex-wrap items-center gap-2">
      {/* Status Filter */}
      <Select
        value={filters.status || 'all'}
        onValueChange={(value) =>
          onFilterChange('status', value === 'all' ? undefined : value)
        }
        disabled={loading}
      >
        <SelectTrigger className="w-[140px] h-9">
          <SelectValue placeholder="Status" />
          {loading && (
            <div className="absolute right-8 top-1/2 transform -translate-y-1/2">
              <div className="w-3 h-3 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
            </div>
          )}
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">Semua Status</SelectItem>
          {SALE_STATUS_OPTIONS.map((status) => (
            <SelectItem key={status.value} value={status.value}>
              {status.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {/* Payment Method Filter */}
      <Select
        value={filters.paymentMethod || 'all'}
        onValueChange={(value) =>
          onFilterChange('paymentMethod', value === 'all' ? undefined : value)
        }
        disabled={loading}
      >
        <SelectTrigger className="w-[140px] h-9">
          <SelectValue placeholder="Metode Bayar" />
          {loading && (
            <div className="absolute right-8 top-1/2 transform -translate-y-1/2">
              <div className="w-3 h-3 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
            </div>
          )}
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">Semua Metode</SelectItem>
          {PAYMENT_METHOD_OPTIONS.map((method) => (
            <SelectItem key={method.value} value={method.value}>
              {method.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {/* Date Range Presets */}
      <Select
        value={selectedPreset}
        onValueChange={handleDateRangePreset}
        disabled={loading}
      >
        <SelectTrigger className="w-[140px] h-9">
          <SelectValue placeholder="Pilih Periode" />
          {loading && (
            <div className="absolute right-8 top-1/2 transform -translate-y-1/2">
              <div className="w-3 h-3 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
            </div>
          )}
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="none">Pilih Periode</SelectItem>
          {DATE_RANGE_PRESETS.map((preset) => (
            <SelectItem key={preset.value} value={preset.value}>
              {preset.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {/* Active Filters Badge and Clear Button */}
      {activeFiltersCount > 0 && (
        <div className="flex items-center gap-2">
          <Badge variant="secondary" className="text-xs">
            <Filter className="mr-1 h-3 w-3" />
            {activeFiltersCount} filter aktif
          </Badge>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClearFilters}
            className="h-8 px-2 lg:px-3"
            disabled={loading}
          >
            <X className="h-4 w-4" />
            Hapus Filter
          </Button>
        </div>
      )}
    </div>
  );
}
