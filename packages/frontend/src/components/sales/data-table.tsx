'use client';

import * as React from 'react';
import {
  ColumnDef,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { ChevronDown, Search, ChevronsLeft, ChevronLeft, ChevronRight, ChevronsRight, Trash2, CheckCircle, XCircle } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useDebounce, DEBOUNCE_DELAYS } from '@/lib/utils/debounce';
import { SaleQueryParams, SaleListResponse } from '@/types/sales';
import { SalesFilters } from './SalesFilters';
import { SALES_TABLE_CONFIG } from '@/lib/constants/sales';

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  meta: SaleListResponse['meta'];
  searchPlaceholder?: string;
  onRowClick?: (row: TData) => void;
  onQueryChange?: (query: SaleQueryParams) => void;
  loading?: boolean;
  query: SaleQueryParams;
  // Filter-related props
  filters: SaleQueryParams;
  onFilterChange: (key: keyof SaleQueryParams, value: any) => void;
  onBatchFilterChange?: (updates: Partial<SaleQueryParams>) => void;
  onClearFilters: () => void;
  // Bulk operations
  onBulkComplete?: (saleIds: string[]) => void;
  onBulkCancel?: (saleIds: string[]) => void;
  onBulkDelete?: (saleIds: string[]) => void;
}

export function DataTable<TData, TValue>({
  columns,
  data,
  meta,
  searchPlaceholder = 'Cari transaksi penjualan...',
  onRowClick,
  onQueryChange,
  loading = false,
  query,
  filters,
  onFilterChange,
  onBatchFilterChange,
  onClearFilters,
  onBulkComplete,
  onBulkCancel,
  onBulkDelete,
}: DataTableProps<TData, TValue>) {
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = React.useState({});
  const [searchValue, setSearchValue] = React.useState(query.search || '');

  // Debounced search using centralized utility
  const debouncedSearch = useDebounce(searchValue, DEBOUNCE_DELAYS.SEARCH);

  // Sync search value with query when query changes externally
  React.useEffect(() => {
    setSearchValue(query.search || '');
  }, [query.search]);

  React.useEffect(() => {
    if (onQueryChange && debouncedSearch !== query.search) {
      onQueryChange({ ...query, search: debouncedSearch || undefined, page: 1 });
    }
  }, [debouncedSearch]);

  // Sync sorting state with query parameters
  React.useEffect(() => {
    if (query.sortBy && query.sortOrder) {
      const newSorting: SortingState = [{
        id: query.sortBy,
        desc: query.sortOrder === 'desc'
      }];
      setSorting(newSorting);
    }
  }, [query.sortBy, query.sortOrder]);

  // Handle sorting changes
  React.useEffect(() => {
    if (sorting.length > 0 && onQueryChange) {
      const sort = sorting[0];
      const newQuery = {
        ...query,
        sortBy: sort.id,
        sortOrder: sort.desc ? 'desc' as const : 'asc' as const,
        page: 1, // Reset to first page when sorting
      };

      // Only update if sorting actually changed
      if (newQuery.sortBy !== query.sortBy || newQuery.sortOrder !== query.sortOrder) {
        onQueryChange(newQuery);
      }
    }
  }, [sorting, onQueryChange, query]);

  // Handle pagination - use dedicated handlers like inventory
  const handlePageChange = (newPage: number) => {
    onQueryChange?.({
      ...query,
      page: newPage,
    });
  };

  const handleLimitChange = (newLimit: string) => {
    onQueryChange?.({
      ...query,
      limit: parseInt(newLimit),
      page: 1, // Reset to first page
    });
  };

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onRowSelectionChange: setRowSelection,
    getCoreRowModel: getCoreRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    state: {
      sorting,
      columnVisibility,
      rowSelection,
    },
    enableRowSelection: true,
    getRowId: (row: any) => row.id,
    manualSorting: true,
    manualFiltering: true,
    manualPagination: true,
  });





  // Count active filters - use memoization to prevent accumulation issues
  const activeFiltersCount = React.useMemo(() => {
    let count = 0;

    // Basic filters
    if (filters.status) count++;
    if (filters.paymentMethod) count++;
    if (filters.customerId) count++;
    if (filters.cashierId) count++;

    // Date range filters (count as one group if both are present)
    if (filters.startDate && filters.endDate) {
      count++;
    } else if (filters.startDate || filters.endDate) {
      count++; // Count partial date range as one filter
    }

    return count;
  }, [filters]);

  return (
    <div className="w-full space-y-4">
      {/* Search and Filters */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex flex-1 items-center space-x-2">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={searchPlaceholder}
              value={searchValue}
              onChange={(event) => setSearchValue(event.target.value)}
              className="pl-8"
            />
          </div>

          {/* Sales Filters Component */}
          <SalesFilters
            filters={filters}
            onFilterChange={onFilterChange}
            onBatchFilterChange={onBatchFilterChange}
            onClearFilters={onClearFilters}
            activeFiltersCount={activeFiltersCount}
            loading={loading}
          />
        </div>

        <div className="flex items-center space-x-2">
          {/* Column Visibility */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="ml-auto">
                Kolom <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => {
                  return (
                    <DropdownMenuCheckboxItem
                      key={column.id}
                      className="capitalize"
                      checked={column.getIsVisible()}
                      onCheckedChange={(value) =>
                        column.toggleVisibility(!!value)
                      }
                    >
                      {column.id}
                    </DropdownMenuCheckboxItem>
                  );
                })}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Bulk Operations */}
      {table.getFilteredSelectedRowModel().rows.length > 0 && (
        <div className="flex items-center gap-2 p-3 bg-muted/50 rounded-lg">
          <span className="text-sm font-medium">
            {table.getFilteredSelectedRowModel().rows.length} transaksi dipilih
          </span>
          <div className="flex items-center gap-2 ml-auto">
            {onBulkComplete && (
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  const selectedIds = table.getFilteredSelectedRowModel().rows.map(row => row.original.id);
                  onBulkComplete(selectedIds);
                  setRowSelection({});
                }}
                className="text-green-600 hover:text-green-700"
              >
                <CheckCircle className="h-4 w-4 mr-1" />
                Selesaikan
              </Button>
            )}
            {onBulkCancel && (
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  const selectedIds = table.getFilteredSelectedRowModel().rows.map(row => row.original.id);
                  onBulkCancel(selectedIds);
                  setRowSelection({});
                }}
                className="text-orange-600 hover:text-orange-700"
              >
                <XCircle className="h-4 w-4 mr-1" />
                Batalkan
              </Button>
            )}
            {onBulkDelete && (
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  const selectedIds = table.getFilteredSelectedRowModel().rows.map(row => row.original.id);
                  onBulkDelete(selectedIds);
                  setRowSelection({});
                }}
                className="text-red-600 hover:text-red-700"
              >
                <Trash2 className="h-4 w-4 mr-1" />
                Hapus
              </Button>
            )}
          </div>
        </div>
      )}

      {/* Table Container with horizontal scroll and sticky actions */}
      <div className="w-full min-w-0 max-w-full overflow-hidden rounded-md border">
        {/* Enhanced Scroll indicators */}
        <div className="bg-blue-50 dark:bg-blue-950/30 px-4 py-3 text-sm text-blue-700 dark:text-blue-300 border-b border-blue-200 dark:border-blue-800">
          <div className="flex items-center justify-center gap-2">
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
              <span className="font-medium">Tabel dapat digulir horizontal</span>
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
            </div>
          </div>
          <div className="text-xs text-center mt-1 text-blue-600 dark:text-blue-400">
            Geser ke kanan untuk melihat kolom aksi dan informasi lainnya
          </div>
        </div>

        <div className="w-full overflow-x-auto" id="table-scroll-container">
          <Table className={`min-w-[${SALES_TABLE_CONFIG.minWidth}]`}>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => {
                    const isActionsColumn = header.id === 'actions';
                    return (
                      <TableHead
                        key={header.id}
                        className={`px-2 lg:px-4 ${isActionsColumn
                          ? 'sticky right-0 bg-background border-l shadow-lg z-10'
                          : ''
                          }`}
                      >
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                      </TableHead>
                    );
                  })}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24 text-center">
                    <div className="flex items-center justify-center space-x-2">
                      <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                      <span>Memuat data penjualan...</span>
                    </div>
                  </TableCell>
                </TableRow>
              ) : table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && 'selected'}
                    className={onRowClick ? 'cursor-pointer hover:bg-muted/50' : ''}
                    onClick={() => onRowClick?.(row.original)}
                  >
                    {row.getVisibleCells().map((cell) => {
                      const isActionsColumn = cell.column.id === 'actions';
                      return (
                        <TableCell
                          key={cell.id}
                          onClick={(e) => e.stopPropagation()}
                          className={`px-2 lg:px-4 ${isActionsColumn
                            ? 'sticky right-0 bg-background border-l shadow-lg z-10'
                            : ''
                            }`}
                        >
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </TableCell>
                      );
                    })}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    className="h-24 text-center"
                  >
                    Tidak ada data penjualan.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Pagination */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between p-4">
        <div className="flex items-center gap-4">
          <div className="text-sm text-muted-foreground">
            Menampilkan {((query.page || 1) - 1) * (query.limit || 10) + 1} - {Math.min((query.page || 1) * (query.limit || 10), meta.total)} dari {meta.total} transaksi
          </div>
          <Select
            value={String(query.limit || 10)}
            onValueChange={handleLimitChange}
            disabled={loading}
          >
            <SelectTrigger className="w-20">
              <SelectValue />
            </SelectTrigger>
            <SelectContent side="top">
              {[5, 10, 20, 30, 40, 50].map((pageSize) => (
                <SelectItem key={pageSize} value={`${pageSize}`}>
                  {pageSize}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(1)}
            disabled={loading || !meta.hasPreviousPage}
            title={`Halaman pertama ${loading ? '(Loading...)' : !meta.hasPreviousPage ? '(Sudah di halaman pertama)' : ''}`}
          >
            <ChevronsLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange((query.page || 1) - 1)}
            disabled={loading || !meta.hasPreviousPage}
            title={`Halaman sebelumnya ${loading ? '(Loading...)' : !meta.hasPreviousPage ? '(Sudah di halaman pertama)' : ''}`}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <div className="flex items-center gap-1">
            <span className="text-sm">Halaman</span>
            <span className="text-sm font-medium">{query.page || 1}</span>
            <span className="text-sm">dari</span>
            <span className="text-sm font-medium">{meta.totalPages}</span>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange((query.page || 1) + 1)}
            disabled={loading || !meta.hasNextPage}
            title={`Halaman selanjutnya ${loading ? '(Loading...)' : !meta.hasNextPage ? '(Sudah di halaman terakhir)' : ''}`}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(meta.totalPages)}
            disabled={loading || !meta.hasNextPage}
            title={`Halaman terakhir ${loading ? '(Loading...)' : !meta.hasNextPage ? '(Sudah di halaman terakhir)' : ''}`}
          >
            <ChevronsRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
