'use client';

import * as React from 'react';
import { Check, ChevronsUpDown, Search, AlertTriangle, Package, ShoppingCart } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { ProductUnit } from '@/types/product';
import { getStockLevelStatus, getStockLevelColor, STOCK_THRESHOLDS } from '@/lib/constants/inventory';
import {
  calculateAvailableStockWithCartImpact,
  convertPosCartItemsToCalculatorFormat,
  extractUnitHierarchies,
  debugCartStockCalculation
} from '@/lib/utils/cart-stock-calculator';

interface UnitWithStock extends ProductUnit {
  stockInfo?: {
    quantityOnHand: number;
    quantityAllocated: number;
    availableStock: number;
  };
}

interface UnitSelectorWithStockProps {
  units: UnitWithStock[];
  value?: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  emptyMessage?: string;
  searchPlaceholder?: string;
  showStockInfo?: boolean;
  productId?: string;
  // Cart-aware stock calculation props
  cartItems?: Array<{
    productId: string;
    unitId: string;
    quantity: number;
    availableUnits: Array<{
      id: string;
      conversionFactor?: number;
    }>;
  }>;
  productData?: {
    unitHierarchies?: Array<{
      unitId: string;
      conversionFactor: number | string;
      level: number;
    }>;
  };
  enableCartAwareStock?: boolean;
  // Compact display mode for cart table
  compactMode?: boolean;
}

export function UnitSelectorWithStock({
  units,
  value,
  onValueChange,
  placeholder = "Pilih unit...",
  disabled = false,
  className,
  emptyMessage = "Tidak ada unit ditemukan.",
  searchPlaceholder = "Cari unit...",
  showStockInfo = true,
  cartItems = [],
  productData,
  enableCartAwareStock = false,
  compactMode = false,
}: UnitSelectorWithStockProps) {
  const [open, setOpen] = React.useState(false);
  const [searchValue, setSearchValue] = React.useState('');

  // Calculate cart-aware stock if enabled
  const unitsWithCartAwareStock = React.useMemo(() => {
    if (!enableCartAwareStock || !productData || !cartItems.length) {
      return units;
    }

    // Extract unit hierarchies from product data
    const unitHierarchies = extractUnitHierarchies(productData);
    if (!unitHierarchies.length) {
      return units;
    }

    // Convert cart items to calculator format
    const cartStockItems = convertPosCartItemsToCalculatorFormat(cartItems);

    // Get the product ID from the first unit (assuming all units belong to same product)
    const productId = units[0]?.stockInfo ?
      (productData as any)?.id || 'unknown' : 'unknown';

    // Create original stock data from units
    const originalStockData: Record<string, any> = {};
    units.forEach(unit => {
      if (unit.stockInfo) {
        originalStockData[unit.id] = unit.stockInfo;
      }
    });

    // Calculate updated stock considering cart impact
    const updatedStockData = calculateAvailableStockWithCartImpact(
      originalStockData,
      cartStockItems,
      productId,
      unitHierarchies
    );

    // Debug logging in development
    if (process.env.NODE_ENV === 'development' && cartStockItems.length > 0) {
      debugCartStockCalculation(
        productId,
        cartStockItems,
        unitHierarchies,
        originalStockData,
        updatedStockData
      );
    }

    // Return units with updated stock info
    return units.map(unit => ({
      ...unit,
      stockInfo: updatedStockData[unit.id] || unit.stockInfo
    }));
  }, [units, enableCartAwareStock, productData, cartItems]);

  // Find the selected unit
  const selectedUnit = unitsWithCartAwareStock.find((unit) => unit.id === value);

  // Filter units based on search
  const filteredUnits = React.useMemo(() => {
    if (!searchValue) return unitsWithCartAwareStock;

    const search = searchValue.toLowerCase();
    return unitsWithCartAwareStock.filter((unit) =>
      unit.name.toLowerCase().includes(search) ||
      unit.abbreviation.toLowerCase().includes(search)
    );
  }, [unitsWithCartAwareStock, searchValue]);

  const handleSelect = (unitId: string) => {
    onValueChange(unitId);
    setOpen(false);
    setSearchValue('');
  };

  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
    if (!newOpen) {
      setSearchValue('');
    }
  };

  const getStockDisplay = (unit: UnitWithStock) => {
    if (!showStockInfo || !unit.stockInfo) return null;

    const { availableStock } = unit.stockInfo;
    const status = getStockLevelStatus(availableStock, STOCK_THRESHOLDS.LOW_STOCK);

    // Check if this is cart-aware stock and if there's a difference
    const originalUnit = units.find(u => u.id === unit.id);
    const originalStock = originalUnit?.stockInfo?.availableStock || 0;
    const hasCartImpact = enableCartAwareStock && originalStock !== availableStock;

    let stockText = `Stok: ${availableStock.toLocaleString('id-ID')}`;

    // Show cart impact if enabled and there's a difference
    if (hasCartImpact && originalStock > availableStock) {
      const cartImpact = originalStock - availableStock;
      stockText = `Stok: ${availableStock.toLocaleString('id-ID')} (${cartImpact.toLocaleString('id-ID')} di keranjang)`;
    }

    return {
      text: stockText,
      status,
      colorClass: getStockLevelColor(status),
      hasCartImpact,
    };
  };

  // Helper function to get stock status color for compact mode
  const getStockStatusColor = (availableStock: number) => {
    if (availableStock === 0) return 'bg-red-500'; // Out of stock
    if (availableStock <= 10) return 'bg-orange-500'; // Low stock
    return 'bg-green-500'; // Good stock
  };

  // Helper function to render compact stock indicator
  const renderCompactStockIndicator = (unit: UnitWithStock) => {
    if (!showStockInfo || !unit.stockInfo) return null;

    const { availableStock } = unit.stockInfo;
    const stockDisplay = getStockDisplay(unit);
    const colorClass = getStockStatusColor(availableStock);

    return (
      <Tooltip>
        <TooltipTrigger asChild>
          <div
            className={cn(
              "w-2 h-2 rounded-full ml-2 flex-shrink-0",
              colorClass
            )}
            aria-label={`Status stok: ${stockDisplay?.text || 'Tidak tersedia'}`}
          />
        </TooltipTrigger>
        <TooltipContent side="top" className="max-w-xs">
          <div className="flex items-center gap-1">
            {stockDisplay?.hasCartImpact && (
              <ShoppingCart className="h-3 w-3" data-testid="shopping-cart-icon" />
            )}
            <span>{stockDisplay?.text || 'Stok tidak tersedia'}</span>
          </div>
        </TooltipContent>
      </Tooltip>
    );
  };

  const selectedStockDisplay = selectedUnit ? getStockDisplay(selectedUnit) : null;

  return (
    <TooltipProvider>
      <Popover open={open} onOpenChange={handleOpenChange}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={cn(
              "h-9 w-full justify-between text-left font-normal",
              !selectedUnit && "text-muted-foreground",
              className
            )}
            disabled={disabled}
          >
            <div className="flex items-center gap-2 min-w-0 flex-1">
              <span className="truncate">
                {selectedUnit
                  ? `${selectedUnit.name} (${selectedUnit.abbreviation})`
                  : placeholder
                }
              </span>
              {/* Compact mode: show only colored circle indicator */}
              {compactMode && selectedUnit && renderCompactStockIndicator(selectedUnit)}

              {/* Normal mode: show full stock badge */}
              {!compactMode && selectedStockDisplay && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Badge
                      variant="outline"
                      className={cn(
                        "text-xs px-1.5 py-0.5 shrink-0",
                        selectedStockDisplay.colorClass
                      )}
                    >
                      {selectedStockDisplay.status === 'out-of-stock' && (
                        <AlertTriangle className="h-3 w-3 mr-1" />
                      )}
                      {selectedStockDisplay.status === 'low' && (
                        <Package className="h-3 w-3 mr-1" />
                      )}
                      {selectedStockDisplay.text}
                    </Badge>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>
                      {selectedStockDisplay.status === 'out-of-stock' && 'Stok habis'}
                      {selectedStockDisplay.status === 'low' && 'Stok rendah'}
                      {selectedStockDisplay.status === 'normal' && 'Stok normal'}
                    </p>
                  </TooltipContent>
                </Tooltip>
              )}
            </div>
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[--radix-popover-trigger-width] p-0" align="start">
          <Command shouldFilter={false}>
            <div className="flex items-center border-b px-3">
              <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
              <input
                placeholder={searchPlaceholder}
                value={searchValue}
                onChange={(e) => setSearchValue(e.target.value)}
                className="flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
                autoFocus
              />
              {searchValue && (
                <button
                  onClick={() => setSearchValue('')}
                  className="ml-2 h-4 w-4 shrink-0 opacity-50 hover:opacity-100 transition-opacity"
                  type="button"
                >
                  ×
                </button>
              )}
            </div>
            <CommandList className="max-h-[200px]">
              <CommandEmpty>{emptyMessage}</CommandEmpty>
              <CommandGroup>
                {searchValue && filteredUnits.length > 0 && (
                  <div className="px-2 py-1.5 text-xs text-muted-foreground border-b">
                    {filteredUnits.length} unit ditemukan
                  </div>
                )}
                {filteredUnits.map((unit) => {
                  const stockDisplay = getStockDisplay(unit);

                  return (
                    <CommandItem
                      key={unit.id}
                      value={unit.id}
                      onSelect={() => handleSelect(unit.id)}
                      className="cursor-pointer py-2"
                    >
                      <div className="flex items-center justify-between w-full">
                        <div className="flex flex-col min-w-0 flex-1">
                          <div className="flex items-center gap-2">
                            <span className="font-medium truncate">{unit.name}</span>
                            <span className="text-xs bg-muted px-1.5 py-0.5 rounded font-mono">
                              {unit.abbreviation}
                            </span>
                            {stockDisplay && (
                              <Badge
                                variant="outline"
                                className={cn(
                                  "text-xs px-1.5 py-0.5",
                                  stockDisplay.colorClass
                                )}
                              >
                                {stockDisplay.hasCartImpact && (
                                  <ShoppingCart className="h-3 w-3 mr-1" />
                                )}
                                {!stockDisplay.hasCartImpact && stockDisplay.status === 'out-of-stock' && (
                                  <AlertTriangle className="h-3 w-3 mr-1" />
                                )}
                                {!stockDisplay.hasCartImpact && stockDisplay.status === 'low' && (
                                  <Package className="h-3 w-3 mr-1" />
                                )}
                                {stockDisplay.text}
                              </Badge>
                            )}
                          </div>
                          {unit.description && (
                            <span className="text-xs text-muted-foreground mt-0.5 truncate">
                              {unit.description}
                            </span>
                          )}
                        </div>
                        <Check
                          className={cn(
                            "ml-2 h-4 w-4 shrink-0",
                            value === unit.id ? "opacity-100" : "opacity-0"
                          )}
                        />
                      </div>
                    </CommandItem>
                  );
                })}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </TooltipProvider>
  );
}
