"use client"

import * as React from "react"
import { Input } from "@/components/ui/input"
import { cn } from "@/lib/utils"

export interface LiveCurrencyInputProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, "onChange" | "value" | "type"> {
  value?: number
  onChange?: (value: number | undefined) => void
  onValueChange?: (value: number | undefined) => void
  error?: string
  allowNegative?: boolean
}

const LiveCurrencyInput = React.forwardRef<HTMLInputElement, LiveCurrencyInputProps>(
  ({ className, value, onChange, onValueChange, error, allowNegative = false, ...props }, ref) => {
    const [displayValue, setDisplayValue] = React.useState("")
    const inputRef = React.useRef<HTMLInputElement>(null)
    const lastCursorPosition = React.useRef<number>(0)
    const isUserInputRef = React.useRef<boolean>(false)
    const lastExternalValueRef = React.useRef<number | undefined>(undefined)
    const isInitializedRef = React.useRef<boolean>(false)

    // Combine refs
    React.useImperativeHandle(ref, () => inputRef.current!, [])

    // Format number to Indonesian currency format using consistent formatting
    const formatCurrency = (num: number | undefined): string => {
      if (num === undefined || num === null || isNaN(num)) {
        return ""
      }

      if (num === 0) {
        return "Rp 0"
      }

      // Use consistent Indonesian formatting with Intl.NumberFormat
      try {
        const formatted = new Intl.NumberFormat('id-ID', {
          style: 'currency',
          currency: 'IDR',
          minimumFractionDigits: 0,
          maximumFractionDigits: 0,
        }).format(Math.abs(num));

        // Handle negative values if allowed
        return allowNegative && num < 0 ? `-${formatted}` : formatted;
      } catch (error) {
        // Fallback to manual formatting if Intl fails
        const formatted = Math.abs(num)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ".")
        return `Rp ${allowNegative && num < 0 ? "-" : ""}${formatted}`
      }
    }

    // Parse currency string to number with improved error handling
    const parseCurrency = (str: string): number | undefined => {
      if (!str || str.trim() === "") {
        return undefined
      }

      // Remove currency symbols, spaces, and thousand separators
      const cleaned = str
        .replace(/Rp\s?/gi, "")  // Remove Rp prefix (case insensitive)
        .replace(/\./g, "")      // Remove dots (thousand separators)
        .replace(/,/g, "")       // Remove commas (alternative separators)
        .replace(/\s/g, "")      // Remove all spaces
        .trim()

      if (cleaned === "" || cleaned === "-") {
        return undefined
      }

      // Handle negative values
      const isNegative = cleaned.startsWith("-")
      const numericString = cleaned.replace(/^-/, "")

      // Validate that remaining string contains only digits
      if (!/^\d+$/.test(numericString)) {
        return undefined
      }

      const parsed = Number.parseInt(numericString, 10)
      if (isNaN(parsed) || parsed < 0) {
        return undefined
      }

      return isNegative && allowNegative ? -parsed : parsed
    }

    // Calculate cursor position after formatting
    const calculateCursorPosition = (oldValue: string, newValue: string, oldCursorPos: number): number => {
      // If cursor was at the end, keep it at the end
      if (oldCursorPos >= oldValue.length) {
        return newValue.length
      }

      // Count digits before cursor in old value
      const beforeCursor = oldValue.slice(0, oldCursorPos)
      const digitsBeforeCursor = beforeCursor.replace(/[^\d]/g, "").length

      // Find position in new value that has the same number of digits before it
      let digitCount = 0
      let newCursorPos = 0

      for (let i = 0; i < newValue.length; i++) {
        if (/\d/.test(newValue[i])) {
          digitCount++
          if (digitCount > digitsBeforeCursor) {
            break
          }
        }
        newCursorPos = i + 1
      }

      return Math.min(newCursorPos, newValue.length)
    }

    // Update display value when value prop changes
    React.useEffect(() => {
      // Handle initial render or external value changes (not from user input)
      const shouldUpdate = !isInitializedRef.current ||
        (!isUserInputRef.current && value !== lastExternalValueRef.current)

      if (shouldUpdate) {
        const formatted = formatCurrency(value)
        setDisplayValue(formatted)
        lastExternalValueRef.current = value
        isInitializedRef.current = true
      }

      // Reset the user input flag after processing
      isUserInputRef.current = false
    }, [value])

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const inputValue = e.target.value
      const cursorPosition = e.target.selectionStart || 0

      // Mark this as user input to prevent useEffect from overriding
      isUserInputRef.current = true

      // Store cursor position
      lastCursorPosition.current = cursorPosition

      // Special case: if user completely clears the field, keep it empty
      if (inputValue.trim() === "") {
        setDisplayValue("")
        onChange?.(undefined)
        onValueChange?.(undefined)
        return
      }

      // Parse the numeric value from input
      const numericValue = parseCurrency(inputValue)

      // Format the value
      const formattedValue = formatCurrency(numericValue)

      // Update display value
      setDisplayValue(formattedValue)

      // Call onChange callbacks
      onChange?.(numericValue)
      onValueChange?.(numericValue)

      // Update cursor position after formatting
      setTimeout(() => {
        if (inputRef.current) {
          const newCursorPos = calculateCursorPosition(inputValue, formattedValue, cursorPosition)
          inputRef.current.setSelectionRange(newCursorPos, newCursorPos)
        }
      }, 0)
    }

    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      const input = e.target as HTMLInputElement
      const cursorPosition = input.selectionStart || 0
      const value = input.value

      // Handle backspace and delete
      if (e.key === "Backspace" || e.key === "Delete") {
        // Allow default behavior for backspace/delete
        return
      }

      // Handle special keys (navigation, selection, etc.)
      if (
        e.key === "ArrowLeft" ||
        e.key === "ArrowRight" ||
        e.key === "ArrowUp" ||
        e.key === "ArrowDown" ||
        e.key === "Home" ||
        e.key === "End" ||
        e.key === "Tab" ||
        e.key === "Escape" ||
        e.key === "Enter" ||
        (e.ctrlKey && (e.key === "a" || e.key === "c" || e.key === "v" || e.key === "x" || e.key === "z"))
      ) {
        return
      }

      // Only allow digits and minus sign (if negative values are allowed)
      if (!/[\d-]/.test(e.key)) {
        e.preventDefault()
        return
      }

      // Handle minus sign - only allow at the beginning and if negative values are allowed
      if (e.key === "-") {
        if (!allowNegative || (cursorPosition !== 0 && !value.startsWith("Rp -"))) {
          e.preventDefault()
          return
        }
      }
    }

    const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
      e.preventDefault()
      const pastedText = e.clipboardData.getData("text")

      // Mark this as user input to prevent useEffect from overriding
      isUserInputRef.current = true

      // Special case: if pasted text is empty, clear the field
      if (pastedText.trim() === "") {
        setDisplayValue("")
        onChange?.(undefined)
        onValueChange?.(undefined)
        return
      }

      // Parse the pasted text using the same logic as manual input
      const numericValue = parseCurrency(pastedText)

      // Always update the display, even if parsing fails
      if (numericValue !== undefined) {
        const formattedValue = formatCurrency(numericValue)
        setDisplayValue(formattedValue)
        onChange?.(numericValue)
        onValueChange?.(numericValue)
      } else {
        // If parsing fails, try to extract just the digits and retry
        const digitsOnly = pastedText.replace(/[^\d]/g, "")
        if (digitsOnly) {
          const fallbackValue = Number.parseInt(digitsOnly, 10)
          if (!isNaN(fallbackValue) && fallbackValue >= 0) {
            const formattedValue = formatCurrency(fallbackValue)
            setDisplayValue(formattedValue)
            onChange?.(fallbackValue)
            onValueChange?.(fallbackValue)
          }
        }
        // If all parsing attempts fail, the field remains unchanged
      }
    }

    return (
      <div className="space-y-1">
        <Input
          ref={inputRef}
          type="text"
          className={cn(
            className,
            error && "border-destructive focus-visible:ring-destructive"
          )}
          value={displayValue}
          onChange={handleChange}
          onKeyDown={handleKeyDown}
          onPaste={handlePaste}
          inputMode="numeric"
          autoComplete="off"
          aria-invalid={error ? "true" : "false"}
          aria-describedby={error ? `${props.id}-error` : undefined}
          {...props}
        />
        {error && (
          <p
            id={`${props.id}-error`}
            className="text-sm text-destructive"
            role="alert"
          >
            {error}
          </p>
        )}
      </div>
    )
  },
)

LiveCurrencyInput.displayName = "LiveCurrencyInput"

export { LiveCurrencyInput }
