'use client';

import * as React from 'react';
import { Check, ChevronsUpDown, Search } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { ProductUnit } from '@/types/product';

interface UnitSelectorProps {
  units: ProductUnit[];
  value?: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  emptyMessage?: string;
  searchPlaceholder?: string;
}

export function UnitSelector({
  units,
  value,
  onValueChange,
  placeholder = "Pilih unit...",
  disabled = false,
  className,
  emptyMessage = "Tidak ada unit ditemukan.",
  searchPlaceholder = "Cari unit...",
}: UnitSelectorProps) {
  const [open, setOpen] = React.useState(false);
  const [searchValue, setSearchValue] = React.useState('');

  // Find the selected unit
  const selectedUnit = units.find((unit) => unit.id === value);

  // Filter units based on search
  const filteredUnits = React.useMemo(() => {
    if (!searchValue) return units;

    const search = searchValue.toLowerCase();
    return units.filter((unit) =>
      unit.name.toLowerCase().includes(search) ||
      unit.abbreviation.toLowerCase().includes(search)
    );
  }, [units, searchValue]);

  const handleSelect = (unitId: string) => {
    onValueChange(unitId);
    setOpen(false);
    setSearchValue('');
  };

  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
    if (!newOpen) {
      setSearchValue('');
    }
  };

  return (
    <Popover open={open} onOpenChange={handleOpenChange}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "h-9 w-full justify-between text-left font-normal",
            !selectedUnit && "text-muted-foreground",
            className
          )}
          disabled={disabled}
        >
          <span className="truncate">
            {selectedUnit
              ? `${selectedUnit.name} (${selectedUnit.abbreviation})`
              : placeholder
            }
          </span>
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[--radix-popover-trigger-width] p-0" align="start">
        <Command shouldFilter={false}>
          <div className="flex items-center border-b px-3">
            <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
            <input
              placeholder={searchPlaceholder}
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              className="flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
              autoFocus
            />
            {searchValue && (
              <button
                onClick={() => setSearchValue('')}
                className="ml-2 h-4 w-4 shrink-0 opacity-50 hover:opacity-100 transition-opacity"
                type="button"
              >
                ×
              </button>
            )}
          </div>
          <CommandList className="max-h-[200px]">
            <CommandEmpty>{emptyMessage}</CommandEmpty>
            <CommandGroup>
              {searchValue && filteredUnits.length > 0 && (
                <div className="px-2 py-1.5 text-xs text-muted-foreground border-b">
                  {filteredUnits.length} unit ditemukan
                </div>
              )}
              {filteredUnits.map((unit) => (
                <CommandItem
                  key={unit.id}
                  value={unit.id}
                  onSelect={() => handleSelect(unit.id)}
                  className="cursor-pointer py-2"
                >
                  <div className="flex items-center justify-between w-full">
                    <div className="flex flex-col min-w-0 flex-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium truncate">{unit.name}</span>
                        <span className="text-xs bg-muted px-1.5 py-0.5 rounded font-mono">
                          {unit.abbreviation}
                        </span>
                      </div>
                      {unit.description && (
                        <span className="text-xs text-muted-foreground mt-0.5 truncate">
                          {unit.description}
                        </span>
                      )}
                    </div>
                    <Check
                      className={cn(
                        "ml-2 h-4 w-4 shrink-0",
                        value === unit.id ? "opacity-100" : "opacity-0"
                      )}
                    />
                  </div>
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
