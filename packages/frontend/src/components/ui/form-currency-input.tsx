"use client"

import * as React from "react"
import { LiveCurrencyInput, LiveCurrencyInputProps } from "./currency-input"
import { FormControl, FormDescription, FormItem, FormLabel, FormMessage } from "./form"

export interface FormCurrencyInputProps extends Omit<LiveCurrencyInputProps, 'error'> {
  label?: string
  description?: string
  required?: boolean
}

/**
 * A wrapper component that integrates LiveCurrencyInput with react-hook-form
 * and shadcn/ui form components for easier form integration.
 */
export const FormCurrencyInput = React.forwardRef<HTMLInputElement, FormCurrencyInputProps>(
  ({ label, description, required, className, ...props }, ref) => {
    return (
      <FormItem>
        {label && (
          <FormLabel>
            {label}
            {required && <span className="text-destructive ml-1">*</span>}
          </FormLabel>
        )}
        <FormControl>
          <LiveCurrencyInput
            ref={ref}
            className={className}
            {...props}
          />
        </FormControl>
        {description && (
          <FormDescription>
            {description}
          </FormDescription>
        )}
        <FormMessage />
      </FormItem>
    )
  }
)

FormCurrencyInput.displayName = "FormCurrencyInput"

export { FormCurrencyInput as default }
