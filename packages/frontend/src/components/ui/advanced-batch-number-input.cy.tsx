import React from 'react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { AdvancedBatchNumberInput } from './advanced-batch-number-input'

// Create a test query client
const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
})

// Wrapper component with providers
const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = createTestQueryClient()
  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}

describe('AdvancedBatchNumberInput Component', () => {
  it('merender input batch number dengan benar', () => {
    const onChange = cy.stub()

    cy.mount(
      <TestWrapper>
        <AdvancedBatchNumberInput
          value=""
          onChange={onChange}
        />
      </TestWrapper>
    )

    cy.get('input[type="text"]').should('be.visible')
    cy.get('input[type="text"]').should('have.attr', 'placeholder', 'Masukkan nomor batch...')
  })

  it('menampilkan tombol panduan format ketika showFormatGuide=true', () => {
    const onChange = cy.stub()

    cy.mount(
      <TestWrapper>
        <AdvancedBatchNumberInput
          value=""
          onChange={onChange}
          showFormatGuide={true}
        />
      </TestWrapper>
    )

    cy.contains('button', 'Panduan Format').should('be.visible')
  })

  it('tidak menampilkan tombol panduan format ketika showFormatGuide=false', () => {
    const onChange = cy.stub()

    cy.mount(
      <TestWrapper>
        <AdvancedBatchNumberInput
          value=""
          onChange={onChange}
          showFormatGuide={false}
        />
      </TestWrapper>
    )

    cy.contains('button', 'Panduan Format').should('not.exist')
  })

  it('menampilkan tombol riwayat batch ketika showHistory=true dan ada value', () => {
    const onChange = cy.stub()

    cy.mount(
      <TestWrapper>
        <AdvancedBatchNumberInput
          value="ABC123"
          onChange={onChange}
          showHistory={true}
        />
      </TestWrapper>
    )

    cy.contains('button', 'Riwayat Batch').should('be.visible')
  })

  it('tidak menampilkan tombol riwayat batch ketika value kosong', () => {
    const onChange = cy.stub()

    cy.mount(
      <TestWrapper>
        <AdvancedBatchNumberInput
          value=""
          onChange={onChange}
          showHistory={true}
        />
      </TestWrapper>
    )

    cy.contains('button', 'Riwayat Batch').should('not.exist')
  })

  it('membuka dialog panduan format ketika tombol diklik', () => {
    const onChange = cy.stub()

    cy.mount(
      <TestWrapper>
        <AdvancedBatchNumberInput
          value=""
          onChange={onChange}
          showFormatGuide={true}
        />
      </TestWrapper>
    )

    cy.contains('button', 'Panduan Format').click()

    cy.get('[role="dialog"]').should('be.visible')
    cy.contains('Panduan Format Nomor Batch').should('be.visible')
  })

  it('memanggil onChange ketika input berubah', () => {
    const onChange = cy.stub()

    cy.mount(
      <TestWrapper>
        <AdvancedBatchNumberInput
          value=""
          onChange={onChange}
        />
      </TestWrapper>
    )

    cy.get('input[type="text"]').type('ABC123')

    // Wait for debounce
    cy.wait(150)

    // Verify onChange was called by checking if the stub was invoked
    cy.then(() => {
      expect(onChange).to.have.been.called
    })
  })

  it('should work on mobile viewport', () => {
    const onChange = cy.stub()

    // Set mobile viewport
    cy.viewport('iphone-x')

    cy.mount(
      <TestWrapper>
        <AdvancedBatchNumberInput
          value=""
          onChange={onChange}
          showFormatGuide={true}
          showHistory={true}
        />
      </TestWrapper>
    )

    // Input should be visible and functional
    cy.get('input[type="text"]').should('be.visible')
    cy.get('input[type="text"]').type('MOBILE-BATCH-001')
    cy.get('input[type="text"]').should('have.value', 'MOBILE-BATCH-001')

    // Buttons should be visible
    cy.contains('button', 'Panduan Format').should('be.visible')

    // Dialog should work on mobile
    cy.contains('button', 'Panduan Format').click()
    cy.get('[role="dialog"]').should('be.visible')
  })
})
