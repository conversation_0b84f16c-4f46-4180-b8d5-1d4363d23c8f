'use client';

import * as React from 'react';
import { Percent, DollarSign } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface DiscountTypeSelectorProps {
  value?: 'PERCENTAGE' | 'FIXED_AMOUNT';
  onValueChange: (value: 'PERCENTAGE' | 'FIXED_AMOUNT' | undefined) => void;
  disabled?: boolean;
  className?: string;
  size?: 'sm' | 'default';
}

export function DiscountTypeSelector({
  value,
  onValueChange,
  disabled = false,
  className,
  size = 'default',
}: DiscountTypeSelectorProps) {
  const handleToggle = (newValue: 'PERCENTAGE' | 'FIXED_AMOUNT') => {
    if (value === newValue) {
      // If clicking the same value, clear it
      onValueChange(undefined);
    } else {
      onValueChange(newValue);
    }
  };

  const buttonSize = size === 'sm' ? 'h-8 w-8' : 'h-9 w-9';

  return (
    <TooltipProvider>
      <div className={cn('flex gap-1', className)}>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              type="button"
              variant={value === 'PERCENTAGE' ? 'default' : 'outline'}
              size="icon"
              className={cn(buttonSize, 'shrink-0')}
              onClick={() => handleToggle('PERCENTAGE')}
              disabled={disabled}
            >
              <Percent className="h-4 w-4" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Diskon Persentase (%)</p>
          </TooltipContent>
        </Tooltip>

        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              type="button"
              variant={value === 'FIXED_AMOUNT' ? 'default' : 'outline'}
              size="icon"
              className={cn(buttonSize, 'shrink-0')}
              onClick={() => handleToggle('FIXED_AMOUNT')}
              disabled={disabled}
            >
              <span className="text-xs font-bold">Rp</span>
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Diskon Nominal (Rp)</p>
          </TooltipContent>
        </Tooltip>
      </div>
    </TooltipProvider>
  );
}
