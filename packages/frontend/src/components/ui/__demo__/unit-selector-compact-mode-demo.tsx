/**
 * Demonstration of UnitSelectorWithStock Compact Mode
 * 
 * This file demonstrates the compact visual indicators feature
 * for the UnitSelectorWithStock component in the POS cart table.
 * 
 * Features demonstrated:
 * 1. Color-coded circle indicators (green, orange, red)
 * 2. Tooltip with full stock information
 * 3. Cart-aware stock calculation
 * 4. Shopping cart icon for cart-affected items
 * 5. Accessibility with ARIA labels
 */

import React from 'react';
import { UnitSelectorWithStock } from '../unit-selector-with-stock';
import { UnitType } from '@/types/product';

// Mock data for demonstration
const mockUnitsGoodStock = [
  {
    id: 'tablet-id',
    name: 'Tablet',
    abbreviation: 'tab',
    type: UnitType.COUNT,
    isBaseUnit: true,
    description: 'Base unit',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    stockInfo: {
      quantityOnHand: 100,
      quantityAllocated: 0,
      availableStock: 100 // Good stock (>10) - Green circle
    }
  }
];

const mockUnitsLowStock = [
  {
    id: 'strip-id',
    name: 'Strip',
    abbreviation: 'strip',
    type: UnitType.PACKAGE,
    isBaseUnit: false,
    description: 'Strip package',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    stockInfo: {
      quantityOnHand: 5,
      quantityAllocated: 0,
      availableStock: 5 // Low stock (1-10) - Orange circle
    }
  }
];

const mockUnitsOutOfStock = [
  {
    id: 'box-id',
    name: 'Box',
    abbreviation: 'box',
    type: UnitType.PACKAGE,
    isBaseUnit: false,
    description: 'Box package',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    stockInfo: {
      quantityOnHand: 0,
      quantityAllocated: 0,
      availableStock: 0 // Out of stock (0) - Red circle
    }
  }
];

// Mock cart items for cart-aware demonstration
const mockCartItems = [
  {
    productId: 'product-1',
    unitId: 'tablet-id',
    quantity: 20,
    availableUnits: [
      { id: 'tablet-id', conversionFactor: 1 }
    ]
  }
];

const mockProductData = {
  unitHierarchies: [
    { unitId: 'tablet-id', conversionFactor: 1, level: 0 }
  ]
};

export function UnitSelectorCompactModeDemo() {
  const [selectedGood, setSelectedGood] = React.useState('tablet-id');
  const [selectedLow, setSelectedLow] = React.useState('strip-id');
  const [selectedOut, setSelectedOut] = React.useState('box-id');

  return (
    <div className="p-6 space-y-8 max-w-4xl mx-auto">
      <div className="space-y-4">
        <h1 className="text-2xl font-bold">UnitSelectorWithStock Compact Mode Demo</h1>
        <p className="text-gray-600">
          This demonstrates the compact visual indicators for stock status in the POS cart table.
        </p>
      </div>

      {/* Normal Mode vs Compact Mode Comparison */}
      <div className="space-y-6">
        <h2 className="text-xl font-semibold">Normal Mode vs Compact Mode</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-3">
            <h3 className="text-lg font-medium">Normal Mode (Full Text)</h3>
            <div className="p-4 border rounded-lg bg-gray-50">
              <UnitSelectorWithStock
                units={mockUnitsGoodStock}
                value={selectedGood}
                onValueChange={setSelectedGood}
                showStockInfo={true}
                compactMode={false}
                className="w-full"
              />
            </div>
            <p className="text-sm text-gray-600">
              Shows full stock badge with text "Stok: 100"
            </p>
          </div>

          <div className="space-y-3">
            <h3 className="text-lg font-medium">Compact Mode (Circle Indicator)</h3>
            <div className="p-4 border rounded-lg bg-gray-50">
              <UnitSelectorWithStock
                units={mockUnitsGoodStock}
                value={selectedGood}
                onValueChange={setSelectedGood}
                showStockInfo={true}
                compactMode={true}
                className="w-full"
              />
            </div>
            <p className="text-sm text-gray-600">
              Shows green circle indicator. Hover for tooltip with full info.
            </p>
          </div>
        </div>
      </div>

      {/* Color-Coded Stock Levels */}
      <div className="space-y-6">
        <h2 className="text-xl font-semibold">Color-Coded Stock Levels</h2>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Good Stock - Green */}
          <div className="space-y-3">
            <h3 className="text-lg font-medium text-green-600">Good Stock (&gt;10)</h3>
            <div className="p-4 border rounded-lg bg-green-50">
              <UnitSelectorWithStock
                units={mockUnitsGoodStock}
                value={selectedGood}
                onValueChange={setSelectedGood}
                showStockInfo={true}
                compactMode={true}
                className="w-full"
              />
            </div>
            <div className="flex items-center gap-2 text-sm">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span>Green circle for good stock</span>
            </div>
          </div>

          {/* Low Stock - Orange */}
          <div className="space-y-3">
            <h3 className="text-lg font-medium text-orange-600">Low Stock (1-10)</h3>
            <div className="p-4 border rounded-lg bg-orange-50">
              <UnitSelectorWithStock
                units={mockUnitsLowStock}
                value={selectedLow}
                onValueChange={setSelectedLow}
                showStockInfo={true}
                compactMode={true}
                className="w-full"
              />
            </div>
            <div className="flex items-center gap-2 text-sm">
              <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
              <span>Orange circle for low stock</span>
            </div>
          </div>

          {/* Out of Stock - Red */}
          <div className="space-y-3">
            <h3 className="text-lg font-medium text-red-600">Out of Stock (0)</h3>
            <div className="p-4 border rounded-lg bg-red-50">
              <UnitSelectorWithStock
                units={mockUnitsOutOfStock}
                value={selectedOut}
                onValueChange={setSelectedOut}
                showStockInfo={true}
                compactMode={true}
                className="w-full"
              />
            </div>
            <div className="flex items-center gap-2 text-sm">
              <div className="w-3 h-3 bg-red-500 rounded-full"></div>
              <span>Red circle for out of stock</span>
            </div>
          </div>
        </div>
      </div>

      {/* Cart-Aware Stock Demonstration */}
      <div className="space-y-6">
        <h2 className="text-xl font-semibold">Cart-Aware Stock Calculation</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-3">
            <h3 className="text-lg font-medium">Without Cart Impact</h3>
            <div className="p-4 border rounded-lg bg-gray-50">
              <UnitSelectorWithStock
                units={mockUnitsGoodStock}
                value={selectedGood}
                onValueChange={setSelectedGood}
                showStockInfo={true}
                compactMode={true}
                enableCartAwareStock={false}
                className="w-full"
              />
            </div>
            <p className="text-sm text-gray-600">
              Shows original stock: 100 tablets
            </p>
          </div>

          <div className="space-y-3">
            <h3 className="text-lg font-medium">With Cart Impact</h3>
            <div className="p-4 border rounded-lg bg-blue-50">
              <UnitSelectorWithStock
                units={mockUnitsGoodStock}
                value={selectedGood}
                onValueChange={setSelectedGood}
                showStockInfo={true}
                compactMode={true}
                enableCartAwareStock={true}
                cartItems={mockCartItems}
                productData={mockProductData}
                className="w-full"
              />
            </div>
            <p className="text-sm text-gray-600">
              Shows remaining stock after cart: 80 tablets (20 in cart)
              <br />
              Hover to see shopping cart icon in tooltip
            </p>
          </div>
        </div>
      </div>

      {/* Usage Instructions */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Usage Instructions</h2>
        <div className="p-4 border rounded-lg bg-blue-50">
          <ol className="list-decimal list-inside space-y-2 text-sm">
            <li><strong>Hover over circle indicators</strong> to see full stock information in tooltip</li>
            <li><strong>Green circles</strong> indicate good stock availability (&gt;10 units)</li>
            <li><strong>Orange circles</strong> indicate low stock warning (1-10 units)</li>
            <li><strong>Red circles</strong> indicate out of stock or critical (0 units)</li>
            <li><strong>Shopping cart icon</strong> in tooltip indicates cart impact on stock</li>
            <li><strong>ARIA labels</strong> provide accessibility for screen readers</li>
          </ol>
        </div>
      </div>

      {/* Implementation Code */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Implementation</h2>
        <div className="p-4 border rounded-lg bg-gray-50">
          <pre className="text-sm overflow-x-auto">
            <code>{`// Enable compact mode in POS cart table
<UnitSelectorWithStock
  units={convertToUnitSelectorFormat(item)}
  value={item.unitId}
  onValueChange={(value) => handleUnitChange(index, value)}
  showStockInfo={true}
  enableCartAwareStock={true}
  cartItems={cartItems}
  productData={createProductDataForCartItem(item)}
  compactMode={true} // Enable compact visual indicators
  className="h-8 w-40"
/>`}</code>
          </pre>
        </div>
      </div>
    </div>
  );
}
