'use client';

import * as React from 'react';
import { Check, ChevronsUpDown, Search, Loader2, Plus } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { CustomerType, Customer } from '@/types/customer';
import { useSearchCustomers, useCustomer } from '@/hooks/useCustomers';
import { useDebounce } from '@/hooks/useDebounce';
import { Skeleton } from '@/components/ui/skeleton';
import { CustomerCreationDialog } from '@/components/customers/customer-creation-dialog';

interface CustomerSelectorProps {
  value?: string;
  onValueChange: (value: string | undefined) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  emptyMessage?: string;
  searchPlaceholder?: string;
  allowWalkIn?: boolean;
  walkInLabel?: string;
  onCreateNew?: () => void;
  /** Enable built-in customer creation dialog */
  enableCustomerCreation?: boolean;
  /** Callback when a new customer is created via the built-in dialog */
  onCustomerCreated?: (customer: Customer) => void;
  /** Default customer type for the creation dialog */
  defaultCustomerType?: CustomerType;
}

export function CustomerSelector({
  value,
  onValueChange,
  placeholder = "Pilih pelanggan...",
  disabled = false,
  className,
  emptyMessage = "Tidak ada pelanggan ditemukan.",
  searchPlaceholder = "Cari pelanggan...",
  allowWalkIn = true,
  walkInLabel = "Pelanggan Umum",
  onCreateNew,
  enableCustomerCreation = false,
  onCustomerCreated,
  defaultCustomerType = CustomerType.REGISTERED,
}: CustomerSelectorProps) {
  const [open, setOpen] = React.useState(false);
  const [searchValue, setSearchValue] = React.useState('');
  const [showCreateDialog, setShowCreateDialog] = React.useState(false);

  // Debounce search to avoid too many API calls
  const debouncedSearch = useDebounce(searchValue, 300);

  // Fetch customers based on search
  const { data: customers = [], isLoading } = useSearchCustomers(
    debouncedSearch,
    20 // limit to 20 results
  );

  // Fetch selected customer details if not found in search results
  const shouldFetchCustomer = value && value !== 'walk-in' && !customers.find(c => c.id === value);
  const {
    data: selectedCustomerData,
    isLoading: isLoadingCustomer,
    error: customerError
  } = useCustomer(shouldFetchCustomer ? value as string : '');

  // Find the selected customer
  const selectedCustomer = React.useMemo(() => {
    if (!value) return null;
    if (value === 'walk-in') return { id: 'walk-in', name: walkInLabel, code: 'WALK-IN', type: CustomerType.WALK_IN };

    // First try to find in search results
    const customerFromSearch = customers.find((customer) => customer.id === value);
    if (customerFromSearch) return customerFromSearch;

    // If not found in search results, use the fetched customer data
    if (selectedCustomerData) {
      return {
        id: selectedCustomerData.id,
        name: selectedCustomerData.fullName,
        code: selectedCustomerData.code,
        type: selectedCustomerData.type,
        phone: selectedCustomerData.phoneNumber,
        membershipLevel: selectedCustomerData.membershipLevel,
      };
    }



    return null;
  }, [customers, value, walkInLabel, selectedCustomerData, shouldFetchCustomer]);

  const handleSelect = (customerId: string) => {
    onValueChange(customerId === value ? undefined : customerId);
    setOpen(false);
    setSearchValue('');
  };

  const handleSearchChange = (newValue: string) => {
    setSearchValue(newValue);
  };

  const clearSearch = () => {
    setSearchValue('');
  };

  // Handle customer creation
  const handleCreateNew = () => {
    if (enableCustomerCreation) {
      setShowCreateDialog(true);
      setOpen(false);
    } else if (onCreateNew) {
      onCreateNew();
      setOpen(false);
    }
  };

  const handleCustomerCreated = (newCustomer: Customer) => {
    // Auto-select the newly created customer
    onValueChange(newCustomer.id);
    onCustomerCreated?.(newCustomer);
    setShowCreateDialog(false);
  };

  // Display value with loading state consideration
  const displayValue = React.useMemo(() => {
    if (isLoadingCustomer && shouldFetchCustomer) {
      return 'Memuat data pelanggan...';
    }
    if (customerError && shouldFetchCustomer) {
      return 'Error memuat pelanggan';
    }
    return selectedCustomer ? selectedCustomer.name : placeholder;
  }, [selectedCustomer, placeholder, isLoadingCustomer, shouldFetchCustomer, customerError]);

  return (
    <>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={cn("w-full justify-between", className)}
            disabled={disabled || (isLoadingCustomer && !!shouldFetchCustomer)}
          >
            <div className="flex items-center gap-2 flex-1 min-w-0">
              {isLoadingCustomer && shouldFetchCustomer ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin shrink-0" />
                  <Skeleton className="h-4 flex-1" />
                </>
              ) : (
                <span className="truncate">{displayValue}</span>
              )}
            </div>
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[--radix-popover-trigger-width] p-0" align="start">
          <Command shouldFilter={false}>
            <div className="flex items-center border-b px-3">
              <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
              <input
                placeholder={searchPlaceholder}
                value={searchValue}
                onChange={(e) => handleSearchChange(e.target.value)}
                className="flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
                autoFocus
              />
              {searchValue && (
                <button
                  onClick={clearSearch}
                  className="ml-2 h-4 w-4 shrink-0 opacity-50 hover:opacity-100"
                >
                  ×
                </button>
              )}
            </div>
            <CommandList>
              <CommandGroup>
                {/* Walk-in option */}
                {allowWalkIn && (
                  <CommandItem
                    value="walk-in"
                    onSelect={() => handleSelect('walk-in')}
                    className="cursor-pointer py-2"
                  >
                    <div className="flex items-center justify-between w-full">
                      <div className="flex flex-col min-w-0 flex-1">
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{walkInLabel}</span>
                          <span className="text-xs bg-muted px-1.5 py-0.5 rounded font-mono">
                            WALK-IN
                          </span>
                        </div>
                        <span className="text-xs text-muted-foreground">
                          Pelanggan tanpa registrasi
                        </span>
                      </div>
                      <Check
                        className={cn(
                          "ml-2 h-4 w-4",
                          value === 'walk-in' ? "opacity-100" : "opacity-0"
                        )}
                      />
                    </div>
                  </CommandItem>
                )}

                {/* Loading state */}
                {isLoading && debouncedSearch && (
                  <CommandItem disabled className="py-2">
                    <div className="flex items-center gap-2">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      <span>Mencari pelanggan...</span>
                    </div>
                  </CommandItem>
                )}

                {/* Customer results */}
                {customers.length === 0 && !isLoading && debouncedSearch ? (
                  <CommandEmpty>
                    <div className="py-2 text-center">
                      <p className="text-sm text-muted-foreground mb-2">{emptyMessage}</p>
                      {(onCreateNew || enableCustomerCreation) && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handleCreateNew}
                          className="gap-2"
                        >
                          <Plus className="h-4 w-4" />
                          Tambah Pelanggan Baru
                        </Button>
                      )}
                    </div>
                  </CommandEmpty>
                ) : (
                  customers.map((customer) => (
                    <CommandItem
                      key={customer.id}
                      value={customer.id}
                      onSelect={() => handleSelect(customer.id)}
                      className="cursor-pointer py-2"
                    >
                      <div className="flex items-center justify-between w-full">
                        <div className="flex flex-col min-w-0 flex-1">
                          <div className="flex items-center gap-2">
                            <span className="font-medium truncate">{customer.name}</span>
                            <span className="text-xs bg-muted px-1.5 py-0.5 rounded font-mono">
                              {customer.code}
                            </span>
                          </div>
                          <div className="flex items-center gap-2 mt-0.5">
                            <span className="text-xs text-muted-foreground truncate">
                              {customer.type === CustomerType.REGISTERED ? 'Terdaftar' : 'Walk-in'}
                            </span>
                            {customer.phone && (
                              <span className="text-xs text-muted-foreground truncate">
                                • {customer.phone}
                              </span>
                            )}
                            {customer.membershipLevel && (
                              <span className="text-xs bg-primary/10 text-primary px-1.5 py-0.5 rounded">
                                {customer.membershipLevel}
                              </span>
                            )}
                          </div>
                        </div>
                        <Check
                          className={cn(
                            "ml-2 h-4 w-4",
                            value === customer.id ? "opacity-100" : "opacity-0"
                          )}
                        />
                      </div>
                    </CommandItem>
                  ))
                )}

                {/* Create new customer option */}
                {(onCreateNew || enableCustomerCreation) && !isLoading && (
                  <CommandItem
                    onSelect={handleCreateNew}
                    className="cursor-pointer py-2 border-t"
                  >
                    <div className="flex items-center gap-2 w-full">
                      <Plus className="h-4 w-4" />
                      <span>Tambah Pelanggan Baru</span>
                    </div>
                  </CommandItem>
                )}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>

      {/* Customer Creation Dialog */}
      {enableCustomerCreation && (
        <CustomerCreationDialog
          open={showCreateDialog}
          onOpenChange={setShowCreateDialog}
          onCustomerCreated={handleCustomerCreated}
          defaultType={defaultCustomerType}
        />
      )}
    </>
  );
}
