import React from 'react';
import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@/test/utils';
import { AdvancedBatchNumberInput } from '../advanced-batch-number-input';

// Mock the hooks
vi.mock('@/hooks/use-batch-validation', () => ({
  useBatchValidation: vi.fn(() => ({
    validationResult: null,
    isLoading: false,
    realTimeError: null,
    getFormatSuggestion: vi.fn(() => null),
  })),
  useBatchValidationRules: vi.fn(() => ({
    data: {
      formats: {
        'Standard': {
          description: 'Format standar untuk produk farmasi',
          example: 'ABC123-2024-001'
        }
      }
    }
  })),
}));

// Mock the BatchHistoryDialog component
vi.mock('@/components/batch-validation/batch-history-dialog', () => ({
  BatchHistoryDialog: ({ open, onOpenChange }: { open: boolean; onOpenChange: (open: boolean) => void }) => (
    <div data-testid="batch-history-dialog" style={{ display: open ? 'block' : 'none' }}>
      <button onClick={() => onOpenChange(false)}>Tutup Riwayat</button>
    </div>
  ),
}));

describe('AdvancedBatchNumberInput', () => {
  const defaultProps = {
    value: '',
    onChange: vi.fn(),
  };

  it('merender input batch number dengan benar', () => {
    render(<AdvancedBatchNumberInput {...defaultProps} />);
    
    const input = screen.getByPlaceholderText('Masukkan nomor batch...');
    expect(input).toBeInTheDocument();
  });

  it('menampilkan tombol panduan format ketika showFormatGuide=true', () => {
    render(<AdvancedBatchNumberInput {...defaultProps} showFormatGuide={true} />);
    
    const formatButton = screen.getByText('Panduan Format');
    expect(formatButton).toBeInTheDocument();
  });

  it('tidak menampilkan tombol panduan format ketika showFormatGuide=false', () => {
    render(<AdvancedBatchNumberInput {...defaultProps} showFormatGuide={false} />);
    
    const formatButton = screen.queryByText('Panduan Format');
    expect(formatButton).not.toBeInTheDocument();
  });

  it('menampilkan tombol riwayat batch ketika showHistory=true dan ada value', () => {
    render(<AdvancedBatchNumberInput {...defaultProps} value="ABC123" showHistory={true} />);
    
    const historyButton = screen.getByText('Riwayat Batch');
    expect(historyButton).toBeInTheDocument();
  });

  it('tidak menampilkan tombol riwayat batch ketika value kosong', () => {
    render(<AdvancedBatchNumberInput {...defaultProps} value="" showHistory={true} />);
    
    const historyButton = screen.queryByText('Riwayat Batch');
    expect(historyButton).not.toBeInTheDocument();
  });

  it('membuka dialog panduan format ketika tombol diklik', async () => {
    render(<AdvancedBatchNumberInput {...defaultProps} showFormatGuide={true} />);
    
    const formatButton = screen.getByText('Panduan Format');
    fireEvent.click(formatButton);
    
    await waitFor(() => {
      const dialogTitle = screen.getByText('Panduan Format Nomor Batch');
      expect(dialogTitle).toBeInTheDocument();
    });
  });

  it('membuka dialog riwayat batch ketika tombol diklik', async () => {
    render(<AdvancedBatchNumberInput {...defaultProps} value="ABC123" showHistory={true} />);
    
    const historyButton = screen.getByText('Riwayat Batch');
    fireEvent.click(historyButton);
    
    await waitFor(() => {
      const historyDialog = screen.getByTestId('batch-history-dialog');
      expect(historyDialog).toBeVisible();
    });
  });

  it('menutup dialog riwayat batch ketika onOpenChange dipanggil', async () => {
    render(<AdvancedBatchNumberInput {...defaultProps} value="ABC123" showHistory={true} />);
    
    // Buka dialog
    const historyButton = screen.getByText('Riwayat Batch');
    fireEvent.click(historyButton);
    
    await waitFor(() => {
      const historyDialog = screen.getByTestId('batch-history-dialog');
      expect(historyDialog).toBeVisible();
    });
    
    // Tutup dialog
    const closeButton = screen.getByText('Tutup Riwayat');
    fireEvent.click(closeButton);
    
    await waitFor(() => {
      const historyDialog = screen.getByTestId('batch-history-dialog');
      expect(historyDialog).not.toBeVisible();
    });
  });

  it('menampilkan format guide dengan data yang benar', async () => {
    render(<AdvancedBatchNumberInput {...defaultProps} showFormatGuide={true} />);
    
    const formatButton = screen.getByText('Panduan Format');
    fireEvent.click(formatButton);
    
    await waitFor(() => {
      expect(screen.getByText('Standard')).toBeInTheDocument();
      expect(screen.getByText('Format standar untuk produk farmasi')).toBeInTheDocument();
      expect(screen.getByText('Contoh: ABC123-2024-001')).toBeInTheDocument();
    });
  });

  it('memanggil onChange ketika input berubah', () => {
    const mockOnChange = vi.fn();
    render(<AdvancedBatchNumberInput {...defaultProps} onChange={mockOnChange} />);
    
    const input = screen.getByPlaceholderText('Masukkan nomor batch...');
    fireEvent.change(input, { target: { value: 'ABC123' } });
    
    // Tunggu debounce
    setTimeout(() => {
      expect(mockOnChange).toHaveBeenCalledWith('ABC123');
    }, 150);
  });
});
