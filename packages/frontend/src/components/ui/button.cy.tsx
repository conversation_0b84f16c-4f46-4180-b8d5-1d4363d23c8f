import { Button } from './button'

describe('Button Component', () => {
  describe('Rendering', () => {
    it('should render with default props', () => {
      cy.mountWithProviders(<Button>Simpan</Button>)
      
      cy.get('button').should('be.visible')
      cy.get('button').should('contain.text', 'Simpan')
      cy.get('button').should('have.attr', 'data-slot', 'button')
    })

    it('should render with custom className', () => {
      cy.mountWithProviders(<Button className="custom-class">Test</Button>)
      
      cy.get('button').should('have.class', 'custom-class')
    })

    it('should render disabled state', () => {
      cy.mountWithProviders(<Button disabled>Disabled</Button>)
      
      cy.get('button').should('be.disabled')
    })
  })

  describe('Variants', () => {
    it('should render default variant', () => {
      cy.mountWithProviders(<Button variant="default">Default</Button>)
      
      cy.get('button').should('have.class', 'bg-primary')
    })

    it('should render destructive variant', () => {
      cy.mountWithProviders(<Button variant="destructive">Hapus</Button>)
      
      cy.get('button').should('have.class', 'bg-destructive')
    })

    it('should render outline variant', () => {
      cy.mountWithProviders(<Button variant="outline">Outline</Button>)
      
      cy.get('button').should('have.class', 'border-input')
    })

    it('should render secondary variant', () => {
      cy.mountWithProviders(<Button variant="secondary">Secondary</Button>)
      
      cy.get('button').should('have.class', 'bg-secondary')
    })

    it('should render ghost variant', () => {
      cy.mountWithProviders(<Button variant="ghost">Ghost</Button>)
      
      cy.get('button').should('have.class', 'hover:bg-accent')
    })

    it('should render link variant', () => {
      cy.mountWithProviders(<Button variant="link">Link</Button>)
      
      cy.get('button').should('have.class', 'text-primary')
    })
  })

  describe('Sizes', () => {
    it('should render default size', () => {
      cy.mountWithProviders(<Button size="default">Default Size</Button>)
      
      cy.get('button').should('have.class', 'h-9')
    })

    it('should render small size', () => {
      cy.mountWithProviders(<Button size="sm">Small</Button>)
      
      cy.get('button').should('have.class', 'h-8')
    })

    it('should render large size', () => {
      cy.mountWithProviders(<Button size="lg">Large</Button>)
      
      cy.get('button').should('have.class', 'h-10')
    })

    it('should render icon size', () => {
      cy.mountWithProviders(<Button size="icon">Icon</Button>)
      
      cy.get('button').should('have.class', 'h-9')
      cy.get('button').should('have.class', 'w-9')
    })
  })

  describe('Interactions', () => {
    it('should handle click events', () => {
      const onClick = cy.stub()
      
      cy.mountWithProviders(<Button onClick={onClick}>Klik Saya</Button>)
      
      cy.get('button').click()
      cy.then(() => {
        expect(onClick).to.have.been.calledOnce
      })
    })

    it('should not trigger click when disabled', () => {
      const onClick = cy.stub()
      
      cy.mountWithProviders(
        <Button onClick={onClick} disabled>
          Disabled Button
        </Button>
      )
      
      cy.get('button').click({ force: true })
      cy.then(() => {
        expect(onClick).not.to.have.been.called
      })
    })

    it('should handle keyboard navigation', () => {
      cy.mountWithProviders(<Button>Keyboard Test</Button>)
      
      cy.get('button').focus()
      cy.get('button').should('have.focus')
      
      cy.get('button').type('{enter}')
      // Button should respond to Enter key
    })
  })

  describe('AsChild Prop', () => {
    it('should render as child component', () => {
      cy.mountWithProviders(
        <Button asChild>
          <a href="/test">Link Button</a>
        </Button>
      )
      
      cy.get('a').should('be.visible')
      cy.get('a').should('have.attr', 'href', '/test')
      cy.get('a').should('have.attr', 'data-slot', 'button')
      cy.get('a').should('contain.text', 'Link Button')
    })

    it('should apply button styles to child component', () => {
      cy.mountWithProviders(
        <Button asChild variant="destructive" size="lg">
          <a href="/delete">Delete Link</a>
        </Button>
      )
      
      cy.get('a').should('have.class', 'bg-destructive')
      cy.get('a').should('have.class', 'h-10')
    })
  })

  describe('Accessibility', () => {
    it('should have proper ARIA attributes', () => {
      cy.mountWithProviders(
        <Button aria-label="Simpan data produk" aria-describedby="save-help">
          Simpan
        </Button>
      )
      
      cy.get('button').should('have.attr', 'aria-label', 'Simpan data produk')
      cy.get('button').should('have.attr', 'aria-describedby', 'save-help')
    })

    it('should be focusable', () => {
      cy.mountWithProviders(<Button>Focusable Button</Button>)
      
      cy.get('button').focus()
      cy.get('button').should('have.focus')
    })

    it('should support keyboard interaction', () => {
      const onClick = cy.stub()
      
      cy.mountWithProviders(<Button onClick={onClick}>Keyboard Button</Button>)
      
      cy.get('button').focus()
      cy.get('button').type('{enter}')
      
      cy.then(() => {
        expect(onClick).to.have.been.calledOnce
      })
    })
  })

  describe('Visual States', () => {
    it('should show hover state', () => {
      cy.mountWithProviders(<Button>Hover Test</Button>)
      
      cy.get('button').trigger('mouseover')
      // Visual hover state should be applied
      cy.get('button').should('have.class', 'hover:bg-primary/90')
    })

    it('should show focus state', () => {
      cy.mountWithProviders(<Button>Focus Test</Button>)
      
      cy.get('button').focus()
      cy.get('button').should('have.focus')
      // Focus ring should be visible
    })

    it('should show active state', () => {
      cy.mountWithProviders(<Button>Active Test</Button>)
      
      cy.get('button').trigger('mousedown')
      // Active state should be applied
    })
  })

  describe('Indonesian UI Text', () => {
    it('should render common Indonesian pharmacy actions', () => {
      const actions = [
        'Simpan',
        'Batal',
        'Hapus',
        'Edit',
        'Tambah',
        'Cari',
        'Filter',
        'Ekspor',
        'Impor',
      ]

      actions.forEach((action, index) => {
        cy.mountWithProviders(<Button key={index}>{action}</Button>)
        cy.get('button').should('contain.text', action)
        
        // Unmount for next iteration
        if (index < actions.length - 1) {
          cy.get('body').then(() => {
            // Clear the component for next test
          })
        }
      })
    })

    it('should handle long Indonesian text', () => {
      const longText = 'Simpan Perubahan Data Produk'
      
      cy.mountWithProviders(<Button>{longText}</Button>)
      
      cy.get('button').should('contain.text', longText)
      cy.get('button').should('be.visible')
    })
  })

  describe('Integration with Forms', () => {
    it('should work as submit button', () => {
      const onSubmit = cy.stub()
      
      cy.mountWithProviders(
        <form onSubmit={onSubmit}>
          <Button type="submit">Kirim</Button>
        </form>
      )
      
      cy.get('button').should('have.attr', 'type', 'submit')
      cy.get('button').click()
      
      cy.then(() => {
        expect(onSubmit).to.have.been.called
      })
    })

    it('should work as reset button', () => {
      cy.mountWithProviders(
        <form>
          <input defaultValue="test" />
          <Button type="reset">Reset</Button>
        </form>
      )
      
      cy.get('button').should('have.attr', 'type', 'reset')
      cy.get('button').click()
      
      cy.get('input').should('have.value', '')
    })
  })
})
