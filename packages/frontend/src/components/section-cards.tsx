import { TrendingDown, TrendingUp, DollarSign, AlertTriangle, Clock, Users } from "lucide-react"
import { formatCurrency, formatNumber } from '@/lib/utils';
import { DashboardStats as StatsType } from '@/types/Dashboard';

import { Badge } from "@/components/ui/badge"
import {
  Card,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"

interface SectionCardsProps {
  stats: StatsType;
}

export function SectionCards({ stats }: SectionCardsProps) {
  const statsCards = [
    {
      title: 'Penjualan Hari Ini',
      value: formatCurrency(stats.dailySales),
      icon: DollarSign,
      description: 'Total penjualan hari ini',
      trend: '+12% dari kemarin',
      trendIcon: TrendingUp,
      trendColor: 'text-green-600',
    },
    {
      title: 'Stok Menipis',
      value: formatNumber(stats.lowStock),
      icon: AlertTriangle,
      description: 'Obat yang perlu restok',
      badge: stats.lowStock > 10 ? 'destructive' : 'secondary',
      trendIcon: stats.lowStock > 10 ? TrendingUp : TrendingDown,
    },
    {
      title: 'Pesanan Tertunda',
      value: formatNumber(stats.pendingOrders),
      icon: Clock,
      description: 'Pesanan menunggu proses',
      badge: stats.pendingOrders > 0 ? 'secondary' : 'default',
      trendIcon: TrendingUp,
    },
    {
      title: 'Pelanggan Aktif',
      value: formatNumber(stats.activeCustomers),
      icon: Users,
      description: 'Pelanggan bulan ini',
      trend: '+8% dari bulan lalu',
      trendIcon: TrendingUp,
      trendColor: 'text-green-600',
    },
  ];
  return (
    <div className="*:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card dark:*:data-[slot=card]:bg-card grid grid-cols-1 gap-4 px-4 *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:shadow-xs lg:px-6 @xl/main:grid-cols-2 @5xl/main:grid-cols-4">
      {statsCards.map((card, index) => (
        <Card key={index} className="@container/card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardDescription className="text-sm font-medium">{card.title}</CardDescription>
            <card.icon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardFooter className="flex-col items-start gap-1.5 text-sm pt-0">
            <div className="flex items-center justify-between w-full">
              <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">
                {card.value}
              </CardTitle>
              {card.badge && (
                <Badge variant={card.badge as any}>
                  {card.value}
                </Badge>
              )}
              {card.trend && !card.badge && (
                <Badge variant="outline">
                  {card.trendIcon && <card.trendIcon className="size-4" />}
                  {card.trend.includes('+') ? card.trend.split(' ')[0] : card.trend.split(' ')[0]}
                </Badge>
              )}
            </div>
            <div className="text-muted-foreground text-xs">
              {card.description}
            </div>
            {card.trend && (
              <div className={`text-xs ${card.trendColor || ''}`}>
                {card.trend}
              </div>
            )}
          </CardFooter>
        </Card>
      ))}
    </div>
  )
}
