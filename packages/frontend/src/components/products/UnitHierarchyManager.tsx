'use client';

import { useState, useEffect } from 'react';
import { Plus, Trash2, GripVertical, Calculator } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { UnitSelector } from '@/components/ui/unit-selector';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useActiveUnits } from '@/hooks/useProductUnits';


// Form-specific type for unit hierarchies
type UnitHierarchyFormData = {
  id?: string;
  unitId: string;
  level: number;
  conversionFactor: number;
  costPrice?: number;
  sellingPrice?: number;
};
import { formatCurrency } from '@/lib/utils';
import { UnitConversionCalculator } from './UnitConversionCalculator';

interface UnitHierarchyManagerProps {
  baseUnitId: string;
  unitHierarchies: UnitHierarchyFormData[];
  onChange: (hierarchies: UnitHierarchyFormData[]) => void;
  errors?: Record<string, string>;
}

interface HierarchyFormData {
  id?: string;
  unitId: string;
  level: number;
  conversionFactor: number;
  costPrice?: number;
  sellingPrice?: number;
}

export function UnitHierarchyManager({
  baseUnitId,
  unitHierarchies,
  onChange,
  errors = {},
}: UnitHierarchyManagerProps) {
  const { data: units = [], isLoading } = useActiveUnits();
  const [hierarchies, setHierarchies] = useState<HierarchyFormData[]>([]);
  const [showCalculator, setShowCalculator] = useState(false);

  // Initialize hierarchies from props
  useEffect(() => {
    if (unitHierarchies.length > 0) {
      const formData = unitHierarchies
        .sort((a, b) => a.level - b.level)
        .map(h => ({
          id: h.id,
          unitId: h.unitId,
          level: h.level,
          conversionFactor: h.conversionFactor,
          costPrice: h.costPrice || undefined,
          sellingPrice: h.sellingPrice || undefined,
        }));
      setHierarchies(formData);
    } else if (baseUnitId) {
      // Initialize with base unit
      setHierarchies([{
        unitId: baseUnitId,
        level: 0,
        conversionFactor: 1,
      }]);
    }
  }, [unitHierarchies, baseUnitId]);

  // Update base unit when baseUnitId changes
  useEffect(() => {
    if (baseUnitId && hierarchies.length > 0) {
      const updatedHierarchies = [...hierarchies];
      const baseHierarchy = updatedHierarchies.find(h => h.level === 0);

      if (baseHierarchy) {
        baseHierarchy.unitId = baseUnitId;
        baseHierarchy.conversionFactor = 1;
        setHierarchies(updatedHierarchies);
        emitChange(updatedHierarchies);
      }
    }
  }, [baseUnitId]);

  const emitChange = (newHierarchies: HierarchyFormData[]) => {
    const validHierarchies = newHierarchies
      .filter(h => h.unitId && h.conversionFactor > 0)
      .map(h => ({
        id: h.id,
        unitId: h.unitId,
        level: h.level,
        conversionFactor: h.conversionFactor,
        costPrice: h.costPrice || undefined,
        sellingPrice: h.sellingPrice || undefined,
      }));

    onChange(validHierarchies);
  };

  const addHierarchyLevel = () => {
    const nextLevel = hierarchies.length;
    const newHierarchy: HierarchyFormData = {
      unitId: '',
      level: nextLevel,
      conversionFactor: nextLevel === 0 ? 1 : 0,
    };

    const updatedHierarchies = [...hierarchies, newHierarchy];
    setHierarchies(updatedHierarchies);
  };

  const removeHierarchyLevel = (index: number) => {
    if (hierarchies[index].level === 0) {
      return; // Cannot remove base unit
    }

    const updatedHierarchies = hierarchies
      .filter((_, i) => i !== index)
      .map((h, i) => ({ ...h, level: i })); // Reorder levels

    setHierarchies(updatedHierarchies);
    emitChange(updatedHierarchies);
  };

  const updateHierarchy = (index: number, field: keyof HierarchyFormData, value: any) => {
    const updatedHierarchies = [...hierarchies];
    updatedHierarchies[index] = {
      ...updatedHierarchies[index],
      [field]: value,
    };

    setHierarchies(updatedHierarchies);
    emitChange(updatedHierarchies);
  };

  const getUnitName = (unitId: string) => {
    const unit = units.find(u => u.id === unitId);
    return unit ? `${unit.name} (${unit.abbreviation})` : 'Pilih unit';
  };

  const getAvailableUnits = (currentLevel: number) => {
    const usedUnitIds = hierarchies
      .filter((_, index) => index !== currentLevel)
      .map(h => h.unitId);

    return units.filter(unit => !usedUnitIds.includes(unit.id));
  };

  const generateConversionChain = () => {
    const sortedHierarchies = [...hierarchies]
      .filter(h => h.unitId && h.conversionFactor > 0)
      .sort((a, b) => b.level - a.level); // Highest level first

    if (sortedHierarchies.length < 2) return '';

    const chain = sortedHierarchies.map((h, index) => {
      const unit = units.find(u => u.id === h.unitId);
      const unitName = unit?.abbreviation || unit?.name || 'Unit';

      if (index === 0) {
        return `1 ${unitName}`;
      }

      const prevHierarchy = sortedHierarchies[index - 1];
      const ratio = Math.round(prevHierarchy.conversionFactor / h.conversionFactor);
      return `${ratio} ${unitName}`;
    });

    return chain.join(' = ');
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Hierarki Unit</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4">Memuat unit...</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
          <CardTitle>Hierarki Unit</CardTitle>
          <div className="flex flex-col gap-2 sm:flex-row">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => setShowCalculator(!showCalculator)}
              className="w-full sm:w-auto"
            >
              <Calculator className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Kalkulator</span>
              <span className="sm:hidden">Kalkulator Konversi</span>
            </Button>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={addHierarchyLevel}
              className="w-full sm:w-auto"
            >
              <Plus className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Tambah Level</span>
              <span className="sm:hidden">Tambah Level Unit</span>
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Conversion Chain Display */}
        {hierarchies.length > 1 && (
          <div className="p-3 bg-muted/50 rounded-lg">
            <Label className="text-sm font-medium text-muted-foreground">Rantai Konversi</Label>
            <p className="text-sm font-mono mt-1 break-all sm:break-normal overflow-hidden">
              {generateConversionChain() || 'Lengkapi hierarki untuk melihat rantai konversi'}
            </p>
          </div>
        )}

        {/* Unit Hierarchy Levels */}
        <div className="space-y-3">
          {hierarchies.map((hierarchy, index) => {
            const isBaseUnit = hierarchy.level === 0;
            const availableUnits = getAvailableUnits(index);

            return (
              <div key={index} className="border rounded-lg p-3 sm:p-4 space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <GripVertical className="h-4 w-4 text-muted-foreground" />
                    <Badge variant={isBaseUnit ? 'default' : 'secondary'} className="text-xs">
                      <span className="hidden sm:inline">Level {hierarchy.level}</span>
                      <span className="sm:hidden">L{hierarchy.level}</span>
                      {isBaseUnit && <span className="hidden sm:inline"> (Unit Dasar)</span>}
                      {isBaseUnit && <span className="sm:hidden"> (Dasar)</span>}
                    </Badge>
                  </div>
                  {!isBaseUnit && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeHierarchyLevel(index)}
                      className="h-8 w-8 p-0"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>

                {/* Main fields - Better layout for wider space */}
                <div className="grid gap-3 sm:grid-cols-2 lg:grid-cols-4">
                  {/* Unit Selection */}
                  <div>
                    <Label className="text-sm">Unit *</Label>
                    {isBaseUnit ? (
                      <Select
                        value={hierarchy.unitId}
                        onValueChange={(value) => updateHierarchy(index, 'unitId', value)}
                        disabled={true}
                      >
                        <SelectTrigger className="h-9">
                          <SelectValue placeholder="Pilih unit" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value={hierarchy.unitId}>
                            {getUnitName(hierarchy.unitId)}
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    ) : (
                      <UnitSelector
                        units={availableUnits}
                        value={hierarchy.unitId}
                        onValueChange={(value) => updateHierarchy(index, 'unitId', value)}
                        placeholder="Pilih unit"
                        searchPlaceholder="Cari unit..."
                        emptyMessage="Tidak ada unit tersedia."
                        className="h-9"
                      />
                    )}
                    {errors[`unitHierarchies.${index}.unitId`] && (
                      <p className="text-xs text-red-600 mt-1">
                        {errors[`unitHierarchies.${index}.unitId`]}
                      </p>
                    )}
                  </div>

                  {/* Conversion Factor */}
                  <div>
                    <Label className="text-sm">Faktor Konversi *</Label>
                    <Input
                      type="number"
                      min="1"
                      step="1"
                      value={hierarchy.conversionFactor || ''}
                      onChange={(e) => updateHierarchy(index, 'conversionFactor', parseInt(e.target.value) || 0)}
                      disabled={isBaseUnit}
                      placeholder={isBaseUnit ? '1' : 'Contoh: 10'}
                      className="h-9"
                    />
                    {errors[`unitHierarchies.${index}.conversionFactor`] && (
                      <p className="text-xs text-red-600 mt-1">
                        {errors[`unitHierarchies.${index}.conversionFactor`]}
                      </p>
                    )}
                  </div>

                  {/* Cost Price */}
                  <div>
                    <Label className="text-sm">Harga Beli</Label>
                    <Input
                      type="number"
                      min="0"
                      step="0.01"
                      value={hierarchy.costPrice || ''}
                      onChange={(e) => updateHierarchy(index, 'costPrice', parseFloat(e.target.value) || undefined)}
                      placeholder="0"
                      className="h-9"
                    />
                  </div>

                  {/* Selling Price */}
                  <div>
                    <Label className="text-sm">Harga Jual</Label>
                    <Input
                      type="number"
                      min="0"
                      step="0.01"
                      value={hierarchy.sellingPrice || ''}
                      onChange={(e) => updateHierarchy(index, 'sellingPrice', parseFloat(e.target.value) || undefined)}
                      placeholder="0"
                      className="h-9"
                    />
                  </div>
                </div>

                {/* Price Display */}
                {(hierarchy.costPrice || hierarchy.sellingPrice) && (
                  <div className="flex flex-col gap-1 sm:flex-row sm:gap-4 text-xs sm:text-sm text-muted-foreground">
                    {hierarchy.costPrice && (
                      <span>Harga Beli: {formatCurrency(hierarchy.costPrice)}</span>
                    )}
                    {hierarchy.sellingPrice && (
                      <span>Harga Jual: {formatCurrency(hierarchy.sellingPrice)}</span>
                    )}
                  </div>
                )}
              </div>
            );
          })}
        </div>

        {/* Unit Conversion Calculator */}
        {showCalculator && hierarchies.length > 1 && (
          <>
            <Separator />
            <UnitConversionCalculator
              hierarchies={hierarchies.filter(h => h.unitId && h.conversionFactor > 0)}
              units={units}
            />
          </>
        )}

        {/* Help Text */}
        <div className="text-xs sm:text-sm text-muted-foreground space-y-1 p-3 bg-muted/30 rounded-lg">
          <p>• Unit dasar (Level 0) memiliki faktor konversi 1 dan tidak dapat diubah</p>
          <p>• Faktor konversi menunjukkan berapa unit dasar dalam 1 unit level ini</p>
          <p>• Contoh: 1 Strip = 10 Tablet, maka faktor konversi Strip adalah 10</p>
        </div>
      </CardContent>
    </Card>
  );
}
