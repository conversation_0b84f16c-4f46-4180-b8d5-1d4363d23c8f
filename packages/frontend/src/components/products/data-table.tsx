'use client';

import * as React from 'react';
import {
  ColumnDef,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { ChevronDown, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';

import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useDebounce, DEBOUNCE_DELAYS } from '@/lib/utils/debounce';
import { ProductQueryParams, ProductListResponse } from '@/types/product';

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  meta: ProductListResponse['meta'];
  searchPlaceholder?: string;
  onRowClick?: (row: TData) => void;
  onQueryChange?: (query: ProductQueryParams) => void;
  loading?: boolean;
  query: ProductQueryParams;
  // Filter-related props
  filters: ProductQueryParams;
  onFilterChange: (key: keyof ProductQueryParams, value: any) => void;
  onClearFilters: () => void;
  filterOptions: {
    productTypes: { value: string; label: string }[];
    productCategories: { value: string; label: string }[];
    medicineClassifications: { value: string; label: string }[];
    productStatuses: { value: string; label: string }[];
  };
}

export function DataTable<TData, TValue>({
  columns,
  data,
  meta,
  searchPlaceholder = 'Cari...',
  onRowClick,
  onQueryChange,
  loading = false,
  query,
  filters,
  onFilterChange,
  onClearFilters,
  filterOptions,
}: DataTableProps<TData, TValue>) {
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
  const [searchValue, setSearchValue] = React.useState(query.search || '');

  // Debounced search using centralized utility
  const debouncedSearch = useDebounce(searchValue, DEBOUNCE_DELAYS.SEARCH);

  React.useEffect(() => {
    if (onQueryChange) {
      onQueryChange({
        ...query,
        search: debouncedSearch || undefined,
        page: 1, // Reset to first page when searching
      });
    }
  }, [debouncedSearch]);

  // Sync search value with query prop
  React.useEffect(() => {
    if (query.search !== searchValue) {
      setSearchValue(query.search || '');
    }
  }, [query.search]);

  // Handle sorting
  React.useEffect(() => {
    if (sorting.length > 0) {
      const sort = sorting[0];
      if (onQueryChange) {
        onQueryChange({
          ...query,
          sortBy: sort.id,
          sortOrder: sort.desc ? 'desc' : 'asc',
          page: 1, // Reset to first page when sorting
        });
      }
    }
  }, [sorting]);

  // Pagination handlers
  const handlePageChange = (newPage: number) => {
    if (onQueryChange) {
      onQueryChange({
        ...query,
        page: newPage,
      });
    }
  };

  const handlePageSizeChange = (newPageSize: string) => {
    if (onQueryChange) {
      onQueryChange({
        ...query,
        limit: parseInt(newPageSize),
        page: 1, // Reset to first page when changing page size
      });
    }
  };

  // Count active filters
  const activeFiltersCount = React.useMemo(() => {
    let count = 0;
    if (filters.type) count++;
    if (filters.category) count++;
    if (filters.medicineClassification) count++;
    if (filters.manufacturer) count++;
    if (filters.isActive !== undefined) count++;
    return count;
  }, [filters]);

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    state: {
      sorting,
      columnVisibility,
    },
    manualSorting: true,
    manualFiltering: true,
    manualPagination: true,
  });

  return (
    <div className="w-full min-w-0 max-w-full space-y-4">
      {/* Unified Single-Row Toolbar */}
      <div className="flex flex-col gap-3 lg:gap-4">
        {/* Main toolbar row - all controls in one line on desktop */}
        <div className="flex flex-col gap-3 lg:flex-row lg:items-center lg:gap-4">
          {/* Search Input */}
          <div className="relative flex-1 max-w-sm">
            <Input
              placeholder={searchPlaceholder}
              value={searchValue}
              onChange={(event) => setSearchValue(event.target.value)}
              className="pr-10 h-9"
              disabled={loading}
            />
            {loading && (
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
              </div>
            )}
          </div>

          {/* Filter Controls - responsive grid */}
          <div className="ml-auto flex flex-col gap-3 sm:flex-row sm:flex-wrap lg:flex-nowrap lg:gap-3">
            {/* Product Type Filter */}
            <div className="min-w-0 flex-shrink-0">
              <Select
                value={filters.type || 'all'}
                onValueChange={(value) => onFilterChange('type', value === 'all' ? undefined : value)}
                disabled={loading}
              >
                <SelectTrigger className="h-9 w-full sm:w-[140px]">
                  <SelectValue placeholder="Semua Jenis" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Jenis</SelectItem>
                  {filterOptions.productTypes.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Product Category Filter */}
            <div className="min-w-0 flex-shrink-0">
              <Select
                value={filters.category || 'all'}
                onValueChange={(value) => onFilterChange('category', value === 'all' ? undefined : value)}
                disabled={loading}
              >
                <SelectTrigger className="h-9 w-full sm:w-[140px]">
                  <SelectValue placeholder="Semua Kategori" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Kategori</SelectItem>
                  {filterOptions.productCategories.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Medicine Classification Filter */}
            <div className="min-w-0 flex-shrink-0">
              <Select
                value={filters.medicineClassification || 'all'}
                onValueChange={(value) => onFilterChange('medicineClassification', value === 'all' ? undefined : value)}
                disabled={loading}
              >
                <SelectTrigger className="h-9 w-full sm:w-[140px]">
                  <SelectValue placeholder="Semua Klasifikasi" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Klasifikasi</SelectItem>
                  {filterOptions.medicineClassifications.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Status Filter */}
            <div className="min-w-0 flex-shrink-0">
              <Select
                value={filters.isActive !== undefined ? String(filters.isActive) : 'all'}
                onValueChange={(value) => onFilterChange('isActive', value === 'all' ? undefined : value === 'true')}
                disabled={loading}
              >
                <SelectTrigger className="h-9 w-full sm:w-[120px]">
                  <SelectValue placeholder="Semua Status" />
                </SelectTrigger>
                <SelectContent>
                  {filterOptions.productStatuses.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Clear Filters Button */}
            {activeFiltersCount > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={onClearFilters}
                className="h-9 px-3 text-xs"
                disabled={loading}
              >
                Hapus Filter
                <Badge variant="secondary" className="ml-2 h-5 w-5 rounded-full p-0 text-xs">
                  {activeFiltersCount}
                </Badge>
              </Button>
            )}

            {/* Column Visibility */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="h-9 px-3 text-xs" disabled={loading}>
                  Kolom <ChevronDown className="ml-2 h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                {table
                  .getAllColumns()
                  .filter((column) => column.getCanHide())
                  .map((column) => {
                    return (
                      <DropdownMenuCheckboxItem
                        key={column.id}
                        className="capitalize"
                        checked={column.getIsVisible()}
                        onCheckedChange={(value) => column.toggleVisibility(!!value)}
                      >
                        {column.id}
                      </DropdownMenuCheckboxItem>
                    );
                  })}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>

      {/* Table Container with horizontal scroll and sticky actions */}
      <div className="w-full min-w-0 max-w-full overflow-hidden rounded-md border">
        {/* Enhanced Scroll indicators */}
        <div className="bg-blue-50 dark:bg-blue-950/30 px-4 py-3 text-sm text-blue-700 dark:text-blue-300 border-b border-blue-200 dark:border-blue-800">
          <div className="flex items-center justify-center gap-2">
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
              <span className="font-medium">Tabel dapat digulir horizontal</span>
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
            </div>
          </div>
          <div className="text-xs text-center mt-1 text-blue-600 dark:text-blue-400">
            Kolom "Aksi" akan tetap terlihat saat menggulir
          </div>
        </div>

        {/* Loading overlay */}
        {loading && (
          <div className="absolute inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
              Memuat data...
            </div>
          </div>
        )}

        {/* Table wrapper with scroll */}
        <div className="relative">
          <div className="w-full overflow-x-auto" id="table-scroll-container">
            <Table className="min-w-[1400px]">
              <TableHeader>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map((header) => {
                      const isActionsColumn = header.id === 'actions';
                      return (
                        <TableHead
                          key={header.id}
                          className={`px-2 lg:px-4 ${isActionsColumn
                            ? 'sticky right-0 bg-background border-l shadow-lg z-10'
                            : ''
                            }`}
                        >
                          {header.isPlaceholder
                            ? null
                            : flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                        </TableHead>
                      );
                    })}
                  </TableRow>
                ))}
              </TableHeader>
              <TableBody>
                {table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row) => (
                    <TableRow
                      key={row.id}
                      data-state={row.getIsSelected() && 'selected'}
                      className={`cursor-pointer hover:bg-muted/50 ${onRowClick ? 'cursor-pointer' : ''}`}
                      onClick={() => onRowClick?.(row.original)}
                    >
                      {row.getVisibleCells().map((cell) => {
                        const isActionsColumn = cell.column.id === 'actions';
                        return (
                          <TableCell
                            key={cell.id}
                            className={`px-2 lg:px-4 ${isActionsColumn
                              ? 'sticky right-0 bg-background border-l shadow-lg z-10'
                              : ''
                              }`}
                            onClick={(e) => {
                              if (isActionsColumn) {
                                e.stopPropagation();
                              }
                            }}
                          >
                            {flexRender(cell.column.columnDef.cell, cell.getContext())}
                          </TableCell>
                        );
                      })}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={columns.length} className="h-24 text-center">
                      {loading ? (
                        <div className="flex items-center justify-center gap-2">
                          <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                          Memuat data...
                        </div>
                      ) : (
                        'Tidak ada data.'
                      )}
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </div>
      </div>

      {/* Pagination */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <span>
            Menampilkan {((meta.page - 1) * meta.limit) + 1} - {Math.min(meta.page * meta.limit, meta.total)} dari {meta.total} produk
          </span>
        </div>

        <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:gap-6">
          {/* Page Size Selector */}
          <div className="flex items-center gap-2">
            <p className="text-sm font-medium">Baris per halaman</p>
            <Select
              value={`${meta.limit}`}
              onValueChange={handlePageSizeChange}
              disabled={loading}
            >
              <SelectTrigger className="h-8 w-[70px]">
                <SelectValue placeholder={meta.limit} />
              </SelectTrigger>
              <SelectContent side="top">
                {[10, 20, 30, 40, 50].map((pageSize) => (
                  <SelectItem key={pageSize} value={`${pageSize}`}>
                    {pageSize}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Page Navigation */}
          <div className="flex items-center gap-2">
            <div className="flex items-center justify-center text-sm font-medium">
              Halaman {meta.page} dari {meta.totalPages}
            </div>
            <div className="flex items-center gap-1">
              <Button
                variant="outline"
                className="hidden h-8 w-8 p-0 lg:flex"
                onClick={() => handlePageChange(1)}
                disabled={!meta.hasPreviousPage || loading}
              >
                <span className="sr-only">Ke halaman pertama</span>
                <ChevronsLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="h-8 w-8 p-0"
                onClick={() => handlePageChange(meta.page - 1)}
                disabled={!meta.hasPreviousPage || loading}
              >
                <span className="sr-only">Ke halaman sebelumnya</span>
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="h-8 w-8 p-0"
                onClick={() => handlePageChange(meta.page + 1)}
                disabled={!meta.hasNextPage || loading}
              >
                <span className="sr-only">Ke halaman selanjutnya</span>
                <ChevronRight className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="hidden h-8 w-8 p-0 lg:flex"
                onClick={() => handlePageChange(meta.totalPages)}
                disabled={!meta.hasNextPage || loading}
              >
                <span className="sr-only">Ke halaman terakhir</span>
                <ChevronsRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
