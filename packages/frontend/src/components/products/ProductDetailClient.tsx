'use client';


import { useRouter } from 'next/navigation';
import { Arrow<PERSON>ef<PERSON>, Edit, Trash2, UserX, UserCheck, Package, Pill, Activity } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { AlertDialogWrapper } from '@/components/alert-dialog-wrapper';
import { ProductWithRelations } from '@/types/product';
import {
  useProduct,
  useActivateProduct,
  useDeactivateProduct,
  useHardDeleteProduct,
} from '@/hooks/useProducts';
import {
  getProductTypeLabel,
  getProductTypeColor,
  getProductCategoryLabel,
  getProductCategoryColor,
  getMedicineClassificationLabel,
  getMedicineClassificationColor,
  getMedicineClassificationSymbol,
  getMedicineClassificationDescription,
} from '@/lib/constants/product';
import { formatDate, formatCurrency } from '@/lib/utils';
import { navigateBackToProducts } from '@/lib/utils/navigation';

interface ProductDetailClientProps {
  product: ProductWithRelations;
  productId: string;
}

export function ProductDetailClient({ product: initialProduct, productId }: ProductDetailClientProps) {
  const router = useRouter();

  // Fetch product data with TanStack Query for real-time updates
  const { data: product, isLoading, error } = useProduct(productId);

  // Mutations
  const activateProductMutation = useActivateProduct();
  const deactivateProductMutation = useDeactivateProduct();
  const hardDeleteProductMutation = useHardDeleteProduct();

  // Use initial product data while loading or if query fails
  const currentProduct = product || initialProduct;

  const handleEdit = () => {
    router.push(`/dashboard/products/${currentProduct.id}/edit`);
  };

  const handleActivate = async () => {
    await activateProductMutation.mutateAsync(currentProduct.id);
  };

  const handleDeactivate = async () => {
    await deactivateProductMutation.mutateAsync(currentProduct.id);
  };

  const handleHardDelete = async () => {
    await hardDeleteProductMutation.mutateAsync(currentProduct.id);
    router.push('/dashboard/products');
  };

  const classificationSymbol = getMedicineClassificationSymbol(currentProduct.medicineClassification);
  const classificationDescription = getMedicineClassificationDescription(currentProduct.medicineClassification);

  // Show loading state if initial load is still happening
  if (isLoading && !currentProduct) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Memuat data produk...</p>
        </div>
      </div>
    );
  }

  // Show error state if query failed and no initial data
  if (error && !currentProduct) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-red-600 mb-4">Gagal memuat data produk</p>
          <Button onClick={() => navigateBackToProducts(router)}>Kembali</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigateBackToProducts(router)}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Kembali
          </Button>
          <div>
            <h1 className="text-2xl font-bold tracking-tight">{currentProduct.name}</h1>
            <p className="text-muted-foreground">
              {currentProduct.code} • {getProductTypeLabel(currentProduct.type)}
            </p>
          </div>
        </div>

        <div className="flex flex-col gap-2 sm:flex-row">
          <Button variant="outline" onClick={handleEdit}>
            <Edit className="mr-2 h-4 w-4" />
            Edit
          </Button>

          {currentProduct.isActive ? (
            <AlertDialogWrapper
              variant="warning"
              title="Nonaktifkan Produk"
              description={`Apakah Anda yakin ingin menonaktifkan produk "${currentProduct.name}"? Produk yang tidak aktif tidak akan muncul dalam transaksi baru.`}
              confirmText="Nonaktifkan"
              cancelText="Batal"
              pendingText="Menonaktifkan..."
              disabled={deactivateProductMutation.isPending}
              handler={handleDeactivate}
            >
              <Button variant="outline">
                <UserX className="mr-2 h-4 w-4" />
                Set Tidak Aktif
              </Button>
            </AlertDialogWrapper>
          ) : (
            <>
              <AlertDialogWrapper
                variant="primary"
                title="Aktifkan Produk"
                description={`Apakah Anda yakin ingin mengaktifkan produk "${currentProduct.name}"?`}
                confirmText="Aktifkan"
                cancelText="Batal"
                pendingText="Mengaktifkan..."
                disabled={activateProductMutation.isPending}
                handler={handleActivate}
              >
                <Button variant="outline">
                  <UserCheck className="mr-2 h-4 w-4" />
                  Set Aktif
                </Button>
              </AlertDialogWrapper>

              <AlertDialogWrapper
                variant="destructive"
                title="Hapus Produk Permanen"
                description={`Anda akan menghapus permanen produk "${currentProduct.name}". Tindakan ini akan:

• Menghapus semua hierarki unit yang terkait secara permanen
• Menghilangkan semua catatan inventori yang terkait dengan produk ini
• Menyebabkan inkonsistensi pada riwayat transaksi yang mereferensikan produk ini
• Mempengaruhi akurasi pelaporan dan analisis data

Tindakan ini TIDAK DAPAT DIBATALKAN dan dapat berdampak serius pada integritas data sistem.`}
                confirmText="Hapus Permanen"
                cancelText="Batal"
                pendingText="Menghapus..."
                requireConfirmationText="HAPUS"
                confirmationPlaceholder="Ketik 'HAPUS' untuk mengonfirmasi"
                disabled={hardDeleteProductMutation.isPending}
                handler={handleHardDelete}
              >
                <Button variant="destructive">
                  <Trash2 className="mr-2 h-4 w-4" />
                  Hapus Permanen
                </Button>
              </AlertDialogWrapper>
            </>
          )}
        </div>
      </div>

      {/* Status Badge */}
      <div className="flex items-center gap-2">
        <Badge variant={currentProduct.isActive ? 'default' : 'secondary'} className="text-sm">
          {currentProduct.isActive ? 'Aktif' : 'Tidak Aktif'}
        </Badge>
        <Badge variant="outline" className={`${getProductTypeColor(currentProduct.type)} text-sm`}>
          {getProductTypeLabel(currentProduct.type)}
        </Badge>
        <Badge variant="outline" className={`${getProductCategoryColor(currentProduct.category)} text-sm`}>
          {getProductCategoryLabel(currentProduct.category)}
        </Badge>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        {/* Main Information */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Informasi Dasar
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 sm:grid-cols-2">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Kode Produk</label>
                  <p className="font-mono text-sm font-medium">{currentProduct.code}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Nama Produk</label>
                  <p className="text-sm font-medium">{currentProduct.name}</p>
                </div>
                {currentProduct.genericName && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Nama Generik</label>
                    <p className="text-sm">{currentProduct.genericName}</p>
                  </div>
                )}
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Jenis Produk</label>
                  <p className="text-sm">{getProductTypeLabel(currentProduct.type)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Kategori</label>
                  <p className="text-sm">{getProductCategoryLabel(currentProduct.category)}</p>
                </div>
                {currentProduct.manufacturer && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Produsen</label>
                    <p className="text-sm">{currentProduct.manufacturer}</p>
                  </div>
                )}
              </div>

              {currentProduct.description && (
                <>
                  <Separator />
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Deskripsi</label>
                    <p className="text-sm mt-1">{currentProduct.description}</p>
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {/* Medicine Information */}
          {currentProduct.type === 'MEDICINE' && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Pill className="h-5 w-5" />
                  Informasi Obat
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 sm:grid-cols-2">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Klasifikasi Obat</label>
                    <div className="flex items-center gap-2 mt-1">
                      {classificationSymbol && <span className="text-lg">{classificationSymbol}</span>}
                      <Badge variant="outline" className={`${getMedicineClassificationColor(currentProduct.medicineClassification)} text-sm`}>
                        {getMedicineClassificationLabel(currentProduct.medicineClassification)}
                      </Badge>
                    </div>
                    {classificationDescription && (
                      <p className="text-xs text-muted-foreground mt-1">{classificationDescription}</p>
                    )}
                  </div>
                  {currentProduct.bpomNumber && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Nomor BPOM</label>
                      <p className="text-sm font-mono">{currentProduct.bpomNumber}</p>
                    </div>
                  )}
                  {currentProduct.activeIngredient && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Kandungan Aktif</label>
                      <p className="text-sm">{currentProduct.activeIngredient}</p>
                    </div>
                  )}
                  {currentProduct.strength && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Kekuatan</label>
                      <p className="text-sm">{currentProduct.strength}</p>
                    </div>
                  )}
                  {currentProduct.dosageForm && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Bentuk Sediaan</label>
                      <p className="text-sm">{currentProduct.dosageForm}</p>
                    </div>
                  )}
                </div>

                {(currentProduct.indication || currentProduct.contraindication || currentProduct.sideEffects || currentProduct.dosage || currentProduct.storage) && (
                  <>
                    <Separator />
                    <div className="space-y-3">
                      {currentProduct.indication && (
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Indikasi</label>
                          <p className="text-sm mt-1">{currentProduct.indication}</p>
                        </div>
                      )}
                      {currentProduct.contraindication && (
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Kontraindikasi</label>
                          <p className="text-sm mt-1">{currentProduct.contraindication}</p>
                        </div>
                      )}
                      {currentProduct.sideEffects && (
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Efek Samping</label>
                          <p className="text-sm mt-1">{currentProduct.sideEffects}</p>
                        </div>
                      )}
                      {currentProduct.dosage && (
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Dosis</label>
                          <p className="text-sm mt-1">{currentProduct.dosage}</p>
                        </div>
                      )}
                      {currentProduct.storage && (
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Penyimpanan</label>
                          <p className="text-sm mt-1">{currentProduct.storage}</p>
                        </div>
                      )}
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Unit Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Unit & Stok
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">Unit Dasar</label>
                <p className="text-sm font-medium">{currentProduct.baseUnit.name} ({currentProduct.baseUnit.abbreviation})</p>
              </div>

              {(currentProduct.minimumStock || currentProduct.maximumStock || currentProduct.reorderPoint) && (
                <>
                  <Separator />
                  <div className="space-y-3">
                    {currentProduct.minimumStock && (
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Stok Minimum</label>
                        <p className="text-sm">{currentProduct.minimumStock.toLocaleString('id-ID')} {currentProduct.baseUnit.abbreviation}</p>
                      </div>
                    )}
                    {currentProduct.maximumStock && (
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Stok Maksimum</label>
                        <p className="text-sm">{currentProduct.maximumStock.toLocaleString('id-ID')} {currentProduct.baseUnit.abbreviation}</p>
                      </div>
                    )}
                    {currentProduct.reorderPoint && (
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Titik Reorder</label>
                        <p className="text-sm">{currentProduct.reorderPoint.toLocaleString('id-ID')} {currentProduct.baseUnit.abbreviation}</p>
                      </div>
                    )}
                  </div>
                </>
              )}

              {currentProduct.unitHierarchies.length > 0 && (
                <>
                  <Separator />
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Hierarki Unit</label>

                    {/* Conversion Chain Display */}
                    <div className="mt-2 p-3 bg-muted/50 rounded-lg">
                      <div className="text-sm font-mono">
                        {currentProduct.unitHierarchies
                          .sort((a, b) => b.level - a.level)
                          .map((hierarchy, index) => {
                            const unitName = hierarchy.unit.abbreviation || hierarchy.unit.name;
                            if (index === 0) {
                              return `1 ${unitName}`;
                            }
                            const prevHierarchy = currentProduct.unitHierarchies
                              .sort((a, b) => b.level - a.level)[index - 1];
                            const ratio = Math.round(prevHierarchy.conversionFactor / hierarchy.conversionFactor);
                            return `${ratio} ${unitName}`;
                          })
                          .join(' = ')}
                      </div>
                    </div>

                    {/* Detailed Unit Hierarchy */}
                    <div className="space-y-2 mt-3">
                      {currentProduct.unitHierarchies
                        .sort((a, b) => a.level - b.level)
                        .map((hierarchy) => (
                          <div key={hierarchy.id} className="border rounded-lg p-3">
                            <div className="flex items-center justify-between mb-2">
                              <div className="flex items-center gap-2">
                                <Badge variant={hierarchy.level === 0 ? 'default' : 'secondary'} className="text-xs">
                                  Level {hierarchy.level}
                                </Badge>
                                <span className="font-medium">{hierarchy.unit.name}</span>
                                <span className="text-sm text-muted-foreground">({hierarchy.unit.abbreviation})</span>
                              </div>
                              <Badge variant="outline" className="text-xs">
                                1:{hierarchy.conversionFactor}
                              </Badge>
                            </div>

                            {(hierarchy.costPrice || hierarchy.sellingPrice) && (
                              <div className="flex gap-4 text-sm text-muted-foreground">
                                {hierarchy.costPrice && (
                                  <span>Harga Beli: {formatCurrency(hierarchy.costPrice)}</span>
                                )}
                                {hierarchy.sellingPrice && (
                                  <span>Harga Jual: {formatCurrency(hierarchy.sellingPrice)}</span>
                                )}
                              </div>
                            )}
                          </div>
                        ))}
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {/* Metadata */}
          <Card>
            <CardHeader>
              <CardTitle>Metadata</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <label className="text-sm font-medium text-muted-foreground">Dibuat</label>
                <p className="text-sm">{formatDate(currentProduct.createdAt)}</p>
                {currentProduct.createdByUser && (
                  <p className="text-xs text-muted-foreground">
                    oleh {currentProduct.createdByUser.firstName} {currentProduct.createdByUser.lastName}
                  </p>
                )}
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">Diperbarui</label>
                <p className="text-sm">{formatDate(currentProduct.updatedAt)}</p>
                {currentProduct.updatedByUser && (
                  <p className="text-xs text-muted-foreground">
                    oleh {currentProduct.updatedByUser.firstName} {currentProduct.updatedByUser.lastName}
                  </p>
                )}
              </div>
              {currentProduct.notes && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Catatan</label>
                  <p className="text-sm mt-1">{currentProduct.notes}</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>


    </div>
  );
}
