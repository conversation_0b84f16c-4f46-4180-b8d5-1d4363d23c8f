'use client';

import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { useProductStats } from '@/hooks/useProducts';
import { 
  Package, 
  CheckCircle, 
  XCircle, 
  Pill, 
  Stethoscope, 
  Heart,
  Sparkles,
  ShoppingBag,
  Activity
} from 'lucide-react';
import {
  getProductTypeLabel,
  getProductCategoryLabel,
  getMedicineClassificationLabel,
} from '@/lib/constants/product';
import { ProductType, ProductCategory, MedicineClassification } from '@/types/product';

export function ProductStatsCards() {
  const { data: stats, isLoading, error } = useProductStats();

  if (error) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="border-red-200 bg-red-50">
            <CardContent className="p-6">
              <div className="text-center text-red-600">
                <XCircle className="h-8 w-8 mx-auto mb-2" />
                <p className="text-sm">Gagal memuat statistik</p>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (isLoading || !stats) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-4" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-16 mb-2" />
              <Skeleton className="h-3 w-32" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const getTypeIcon = (type: ProductType) => {
    switch (type) {
      case ProductType.MEDICINE:
        return <Pill className="h-4 w-4" />;
      case ProductType.MEDICAL_DEVICE:
        return <Stethoscope className="h-4 w-4" />;
      case ProductType.SUPPLEMENT:
        return <Heart className="h-4 w-4" />;
      case ProductType.COSMETIC:
        return <Sparkles className="h-4 w-4" />;
      case ProductType.GENERAL:
        return <ShoppingBag className="h-4 w-4" />;
      default:
        return <Package className="h-4 w-4" />;
    }
  };

  const getCategoryIcon = (category: ProductCategory) => {
    switch (category) {
      case ProductCategory.ANALGESIC:
      case ProductCategory.ANTIBIOTIC:
      case ProductCategory.CARDIOVASCULAR:
        return <Activity className="h-4 w-4" />;
      case ProductCategory.VITAMIN:
      case ProductCategory.SUPPLEMENT:
        return <Heart className="h-4 w-4" />;
      case ProductCategory.MEDICAL_DEVICE:
        return <Stethoscope className="h-4 w-4" />;
      case ProductCategory.COSMETIC:
      case ProductCategory.PERSONAL_CARE:
        return <Sparkles className="h-4 w-4" />;
      default:
        return <Package className="h-4 w-4" />;
    }
  };

  const getClassificationIcon = (classification: MedicineClassification) => {
    switch (classification) {
      case MedicineClassification.OBAT_BEBAS:
        return <span className="text-green-600">🟢</span>;
      case MedicineClassification.OBAT_BEBAS_TERBATAS:
        return <span className="text-blue-600">🔵</span>;
      case MedicineClassification.OBAT_KERAS:
        return <span className="text-red-600">🔴</span>;
      case MedicineClassification.NARKOTIKA:
        return <span className="text-red-600">🔴</span>;
      case MedicineClassification.PSIKOTROPIKA:
        return <span className="text-orange-600">⚠️</span>;
      case MedicineClassification.JAMU:
      case MedicineClassification.OBAT_HERBAL_TERSTANDAR:
      case MedicineClassification.FITOFARMAKA:
        return <span className="text-green-600">🌿</span>;
      default:
        return <Package className="h-4 w-4" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Main Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Produk</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total.toLocaleString('id-ID')}</div>
            <p className="text-xs text-muted-foreground">
              Semua produk dalam sistem
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Produk Aktif</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.active.toLocaleString('id-ID')}</div>
            <p className="text-xs text-muted-foreground">
              {((stats.active / stats.total) * 100).toFixed(1)}% dari total
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Produk Tidak Aktif</CardTitle>
            <XCircle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.inactive.toLocaleString('id-ID')}</div>
            <p className="text-xs text-muted-foreground">
              {((stats.inactive / stats.total) * 100).toFixed(1)}% dari total
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Jenis Produk</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{Object.keys(stats.byType).length}</div>
            <p className="text-xs text-muted-foreground">
              Kategori jenis produk
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Product Types Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Distribusi Jenis Produk</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {Object.entries(stats.byType).map(([type, count]) => (
              <div key={type} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-2">
                  {getTypeIcon(type as ProductType)}
                  <span className="text-sm font-medium">
                    {getProductTypeLabel(type as ProductType)}
                  </span>
                </div>
                <Badge variant="secondary">
                  {(count as number).toLocaleString('id-ID')}
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Top Categories */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Kategori Produk Teratas</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {Object.entries(stats.byCategory)
              .sort(([, a], [, b]) => (b as number) - (a as number))
              .slice(0, 6)
              .map(([category, count]) => (
                <div key={category} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-2">
                    {getCategoryIcon(category as ProductCategory)}
                    <span className="text-sm font-medium">
                      {getProductCategoryLabel(category as ProductCategory)}
                    </span>
                  </div>
                  <Badge variant="secondary">
                    {(count as number).toLocaleString('id-ID')}
                  </Badge>
                </div>
              ))}
          </div>
        </CardContent>
      </Card>

      {/* Medicine Classifications */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Klasifikasi Obat</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {Object.entries(stats.byMedicineClassification)
              .filter(([, count]) => (count as number) > 0)
              .sort(([, a], [, b]) => (b as number) - (a as number))
              .map(([classification, count]) => (
                <div key={classification} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-2">
                    {getClassificationIcon(classification as MedicineClassification)}
                    <span className="text-sm font-medium">
                      {getMedicineClassificationLabel(classification as MedicineClassification)}
                    </span>
                  </div>
                  <Badge variant="secondary">
                    {(count as number).toLocaleString('id-ID')}
                  </Badge>
                </div>
              ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
