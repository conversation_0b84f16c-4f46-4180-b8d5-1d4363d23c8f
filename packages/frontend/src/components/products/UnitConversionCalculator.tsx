'use client';

import { useState, useEffect } from 'react';
import { ArrowR<PERSON>, Calculator } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { UnitSelector } from '@/components/ui/unit-selector';
import { Badge } from '@/components/ui/badge';
import { ProductUnit } from '@/types/product';
import { formatCurrency } from '@/lib/utils';

interface HierarchyData {
  unitId: string;
  level: number;
  conversionFactor: number;
  costPrice?: number;
  sellingPrice?: number;
}

interface UnitConversionCalculatorProps {
  hierarchies: HierarchyData[];
  units: ProductUnit[];
}

export function UnitConversionCalculator({
  hierarchies,
  units,
}: UnitConversionCalculatorProps) {
  const [fromUnitId, setFromUnitId] = useState('');
  const [toUnitId, setToUnitId] = useState('');
  const [fromAmount, setFromAmount] = useState<number>(1);
  const [toAmount, setToAmount] = useState<number>(0);
  const [priceCalculation, setPriceCalculation] = useState<{
    fromCost?: number;
    fromSelling?: number;
    toCost?: number;
    toSelling?: number;
  }>({});

  // Initialize with first two units if available
  useEffect(() => {
    if (hierarchies.length >= 2 && !fromUnitId && !toUnitId) {
      const sortedHierarchies = [...hierarchies].sort((a, b) => a.level - b.level);
      setFromUnitId(sortedHierarchies[0].unitId);
      setToUnitId(sortedHierarchies[1].unitId);
    }
  }, [hierarchies, fromUnitId, toUnitId]);

  // Calculate conversion when inputs change
  useEffect(() => {
    if (fromUnitId && toUnitId && fromAmount > 0) {
      calculateConversion();
    }
  }, [fromUnitId, toUnitId, fromAmount]);

  const calculateConversion = () => {
    const fromHierarchy = hierarchies.find(h => h.unitId === fromUnitId);
    const toHierarchy = hierarchies.find(h => h.unitId === toUnitId);

    if (!fromHierarchy || !toHierarchy) {
      setToAmount(0);
      setPriceCalculation({});
      return;
    }

    // Convert to base unit first, then to target unit
    const baseAmount = fromAmount * fromHierarchy.conversionFactor;
    const convertedAmount = baseAmount / toHierarchy.conversionFactor;

    setToAmount(convertedAmount);

    // Calculate price conversions
    const newPriceCalculation: typeof priceCalculation = {};

    if (fromHierarchy.costPrice) {
      newPriceCalculation.fromCost = fromAmount * fromHierarchy.costPrice;
      if (toHierarchy.costPrice) {
        newPriceCalculation.toCost = convertedAmount * toHierarchy.costPrice;
      }
    }

    if (fromHierarchy.sellingPrice) {
      newPriceCalculation.fromSelling = fromAmount * fromHierarchy.sellingPrice;
      if (toHierarchy.sellingPrice) {
        newPriceCalculation.toSelling = convertedAmount * toHierarchy.sellingPrice;
      }
    }

    setPriceCalculation(newPriceCalculation);
  };

  const getUnitName = (unitId: string) => {
    const unit = units.find(u => u.id === unitId);
    return unit ? `${unit.name} (${unit.abbreviation})` : 'Unit';
  };

  const getUnitAbbreviation = (unitId: string) => {
    const unit = units.find(u => u.id === unitId);
    return unit?.abbreviation || 'unit';
  };

  const formatAmount = (amount: number) => {
    return amount % 1 === 0 ? amount.toString() : amount.toFixed(2);
  };

  if (hierarchies.length < 2) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calculator className="h-5 w-5" />
            Kalkulator Konversi Unit
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            Tambahkan minimal 2 level unit untuk menggunakan kalkulator konversi.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calculator className="h-5 w-5" />
          Kalkulator Konversi Unit
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Conversion Input */}
        <div className="space-y-4">
          {/* Mobile: Stack all inputs vertically */}
          <div className="grid gap-3 sm:hidden">
            {/* From Amount */}
            <div>
              <Label className="text-sm">Jumlah</Label>
              <Input
                type="number"
                min="0"
                step="0.01"
                value={fromAmount}
                onChange={(e) => setFromAmount(parseFloat(e.target.value) || 0)}
                placeholder="1"
                className="h-9"
              />
            </div>

            {/* From Unit */}
            <div>
              <Label className="text-sm">Dari Unit</Label>
              <UnitSelector
                units={hierarchies.map(h => units.find(u => u.id === h.unitId)).filter(Boolean) as ProductUnit[]}
                value={fromUnitId}
                onValueChange={setFromUnitId}
                placeholder="Pilih unit asal"
                searchPlaceholder="Cari unit asal..."
                emptyMessage="Tidak ada unit tersedia."
                className="h-9"
              />
            </div>

            {/* Arrow */}
            <div className="flex justify-center py-2">
              <ArrowRight className="h-5 w-5 text-muted-foreground rotate-90" />
            </div>

            {/* To Amount (Read-only) */}
            <div>
              <Label className="text-sm">Hasil</Label>
              <Input
                type="text"
                value={formatAmount(toAmount)}
                readOnly
                className="bg-muted h-9"
              />
            </div>

            {/* To Unit */}
            <div>
              <Label className="text-sm">Ke Unit</Label>
              <UnitSelector
                units={hierarchies.map(h => units.find(u => u.id === h.unitId)).filter(Boolean) as ProductUnit[]}
                value={toUnitId}
                onValueChange={setToUnitId}
                placeholder="Pilih unit tujuan"
                searchPlaceholder="Cari unit tujuan..."
                emptyMessage="Tidak ada unit tersedia."
                className="h-9"
              />
            </div>
          </div>

          {/* Desktop: Horizontal layout - Better spacing for wider screens */}
          <div className="hidden sm:grid sm:grid-cols-5 lg:grid-cols-7 gap-4 items-end">
            {/* From Amount */}
            <div className="lg:col-span-1">
              <Label className="text-sm">Jumlah</Label>
              <Input
                type="number"
                min="0"
                step="0.01"
                value={fromAmount}
                onChange={(e) => setFromAmount(parseFloat(e.target.value) || 0)}
                placeholder="1"
                className="h-9"
              />
            </div>

            {/* From Unit */}
            <div className="lg:col-span-2">
              <Label className="text-sm">Dari Unit</Label>
              <UnitSelector
                units={hierarchies.map(h => units.find(u => u.id === h.unitId)).filter(Boolean) as ProductUnit[]}
                value={fromUnitId}
                onValueChange={setFromUnitId}
                placeholder="Pilih unit asal"
                searchPlaceholder="Cari unit asal..."
                emptyMessage="Tidak ada unit tersedia."
                className="h-9"
              />
            </div>

            {/* Arrow */}
            <div className="flex justify-center lg:col-span-1">
              <ArrowRight className="h-5 w-5 text-muted-foreground" />
            </div>

            {/* To Amount (Read-only) */}
            <div className="lg:col-span-1">
              <Label className="text-sm">Hasil</Label>
              <Input
                type="text"
                value={formatAmount(toAmount)}
                readOnly
                className="bg-muted h-9"
              />
            </div>

            {/* To Unit */}
            <div className="lg:col-span-2">
              <Label className="text-sm">Ke Unit</Label>
              <UnitSelector
                units={hierarchies.map(h => units.find(u => u.id === h.unitId)).filter(Boolean) as ProductUnit[]}
                value={toUnitId}
                onValueChange={setToUnitId}
                placeholder="Pilih unit tujuan"
                searchPlaceholder="Cari unit tujuan..."
                emptyMessage="Tidak ada unit tersedia."
                className="h-9"
              />
            </div>
          </div>
        </div>

        {/* Conversion Result Display */}
        {fromUnitId && toUnitId && fromAmount > 0 && (
          <div className="p-3 bg-muted/50 rounded-lg">
            <div className="text-center">
              <Badge variant="outline" className="text-sm sm:text-base px-2 sm:px-3 py-1 break-all">
                {formatAmount(fromAmount)} {getUnitAbbreviation(fromUnitId)} = {formatAmount(toAmount)} {getUnitAbbreviation(toUnitId)}
              </Badge>
            </div>
          </div>
        )}

        {/* Price Calculations */}
        {Object.keys(priceCalculation).length > 0 && (
          <div className="space-y-3">
            <Label className="text-sm font-medium">Kalkulasi Harga</Label>
            <div className="grid gap-3 sm:grid-cols-2">
              {/* From Unit Prices */}
              <div className="space-y-2 p-3 bg-muted/30 rounded-lg">
                <p className="text-xs sm:text-sm font-medium text-muted-foreground break-all">
                  {formatAmount(fromAmount)} {getUnitAbbreviation(fromUnitId)}
                </p>
                {priceCalculation.fromCost && (
                  <p className="text-xs sm:text-sm">
                    Harga Beli: {formatCurrency(priceCalculation.fromCost)}
                  </p>
                )}
                {priceCalculation.fromSelling && (
                  <p className="text-xs sm:text-sm">
                    Harga Jual: {formatCurrency(priceCalculation.fromSelling)}
                  </p>
                )}
              </div>

              {/* To Unit Prices */}
              <div className="space-y-2 p-3 bg-muted/30 rounded-lg">
                <p className="text-xs sm:text-sm font-medium text-muted-foreground break-all">
                  {formatAmount(toAmount)} {getUnitAbbreviation(toUnitId)}
                </p>
                {priceCalculation.toCost && (
                  <p className="text-xs sm:text-sm">
                    Harga Beli: {formatCurrency(priceCalculation.toCost)}
                  </p>
                )}
                {priceCalculation.toSelling && (
                  <p className="text-xs sm:text-sm">
                    Harga Jual: {formatCurrency(priceCalculation.toSelling)}
                  </p>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Conversion Factors Display */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Faktor Konversi</Label>
          <div className="grid gap-2 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            {hierarchies
              .sort((a, b) => a.level - b.level)
              .map((hierarchy) => {
                const unit = units.find(u => u.id === hierarchy.unitId);
                return (
                  <div key={hierarchy.unitId} className="flex justify-between items-center p-2 bg-muted/30 rounded text-xs sm:text-sm">
                    <span className="truncate mr-2">{unit?.name} ({unit?.abbreviation})</span>
                    <Badge variant="outline" className="text-xs shrink-0">
                      1:{hierarchy.conversionFactor}
                    </Badge>
                  </div>
                );
              })}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
