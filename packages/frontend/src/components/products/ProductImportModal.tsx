'use client';

import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Upload,
  Download,
  FileText,
  CheckCircle,
  AlertCircle,
  X,
  RefreshCw,
} from 'lucide-react';
import { productsApi } from '@/lib/api/products';

interface ImportError {
  row: number;
  code?: string;
  name?: string;
  errors: string[];
}

interface ImportResult {
  success: boolean;
  totalRows: number;
  successfulImports: number;
  failedImports: number;
  errors: ImportError[];
  importedProducts?: any[];
}

interface ProductImportModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onImportComplete?: () => void;
}

export function ProductImportModal({
  open,
  onOpenChange,
  onImportComplete,
}: ProductImportModalProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [importResult, setImportResult] = useState<ImportResult | null>(null);
  const [step, setStep] = useState<'upload' | 'processing' | 'results'>('upload');

  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles.length > 0) {
      const file = acceptedFiles[0];
      setSelectedFile(file);
      setImportResult(null);
      setStep('upload');
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/csv': ['.csv'],
      'application/vnd.ms-excel': ['.csv'],
    },
    maxFiles: 1,
    maxSize: 5 * 1024 * 1024, // 5MB
  });

  const handleDownloadTemplate = async () => {
    try {
      const blob = await productsApi.downloadTemplate();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = 'template-product.csv';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success('Template berhasil diunduh');
    } catch (error: any) {
      console.error('Error downloading template:', error);
      toast.error('Gagal mengunduh template');
    }
  };

  const handleImport = async () => {
    if (!selectedFile) return;

    setIsUploading(true);
    setStep('processing');
    setUploadProgress(0);

    // Simulate progress
    const progressInterval = setInterval(() => {
      setUploadProgress(prev => {
        if (prev >= 90) {
          clearInterval(progressInterval);
          return prev;
        }
        return prev + 10;
      });
    }, 200);

    try {
      const result = await productsApi.importProducts(selectedFile);
      setUploadProgress(100);
      setImportResult(result);
      setStep('results');

      if (result.success && result.successfulImports > 0) {
        toast.success(`Berhasil mengimpor ${result.successfulImports} produk`);
        onImportComplete?.();
      } else if (result.failedImports > 0) {
        toast.warning(`Import selesai dengan ${result.failedImports} error`);
      }
    } catch (error: any) {
      console.error('Import failed:', error);
      toast.error(error?.response?.data?.message || 'Gagal mengimpor data produk');
      setStep('upload');
    } finally {
      setIsUploading(false);
      clearInterval(progressInterval);
    }
  };

  const handleReset = () => {
    setSelectedFile(null);
    setImportResult(null);
    setStep('upload');
    setUploadProgress(0);
  };

  const handleClose = () => {
    handleReset();
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Import Data Produk
          </DialogTitle>
          <DialogDescription>
            Upload file CSV untuk mengimpor data produk secara massal
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Step 1: Upload */}
          {step === 'upload' && (
            <>
              {/* Template Download */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Template CSV</CardTitle>
                  <CardDescription>
                    Unduh template CSV untuk melihat format yang diperlukan
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button
                    variant="outline"
                    onClick={handleDownloadTemplate}
                    className="w-full sm:w-auto"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Unduh Template
                  </Button>
                </CardContent>
              </Card>

              {/* File Upload */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Upload File CSV</CardTitle>
                  <CardDescription>
                    Pilih file CSV yang berisi data produk
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {!selectedFile ? (
                    <div
                      {...getRootProps()}
                      className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${isDragActive
                        ? 'border-primary bg-primary/5'
                        : 'border-muted-foreground/25 hover:border-primary/50 hover:bg-muted/50'
                        }`}
                    >
                      <input {...getInputProps()} />
                      <div className="flex flex-col items-center gap-3">
                        <div className="p-3 bg-muted rounded-full">
                          <Upload className="h-6 w-6 text-muted-foreground" />
                        </div>
                        <div>
                          <p className="font-medium">
                            {isDragActive ? 'Lepaskan file di sini' : 'Seret file ke sini atau klik untuk memilih'}
                          </p>
                          <p className="text-sm text-muted-foreground mt-1">
                            File CSV (maksimal 5MB)
                          </p>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <FileText className="h-8 w-8 text-primary" />
                        <div>
                          <p className="font-medium">{selectedFile.name}</p>
                          <p className="text-sm text-muted-foreground">
                            {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                          </p>
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setSelectedFile(null)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Action Buttons */}
              <div className="flex justify-end gap-3">
                <Button variant="outline" onClick={handleClose}>
                  Batal
                </Button>
                <Button
                  onClick={handleImport}
                  disabled={!selectedFile || isUploading}
                >
                  {isUploading ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Memproses...
                    </>
                  ) : (
                    <>
                      <Upload className="h-4 w-4 mr-2" />
                      Import Data
                    </>
                  )}
                </Button>
              </div>
            </>
          )}

          {/* Step 2: Processing */}
          {step === 'processing' && (
            <Card>
              <CardContent className="p-6">
                <div className="text-center space-y-4">
                  <div className="p-4 bg-primary/10 rounded-full w-16 h-16 mx-auto flex items-center justify-center">
                    <RefreshCw className="h-8 w-8 text-primary animate-spin" />
                  </div>
                  <div>
                    <h3 className="font-semibold">Memproses Import</h3>
                    <p className="text-sm text-muted-foreground">
                      Sedang memvalidasi dan mengimpor data produk...
                    </p>
                  </div>
                  <div className="space-y-2">
                    <Progress value={uploadProgress} className="w-full" />
                    <p className="text-sm text-muted-foreground">
                      {uploadProgress}% selesai
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Step 3: Results */}
          {step === 'results' && importResult && (
            <>
              {/* Summary */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    {importResult.success ? (
                      <CheckCircle className="h-5 w-5 text-green-600" />
                    ) : (
                      <AlertCircle className="h-5 w-5 text-yellow-600" />
                    )}
                    Hasil Import
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold">{importResult.totalRows}</div>
                      <div className="text-sm text-muted-foreground">Total Baris</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">{importResult.successfulImports}</div>
                      <div className="text-sm text-muted-foreground">Berhasil</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-red-600">{importResult.failedImports}</div>
                      <div className="text-sm text-muted-foreground">Gagal</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold">
                        {importResult.totalRows > 0
                          ? Math.round((importResult.successfulImports / importResult.totalRows) * 100)
                          : 0}%
                      </div>
                      <div className="text-sm text-muted-foreground">Tingkat Keberhasilan</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Errors */}
              {importResult.errors && importResult.errors.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <AlertCircle className="h-5 w-5 text-red-600" />
                      Error Detail ({importResult.errors.length})
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="max-h-60 overflow-y-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Baris</TableHead>
                            <TableHead>Kode</TableHead>
                            <TableHead>Nama</TableHead>
                            <TableHead>Error</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {importResult.errors.map((error, index) => (
                            <TableRow key={index}>
                              <TableCell>{error.row}</TableCell>
                              <TableCell>{error.code || '-'}</TableCell>
                              <TableCell>{error.name || '-'}</TableCell>
                              <TableCell>
                                <div className="space-y-1">
                                  {error.errors.map((err, errIndex) => (
                                    <Badge key={errIndex} variant="destructive" className="text-xs">
                                      {err}
                                    </Badge>
                                  ))}
                                </div>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Action Buttons */}
              <div className="flex justify-end gap-3">
                <Button variant="outline" onClick={handleReset}>
                  Import Lagi
                </Button>
                <Button onClick={handleClose}>
                  Selesai
                </Button>
              </div>
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
