'use client';

import React, { useState } from 'react';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Download,
  FileText,
  RefreshCw,
} from 'lucide-react';
import { productsApi } from '@/lib/api/products';
import { ProductType, ProductCategory, MedicineClassification } from '@/types/product';
import {
  PRODUCT_TYPE_OPTIONS,
  PRODUCT_CATEGORY_OPTIONS,
  MEDICINE_CLASSIFICATION_OPTIONS
} from '@/lib/constants/product';

interface ProductExportModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  currentFilters?: {
    search?: string;
    type?: ProductType;
    category?: ProductCategory;
    medicineClassification?: MedicineClassification;
    manufacturer?: string;
    isActive?: boolean;
  };
}

export function ProductExportModal({
  open,
  onOpenChange,
  currentFilters = {},
}: ProductExportModalProps) {
  const [format, setFormat] = useState<'csv' | 'xlsx'>('csv');
  const [exportType, setExportType] = useState<'all' | 'filtered'>('all');
  const [isExporting, setIsExporting] = useState(false);
  const [filters, setFilters] = useState({
    search: currentFilters.search || '',
    type: currentFilters.type || 'all',
    category: currentFilters.category || 'all',
    medicineClassification: currentFilters.medicineClassification || 'all',
    manufacturer: currentFilters.manufacturer || '',
    isActive: currentFilters.isActive !== undefined ? currentFilters.isActive.toString() : 'all',
  });

  const handleExport = async () => {
    setIsExporting(true);

    try {
      const exportParams: any = { format };

      // Apply filters if exporting filtered data
      if (exportType === 'filtered') {
        if (filters.search) exportParams.search = filters.search;
        if (filters.type && filters.type !== 'all') exportParams.type = filters.type;
        if (filters.category && filters.category !== 'all') exportParams.category = filters.category;
        if (filters.medicineClassification && filters.medicineClassification !== 'all') {
          exportParams.medicineClassification = filters.medicineClassification;
        }
        if (filters.manufacturer) exportParams.manufacturer = filters.manufacturer;
        if (filters.isActive && filters.isActive !== 'all') exportParams.isActive = filters.isActive;
      }

      const blob = await productsApi.exportProducts(exportParams);

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;

      const timestamp = new Date().toISOString().split('T')[0];
      const filename = `products-export-${timestamp}.${format}`;
      link.download = filename;

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success(`Data produk berhasil diekspor ke ${format.toUpperCase()}`);
      onOpenChange(false);
    } catch (error: any) {
      console.error('Export failed:', error);
      toast.error(error?.response?.data?.message || 'Gagal mengekspor data produk');
    } finally {
      setIsExporting(false);
    }
  };

  const handleClose = () => {
    if (!isExporting) {
      onOpenChange(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Export Data Produk
          </DialogTitle>
          <DialogDescription>
            Pilih format dan filter untuk mengekspor data produk
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Format Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Format File</CardTitle>
              <CardDescription>
                Pilih format file untuk export data
              </CardDescription>
            </CardHeader>
            <CardContent>
              <RadioGroup
                value={format}
                onValueChange={(value: 'csv' | 'xlsx') => setFormat(value)}
                className="grid grid-cols-2 gap-4"
              >
                <div className="flex items-center space-x-2 border rounded-lg p-4 cursor-pointer hover:bg-muted/50">
                  <RadioGroupItem value="csv" id="csv" />
                  <Label htmlFor="csv" className="flex-1 cursor-pointer">
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      <div>
                        <div className="font-medium">CSV</div>
                        <div className="text-xs text-muted-foreground">
                          Comma Separated Values
                        </div>
                      </div>
                    </div>
                  </Label>
                </div>
                <div className="flex items-center space-x-2 border rounded-lg p-4 cursor-pointer hover:bg-muted/50">
                  <RadioGroupItem value="xlsx" id="xlsx" />
                  <Label htmlFor="xlsx" className="flex-1 cursor-pointer">
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      <div>
                        <div className="font-medium">XLSX</div>
                        <div className="text-xs text-muted-foreground">
                          Microsoft Excel
                        </div>
                      </div>
                    </div>
                  </Label>
                </div>
              </RadioGroup>
            </CardContent>
          </Card>

          {/* Export Type */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Data yang Diekspor</CardTitle>
              <CardDescription>
                Pilih data mana yang akan diekspor
              </CardDescription>
            </CardHeader>
            <CardContent>
              <RadioGroup
                value={exportType}
                onValueChange={(value: 'all' | 'filtered') => setExportType(value)}
                className="space-y-3"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="all" id="all" />
                  <Label htmlFor="all" className="cursor-pointer">
                    Semua Data Produk
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="filtered" id="filtered" />
                  <Label htmlFor="filtered" className="cursor-pointer">
                    Data dengan Filter
                  </Label>
                </div>
              </RadioGroup>
            </CardContent>
          </Card>

          {/* Filters (only show when filtered export is selected) */}
          {exportType === 'filtered' && (
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Filter Data</CardTitle>
                <CardDescription>
                  Atur filter untuk data yang akan diekspor
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Product Type Filter */}
                  <div className="space-y-2">
                    <Label>Jenis Produk</Label>
                    <Select
                      value={filters.type}
                      onValueChange={(value) => setFilters(prev => ({ ...prev, type: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Semua jenis" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Semua jenis</SelectItem>
                        {PRODUCT_TYPE_OPTIONS.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Category Filter */}
                  <div className="space-y-2">
                    <Label>Kategori</Label>
                    <Select
                      value={filters.category}
                      onValueChange={(value) => setFilters(prev => ({ ...prev, category: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Semua kategori" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Semua kategori</SelectItem>
                        {PRODUCT_CATEGORY_OPTIONS.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Medicine Classification Filter */}
                  <div className="space-y-2">
                    <Label>Klasifikasi Obat</Label>
                    <Select
                      value={filters.medicineClassification}
                      onValueChange={(value) => setFilters(prev => ({ ...prev, medicineClassification: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Semua klasifikasi" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Semua klasifikasi</SelectItem>
                        {MEDICINE_CLASSIFICATION_OPTIONS.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Status Filter */}
                  <div className="space-y-2">
                    <Label>Status</Label>
                    <Select
                      value={filters.isActive}
                      onValueChange={(value) => setFilters(prev => ({ ...prev, isActive: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Semua status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Semua status</SelectItem>
                        <SelectItem value="true">Aktif</SelectItem>
                        <SelectItem value="false">Tidak Aktif</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end gap-3">
            <Button variant="outline" onClick={handleClose} disabled={isExporting}>
              Batal
            </Button>
            <Button onClick={handleExport} disabled={isExporting}>
              {isExporting ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Mengekspor...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  Export {format.toUpperCase()}
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
