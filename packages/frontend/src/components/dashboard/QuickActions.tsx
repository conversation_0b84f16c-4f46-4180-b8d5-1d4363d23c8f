'use client';

import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { QuickAction } from '@/types/Dashboard';
import * as Icons from 'lucide-react';

interface QuickActionsProps {
  actions: QuickAction[];
}

export function QuickActions({ actions }: QuickActionsProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Aksi <PERSON></CardTitle>
        <CardDescription>
          Akses cepat ke fitur yang sering digunakan
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid gap-3">
          {actions.map((action) => {
            const IconComponent = Icons[action.icon as keyof typeof Icons] as React.ComponentType<{ className?: string }>;
            
            return (
              <Link key={action.id} href={action.href}>
                <Button
                  variant={action.variant as any}
                  className="w-full justify-start h-auto p-4"
                >
                  <div className="flex items-center space-x-3">
                    {IconComponent && (
                      <IconComponent className="h-5 w-5 flex-shrink-0" />
                    )}
                    <div className="text-left">
                      <div className="font-medium">{action.title}</div>
                      <div className="text-sm text-muted-foreground">
                        {action.description}
                      </div>
                    </div>
                  </div>
                </Button>
              </Link>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}
