'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { formatCurrency, formatTime } from '@/lib/utils';
import { RecentActivity as ActivityType } from '@/types/Dashboard';
import {
  ShoppingCart,
  Package,
  FileText,
  User,
} from 'lucide-react';

interface RecentActivityProps {
  activities: ActivityType[];
}

const activityIcons = {
  sale: ShoppingCart,
  stock: Package,
  order: FileText,
  customer: User,
};

const activityColors = {
  sale: 'text-green-600',
  stock: 'text-orange-600',
  order: 'text-blue-600',
  customer: 'text-purple-600',
};

export function RecentActivity({ activities }: RecentActivityProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Aktivitas Terbaru</CardTitle>
        <CardDescription>
          Aktivitas terbaru di apotek Anda
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-80">
          <div className="space-y-4">
            {activities.map((activity) => {
              const IconComponent = activityIcons[activity.type];
              const iconColor = activityColors[activity.type];
              
              return (
                <div key={activity.id} className="flex items-start space-x-3">
                  <div className={`flex-shrink-0 ${iconColor}`}>
                    <IconComponent className="h-5 w-5" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium text-foreground">
                        {activity.title}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {formatTime(activity.timestamp)}
                      </p>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {activity.description}
                    </p>
                    {activity.amount && (
                      <Badge variant="secondary" className="mt-1">
                        {formatCurrency(activity.amount)}
                      </Badge>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
