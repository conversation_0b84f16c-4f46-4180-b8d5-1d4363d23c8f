'use client';

import { Card, CardContent } from '@/components/ui/card';
import { getGreeting, formatDate } from '@/lib/utils';

interface WelcomeCardProps {
  userName: string;
}

export function WelcomeCard({ userName }: WelcomeCardProps) {
  const currentDate = new Date();
  const greeting = getGreeting();

  return (
    <Card className="bg-gradient-to-r from-primary to-primary/80 text-primary-foreground">
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">
              {greeting}, {userName}!
            </h1>
            <p className="text-primary-foreground/80 mt-1">
              Selamat datang kembali di dashboard apotek Anda
            </p>
          </div>
          <div className="text-right">
            <p className="text-sm text-primary-foreground/80">
              Hari ini
            </p>
            <p className="text-lg font-semibold">
              {formatDate(currentDate)}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
