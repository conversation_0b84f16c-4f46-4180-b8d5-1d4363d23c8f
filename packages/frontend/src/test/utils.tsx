import React, { ReactElement } from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ThemeProvider } from '@/components/theme-provider'
import { vi } from 'vitest'

// Create a custom render function that includes providers
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  // Create a new QueryClient for each test to avoid state leakage
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: 0,
      },
      mutations: {
        retry: false,
      },
    },
  })

  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider
        attribute="class"
        defaultTheme="system"
        enableSystem
        disableTransitionOnChange
      >
        {children}
      </ThemeProvider>
    </QueryClientProvider>
  )
}

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options })

export * from '@testing-library/react'
export { customRender as render }

// Mock data factories
export const createMockProduct = (overrides: Record<string, unknown> = {}) => ({
  id: 'product-1',
  code: 'PRD-001',
  name: 'Test Product',
  description: 'Test product description',
  category: 'Medicine',
  manufacturer: 'Test Manufacturer',
  isActive: true,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  ...overrides,
})

export const createMockCustomer = (overrides: Record<string, unknown> = {}) => ({
  id: 'customer-1',
  code: 'CUST-001',
  fullName: 'Test Customer',
  phoneNumber: '081234567890',
  email: '<EMAIL>',
  address: 'Test Address',
  type: 'REGISTERED' as const,
  membershipLevel: 'BRONZE' as const,
  isActive: true,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  ...overrides,
})

export const createMockInventoryItem = (overrides: Record<string, unknown> = {}) => ({
  id: 'inventory-1',
  productId: 'product-1',
  unitId: 'unit-1',
  quantityOnHand: 100,
  quantityAllocated: 10,
  availableStock: 90,
  minimumStock: 20,
  maximumStock: 500,
  reorderPoint: 30,
  lastRestockDate: new Date().toISOString(),
  expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
  batchNumber: 'BATCH-001',
  costPrice: 5000,
  sellingPrice: 7500,
  isActive: true,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  ...overrides,
})

// API mock helpers
export const createMockApiResponse = <T,>(data: T, meta: Record<string, unknown> = {}) => ({
  data,
  meta: {
    total: Array.isArray(data) ? data.length : 1,
    page: 1,
    limit: 10,
    totalPages: 1,
    ...meta,
  },
})

// Query client test utilities - creates fresh instance to prevent state leakage
export const createTestQueryClient = () => {
  const client = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: 0,
        staleTime: 0,
      },
      mutations: {
        retry: false,
      },
    },
  })

  // Clear any existing cache to prevent state leakage
  client.clear()

  return client
}

// Mock API client
export const createMockApiClient = () => ({
  get: vi.fn(),
  post: vi.fn(),
  put: vi.fn(),
  patch: vi.fn(),
  delete: vi.fn(),
})

// Wait for async operations
export const waitForLoadingToFinish = () =>
  new Promise(resolve => setTimeout(resolve, 0))

// Cleanup utilities to prevent state leakage
export const cleanupTestState = () => {
  // Clear localStorage
  if (typeof window !== 'undefined') {
    window.localStorage.clear()
    window.sessionStorage.clear()
  }

  // Clear all mocks
  vi.clearAllMocks()
}

// Reset all test state between tests
export const resetTestEnvironment = () => {
  cleanupTestState()

  // Reset any global state if needed
  if (typeof window !== 'undefined') {
    // Reset any global variables or state
    delete (window as any).__TEST_STATE__
  }
}

// Mock toast notifications
export const mockToast = {
  success: vi.fn(),
  error: vi.fn(),
  info: vi.fn(),
  warning: vi.fn(),
  loading: vi.fn(),
  dismiss: vi.fn(),
}

vi.mock('sonner', () => ({
  toast: mockToast,
}))

// Form testing utilities
export const fillForm = async (form: HTMLFormElement, data: Record<string, string>) => {
  const { fireEvent } = await import('@testing-library/react')
  
  Object.entries(data).forEach(([name, value]) => {
    const input = form.querySelector(`[name="${name}"]`) as HTMLInputElement
    if (input) {
      fireEvent.change(input, { target: { value } })
    }
  })
}

export const submitForm = async (form: HTMLFormElement) => {
  const { fireEvent } = await import('@testing-library/react')
  fireEvent.submit(form)
}
