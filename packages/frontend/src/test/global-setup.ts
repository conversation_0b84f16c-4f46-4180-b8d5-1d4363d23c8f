/**
 * Global setup for Vitest tests
 * Prevents state leakage between test runs
 */

export default function globalSetup() {
  // Setup function runs once before all tests
  return () => {
    // Teardown function runs once after all tests
    // Clean up any global state to prevent leakage
    if (typeof global !== 'undefined') {
      // Clear any global test state
      delete (global as any).__TEST_STATE__
      delete (global as any).__MOCK_STATE__
    }
    
    // Clear any environment variables that might leak
    if (typeof process !== 'undefined' && process.env) {
      Object.keys(process.env).forEach(key => {
        if (key.startsWith('TEST_') || key.startsWith('VITEST_')) {
          delete process.env[key]
        }
      })
    }
  }
}
