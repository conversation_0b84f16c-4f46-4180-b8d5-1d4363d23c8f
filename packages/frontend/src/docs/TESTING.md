# Testing Guide - Pharmacy Store Frontend

Panduan lengkap untuk testing pada aplikasi frontend pharmacy store menggunakan Vitest dan Cypress.

## Overview

Aplikasi ini menggunakan dua framework testing utama:
- **Vitest**: Unit testing untuk komponen React, hooks, dan utility functions
- **Cypress**: E2E testing dan component testing untuk workflow lengkap

## Setup dan Instalasi

### Dependencies
```bash
# Unit testing dengan Vitest
bun add -D vitest @vitest/ui @vitest/coverage-v8 jsdom @testing-library/jest-dom @testing-library/user-event happy-dom

# E2E testing dengan Cypress
bun add -D cypress@^13.6.3
```

### Konfigurasi
- `vitest.config.ts` - Konfigurasi Vitest
- `cypress.config.ts` - Konfigurasi Cypress
- `src/test/setup.ts` - Setup file untuk Vitest
- `cypress/support/` - Support files untuk Cypress

## Struktur File Testing

```
packages/frontend/
├── src/
│   ├── test/
│   │   ├── setup.ts              # Vitest setup
│   │   └── utils.tsx             # Testing utilities
│   ├── components/
│   │   └── ui/
│   │       ├── __tests__/        # Unit tests
│   │       │   └── button.test.tsx
│   │       └── button.cy.tsx     # Component tests
│   ├── hooks/
│   │   └── __tests__/            # Hook tests
│   │       ├── useProducts.test.ts
│   │       └── useCustomers.test.ts
│   └── lib/
│       └── utils/
│           └── __tests__/        # Utility tests
│               └── debounce.test.ts
├── cypress/
│   ├── e2e/                      # E2E tests
│   │   ├── product-management.cy.ts
│   │   ├── customer-management.cy.ts
│   │   └── procurement-workflow.cy.ts
│   ├── fixtures/                 # Test data
│   │   ├── auth/
│   │   ├── products/
│   │   └── customers/
│   └── support/                  # Support files
│       ├── commands.ts
│       ├── e2e.ts
│       └── component.ts
└── vitest.config.ts
```

## Naming Conventions

### File Naming
- **Unit tests**: `*.test.ts` atau `*.test.tsx`
- **Component tests**: `*.cy.tsx`
- **E2E tests**: `*.cy.ts`
- **Test utilities**: `*.utils.ts`
- **Fixtures**: `*.json`

### Test Descriptions
- Gunakan bahasa Indonesia untuk deskripsi test yang user-facing
- Gunakan bahasa Inggris untuk deskripsi teknis
- Format: `describe('Nama Komponen/Fitur', () => {})`

## Scripts Testing

### Vitest (Unit Testing)
```bash
# Run all unit tests
bun test

# Run tests in watch mode
bun test:watch

# Run tests with UI
bun test:ui

# Run tests once (CI mode)
bun test:run

# Generate coverage report
bun test:coverage

# Type checking
bun type-check
```

### Cypress (E2E & Component Testing)
```bash
# Open Cypress UI for E2E tests
bun test:e2e:open

# Run E2E tests in headless mode
bun test:e2e

# Open Cypress UI for component tests
bun test:component:open

# Run component tests in headless mode
bun test:component
```

## Testing Patterns

### Unit Testing dengan Vitest

#### Testing Hooks
```typescript
import { renderHook, waitFor } from '@testing-library/react'
import { useProducts } from '../useProducts'

describe('useProducts Hook', () => {
  it('should fetch products successfully', async () => {
    const { result } = renderHook(() => useProducts({ page: 1 }), { wrapper })
    
    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true)
    })
  })
})
```

#### Testing Components
```typescript
import { render, screen, fireEvent } from '@/test/utils'
import { Button } from '../button'

describe('Button Component', () => {
  it('should handle click events', () => {
    const handleClick = vi.fn()
    render(<Button onClick={handleClick}>Simpan</Button>)
    
    fireEvent.click(screen.getByRole('button'))
    expect(handleClick).toHaveBeenCalledTimes(1)
  })
})
```

### E2E Testing dengan Cypress

#### Page Navigation
```typescript
describe('Manajemen Produk', () => {
  beforeEach(() => {
    cy.loginAsAdmin()
    cy.visit('/dashboard/products')
    cy.waitForLoadingToFinish()
  })
})
```

#### Form Testing
```typescript
it('should create new product', () => {
  cy.get('[data-testid="add-product-button"]').click()
  cy.fillForm({
    code: 'PRD-001',
    name: 'Test Product',
  })
  cy.get('button[type="submit"]').click()
  cy.checkToast('Produk berhasil ditambahkan', 'success')
})
```

## Test Data Management

### Fixtures
Gunakan fixtures untuk data testing yang konsisten:
```json
// cypress/fixtures/products/list.json
{
  "data": [
    {
      "id": "1",
      "code": "PRD-001",
      "name": "Paracetamol 500mg"
    }
  ]
}
```

### Mock Data Factories
```typescript
export const createMockProduct = (overrides = {}) => ({
  id: 'product-1',
  code: 'PRD-001',
  name: 'Test Product',
  ...overrides,
})
```

## Custom Commands

### Cypress Commands
```typescript
// Authentication
cy.loginAsAdmin()
cy.logout()

// UI Interactions
cy.fillForm({ name: 'value' })
cy.checkToast('message', 'success')
cy.waitForLoadingToFinish()

// API Mocking
cy.setupApiInterceptors()
cy.mockApiResponse('GET', '/api/products', mockData)
```

## Coverage Requirements

### Minimum Coverage Thresholds
- **Branches**: 70%
- **Functions**: 70%
- **Lines**: 70%
- **Statements**: 70%

### Coverage Exclusions
- Configuration files
- Test files
- Type definitions
- Build artifacts

## Best Practices

### Unit Testing
1. Test behavior, not implementation
2. Use descriptive test names in Indonesian for user features
3. Mock external dependencies
4. Test error scenarios
5. Use proper cleanup in `afterEach`

### E2E Testing
1. Test complete user workflows
2. Use data-testid attributes for reliable selectors
3. Clean up test data after each test
4. Use proper wait strategies
5. Test responsive design

### Component Testing
1. Test component in isolation
2. Test all props and variants
3. Test user interactions
4. Test accessibility features
5. Use proper providers for context

## Debugging

### Vitest Debugging
```bash
# Run specific test file
bun test src/components/ui/__tests__/button.test.tsx

# Run tests with verbose output
bun test --reporter=verbose

# Debug with browser
bun test --ui
```

### Cypress Debugging
```bash
# Open Cypress with debugging
bun cypress:open

# Run specific test file
bun cypress run --spec "cypress/e2e/product-management.cy.ts"

# Debug mode
bun cypress open --config video=false
```

## CI/CD Integration

### GitHub Actions Example
```yaml
- name: Run Unit Tests
  run: bun test:run

- name: Run E2E Tests
  run: bun test:e2e
  env:
    CYPRESS_baseUrl: http://localhost:3000
```

## Troubleshooting

### Common Issues
1. **Timeout errors**: Increase timeout in test configuration
2. **Flaky tests**: Add proper wait conditions
3. **Memory issues**: Use `--max-old-space-size` flag
4. **Mock issues**: Ensure proper cleanup between tests

### Performance Tips
1. Use `cy.intercept()` untuk mock API calls
2. Gunakan `data-testid` untuk selector yang stabil
3. Avoid unnecessary `cy.wait()` calls
4. Use proper test isolation
