/**
 * CustomerSelector Usage Examples and Documentation
 * 
 * This file demonstrates various ways to use the enhanced CustomerSelector component
 * with different configurations and features.
 */

'use client';

import * as React from 'react';
import { CustomerSelector } from '@/components/ui/customer-selector';
import { CustomerType, Customer } from '@/types/customer';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';

export function CustomerSelectorExamples() {
  const [basicValue, setBasicValue] = React.useState<string | undefined>();
  const [withCreationValue, setWithCreationValue] = React.useState<string | undefined>();
  const [customHandlerValue, setCustomHandlerValue] = React.useState<string | undefined>();
  const [walkInDisabledValue, setWalkInDisabledValue] = React.useState<string | undefined>();

  // Custom handler for external customer creation
  const handleExternalCustomerCreation = () => {
    toast.info('Opening external customer creation dialog...');
    // Here you would typically open your own customer creation modal/dialog
    // For example: setShowCustomerModal(true);
  };

  // Handler for when a customer is created via built-in dialog
  const handleCustomerCreated = (customer: Customer) => {
    toast.success(`Customer ${customer.fullName} created and selected!`);
    console.log('New customer created:', customer);
  };

  return (
    <div className="space-y-8 p-6">
      <div>
        <h1 className="text-3xl font-bold mb-2">CustomerSelector Examples</h1>
        <p className="text-muted-foreground">
          Comprehensive examples showing different configurations of the CustomerSelector component.
        </p>
      </div>

      {/* Basic Usage */}
      <Card>
        <CardHeader>
          <CardTitle>1. Basic Usage</CardTitle>
          <CardDescription>
            Simple customer selector with default settings. Includes walk-in option and search functionality.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <CustomerSelector
            value={basicValue}
            onValueChange={setBasicValue}
            placeholder="Select a customer..."
          />
          <p className="text-sm text-muted-foreground">
            Selected: {basicValue || 'None'}
          </p>
        </CardContent>
      </Card>

      {/* With Built-in Customer Creation */}
      <Card>
        <CardHeader>
          <CardTitle>2. With Built-in Customer Creation</CardTitle>
          <CardDescription>
            Enables the built-in customer creation dialog. New customers are automatically selected after creation.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <CustomerSelector
            value={withCreationValue}
            onValueChange={setWithCreationValue}
            placeholder="Select or create a customer..."
            enableCustomerCreation={true}
            onCustomerCreated={handleCustomerCreated}
            defaultCustomerType={CustomerType.REGISTERED}
          />
          <p className="text-sm text-muted-foreground">
            Selected: {withCreationValue || 'None'}
          </p>
        </CardContent>
      </Card>

      {/* With Custom Creation Handler */}
      <Card>
        <CardHeader>
          <CardTitle>3. With Custom Creation Handler</CardTitle>
          <CardDescription>
            Uses a custom handler for customer creation instead of the built-in dialog.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <CustomerSelector
            value={customHandlerValue}
            onValueChange={setCustomHandlerValue}
            placeholder="Select a customer..."
            onCreateNew={handleExternalCustomerCreation}
          />
          <p className="text-sm text-muted-foreground">
            Selected: {customHandlerValue || 'None'}
          </p>
        </CardContent>
      </Card>

      {/* Walk-in Disabled */}
      <Card>
        <CardHeader>
          <CardTitle>4. Walk-in Disabled</CardTitle>
          <CardDescription>
            Only allows selection of registered customers. Walk-in option is disabled.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <CustomerSelector
            value={walkInDisabledValue}
            onValueChange={setWalkInDisabledValue}
            placeholder="Select a registered customer..."
            allowWalkIn={false}
            enableCustomerCreation={true}
            defaultCustomerType={CustomerType.REGISTERED}
          />
          <p className="text-sm text-muted-foreground">
            Selected: {walkInDisabledValue || 'None'}
          </p>
        </CardContent>
      </Card>

      {/* Reset All Button */}
      <Card>
        <CardHeader>
          <CardTitle>Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <Button
            onClick={() => {
              setBasicValue(undefined);
              setWithCreationValue(undefined);
              setCustomHandlerValue(undefined);
              setWalkInDisabledValue(undefined);
              toast.info('All selections cleared');
            }}
            variant="outline"
          >
            Reset All Selections
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}

/**
 * USAGE DOCUMENTATION
 *
 * ## Basic Props
 *
 * - `value`: Current selected customer ID
 * - `onValueChange`: Callback when selection changes
 * - `placeholder`: Placeholder text when no customer is selected
 * - `disabled`: Disable the entire component
 * - `className`: Additional CSS classes
 *
 * ## Search & Display Props
 *
 * - `searchPlaceholder`: Placeholder for the search input
 * - `emptyMessage`: Message shown when no customers are found
 *
 * ## Walk-in Customer Props
 *
 * - `allowWalkIn`: Enable/disable walk-in customer option (default: true)
 * - `walkInLabel`: Label for walk-in option (default: "Pelanggan Umum")
 *
 * ## Customer Creation Props
 *
 * ### Option 1: Built-in Creation Dialog
 * ```tsx
 * <CustomerSelector
 *   enableCustomerCreation={true}
 *   onCustomerCreated={(customer) => console.log('New customer:', customer)}
 *   defaultCustomerType={CustomerType.REGISTERED}
 * />
 * ```
 *
 * ### Option 2: Custom Creation Handler
 * ```tsx
 * <CustomerSelector
 *   onCreateNew={() => openYourCustomDialog()}
 * />
 * ```
 *
 * ## Loading States
 *
 * The component automatically handles loading states:
 * - Shows skeleton loader when fetching individual customer details
 * - Disables interaction during loading
 * - Prevents visual "flipping" during data fetching
 *
 * ## Error Handling
 *
 * - Network errors are handled gracefully
 * - Error states are displayed in the trigger button
 * - Component remains functional even with partial failures
 *
 * ## Integration with Forms
 *
 * ```tsx
 * // React Hook Form integration
 * <FormField
 *   control={form.control}
 *   name="customerId"
 *   render={({ field }) => (
 *     <CustomerSelector
 *       value={field.value}
 *       onValueChange={field.onChange}
 *       enableCustomerCreation={true}
 *       onCustomerCreated={(customer) => {
 *         // Customer is auto-selected, but you can add additional logic here
 *         console.log('Customer created:', customer);
 *       }}
 *     />
 *   )}
 * />
 * ```
 *
 * ## Best Practices
 *
 * 1. **Use built-in creation for simple forms**: The built-in dialog covers most use cases
 * 2. **Use custom handler for complex workflows**: When you need custom validation or multi-step creation
 * 3. **Handle customer creation callbacks**: Always provide feedback when customers are created
 * 4. **Consider default customer type**: Set appropriate default for your use case
 * 5. **Test loading states**: Ensure your UI handles loading gracefully
 */
