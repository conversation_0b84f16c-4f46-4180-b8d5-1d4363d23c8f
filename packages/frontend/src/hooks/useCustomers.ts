import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query';
import {
  CustomerQueryParams,
  CreateCustomerDto,
  UpdateCustomerDto,
} from '@/types/customer';
import { customersApi } from '@/lib/api/customers';
import { toast } from 'sonner';

// Query key factory
export const customerKeys = {
  all: ['customers'] as const,
  lists: () => [...customerKeys.all, 'list'] as const,
  list: (params: CustomerQueryParams) => [...customerKeys.lists(), params] as const,
  details: () => [...customerKeys.all, 'detail'] as const,
  detail: (id: string) => [...customerKeys.details(), id] as const,
  stats: () => [...customerKeys.all, 'stats'] as const,
  search: (query: string) => [...customerKeys.all, 'search', query] as const,
};

// Query Hooks

export function useCustomers(params: CustomerQueryParams) {
  return useQuery({
    queryKey: customerKeys.list(params),
    queryFn: () => customersApi.getCustomers(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
    placeholderData: (previousData) => previousData, // Keep previous data while loading
  });
}

export function useCustomer(id: string) {
  return useQuery({
    queryKey: customerKeys.detail(id),
    queryFn: () => customersApi.getCustomer(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useCustomerStats() {
  return useQuery({
    queryKey: customerKeys.stats(),
    queryFn: () => customersApi.getCustomerStats(),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

export function useSearchCustomers(query: string, limit?: number) {
  return useQuery({
    queryKey: customerKeys.search(query),
    queryFn: () => customersApi.searchCustomers(query, limit),
    enabled: query.length >= 2, // Only search when query is at least 2 characters
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 2 * 60 * 1000, // 2 minutes
  });
}

// Mutation Hooks

export function useCreateCustomer() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateCustomerDto) => customersApi.createCustomer(data),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: customerKeys.lists() });
      queryClient.invalidateQueries({ queryKey: customerKeys.stats() });
      toast.success(`Pelanggan ${data.fullName} berhasil dibuat`);
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal membuat pelanggan';
      toast.error(message);
    },
  });
}

export function useUpdateCustomer() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateCustomerDto }) =>
      customersApi.updateCustomer(id, data),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: customerKeys.lists() });
      queryClient.invalidateQueries({ queryKey: customerKeys.detail(data.id) });
      queryClient.invalidateQueries({ queryKey: customerKeys.stats() });
      toast.success(`Pelanggan ${data.fullName} berhasil diperbarui`);
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal memperbarui pelanggan';
      toast.error(message);
    },
  });
}

export function useDeleteCustomer() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => customersApi.deleteCustomer(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: customerKeys.lists() });
      queryClient.invalidateQueries({ queryKey: customerKeys.stats() });
      toast.success('Pelanggan berhasil dihapus');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal menghapus pelanggan';
      toast.error(message);
    },
  });
}

export function useActivateCustomer() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => customersApi.activateCustomer(id),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: customerKeys.lists() });
      queryClient.invalidateQueries({ queryKey: customerKeys.detail(data.id) });
      queryClient.invalidateQueries({ queryKey: customerKeys.stats() });
      toast.success(`Pelanggan ${data.fullName} berhasil diaktifkan`);
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal mengaktifkan pelanggan';
      toast.error(message);
    },
  });
}

export function useDeactivateCustomer() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => customersApi.deactivateCustomer(id),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: customerKeys.lists() });
      queryClient.invalidateQueries({ queryKey: customerKeys.detail(data.id) });
      queryClient.invalidateQueries({ queryKey: customerKeys.stats() });
      toast.success(`Pelanggan ${data.fullName} berhasil dinonaktifkan`);
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal menonaktifkan pelanggan';
      toast.error(message);
    },
  });
}

// Code generation hooks
export function useGenerateCustomerCode(type: 'WALK_IN' | 'REGISTERED') {
  return useQuery({
    queryKey: ['customer-code', type],
    queryFn: () => customersApi.generateCustomerCode(type),
    enabled: false, // Only run when manually triggered
  });
}

export function useValidateCustomerCode(code: string) {
  return useQuery({
    queryKey: ['customer-code-validation', code],
    queryFn: () => customersApi.validateCustomerCode(code),
    enabled: !!code && code.length > 0,
    staleTime: 30 * 1000, // 30 seconds
  });
}

// Invalidation helper
export function useCustomersInvalidate() {
  const queryClient = useQueryClient();

  return () => {
    queryClient.invalidateQueries({ queryKey: customerKeys.lists() });
    queryClient.invalidateQueries({ queryKey: customerKeys.stats() });
  };
}
