import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { inventoryApi } from '@/lib/api/inventory';
import {
  InventoryQueryParams,
  UpdateInventoryItemData,
  StockAdjustmentData,
  StockMovementQueryParams,
  AllocationHistoryQueryParams,
} from '@/types/inventory';

// Query keys
export const inventoryKeys = {
  all: ['inventory'] as const,
  lists: () => [...inventoryKeys.all, 'list'] as const,
  list: (params: InventoryQueryParams) => [...inventoryKeys.lists(), params] as const,
  details: () => [...inventoryKeys.all, 'detail'] as const,
  detail: (id: string) => [...inventoryKeys.details(), id] as const,
  stats: () => [...inventoryKeys.all, 'stats'] as const,
  stockMovements: () => [...inventoryKeys.all, 'stock-movements'] as const,
  stockMovement: (id: string, params?: StockMovementQueryParams) =>
    [...inventoryKeys.stockMovements(), id, params] as const,
  allocationHistory: () => [...inventoryKeys.all, 'allocation-history'] as const,
  allocationHistoryList: (params?: AllocationHistoryQueryParams) =>
    [...inventoryKeys.allocationHistory(), params] as const,
  productAllocationSummary: (productId: string) =>
    [...inventoryKeys.all, 'product-allocation-summary', productId] as const,
  stock: () => [...inventoryKeys.all, 'stock'] as const,
  productStock: (productId: string) =>
    [...inventoryKeys.stock(), 'product', productId] as const,
  productUnitStock: (productId: string, unitId: string) =>
    [...inventoryKeys.productStock(productId), unitId] as const,
};

// Get inventory items with filtering and pagination
export function useInventoryItems(params?: InventoryQueryParams) {
  return useQuery({
    queryKey: inventoryKeys.list(params || {}),
    queryFn: () => inventoryApi.getInventoryItems(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
    placeholderData: (previousData) => previousData, // Keep previous data while loading
  });
}

// Get inventory statistics
export function useInventoryStats() {
  return useQuery({
    queryKey: inventoryKeys.stats(),
    queryFn: inventoryApi.getInventoryStats,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Get single inventory item
export function useInventoryItem(id: string) {
  return useQuery({
    queryKey: inventoryKeys.detail(id),
    queryFn: () => inventoryApi.getInventoryItem(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Activate inventory item
export function useActivateInventoryItem() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: inventoryApi.activateInventoryItem,
    onSuccess: () => {
      // Invalidate and refetch inventory queries
      queryClient.invalidateQueries({ queryKey: inventoryKeys.all });
      toast.success('Item inventori berhasil diaktifkan');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Gagal mengaktifkan item inventori');
    },
  });
}

// Deactivate inventory item
export function useDeactivateInventoryItem() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: inventoryApi.deactivateInventoryItem,
    onSuccess: () => {
      // Invalidate and refetch inventory queries
      queryClient.invalidateQueries({ queryKey: inventoryKeys.all });
      toast.success('Item inventori berhasil dinonaktifkan');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Gagal menonaktifkan item inventori');
    },
  });
}

// Hard delete inventory item
export function useHardDeleteInventoryItem() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: inventoryApi.hardDeleteInventoryItem,
    onSuccess: () => {
      // Invalidate and refetch inventory queries
      queryClient.invalidateQueries({ queryKey: inventoryKeys.all });
      toast.success('Item inventori berhasil dihapus permanen');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Gagal menghapus item inventori');
    },
  });
}

// Invalidation helper
export function useInventoryInvalidate() {
  const queryClient = useQueryClient();

  return {
    invalidateAll: () => queryClient.invalidateQueries({ queryKey: inventoryKeys.all }),
    invalidateList: () => queryClient.invalidateQueries({ queryKey: inventoryKeys.lists() }),
    invalidateStats: () => queryClient.invalidateQueries({ queryKey: inventoryKeys.stats() }),
    invalidateDetail: (id: string) => queryClient.invalidateQueries({ queryKey: inventoryKeys.detail(id) }),
    invalidateProductStock: (productId: string) => queryClient.invalidateQueries({ queryKey: inventoryKeys.productStock(productId) }),
    invalidateProductStocks: (productIds: string[]) => {
      productIds.forEach(productId => {
        queryClient.invalidateQueries({ queryKey: inventoryKeys.productStock(productId) });
      });
    },
  };
}

// Hook specifically for POS stock refresh after sales
export function usePosStockRefresh() {
  const queryClient = useQueryClient();

  return {
    // Refresh stock data for products that were sold
    refreshAfterSale: (productIds: string[]) => {
      // Invalidate inventory queries for affected products
      queryClient.invalidateQueries({ queryKey: inventoryKeys.all });

      // Invalidate specific product stock queries
      productIds.forEach(productId => {
        queryClient.invalidateQueries({ queryKey: inventoryKeys.productStock(productId) });
      });

      // Force refetch to ensure immediate UI updates
      productIds.forEach(productId => {
        queryClient.refetchQueries({ queryKey: inventoryKeys.productStock(productId) });
      });
    },

    // Refresh all stock data (for manual refresh)
    refreshAll: () => {
      queryClient.invalidateQueries({ queryKey: inventoryKeys.all });
      queryClient.refetchQueries({ queryKey: inventoryKeys.all });
    }
  };
}

// Create inventory item
export function useCreateInventoryItem() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: inventoryApi.createInventoryItem,
    onSuccess: (data) => {
      // Invalidate and refetch inventory lists
      queryClient.invalidateQueries({ queryKey: inventoryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: inventoryKeys.stats() });

      // Add the new item to cache
      queryClient.setQueryData(inventoryKeys.detail(data.id), data);

      toast.success('Item inventori berhasil ditambahkan');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal menambahkan item inventori';
      toast.error(message);
    },
  });
}

// Update inventory item
export function useUpdateInventoryItem() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateInventoryItemData }) =>
      inventoryApi.updateInventoryItem(id, data),
    onSuccess: (data) => {
      // Invalidate and refetch inventory lists
      queryClient.invalidateQueries({ queryKey: inventoryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: inventoryKeys.stats() });

      // Update the item in cache
      queryClient.setQueryData(inventoryKeys.detail(data.id), data);

      toast.success('Item inventori berhasil diperbarui');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal memperbarui item inventori';
      toast.error(message);
    },
  });
}

// Adjust stock levels
export function useAdjustStock() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: StockAdjustmentData }) =>
      inventoryApi.adjustStock(id, data),
    onSuccess: (updatedItem, { data: adjustmentData }) => {
      // Invalidate and refetch inventory lists
      queryClient.invalidateQueries({ queryKey: inventoryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: inventoryKeys.stats() });

      // Update the item in cache
      queryClient.setQueryData(inventoryKeys.detail(updatedItem.id), updatedItem);

      // Show success message based on adjustment type
      const actionText = adjustmentData.quantity > 0 ? 'ditambahkan' : 'dikurangi';
      const quantity = Math.abs(adjustmentData.quantity);
      toast.success(`Stok berhasil ${actionText} sebanyak ${quantity} unit`);
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal menyesuaikan stok';
      toast.error(message);
    },
  });
}

// Delete inventory item
export function useDeleteInventoryItem() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: inventoryApi.deleteInventoryItem,
    onSuccess: (_, deletedId) => {
      // Invalidate and refetch inventory lists
      queryClient.invalidateQueries({ queryKey: inventoryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: inventoryKeys.stats() });

      // Remove the item from cache
      queryClient.removeQueries({ queryKey: inventoryKeys.detail(deletedId) });

      toast.success('Item inventori berhasil dihapus');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal menghapus item inventori';
      toast.error(message);
    },
  });
}

// Get stock movements for an inventory item
export function useStockMovements(inventoryItemId: string, params?: StockMovementQueryParams) {
  return useQuery({
    queryKey: inventoryKeys.stockMovement(inventoryItemId, params),
    queryFn: () => inventoryApi.getStockMovements(inventoryItemId, params),
    enabled: !!inventoryItemId,
  });
}

// Allocation tracking hooks
export function useAllocationHistory(params?: AllocationHistoryQueryParams) {
  return useQuery({
    queryKey: inventoryKeys.allocationHistoryList(params),
    queryFn: () => inventoryApi.getAllocationHistory(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useProductAllocationSummary(productId: string) {
  return useQuery({
    queryKey: inventoryKeys.productAllocationSummary(productId),
    queryFn: () => inventoryApi.getProductAllocationSummary(productId),
    enabled: !!productId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook to get stock for all units of a product
export function useProductStock(productId: string) {
  return useQuery({
    queryKey: inventoryKeys.productStock(productId),
    queryFn: () => inventoryApi.getProductStockAllUnits(productId),
    enabled: !!productId,
    staleTime: 30 * 1000, // 30 seconds
  });
}

// Hook to get stock for a specific unit of a product
export function useProductUnitStock(productId: string, unitId: string) {
  return useQuery({
    queryKey: inventoryKeys.productUnitStock(productId, unitId),
    queryFn: () => inventoryApi.getProductStockByUnit(productId, unitId),
    enabled: !!productId && !!unitId,
    staleTime: 30 * 1000, // 30 seconds
  });
}

// Invalidate inventory queries (useful for manual refresh) - keeping the enhanced version
