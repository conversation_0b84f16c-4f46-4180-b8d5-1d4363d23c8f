import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import {
  stockAllocationApi,
  StockAvailabilityData,
} from '@/lib/api/stock-allocation';
import {
  StockAllocationData,
  AllocationMethod,
  AllocationReportRequest,
} from '@/types/stock-allocation';

// Query keys for stock allocation
export const stockAllocationKeys = {
  all: ['stock-allocation'] as const,
  stockSummary: (productId: string) => [...stockAllocationKeys.all, 'stock-summary', productId] as const,
  availableStock: (productId: string, includeExpired: boolean) => 
    [...stockAllocationKeys.all, 'available-stock', productId, includeExpired] as const,
  stockAvailability: (data: StockAvailabilityData) => 
    [...stockAllocationKeys.all, 'stock-availability', data] as const,
};

// Get stock summary for a product
export function useStockSummary(productId: string) {
  return useQuery({
    queryKey: stockAllocationKeys.stockSummary(productId),
    queryFn: () => stockAllocationApi.getStockSummary(productId),
    enabled: !!productId,
  });
}

// Get available stock for a product
export function useAvailableStock(productId: string, includeExpired = false) {
  return useQuery({
    queryKey: stockAllocationKeys.availableStock(productId, includeExpired),
    queryFn: () => stockAllocationApi.getAvailableStock(productId, includeExpired),
    enabled: !!productId,
  });
}

// Validate stock availability
export function useValidateStockAvailability() {
  return useMutation({
    mutationFn: (data: StockAvailabilityData) =>
      stockAllocationApi.validateStockAvailability(data),
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal memvalidasi ketersediaan stok';
      toast.error(message);
    },
  });
}

// Preview allocation
export function usePreviewAllocation() {
  return useMutation({
    mutationFn: (data: StockAllocationData) =>
      stockAllocationApi.previewAllocation(data),
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal melakukan preview alokasi';
      toast.error(message);
    },
  });
}

// Execute stock allocation
export function useAllocateStock() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: StockAllocationData) =>
      stockAllocationApi.allocateStock(data),
    onSuccess: (result, variables) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['inventory'] });
      queryClient.invalidateQueries({ queryKey: stockAllocationKeys.all });
      
      // Show success message
      const methodText = variables.method === AllocationMethod.FIFO ? 'FIFO' : 'FEFO';
      if (result.success) {
        toast.success(
          `Alokasi stok berhasil menggunakan metode ${methodText}. ` +
          `Dialokasikan: ${result.allocatedQuantity} unit`
        );
      } else {
        toast.warning(
          `Alokasi stok sebagian berhasil menggunakan metode ${methodText}. ` +
          `Dialokasikan: ${result.allocatedQuantity} dari ${result.requestedQuantity} unit`
        );
      }
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal melakukan alokasi stok';
      toast.error(message);
    },
  });
}

// Consume stock (alternative allocation method)
export function useConsumeStock() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: {
      productId: string;
      requestedQuantity: number;
      method: 'FIFO' | 'FEFO';
      userId?: string;
      reason?: string;
      notes?: string;
      allowPartialAllocation?: boolean;
      nearExpiryWarningDays?: number;
    }) => stockAllocationApi.consumeStock(data),
    onSuccess: (result, variables) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['inventory'] });
      queryClient.invalidateQueries({ queryKey: stockAllocationKeys.all });
      
      // Show success message
      const methodText = variables.method;
      if (result.success) {
        toast.success(
          `Konsumsi stok berhasil menggunakan metode ${methodText}. ` +
          `Dikonsumsi: ${result.allocatedQuantity} unit`
        );
      } else {
        toast.warning(
          `Konsumsi stok sebagian berhasil menggunakan metode ${methodText}. ` +
          `Dikonsumsi: ${result.allocatedQuantity} dari ${result.requestedQuantity} unit`
        );
      }
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal melakukan konsumsi stok';
      toast.error(message);
    },
  });
}

// Preview stock consumption
export function usePreviewStockConsumption() {
  return useMutation({
    mutationFn: (data: {
      productId: string;
      requestedQuantity: number;
      method: 'FIFO' | 'FEFO';
    }) => stockAllocationApi.previewStockConsumption(data),
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal melakukan preview konsumsi stok';
      toast.error(message);
    },
  });
}

// Generate allocation report
export function useGenerateAllocationReport() {
  return useMutation({
    mutationFn: (request: AllocationReportRequest) =>
      stockAllocationApi.generateReport(request),
    onSuccess: (response) => {
      toast.success('Laporan berhasil dibuat dan siap diunduh');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal membuat laporan alokasi';
      toast.error(message);
    },
  });
}
