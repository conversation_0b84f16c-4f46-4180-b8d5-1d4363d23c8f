import { useSession } from 'next-auth/react';

/**
 * Hook that provides authentication status and session data.
 * Token management is now handled globally by AuthTokenManager in providers.tsx
 */
export const useAuthenticatedApi = () => {
  const { data: session, status } = useSession();

  return {
    session,
    status,
    isAuthenticated: status === 'authenticated',
    isLoading: status === 'loading',
  };
};
