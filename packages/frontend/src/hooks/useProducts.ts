import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { productsApi } from '@/lib/api/products';
import {
  Product,
  CreateProductDto,
  UpdateProductDto,
  ProductQueryParams,
  ProductListResponse,
  ProductStats,
} from '@/types/product';

// Query key factory
export const productKeys = {
  all: ['products'] as const,
  lists: () => [...productKeys.all, 'list'] as const,
  list: (params: ProductQueryParams) => [...productKeys.lists(), params] as const,
  details: () => [...productKeys.all, 'detail'] as const,
  detail: (id: string) => [...productKeys.details(), id] as const,
  stats: () => [...productKeys.all, 'stats'] as const,
};

export function useProducts(params: ProductQueryParams) {
  return useQuery({
    queryKey: productKeys.list(params),
    queryFn: () => productsApi.getProducts(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
    placeholderData: (previousData) => previousData, // Keep previous data while loading
  });
}

export function useProduct(id: string) {
  return useQuery({
    queryKey: productKeys.detail(id),
    queryFn: () => productsApi.getProduct(id),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    enabled: !!id,
  });
}

export function useProductStats() {
  return useQuery({
    queryKey: productKeys.stats(),
    queryFn: () => productsApi.getStats(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Mutation hooks
export function useCreateProduct() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateProductDto) => productsApi.createProduct(data),
    onSuccess: (data) => {
      // Invalidate and refetch products list
      queryClient.invalidateQueries({ queryKey: productKeys.lists() });
      queryClient.invalidateQueries({ queryKey: productKeys.stats() });
      
      // Set the new product data in cache
      queryClient.setQueryData(productKeys.detail(data.id), data);
      
      toast.success('Produk berhasil dibuat');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal membuat produk';
      toast.error(message);
    },
  });
}

export function useUpdateProduct() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateProductDto }) =>
      productsApi.updateProduct(id, data),
    onSuccess: (data, variables) => {
      // Invalidate and refetch products list
      queryClient.invalidateQueries({ queryKey: productKeys.lists() });
      queryClient.invalidateQueries({ queryKey: productKeys.stats() });
      
      // Update the product data in cache
      queryClient.setQueryData(productKeys.detail(variables.id), data);
      
      toast.success('Produk berhasil diperbarui');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal memperbarui produk';
      toast.error(message);
    },
  });
}

export function useDeleteProduct() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => productsApi.deleteProduct(id),
    onSuccess: () => {
      // Invalidate and refetch products list
      queryClient.invalidateQueries({ queryKey: productKeys.lists() });
      queryClient.invalidateQueries({ queryKey: productKeys.stats() });
      
      toast.success('Produk berhasil dihapus');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal menghapus produk';
      toast.error(message);
    },
  });
}

export function useActivateProduct() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => productsApi.activateProduct(id),
    onSuccess: (data) => {
      // Invalidate and refetch products list
      queryClient.invalidateQueries({ queryKey: productKeys.lists() });
      queryClient.invalidateQueries({ queryKey: productKeys.stats() });
      
      // Update the product data in cache
      queryClient.setQueryData(productKeys.detail(data.id), data);
      
      toast.success('Produk berhasil diaktifkan');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal mengaktifkan produk';
      toast.error(message);
    },
  });
}

export function useDeactivateProduct() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => productsApi.deactivateProduct(id),
    onSuccess: (data) => {
      // Invalidate and refetch products list
      queryClient.invalidateQueries({ queryKey: productKeys.lists() });
      queryClient.invalidateQueries({ queryKey: productKeys.stats() });
      
      // Update the product data in cache
      queryClient.setQueryData(productKeys.detail(data.id), data);
      
      toast.success('Produk berhasil dinonaktifkan');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal menonaktifkan produk';
      toast.error(message);
    },
  });
}

export function useHardDeleteProduct() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => productsApi.hardDeleteProduct(id),
    onSuccess: (_, id) => {
      // Invalidate and refetch products list
      queryClient.invalidateQueries({ queryKey: productKeys.lists() });
      queryClient.invalidateQueries({ queryKey: productKeys.stats() });
      
      // Remove the product from cache
      queryClient.removeQueries({ queryKey: productKeys.detail(id) });
      
      toast.success('Produk berhasil dihapus permanen');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal menghapus produk permanen';
      toast.error(message);
    },
  });
}

// Invalidation hooks
export function useProductsInvalidate() {
  const queryClient = useQueryClient();

  return {
    invalidateList: () => {
      queryClient.invalidateQueries({ queryKey: productKeys.lists() });
    },
    invalidateStats: () => {
      queryClient.invalidateQueries({ queryKey: productKeys.stats() });
    },
    invalidateDetail: (id: string) => {
      queryClient.invalidateQueries({ queryKey: productKeys.detail(id) });
    },
    invalidateAll: () => {
      queryClient.invalidateQueries({ queryKey: productKeys.all });
    },
  };
}
