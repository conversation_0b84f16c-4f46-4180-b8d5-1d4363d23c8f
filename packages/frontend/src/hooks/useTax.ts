import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { taxApi } from '@/lib/api/tax';
import {
  TaxConfigurationResponse,
  TaxCalculationResult,
  TaxBreakdownResponse,
  AllTaxSettingsResponse,
  TaxStatusResponse,
  CalculateTaxRequest,
  CalculateMultipleItemsTaxRequest,
  UpdatePPNConfigurationRequest,
  SetTaxCalculationModeRequest,
  TaxCalculationMode,
  TaxEntityType,
} from '@/types/tax';

// Query Keys
export const taxKeys = {
  all: ['tax'] as const,
  ppnConfiguration: () => [...taxKeys.all, 'ppn', 'configuration'] as const,
  calculationMode: () => [...taxKeys.all, 'calculation-mode'] as const,
  allSettings: () => [...taxKeys.all, 'settings'] as const,
  ppnStatus: () => [...taxKeys.all, 'ppn', 'status'] as const,
  calculation: (data: CalculateTaxRequest) => [...taxKeys.all, 'calculate', data] as const,
  breakdown: (data: CalculateTaxRequest) => [...taxKeys.all, 'breakdown', data] as const,
  applicableTaxes: (params: any) => [...taxKeys.all, 'applicable-taxes', params] as const,
  complianceStatus: (params: any) => [...taxKeys.all, 'compliance-status', params] as const,
  taxCalendar: () => [...taxKeys.all, 'calendar'] as const,
};

// Query Hooks

/**
 * Get current PPN configuration
 */
export function usePPNConfiguration() {
  return useQuery({
    queryKey: taxKeys.ppnConfiguration(),
    queryFn: () => taxApi.getPPNConfiguration(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

/**
 * Get current tax calculation mode
 */
export function useTaxCalculationMode() {
  return useQuery({
    queryKey: taxKeys.calculationMode(),
    queryFn: () => taxApi.getCalculationMode(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

/**
 * Get all tax settings
 */
export function useAllTaxSettings() {
  return useQuery({
    queryKey: taxKeys.allSettings(),
    queryFn: () => taxApi.getAllTaxSettings(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

/**
 * Get PPN status
 */
export function usePPNStatus() {
  return useQuery({
    queryKey: taxKeys.ppnStatus(),
    queryFn: () => taxApi.getPPNStatus(),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Calculate tax for a given amount
 * This hook is used for real-time tax calculations in forms
 */
export function useTaxCalculation(data: CalculateTaxRequest, enabled: boolean = true) {
  return useQuery({
    queryKey: taxKeys.calculation(data),
    queryFn: () => taxApi.calculateTax(data),
    enabled: enabled && data.subtotal > 0,
    staleTime: 30 * 1000, // 30 seconds for real-time calculations
    gcTime: 2 * 60 * 1000, // 2 minutes
  });
}

/**
 * Get tax breakdown for display purposes
 */
export function useTaxBreakdown(data: CalculateTaxRequest, enabled: boolean = true) {
  return useQuery({
    queryKey: taxKeys.breakdown(data),
    queryFn: () => taxApi.getTaxBreakdown(data),
    enabled: enabled && data.subtotal > 0,
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 2 * 60 * 1000, // 2 minutes
  });
}

/**
 * Get applicable taxes for entity
 */
export function useApplicableTaxes(params: {
  entityType: TaxEntityType;
  annualRevenue?: number;
  yearsInOperation?: number;
}, enabled: boolean = true) {
  return useQuery({
    queryKey: taxKeys.applicableTaxes(params),
    queryFn: () => taxApi.getApplicableTaxes(params),
    enabled,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  });
}

/**
 * Get tax compliance status
 */
export function useComplianceStatus(params: {
  entityType: TaxEntityType;
  annualRevenue?: number;
  yearsInOperation?: number;
  hasEmployees?: boolean;
}, enabled: boolean = true) {
  return useQuery({
    queryKey: taxKeys.complianceStatus(params),
    queryFn: () => taxApi.getComplianceStatus(params),
    enabled,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  });
}

/**
 * Get tax calendar
 */
export function useTaxCalendar() {
  return useQuery({
    queryKey: taxKeys.taxCalendar(),
    queryFn: () => taxApi.getTaxCalendar(),
    staleTime: 24 * 60 * 60 * 1000, // 24 hours
    gcTime: 7 * 24 * 60 * 60 * 1000, // 7 days
  });
}

// Mutation Hooks

/**
 * Update PPN configuration
 */
export function useUpdatePPNConfiguration() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: UpdatePPNConfigurationRequest) => taxApi.updatePPNConfiguration(data),
    onSuccess: () => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: taxKeys.ppnConfiguration() });
      queryClient.invalidateQueries({ queryKey: taxKeys.allSettings() });
      queryClient.invalidateQueries({ queryKey: taxKeys.ppnStatus() });
      // Clear calculation cache since tax rate might have changed
      queryClient.invalidateQueries({ queryKey: taxKeys.all });
    },
  });
}

/**
 * Set tax calculation mode
 */
export function useSetTaxCalculationMode() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: SetTaxCalculationModeRequest) => taxApi.setCalculationMode(data),
    onSuccess: () => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: taxKeys.calculationMode() });
      queryClient.invalidateQueries({ queryKey: taxKeys.allSettings() });
      // Clear calculation cache since calculation mode changed
      queryClient.invalidateQueries({ queryKey: taxKeys.all });
    },
  });
}

/**
 * Calculate multiple items tax (for complex purchase orders)
 */
export function useCalculateMultipleItemsTax() {
  return useMutation({
    mutationFn: (data: CalculateMultipleItemsTaxRequest) => taxApi.calculateMultipleItemsTax(data),
  });
}

/**
 * Validate tax configuration
 */
export function useValidateTaxConfiguration() {
  return useMutation({
    mutationFn: (data: {
      taxType: string;
      taxRate: number;
      entityType: TaxEntityType;
    }) => taxApi.validateTaxConfiguration(data),
  });
}
