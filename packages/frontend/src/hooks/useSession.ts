import { useQuery } from '@tanstack/react-query';
import { sessionApi } from '@/lib/api/session';

// Query key factory
export const sessionKeys = {
  all: ['session'] as const,
  sales: (cashierId: string, sessionDate: string) => [...sessionKeys.all, 'sales', cashierId, sessionDate] as const,
  stats: (cashierId: string, sessionDate: string) => [...sessionKeys.all, 'stats', cashierId, sessionDate] as const,
  sessionSales: (cashierId: string, sessionStartTime: string, sessionEndTime?: string) => [...sessionKeys.all, 'session-sales', cashierId, sessionStartTime, sessionEndTime] as const,
  sessionStats: (cashierId: string, sessionStartTime: string, sessionEndTime?: string) => [...sessionKeys.all, 'session-stats', cashierId, sessionStartTime, sessionEndTime] as const,
};

/**
 * Hook to get sales for a specific cashier's session (legacy - by date)
 */
export function useSessionSales(cashierId: string, sessionDate: string) {
  return useQuery({
    queryKey: sessionKeys.sales(cashierId, sessionDate),
    queryFn: () => sessionApi.getCashierSessionSales(cashierId, sessionDate),
    enabled: !!cashierId && !!sessionDate,
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 2 * 60 * 1000, // 2 minutes
  });
}

/**
 * Hook to get session statistics for a specific cashier (legacy - by date)
 */
export function useSessionStats(cashierId: string, sessionDate: string) {
  return useQuery({
    queryKey: sessionKeys.stats(cashierId, sessionDate),
    queryFn: () => sessionApi.getSessionStats(cashierId, sessionDate),
    enabled: !!cashierId && !!sessionDate,
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 2 * 60 * 1000, // 2 minutes
  });
}

/**
 * Hook to get sales for a specific session based on session start time
 */
export function useSessionSalesById(cashierId: string, sessionStartTime: string, sessionEndTime?: string) {
  return useQuery({
    queryKey: sessionKeys.sessionSales(cashierId, sessionStartTime, sessionEndTime),
    queryFn: () => sessionApi.getSessionSales(cashierId, sessionStartTime, sessionEndTime),
    enabled: !!cashierId && !!sessionStartTime && cashierId !== 'null' && sessionStartTime !== '',
    staleTime: 0, // Always fresh for real-time POS updates
    gcTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: true,
  });
}

/**
 * Hook to get session statistics based on session start time
 */
export function useSessionStatsById(cashierId: string, sessionStartTime: string, sessionEndTime?: string) {
  return useQuery({
    queryKey: sessionKeys.sessionStats(cashierId, sessionStartTime, sessionEndTime),
    queryFn: () => sessionApi.getSessionStatsById(cashierId, sessionStartTime, sessionEndTime),
    enabled: !!cashierId && !!sessionStartTime && cashierId !== 'null' && sessionStartTime !== '',
    staleTime: 0, // Always fresh for real-time POS updates
    gcTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: true,
  });
}
