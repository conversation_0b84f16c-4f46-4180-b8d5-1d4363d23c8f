import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { inventoryReportsApi } from '@/lib/api/inventory-reports';
import {
  InventoryReportRequest,
  InventoryReportResponse,
  ReportGenerationProgress,
  ReportFormat,
  ReportFilters,
} from '@/types/inventory-reports';
import { AllocationReportRequest } from '@/types/stock-allocation';

// Generate inventory report
export function useGenerateInventoryReport() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (request: InventoryReportRequest) =>
      inventoryReportsApi.generateInventoryReport(request),
    onSuccess: (data: InventoryReportResponse) => {
      toast.success(`Laporan ${inventoryReportsApi.formatReportTypeName(data.type as any)} berhasil dibuat`);

      // Invalidate any report-related queries
      queryClient.invalidateQueries({ queryKey: ['inventory-reports'] });
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal membuat laporan inventori';
      toast.error(message);
      console.error('Error generating inventory report:', error);
    },
  });
}

// Generate allocation report
export function useGenerateAllocationReport() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (request: AllocationReportRequest) =>
      inventoryReportsApi.generateAllocationReport(request),
    onSuccess: (data) => {
      toast.success('Laporan alokasi berhasil dibuat');

      // Invalidate any report-related queries
      queryClient.invalidateQueries({ queryKey: ['allocation-reports'] });
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal membuat laporan alokasi';
      toast.error(message);
      console.error('Error generating allocation report:', error);
    },
  });
}

// Download report
export function useDownloadReport() {
  return useMutation({
    mutationFn: async ({ fileName, reportName }: { fileName: string; reportName?: string }) => {
      const blob = await inventoryReportsApi.downloadReport(fileName);

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = reportName || fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      return { fileName, success: true };
    },
    onSuccess: ({ fileName }) => {
      toast.success(`File ${fileName} berhasil diunduh`);
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal mengunduh laporan';
      toast.error(message);
      console.error('Error downloading report:', error);
    },
  });
}

// Export current stock
export function useExportCurrentStock() {
  const downloadMutation = useDownloadReport();

  return useMutation({
    mutationFn: async (format: ReportFormat = ReportFormat.PDF) => {
      try {
        const reportResponse = await inventoryReportsApi.exportCurrentStock(format);

        // Automatically download the report using standardized method
        await downloadMutation.mutateAsync({
          fileName: reportResponse.fileName,
          reportName: `Laporan_Stok_Saat_Ini.${reportResponse.format === "excel" ? "xlsx" : reportResponse.format}`,
        });

        return reportResponse;
      } catch (error) {
        // Enhanced error handling with specific messages
        if (error instanceof Error) {
          if (error.message.includes('network') || error.message.includes('fetch')) {
            throw new Error('Gagal mengunduh laporan. Periksa koneksi internet Anda.');
          } else if (error.message.includes('unauthorized') || error.message.includes('authentication')) {
            throw new Error('Sesi Anda telah berakhir. Silakan login ulang.');
          } else if (error.message.includes('not found')) {
            throw new Error('Data stok tidak ditemukan.');
          }
        }
        throw error;
      }
    },
    onSuccess: () => {
      toast.success('Laporan stok saat ini berhasil diunduh');
    },
    onError: (error: any) => {
      const message = error?.message || error?.response?.data?.message || 'Gagal mengekspor data stok';
      toast.error(message);
      console.error('Error exporting current stock:', error);
    },
  });
}

// Export low stock
export function useExportLowStock() {
  const downloadMutation = useDownloadReport();

  return useMutation({
    mutationFn: async (format: ReportFormat = ReportFormat.PDF) => {
      try {
        const reportResponse = await inventoryReportsApi.exportLowStock(format);

        // Automatically download the report using standardized method
        await downloadMutation.mutateAsync({
          fileName: reportResponse.fileName,
          reportName: `Laporan_Stok_Rendah.${reportResponse.format === "excel" ? "xlsx" : reportResponse.format}`,
        });

        return reportResponse;
      } catch (error) {
        // Enhanced error handling with specific messages
        if (error instanceof Error) {
          if (error.message.includes('network') || error.message.includes('fetch')) {
            throw new Error('Gagal mengunduh laporan. Periksa koneksi internet Anda.');
          } else if (error.message.includes('unauthorized') || error.message.includes('authentication')) {
            throw new Error('Sesi Anda telah berakhir. Silakan login ulang.');
          } else if (error.message.includes('not found')) {
            throw new Error('Data stok rendah tidak ditemukan.');
          }
        }
        throw error;
      }
    },
    onSuccess: () => {
      toast.success('Laporan stok rendah berhasil diunduh');
    },
    onError: (error: any) => {
      const message = error?.message || error?.response?.data?.message || 'Gagal mengekspor data stok rendah';
      toast.error(message);
      console.error('Error exporting low stock:', error);
    },
  });
}

// Export expiring items
export function useExportExpiringItems() {
  const downloadMutation = useDownloadReport();

  return useMutation({
    mutationFn: async ({ days = 30, format = ReportFormat.PDF }: { days?: number; format?: ReportFormat }) => {
      const reportResponse = await inventoryReportsApi.exportExpiringItems(days, format);

      // Automatically download the report
      await downloadMutation.mutateAsync({
        fileName: reportResponse.fileName,
        reportName: `Laporan_Kedaluwarsa_${days}_Hari.${reportResponse.format === "excel" ? "xlsx" : reportResponse.format}`,
      });

      return reportResponse;
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal mengekspor data kedaluwarsa';
      toast.error(message);
      console.error('Error exporting expiring items:', error);
    },
  });
}

// Export stock movements
export function useExportStockMovements() {
  const downloadMutation = useDownloadReport();

  return useMutation({
    mutationFn: async ({
      startDate,
      endDate,
      format = ReportFormat.EXCEL,
    }: {
      startDate: string;
      endDate: string;
      format?: ReportFormat;
    }) => {
      const reportResponse = await inventoryReportsApi.exportStockMovements(startDate, endDate, format);

      // Automatically download the report
      await downloadMutation.mutateAsync({
        fileName: reportResponse.fileName,
        reportName: `Laporan_Pergerakan_Stok.${reportResponse.format === "excel" ? "xlsx" : reportResponse.format}`,
      });

      return reportResponse;
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal mengekspor data pergerakan stok';
      toast.error(message);
      console.error('Error exporting stock movements:', error);
    },
  });
}

// Import inventory data
export function useImportInventoryData() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ file, validateOnly = false }: { file: File; validateOnly?: boolean }) =>
      inventoryReportsApi.importInventoryData(file, { validateOnly }),
    onSuccess: (data) => {
      if (data.success) {
        if (data.importedCount !== undefined) {
          toast.success(`Berhasil mengimpor ${data.importedCount} item inventori`);

          // Invalidate inventory queries to refresh data
          queryClient.invalidateQueries({ queryKey: ['inventory'] });
          queryClient.invalidateQueries({ queryKey: ['inventory-stats'] });
        } else {
          toast.success('Validasi file berhasil, tidak ada error ditemukan');
        }

        if (data.warnings && data.warnings.length > 0) {
          data.warnings.forEach(warning => {
            toast.warning(warning);
          });
        }
      } else {
        toast.error(data.message || 'Gagal mengimpor data inventori');

        if (data.errors && data.errors.length > 0) {
          data.errors.forEach(error => {
            toast.error(error);
          });
        }
      }
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal mengimpor data inventori';
      toast.error(message);
      console.error('Error importing inventory data:', error);
    },
  });
}

// Download import template
export function useDownloadImportTemplate() {
  return useMutation({
    mutationFn: async (format: 'xlsx' | 'csv' = 'xlsx') => {
      try {
        const blob = await inventoryReportsApi.getImportTemplate(format);

        // Create download link with proper cleanup
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `Template_Import_Inventori.${format}`;

        // Ensure proper cleanup
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Clean up the object URL after a short delay to ensure download starts
        setTimeout(() => {
          window.URL.revokeObjectURL(url);
        }, 100);

        return { format, success: true };
      } catch (error) {
        // Enhanced error handling
        if (error instanceof Error) {
          if (error.message.includes('network') || error.message.includes('fetch')) {
            throw new Error('Gagal mengunduh template. Periksa koneksi internet Anda.');
          } else if (error.message.includes('unauthorized') || error.message.includes('authentication')) {
            throw new Error('Sesi Anda telah berakhir. Silakan login ulang.');
          }
        }
        throw error;
      }
    },
    onSuccess: ({ format }) => {
      toast.success(`Template import ${format.toUpperCase()} berhasil diunduh`);
    },
    onError: (error: any) => {
      const message = error?.message || error?.response?.data?.message || 'Gagal mengunduh template import';
      toast.error(message);
      console.error('Error downloading import template:', error);
    },
  });
}

// Get report types
export function useReportTypes() {
  return useQuery({
    queryKey: ['report-types'],
    queryFn: () => inventoryReportsApi.getReportTypes(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Get report progress (for long-running reports)
export function useReportProgress(reportId: string | null, enabled: boolean = false) {
  return useQuery({
    queryKey: ['report-progress', reportId],
    queryFn: () => inventoryReportsApi.getReportProgress(reportId!),
    enabled: enabled && !!reportId,
    refetchInterval: (query) => {
      // Stop polling when report is completed or failed
      const data = query.state.data as ReportGenerationProgress | undefined;
      if (data?.status === 'completed' || data?.status === 'failed') {
        return false;
      }
      return 2000; // Poll every 2 seconds
    },
  });
}

// Utility hook for report validation
export function useReportValidation() {
  return {
    validateFilters: inventoryReportsApi.validateReportFilters,
    formatFileSize: inventoryReportsApi.formatFileSize,
    formatReportTypeName: inventoryReportsApi.formatReportTypeName,
  };
}
