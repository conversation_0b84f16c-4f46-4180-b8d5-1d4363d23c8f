import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { purchaseOrdersApi } from '@/lib/api/purchase-orders';
import {
  PurchaseOrderQueryParams,
  CreatePurchaseOrderDto,
  UpdatePurchaseOrderDto,
  PurchaseOrderStatusUpdateDto,
} from '@/types/purchase-order';

// Query key factory
export const purchaseOrderKeys = {
  all: ['purchase-orders'] as const,
  lists: () => [...purchaseOrderKeys.all, 'list'] as const,
  list: (params: PurchaseOrderQueryParams) => [...purchaseOrderKeys.lists(), params] as const,
  details: () => [...purchaseOrderKeys.all, 'detail'] as const,
  detail: (id: string) => [...purchaseOrderKeys.details(), id] as const,
  stats: () => [...purchaseOrderKeys.all, 'stats'] as const,
};

// Query hooks
export function usePurchaseOrders(params: PurchaseOrderQueryParams) {
  return useQuery({
    queryKey: purchaseOrderKeys.list(params),
    queryFn: () => purchaseOrdersApi.getPurchaseOrders(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
    placeholderData: (previousData) => previousData, // Keep previous data while loading
  });
}

export function usePurchaseOrder(id: string) {
  return useQuery({
    queryKey: purchaseOrderKeys.detail(id),
    queryFn: () => purchaseOrdersApi.getPurchaseOrder(id),
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: !!id,
  });
}

export function usePurchaseOrderStats(period?: string) {
  return useQuery({
    queryKey: [...purchaseOrderKeys.stats(), period],
    queryFn: () => purchaseOrdersApi.getPurchaseOrderStats(period),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Invalidation hooks
export function usePurchaseOrdersInvalidate() {
  const queryClient = useQueryClient();

  return {
    invalidateAll: () => queryClient.invalidateQueries({ queryKey: purchaseOrderKeys.all }),
    invalidateLists: () => queryClient.invalidateQueries({ queryKey: purchaseOrderKeys.lists() }),
    invalidateStats: () => queryClient.invalidateQueries({ queryKey: purchaseOrderKeys.stats() }),
    invalidateDetail: (id: string) => queryClient.invalidateQueries({ queryKey: purchaseOrderKeys.detail(id) }),
  };
}

// Mutation hooks
export function useCreatePurchaseOrder() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreatePurchaseOrderDto) => purchaseOrdersApi.createPurchaseOrder(data),
    onSuccess: (data) => {
      // Invalidate and refetch purchase orders list
      queryClient.invalidateQueries({ queryKey: purchaseOrderKeys.lists() });
      queryClient.invalidateQueries({ queryKey: purchaseOrderKeys.stats() });

      // Set the new purchase order data in cache
      queryClient.setQueryData(purchaseOrderKeys.detail(data.id), data);

      toast.success('Purchase order berhasil dibuat');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal membuat purchase order';
      toast.error(message);
    },
  });
}

export function useUpdatePurchaseOrder() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdatePurchaseOrderDto }) =>
      purchaseOrdersApi.updatePurchaseOrder(id, data),
    onSuccess: (data) => {
      // Update the purchase order data in cache
      queryClient.setQueryData(purchaseOrderKeys.detail(data.id), data);

      // Invalidate lists to refresh any aggregated data
      queryClient.invalidateQueries({ queryKey: purchaseOrderKeys.lists() });
      queryClient.invalidateQueries({ queryKey: purchaseOrderKeys.stats() });

      toast.success('Purchase order berhasil diperbarui');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal memperbarui purchase order';
      toast.error(message);
    },
  });
}

export function useDeletePurchaseOrder() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => purchaseOrdersApi.deletePurchaseOrder(id),
    onSuccess: (_, id) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: purchaseOrderKeys.detail(id) });

      // Invalidate lists and stats
      queryClient.invalidateQueries({ queryKey: purchaseOrderKeys.lists() });
      queryClient.invalidateQueries({ queryKey: purchaseOrderKeys.stats() });

      toast.success('Purchase order berhasil dihapus');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal menghapus purchase order';
      toast.error(message);
    },
  });
}



export function useCancelPurchaseOrder() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, reason }: { id: string; reason?: string }) =>
      purchaseOrdersApi.cancelPurchaseOrder(id, { reason }),
    onSuccess: (data) => {
      // Update the purchase order data in cache
      queryClient.setQueryData(purchaseOrderKeys.detail(data.id), data);

      // Invalidate lists and stats
      queryClient.invalidateQueries({ queryKey: purchaseOrderKeys.lists() });
      queryClient.invalidateQueries({ queryKey: purchaseOrderKeys.stats() });

      toast.success('Purchase order berhasil dibatalkan');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal membatalkan purchase order';
      toast.error(message);
    },
  });
}

export function useUpdatePurchaseOrderStatus() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: PurchaseOrderStatusUpdateDto }) =>
      purchaseOrdersApi.updatePurchaseOrderStatus(id, data),
    onSuccess: (data) => {
      // Update the purchase order data in cache
      queryClient.setQueryData(purchaseOrderKeys.detail(data.id), data);

      // Invalidate lists and stats
      queryClient.invalidateQueries({ queryKey: purchaseOrderKeys.lists() });
      queryClient.invalidateQueries({ queryKey: purchaseOrderKeys.stats() });

      toast.success('Status purchase order berhasil diperbarui');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal memperbarui status purchase order';
      toast.error(message);
    },
  });
}

export function useSendPurchaseOrder() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => purchaseOrdersApi.sendPurchaseOrder(id),
    onSuccess: (data) => {
      // Update the purchase order data in cache
      queryClient.setQueryData(purchaseOrderKeys.detail(data.id), data);

      // Invalidate lists and stats
      queryClient.invalidateQueries({ queryKey: purchaseOrderKeys.lists() });
      queryClient.invalidateQueries({ queryKey: purchaseOrderKeys.stats() });

      toast.success('Purchase order berhasil dikirim ke supplier');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal mengirim purchase order';
      toast.error(message);
    },
  });
}

export function useExportPurchaseOrders() {
  return useMutation({
    mutationFn: (params: PurchaseOrderQueryParams & { format?: 'xlsx' | 'csv' }) =>
      purchaseOrdersApi.exportPurchaseOrders(params),
    onSuccess: (blob, variables) => {
      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `purchase-orders.${variables.format || 'xlsx'}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success('Data purchase order berhasil diekspor');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal mengekspor data purchase order';
      toast.error(message);
    },
  });
}

export function useDownloadPurchaseOrderPdf() {
  return useMutation({
    mutationFn: (id: string) => purchaseOrdersApi.generatePurchaseOrderPdf(id),
    onSuccess: (blob, id) => {
      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `purchase-order-${id}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success('PDF purchase order berhasil diunduh');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal mengunduh PDF purchase order';
      toast.error(message);
    },
  });
}
