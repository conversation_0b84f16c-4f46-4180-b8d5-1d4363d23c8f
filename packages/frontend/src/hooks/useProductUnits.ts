import { useQuery } from '@tanstack/react-query';
import { productUnitsApi, ProductUnitQueryParams } from '@/lib/api/product-units';
import { UnitType } from '@/types/product';

// Query keys
export const productUnitsKeys = {
  all: ['product-units'] as const,
  lists: () => [...productUnitsKeys.all, 'list'] as const,
  list: (params?: ProductUnitQueryParams) => [...productUnitsKeys.lists(), params] as const,
  baseUnits: () => [...productUnitsKeys.all, 'base-units'] as const,
  byType: (type?: UnitType) => [...productUnitsKeys.all, 'by-type', type] as const,
};

// Get all product units
export function useProductUnits(params?: ProductUnitQueryParams) {
  return useQuery({
    queryKey: productUnitsKeys.list(params),
    queryFn: () => productUnitsApi.getProductUnits(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Get base units only
export function useBaseUnits() {
  return useQuery({
    queryKey: productUnitsKeys.baseUnits(),
    queryFn: () => productUnitsApi.getBaseUnits(),
    staleTime: 10 * 60 * 1000, // 10 minutes - base units change rarely
  });
}

// Get units by type
export function useUnitsByType(type?: UnitType) {
  return useQuery({
    queryKey: productUnitsKeys.byType(type),
    queryFn: () => productUnitsApi.getUnitsByType(type),
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: !!type, // Only run query if type is provided
  });
}

// Get all active units (for form dropdowns)
export function useActiveUnits() {
  return useQuery({
    queryKey: productUnitsKeys.list({ isActive: true }),
    queryFn: () => productUnitsApi.getProductUnits({ isActive: true }),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}
