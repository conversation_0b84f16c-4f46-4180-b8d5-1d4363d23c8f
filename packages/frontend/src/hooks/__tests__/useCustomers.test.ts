import { describe, it, expect, vi, beforeEach } from 'vitest'
import { renderHook, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useCustomers, useCustomer, useCreateCustomer, useUpdateCustomer, customerKeys } from '../useCustomers'
import { customersApi } from '@/lib/api/customers'
import { mockToast, createTestQueryClient, resetTestEnvironment } from '@/test/utils'
import { type CustomerQueryParams, type Customer, type CreateCustomerDto, type UpdateCustomerDto, CustomerType, CustomerWithRelations } from '@/types/customer'

// Mock the customers API
vi.mock('@/lib/api/customers', () => ({
  customersApi: {
    getCustomers: vi.fn(),
    getCustomer: vi.fn(),
    createCustomer: vi.fn(),
    updateCustomer: vi.fn(),
    deleteCustomer: vi.fn(),
    getCustomerStats: vi.fn(),
    searchCustomers: vi.fn(),
  },
}))

const mockCustomersApi = vi.mocked(customersApi)

describe('useCustomers Hook', () => {
  let queryClient: QueryClient

  beforeEach(() => {
    resetTestEnvironment() // Prevent state leakage
    queryClient = createTestQueryClient()
  })

  const wrapper = ({ children }: { children: React.ReactNode }) => {
    const React = require('react')
    return React.createElement(QueryClientProvider, { client: queryClient }, children)
  }

  describe('useCustomers', () => {
    it('should fetch customers successfully', async () => {
      const mockCustomers = {
        data: [
          {
            id: '1',
            code: 'CUST-001',
            type: CustomerType.REGISTERED,
            firstName: 'Budi',
            lastName: 'Santoso',
            fullName: 'Budi Santoso',
            phoneNumber: '081234567890',
            email: '<EMAIL>',
            dateOfBirth: null,
            gender: null,
            address: 'Jakarta',
            city: 'Jakarta',
            province: 'DKI Jakarta',
            postalCode: null,
            membershipNumber: null,
            membershipLevel: 'GOLD',
            loyaltyPoints: 100,
            notes: null,
            isActive: true,
            createdAt: '2024-01-01T00:00:00.000Z',
            updatedAt: '2024-01-01T00:00:00.000Z',
            createdBy: 'user-1',
            updatedBy: 'user-1',
          },
        ],
        meta: { total: 1, page: 1, limit: 10, totalPages: 1, hasNextPage: false, hasPreviousPage: false },
      }

      mockCustomersApi.getCustomers.mockResolvedValue(mockCustomers)

      const params: CustomerQueryParams = { page: 1, limit: 10 }
      const { result } = renderHook(() => useCustomers(params), { wrapper })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockCustomers)
      expect(mockCustomersApi.getCustomers).toHaveBeenCalledWith(params)
    })

    it('should handle error when fetching customers fails', async () => {
      const errorMessage = 'Failed to fetch customers'
      mockCustomersApi.getCustomers.mockRejectedValue(new Error(errorMessage))

      const params: CustomerQueryParams = { page: 1, limit: 10 }
      const { result } = renderHook(() => useCustomers(params), { wrapper })

      await waitFor(() => {
        expect(result.current.isError).toBe(true)
      })

      expect(result.current.error).toBeInstanceOf(Error)
    })

    it('should use correct query key with search params', () => {
      const params: CustomerQueryParams = { 
        page: 1, 
        limit: 10, 
        search: 'Budi',
        type: CustomerType.REGISTERED
      }
      const expectedKey = customerKeys.list(params)

      expect(expectedKey).toEqual(['customers', 'list', params])
    })
  })

  describe('useCustomer', () => {
    it('should fetch single customer successfully', async () => {
      const mockCustomer: CustomerWithRelations = {
        id: '1',
        code: 'CUST-001',
        type: CustomerType.REGISTERED,
        firstName: 'Budi',
        lastName: 'Santoso',
        fullName: 'Budi Santoso',
        phoneNumber: '081234567890',
        email: '<EMAIL>',
        dateOfBirth: null,
        gender: null,
        address: 'Jakarta',
        city: 'Jakarta',
        province: 'DKI Jakarta',
        postalCode: null,
        membershipNumber: null,
        membershipLevel: 'GOLD',
        loyaltyPoints: 100,
        notes: null,
        isActive: true,
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-01T00:00:00.000Z',
        createdBy: 'user-1',
        updatedBy: 'user-1',
      }

      mockCustomersApi.getCustomer.mockResolvedValue(mockCustomer)

      const { result } = renderHook(() => useCustomer('1'), { wrapper })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockCustomer)
      expect(mockCustomersApi.getCustomer).toHaveBeenCalledWith('1')
    })

    it('should not fetch when id is empty', () => {
      const { result } = renderHook(() => useCustomer(''), { wrapper })

      expect(result.current.fetchStatus).toBe('idle')
      expect(mockCustomersApi.getCustomer).not.toHaveBeenCalled()
    })
  })

  describe('useCreateCustomer', () => {
    it('should create customer successfully', async () => {
      const newCustomer: CreateCustomerDto = {
        fullName: 'Siti Nurhaliza',
        phoneNumber: '081234567891',
        email: '<EMAIL>',
        address: 'Bandung',
        type: CustomerType.REGISTERED,
        membershipLevel: 'SILVER',
      }

      const createdCustomer: CustomerWithRelations = {
        id: '2',
        code: 'CUST-002', // Generated by backend
        type: newCustomer.type,
        firstName: newCustomer.firstName || null,
        lastName: newCustomer.lastName || null,
        fullName: newCustomer.fullName,
        phoneNumber: newCustomer.phoneNumber || null,
        email: newCustomer.email || null,
        dateOfBirth: null,
        gender: null,
        address: newCustomer.address || null,
        city: newCustomer.city || null,
        province: newCustomer.province || null,
        postalCode: newCustomer.postalCode || null,
        membershipNumber: newCustomer.membershipNumber || null,
        membershipLevel: newCustomer.membershipLevel || null,
        loyaltyPoints: 0,
        notes: newCustomer.notes || null,
        isActive: true,
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-01T00:00:00.000Z',
        createdBy: 'user-1',
        updatedBy: 'user-1',
      }

      mockCustomersApi.createCustomer.mockResolvedValue(createdCustomer)

      const { result } = renderHook(() => useCreateCustomer(), { wrapper })

      await result.current.mutateAsync(newCustomer)

      expect(mockCustomersApi.createCustomer).toHaveBeenCalledWith(newCustomer)
      expect(mockToast.success).toHaveBeenCalledWith('Pelanggan berhasil ditambahkan')
    })

    it('should handle create customer error', async () => {
      const newCustomer: CreateCustomerDto = {
        code: 'CUST-002',
        fullName: 'Siti Nurhaliza',
        phoneNumber: '081234567891',
        email: '<EMAIL>',
        address: 'Bandung',
        type: CustomerType.REGISTERED,
        membershipLevel: 'SILVER',
      }

      const errorMessage = 'Failed to create customer'
      mockCustomersApi.createCustomer.mockRejectedValue(new Error(errorMessage))

      const { result } = renderHook(() => useCreateCustomer(), { wrapper })

      await expect(result.current.mutateAsync(newCustomer)).rejects.toThrow(errorMessage)
      expect(mockToast.error).toHaveBeenCalledWith('Gagal menambahkan pelanggan')
    })
  })

  describe('useUpdateCustomer', () => {
    it('should update customer successfully', async () => {
      const updateData: UpdateCustomerDto = {
        fullName: 'Budi Santoso Updated',
        phoneNumber: '081234567899',
      }

      const updatedCustomer: CustomerWithRelations = {
        id: '1',
        code: 'CUST-001',
        type: CustomerType.REGISTERED,
        firstName: 'Budi',
        lastName: 'Santoso',
        fullName: 'Budi Santoso Updated',
        phoneNumber: '081234567899',
        email: '<EMAIL>',
        dateOfBirth: null,
        gender: null,
        address: 'Jakarta',
        city: 'Jakarta',
        province: 'DKI Jakarta',
        postalCode: null,
        membershipNumber: null,
        membershipLevel: 'GOLD',
        loyaltyPoints: 100,
        notes: null,
        isActive: true,
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-01T00:00:00.000Z',
        createdBy: 'user-1',
        updatedBy: 'user-1',
      }

      mockCustomersApi.updateCustomer.mockResolvedValue(updatedCustomer)

      const { result } = renderHook(() => useUpdateCustomer(), { wrapper })

      await result.current.mutateAsync({ id: '1', data: updateData })

      expect(mockCustomersApi.updateCustomer).toHaveBeenCalledWith('1', updateData)
      expect(mockToast.success).toHaveBeenCalledWith('Pelanggan berhasil diperbarui')
    })
  })

  describe('Query Key Factory', () => {
    it('should generate correct query keys', () => {
      expect(customerKeys.all).toEqual(['customers'])
      expect(customerKeys.lists()).toEqual(['customers', 'list'])
      expect(customerKeys.details()).toEqual(['customers', 'detail'])
      expect(customerKeys.detail('1')).toEqual(['customers', 'detail', '1'])
      expect(customerKeys.stats()).toEqual(['customers', 'stats'])
      expect(customerKeys.search('test')).toEqual(['customers', 'search', 'test'])
    })
  })
})
