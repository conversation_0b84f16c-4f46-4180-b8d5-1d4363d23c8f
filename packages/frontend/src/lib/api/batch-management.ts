import { apiClient } from "../axios";
import {
  BatchManagementStats,
  BatchHistoryItem,
  BatchAuditLogItem,
  ExpiryTrackingItem,
  BatchValidationResult,
  BatchManagementQueryParams,
  ValidateBatchNumberDto,
  BatchExportParams,
  PaginatedResponse,
} from "@/types/batch-management";

export const batchManagementApi = {
  // Statistics
  async getBatchManagementStats(
    period?: string,
  ): Promise<BatchManagementStats> {
    const response = await apiClient.get(
      "/procurement/batch-validation/statistics",
      {
        params: { period },
      },
    );

    // Transform backend response to match frontend interface
    const backendData = response.data.data;
    return {
      totalBatches: backendData.totalBatches || 0,
      activeBatches: backendData.activeBatches || 0,
      expiringSoon: backendData.expiringSoon || 0,
      expired: backendData.expiredBatches || 0,
      validationErrors: backendData.validationErrors || 0,
      bpomCompliant: backendData.bpomCompliant || 0,
      recentActivity: backendData.totalBatches || 0, // Use total batches as recent activity for now
      validationSuccessRate: backendData.validationSuccessRate || 0,
    };
  },

  // Batch History
  async getBatchHistory(
    params: BatchManagementQueryParams,
  ): Promise<PaginatedResponse<BatchHistoryItem>> {
    const response = await apiClient.get(
      "/procurement/batch-validation/history",
      { params },
    );
    return response.data;
  },

  // Get specific batch history
  async getBatchHistoryByNumber(
    batchNumber: string,
    includeAudit = false,
    limit = 50,
  ): Promise<any> {
    const response = await apiClient.get(
      `/procurement/batch-validation/history/${encodeURIComponent(batchNumber)}`,
      {
        params: { includeAudit, limit },
      },
    );
    return response.data;
  },

  // Batch Validation
  async validateBatchNumber(
    data: ValidateBatchNumberDto,
  ): Promise<BatchValidationResult> {
    const response = await apiClient.post(
      "/procurement/batch-validation/validate",
      data,
    );
    return response.data.data;
  },

  // Real-time validation
  async validateBatchNumberRealTime(
    batchNumber: string,
    productId: string,
  ): Promise<{
    isValid: boolean;
    message?: string;
    level: "error" | "warning" | "success";
  }> {
    const response = await apiClient.post(
      "/procurement/batch-validation/real-time",
      {
        batchNumber,
        productId,
      },
    );
    return response.data.data;
  },

  // Uniqueness check
  async checkBatchUniqueness(
    batchNumber: string,
    productId: string,
    supplierId?: string,
  ): Promise<{
    isUnique: boolean;
    conflictingBatches?: any[];
  }> {
    const response = await apiClient.post(
      "/procurement/batch-validation/check-uniqueness",
      {
        batchNumber,
        productId,
        supplierId,
      },
    );
    return response.data.data;
  },

  // Get validation rules
  async getValidationRules(): Promise<any> {
    const response = await apiClient.get("/procurement/batch-validation/rules");
    return response.data.data;
  },



  // Batch Audit Logs
  async getBatchAuditLogs(
    params: BatchManagementQueryParams,
  ): Promise<PaginatedResponse<BatchAuditLogItem>> {
    const response = await apiClient.get(
      "/procurement/batch-validation/audit-logs",
      { params },
    );
    return response.data;
  },

  // Expiry Tracking
  async getExpiryTracking(
    params: BatchManagementQueryParams,
  ): Promise<PaginatedResponse<ExpiryTrackingItem>> {
    const response = await apiClient.get(
      "/procurement/batch-validation/expiry-tracking",
      { params },
    );
    return response.data;
  },

  // Export functionality
  async exportBatchData(params: BatchExportParams): Promise<Blob> {
    const queryParams: any = {
      format: params.format,
    };

    if (params.includeAuditTrail) queryParams.includeAuditTrail = "true";

    // Add filter parameters if provided
    if (params.filters) {
      Object.entries(params.filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          queryParams[key] = value.toString();
        }
      });
    }

    const response = await apiClient.get(
      "/procurement/batch-validation/export",
      {
        params: queryParams,
        responseType: "blob",
      },
    );
    return response.data;
  },
};
