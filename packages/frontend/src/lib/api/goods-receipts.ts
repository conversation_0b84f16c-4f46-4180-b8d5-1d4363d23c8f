import { apiClient } from '../axios';
import {
  GoodsReceiptWithRelations,
  GoodsReceiptQueryParams,
  GoodsReceiptListResponse,
  GoodsReceiptStatsResponse,
  CreateGoodsReceiptDto,
  UpdateGoodsReceiptDto,
  GoodsReceiptStatusUpdateDto,
} from '@/types/goods-receipt';

export const goodsReceiptsApi = {
  // Get all goods receipts with filtering and pagination
  getGoodsReceipts: async (params: GoodsReceiptQueryParams = {}): Promise<GoodsReceiptListResponse> => {
    const searchParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        searchParams.append(key, String(value));
      }
    });

    const response = await apiClient.get(`/goods-receipts?${searchParams.toString()}`);
    return response.data;
  },

  // Get a specific goods receipt by ID
  getGoodsReceipt: async (id: string): Promise<GoodsReceiptWithRelations> => {
    const response = await apiClient.get(`/goods-receipts/${id}`);
    return response.data;
  },

  // Create a new goods receipt
  createGoodsReceipt: async (data: CreateGoodsReceiptDto): Promise<GoodsReceiptWithRelations> => {
    const response = await apiClient.post('/goods-receipts', data);
    return response.data;
  },

  // Update an existing goods receipt
  updateGoodsReceipt: async (id: string, data: UpdateGoodsReceiptDto): Promise<GoodsReceiptWithRelations> => {
    const response = await apiClient.patch(`/goods-receipts/${id}`, data);
    return response.data;
  },

  // Delete a goods receipt
  deleteGoodsReceipt: async (id: string): Promise<void> => {
    await apiClient.delete(`/goods-receipts/${id}`);
  },

  // Get goods receipt statistics
  getGoodsReceiptStats: async (period?: string): Promise<GoodsReceiptStatsResponse> => {
    try {
      const params = period ? { period } : {};
      const response = await apiClient.get('/goods-receipts/stats', { params });

      // Validate response data and provide defaults for missing fields
      const data = response.data;
      return {
        totalReceipts: data.totalReceipts || 0,
        pending: data.pending || 0,

        rejected: data.rejected || 0,
        completed: data.completed || 0,
        totalValue: data.totalValue || 0,
        statusStats: data.statusStats || {},
        recentReceipts: data.recentReceipts || [],
        processingTimeAnalytics: data.processingTimeAnalytics || {
          averageProcessingTime: 0,
          totalReceipts: 0,
          efficiencyMetrics: {
            fastProcessing: 0,
            slowProcessing: 0,
            averageProcessing: 0,
            fastPercentage: 0,
            slowPercentage: 0,
          },
        },
        volumeTrends: data.volumeTrends || [],
        period: data.period || 'all',
      };
    } catch (error) {
      console.error('Error fetching goods receipt statistics:', error);

      // Return fallback data structure to prevent UI crashes
      return {
        totalReceipts: 0,
        pending: 0,
        rejected: 0,
        completed: 0,
        totalValue: 0,
        statusStats: {},
        recentReceipts: [],
        processingTimeAnalytics: {
          averageProcessingTime: 0,
          totalReceipts: 0,
          efficiencyMetrics: {
            fastProcessing: 0,
            slowProcessing: 0,
            averageProcessing: 0,
            fastPercentage: 0,
            slowPercentage: 0,
          },
        },
        volumeTrends: [],
        period: 'all',
      };
    }
  },



  // Status management operations
  updateStatus: async (id: string, data: GoodsReceiptStatusUpdateDto): Promise<GoodsReceiptWithRelations> => {
    const response = await apiClient.patch(`/goods-receipts/${id}/status`, data);
    return response.data;
  },

  // Reject goods receipt
  rejectGoodsReceipt: async (id: string): Promise<GoodsReceiptWithRelations> => {
    const response = await apiClient.post(`/goods-receipts/${id}/reject`);
    return response.data;
  },

  // Complete goods receipt (finalize and create inventory)
  completeGoodsReceipt: async (id: string): Promise<GoodsReceiptWithRelations> => {
    const response = await apiClient.post(`/goods-receipts/${id}/complete`);
    return response.data;
  },

  // Generate goods receipt number
  generateReceiptNumber: async (): Promise<{ receiptNumber: string }> => {
    const response = await apiClient.get('/goods-receipts/generate-number');
    return response.data;
  },

  // Validate goods receipt number
  validateReceiptNumber: async (number: string): Promise<{ isUnique: boolean }> => {
    const response = await apiClient.get(`/goods-receipts/validate-number/${encodeURIComponent(number)}`);
    return response.data;
  },

  // Export goods receipts
  exportGoodsReceipts: async (params: GoodsReceiptQueryParams = {}): Promise<Blob> => {
    const searchParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        searchParams.append(key, String(value));
      }
    });

    const response = await apiClient.get(`/goods-receipts/export?${searchParams.toString()}`, {
      responseType: 'blob',
    });
    return response.data;
  },

  // Bulk operations
  bulkUpdateStatus: async (ids: string[], status: string): Promise<{ updated: number }> => {
    const response = await apiClient.patch('/goods-receipts/bulk-status', {
      ids,
      status,
    });
    return response.data;
  },

  bulkDelete: async (ids: string[]): Promise<{ deleted: number }> => {
    const response = await apiClient.delete('/goods-receipts/bulk', {
      data: { ids },
    });
    return response.data;
  },



  // Get goods receipts by purchase order
  getGoodsReceiptsByPurchaseOrder: async (purchaseOrderId: string): Promise<GoodsReceiptWithRelations[]> => {
    const response = await apiClient.get(`/goods-receipts/by-purchase-order/${purchaseOrderId}`);
    return response.data;
  },

  // Get goods receipts ready for completion
  getReadyForCompletion: async (): Promise<GoodsReceiptWithRelations[]> => {
    const response = await apiClient.get('/goods-receipts/ready-for-completion');
    return response.data;
  },

  // Validate goods receipt for completion
  validateForCompletion: async (id: string): Promise<{ canComplete: boolean; reason?: string }> => {
    const response = await apiClient.get(`/goods-receipts/${id}/validate-completion`);
    return response.data;
  },

  // Get discrepancy report
  getDiscrepancyReport: async (id: string): Promise<{
    hasDiscrepancies: boolean;
    discrepancies: Array<{
      itemId: string;
      productName: string;
      ordered: number;
      received: number;
      difference: number;
      reason?: string;
    }>;
  }> => {
    const response = await apiClient.get(`/goods-receipts/${id}/discrepancy-report`);
    return response.data;
  },

  // Create goods receipt from purchase order
  createFromPurchaseOrder: async (purchaseOrderId: string): Promise<GoodsReceiptWithRelations> => {
    const response = await apiClient.post(`/goods-receipts/from-purchase-order/${purchaseOrderId}`);
    return response.data;
  },
};
