import { apiClient } from '../axios';
import {
  TaxConfigurationResponse,
  TaxCalculationResult,
  TaxBreakdownResponse,
  AllTaxSettingsResponse,
  TaxStatusResponse,
  CalculateTaxRequest,
  CalculateMultipleItemsTaxRequest,
  UpdatePPNConfigurationRequest,
  SetTaxCalculationModeRequest,
  TaxCalculationMode,
  TaxEntityType,
} from '@/types/tax';

export const taxApi = {
  // Tax Configuration Management
  async getPPNConfiguration(): Promise<TaxConfigurationResponse> {
    const response = await apiClient.get('/tax/ppn/configuration');
    return response.data;
  },

  async updatePPNConfiguration(data: UpdatePPNConfigurationRequest): Promise<TaxConfigurationResponse> {
    const response = await apiClient.put('/tax/ppn/configuration', data);
    return response.data;
  },

  async getCalculationMode(): Promise<{ mode: TaxCalculationMode }> {
    const response = await apiClient.get('/tax/calculation-mode');
    return response.data;
  },

  async setCalculationMode(data: SetTaxCalculationModeRequest): Promise<{ mode: TaxCalculationMode }> {
    const response = await apiClient.put('/tax/calculation-mode', data);
    return response.data;
  },

  async getAllTaxSettings(): Promise<AllTaxSettingsResponse> {
    const response = await apiClient.get('/tax/settings');
    return response.data;
  },

  async getPPNStatus(): Promise<TaxStatusResponse> {
    const response = await apiClient.get('/tax/ppn/status');
    return response.data;
  },

  // Tax Calculation
  async calculateTax(data: CalculateTaxRequest): Promise<TaxCalculationResult> {
    const response = await apiClient.post('/tax/calculate', data);
    return response.data;
  },

  async calculateMultipleItemsTax(data: CalculateMultipleItemsTaxRequest): Promise<TaxCalculationResult> {
    const response = await apiClient.post('/tax/calculate-multiple', data);
    return response.data;
  },

  async getTaxBreakdown(data: CalculateTaxRequest): Promise<TaxBreakdownResponse> {
    const response = await apiClient.post('/tax/breakdown', data);
    return response.data;
  },

  // Indonesian Tax Services
  async getApplicableTaxes(params: {
    entityType: TaxEntityType;
    annualRevenue?: number;
    yearsInOperation?: number;
  }): Promise<any> {
    const searchParams = new URLSearchParams();
    searchParams.append('entityType', params.entityType);
    if (params.annualRevenue) searchParams.append('annualRevenue', params.annualRevenue.toString());
    if (params.yearsInOperation) searchParams.append('yearsInOperation', params.yearsInOperation.toString());

    const response = await apiClient.get(`/tax/applicable-taxes?${searchParams.toString()}`);
    return response.data;
  },

  async calculatePP23(data: {
    grossIncome: number;
    entityType: TaxEntityType;
    isRetailTransaction?: boolean;
  }): Promise<any> {
    const response = await apiClient.post('/tax/calculate/pp23', data);
    return response.data;
  },

  async calculatePPH25(data: {
    annualIncome: number;
    entityType: TaxEntityType;
    previousPayments?: number;
  }): Promise<any> {
    const response = await apiClient.post('/tax/calculate/pph25', data);
    return response.data;
  },

  async calculateIndonesianPPN(data: {
    subtotal: number;
    isRetailTransaction?: boolean;
  }): Promise<any> {
    const response = await apiClient.post('/tax/calculate/indonesian-ppn', data);
    return response.data;
  },

  async getComplianceStatus(params: {
    entityType: TaxEntityType;
    annualRevenue?: number;
    yearsInOperation?: number;
    hasEmployees?: boolean;
  }): Promise<any> {
    const searchParams = new URLSearchParams();
    searchParams.append('entityType', params.entityType);
    if (params.annualRevenue) searchParams.append('annualRevenue', params.annualRevenue.toString());
    if (params.yearsInOperation) searchParams.append('yearsInOperation', params.yearsInOperation.toString());
    if (params.hasEmployees !== undefined) searchParams.append('hasEmployees', params.hasEmployees.toString());

    const response = await apiClient.get(`/tax/compliance-status?${searchParams.toString()}`);
    return response.data;
  },

  async getTaxCalendar(): Promise<any> {
    const response = await apiClient.get('/tax/calendar');
    return response.data;
  },

  async validateTaxConfiguration(data: {
    taxType: string;
    taxRate: number;
    entityType: TaxEntityType;
  }): Promise<any> {
    const response = await apiClient.post('/tax/validate-configuration', data);
    return response.data;
  },
};
