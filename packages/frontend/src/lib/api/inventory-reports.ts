import { apiClient } from '../axios';
import {
  InventoryReportRequest,
  InventoryReportResponse,
  ReportGenerationProgress,
  ReportType,
  ReportFormat,
  ReportLanguage,
  ReportFilters,
} from '@/types/inventory-reports';
import { AllocationReportRequest, AllocationReportResponse } from '@/types/stock-allocation';

export const inventoryReportsApi = {
  // Generate general inventory report
  generateInventoryReport: async (request: InventoryReportRequest): Promise<InventoryReportResponse> => {
    // For now, we'll use the existing allocation report endpoint as a base
    // and extend it to handle different report types
    const response = await apiClient.post('/inventory/reports/generate', request);
    return response.data;
  },

  // Generate allocation report (existing functionality)
  generateAllocationReport: async (request: AllocationReportRequest): Promise<AllocationReportResponse> => {
    const response = await apiClient.post('/inventory/allocation-reports/generate', request);
    return response.data;
  },

  // Get report generation progress
  getReportProgress: async (reportId: string): Promise<ReportGenerationProgress> => {
    const response = await apiClient.get(`/inventory/reports/${reportId}/progress`);
    return response.data;
  },

  // Download report file
  downloadReport: async (fileName: string): Promise<Blob> => {
    const response = await apiClient.get(`/reports/download/${fileName}`, {
      responseType: 'blob',
    });
    return response.data;
  },

  // Get available report types
  getReportTypes: async (): Promise<{ type: ReportType; name: string; description: string }[]> => {
    // This could be a static list or fetched from backend
    return [
      {
        type: ReportType.STOCK_LEVELS,
        name: 'Laporan Tingkat Stok',
        description: 'Laporan lengkap tingkat stok inventori saat ini',
      },
      {
        type: ReportType.STOCK_MOVEMENTS,
        name: 'Laporan Pergerakan Stok',
        description: 'Riwayat pergerakan stok dengan detail transaksi',
      },
      {
        type: ReportType.LOW_STOCK,
        name: 'Laporan Stok Rendah',
        description: 'Daftar produk dengan stok di bawah batas minimum',
      },
      {
        type: ReportType.EXPIRY_REPORT,
        name: 'Laporan Kedaluwarsa',
        description: 'Produk yang sudah atau akan kedaluwarsa',
      },
      {
        type: ReportType.ALLOCATION_HISTORY,
        name: 'Riwayat Alokasi',
        description: 'Riwayat alokasi stok dengan metode FIFO/FEFO',
      },
      {
        type: ReportType.SUPPLIER_PERFORMANCE,
        name: 'Performa Supplier',
        description: 'Analisis performa supplier berdasarkan stok dan kualitas',
      },
      {
        type: ReportType.CATEGORY_ANALYSIS,
        name: 'Analisis Kategori',
        description: 'Analisis stok berdasarkan kategori produk',
      },
      {
        type: ReportType.STOCK_AGING,
        name: 'Analisis Umur Stok',
        description: 'Analisis umur stok untuk optimasi inventori',
      },
      {
        type: ReportType.TURNOVER_ANALYSIS,
        name: 'Analisis Perputaran',
        description: 'Analisis tingkat perputaran stok inventori',
      },
    ];
  },

  // Export current inventory data (for the existing export buttons)
  exportInventoryData: async (filters: ReportFilters, format: ReportFormat): Promise<InventoryReportResponse> => {
    const request: InventoryReportRequest = {
      type: ReportType.STOCK_LEVELS,
      format,
      language: ReportLanguage.ID,
      filters,
      includeDetails: true,
      includeSummary: true,
      includeCharts: false,
    };

    return inventoryReportsApi.generateInventoryReport(request);
  },

  // Quick export functions for common use cases
  exportCurrentStock: async (format: ReportFormat = ReportFormat.PDF): Promise<InventoryReportResponse> => {
    return inventoryReportsApi.exportInventoryData(
      { isActive: true },
      format
    );
  },

  exportLowStock: async (format: ReportFormat = ReportFormat.PDF): Promise<InventoryReportResponse> => {
    return inventoryReportsApi.exportInventoryData(
      { lowStockOnly: true, isActive: true },
      format
    );
  },

  exportExpiringItems: async (
    days: number = 30,
    format: ReportFormat = ReportFormat.PDF
  ): Promise<InventoryReportResponse> => {
    return inventoryReportsApi.exportInventoryData(
      { expiringSoon: true, expiringSoonDays: days, isActive: true },
      format
    );
  },

  exportStockMovements: async (
    startDate: string,
    endDate: string,
    format: ReportFormat = ReportFormat.EXCEL
  ): Promise<InventoryReportResponse> => {
    const request: InventoryReportRequest = {
      type: ReportType.STOCK_MOVEMENTS,
      format,
      language: ReportLanguage.ID,
      filters: { startDate, endDate },
      includeDetails: true,
      includeSummary: true,
    };

    return inventoryReportsApi.generateInventoryReport(request);
  },

  // Import functionality placeholder
  importInventoryData: async (file: File, options?: { validateOnly?: boolean }): Promise<{
    success: boolean;
    message: string;
    errors?: string[];
    warnings?: string[];
    importedCount?: number;
    skippedCount?: number;
  }> => {
    const formData = new FormData();
    formData.append('file', file);

    if (options?.validateOnly) {
      formData.append('validateOnly', 'true');
    }

    const response = await apiClient.post('/inventory/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data;
  },

  // Get import template
  getImportTemplate: async (format: 'xlsx' | 'csv' = 'xlsx'): Promise<Blob> => {
    const response = await apiClient.get(`/inventory/import-template?format=${format}`, {
      responseType: 'blob',
    });
    return response.data;
  },

  // Validate report filters
  validateReportFilters: (filters: ReportFilters): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    // Date validation
    if (filters.startDate && filters.endDate) {
      const startDate = new Date(filters.startDate);
      const endDate = new Date(filters.endDate);

      if (startDate > endDate) {
        errors.push('Tanggal mulai tidak boleh lebih besar dari tanggal akhir');
      }

      if (startDate > new Date()) {
        errors.push('Tanggal mulai tidak boleh di masa depan');
      }
    }

    // Quantity validation
    if (filters.minQuantity !== undefined && filters.maxQuantity !== undefined) {
      if (filters.minQuantity > filters.maxQuantity) {
        errors.push('Jumlah minimum tidak boleh lebih besar dari jumlah maksimum');
      }
    }

    // Value validation
    if (filters.minValue !== undefined && filters.maxValue !== undefined) {
      if (filters.minValue > filters.maxValue) {
        errors.push('Nilai minimum tidak boleh lebih besar dari nilai maksimum');
      }
    }

    // Expiry days validation
    if (filters.expiringSoonDays !== undefined) {
      if (filters.expiringSoonDays < 1 || filters.expiringSoonDays > 365) {
        errors.push('Hari kedaluwarsa harus antara 1-365 hari');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  },

  // Format file size for display
  formatFileSize: (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },

  // Format report type name for display
  formatReportTypeName: (type: ReportType): string => {
    const typeMap = {
      [ReportType.STOCK_LEVELS]: 'Laporan Tingkat Stok',
      [ReportType.STOCK_MOVEMENTS]: 'Laporan Pergerakan Stok',
      [ReportType.LOW_STOCK]: 'Laporan Stok Rendah',
      [ReportType.EXPIRY_REPORT]: 'Laporan Kedaluwarsa',
      [ReportType.ALLOCATION_HISTORY]: 'Riwayat Alokasi',
      [ReportType.SUPPLIER_PERFORMANCE]: 'Performa Supplier',
      [ReportType.CATEGORY_ANALYSIS]: 'Analisis Kategori',
      [ReportType.STOCK_AGING]: 'Analisis Umur Stok',
      [ReportType.TURNOVER_ANALYSIS]: 'Analisis Perputaran',
    };

    return typeMap[type] || type;
  },
};
