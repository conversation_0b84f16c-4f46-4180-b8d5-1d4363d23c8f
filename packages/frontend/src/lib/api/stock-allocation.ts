import { apiClient } from '../axios';
import {
  StockAllocationData,
  AllocationResult,
  StockSummary,
  AllocationReportRequest,
  AllocationReportResponse,
} from '@/types/stock-allocation';

export interface StockAvailabilityData {
  productId: string;
  requestedQuantity: number;
}

export interface StockAvailabilityResult {
  isAvailable: boolean;
  totalAvailable: number;
  requestedQuantity: number;
  shortfall: number;
  batchCount: number;
  batches: Array<{
    inventoryItemId: string;
    batchNumber?: string;
    quantityOnHand: number;
    expiryDate?: string;
    receivedDate: string;
    location?: string;
  }>;
}

export const stockAllocationApi = {
  // Execute stock allocation
  allocateStock: async (data: StockAllocationData): Promise<AllocationResult> => {
    const response = await apiClient.post('/inventory/allocate', data);
    return response.data;
  },

  // Preview allocation without executing
  previewAllocation: async (data: StockAllocationData): Promise<AllocationResult> => {
    const previewData = { ...data, previewOnly: true };
    const response = await apiClient.post('/inventory/allocate/preview', previewData);
    return response.data;
  },

  // Validate stock availability
  validateStockAvailability: async (data: StockAvailabilityData): Promise<StockAvailabilityResult> => {
    const response = await apiClient.post('/inventory/validate-stock-availability', data);
    return response.data;
  },

  // Get available stock summary for a product
  getStockSummary: async (productId: string): Promise<StockSummary> => {
    const response = await apiClient.get(`/inventory/products/${productId}/stock-summary`);
    return response.data;
  },

  // Get available stock for a product
  getAvailableStock: async (productId: string, includeExpired = false): Promise<{ productId: string; totalAvailable: number; includeExpired: boolean }> => {
    const response = await apiClient.get(`/inventory/products/${productId}/available-stock?includeExpired=${includeExpired}`);
    return response.data;
  },

  // Preview stock consumption (alternative endpoint)
  previewStockConsumption: async (data: {
    productId: string;
    requestedQuantity: number;
    method: 'FIFO' | 'FEFO';
  }): Promise<AllocationResult> => {
    const response = await apiClient.post('/inventory/preview-stock-consumption', data);
    return response.data;
  },

  // Consume stock (execute allocation)
  consumeStock: async (data: {
    productId: string;
    requestedQuantity: number;
    method: 'FIFO' | 'FEFO';
    userId?: string;
    reason?: string;
    notes?: string;
    allowPartialAllocation?: boolean;
    nearExpiryWarningDays?: number;
  }): Promise<AllocationResult> => {
    const response = await apiClient.post('/inventory/consume-stock', data);
    return response.data;
  },

  // Generate allocation report
  generateReport: async (request: AllocationReportRequest): Promise<AllocationReportResponse> => {
    const response = await apiClient.post('/inventory/allocation-reports/generate', request);
    return response.data;
  },

  // Download report file
  downloadReport: async (reportUrl: string): Promise<Blob> => {
    const response = await apiClient.get(reportUrl, {
      responseType: 'blob',
    });
    return response.data;
  },
};
