import { apiClient } from '../axios';
import { ProductUnit, UnitType } from '@/types/product';

export interface ProductUnitQueryParams {
  search?: string;
  type?: UnitType;
  isActive?: boolean;
  isBaseUnit?: boolean;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export const productUnitsApi = {
  // Get all product units
  async getProductUnits(params?: ProductUnitQueryParams): Promise<ProductUnit[]> {
    const response = await apiClient.get('/product-units', { params });
    return response.data;
  },

  // Get base units only
  async getBaseUnits(): Promise<ProductUnit[]> {
    const response = await apiClient.get('/product-units/base-units');
    return response.data;
  },

  // Get units by type
  async getUnitsByType(type?: UnitType): Promise<ProductUnit[]> {
    const response = await apiClient.get('/product-units/by-type', { 
      params: type ? { type } : undefined 
    });
    return response.data;
  },
};
