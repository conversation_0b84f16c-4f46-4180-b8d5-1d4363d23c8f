import { apiClient } from '../axios';
import { SessionSalesResponse, SessionStats } from '@/types/session';

export const sessionApi = {
  /**
   * Get sales for a specific cashier's session on a given date (legacy)
   */
  getCashierSessionSales: async (cashierId: string, sessionDate: string): Promise<SessionSalesResponse> => {
    const response = await apiClient.get(`/sales/cashier/${cashierId}/session/${sessionDate}`);
    return response.data;
  },

  /**
   * Get session statistics for a specific cashier on a given date (legacy)
   */
  getSessionStats: async (cashierId: string, sessionDate: string): Promise<SessionStats> => {
    const response = await apiClient.get(`/sales/session-stats/${cashierId}/${sessionDate}`);
    return response.data;
  },

  /**
   * Get sales for a specific session based on session start time
   */
  getSessionSales: async (cashierId: string, sessionStartTime: string, sessionEndTime?: string): Promise<SessionSalesResponse> => {
    const params = new URLSearchParams();
    if (sessionEndTime) {
      params.append('sessionEndTime', sessionEndTime);
    }
    const queryString = params.toString();
    const url = `/sales/session/${cashierId}/${encodeURIComponent(sessionStartTime)}${queryString ? `?${queryString}` : ''}`;
    const response = await apiClient.get(url);
    return response.data;
  },

  /**
   * Get session statistics based on session start time
   */
  getSessionStatsById: async (cashierId: string, sessionStartTime: string, sessionEndTime?: string): Promise<SessionStats> => {
    const params = new URLSearchParams();
    if (sessionEndTime) {
      params.append('sessionEndTime', sessionEndTime);
    }
    const queryString = params.toString();
    const url = `/sales/session-stats/${cashierId}/${encodeURIComponent(sessionStartTime)}/stats${queryString ? `?${queryString}` : ''}`;
    const response = await apiClient.get(url);
    return response.data;
  },
};
