import { apiClient } from '../axios';
import {
  Customer,
  CustomerWithRelations,
  CreateCustomerDto,
  UpdateCustomerDto,
  CustomerQueryParams,
  CustomerListResponse,
  CustomerStats,
  CustomerSearchResult,
} from '@/types/customer';

export const customersApi = {
  // Customer CRUD operations
  async getCustomers(params?: CustomerQueryParams): Promise<CustomerListResponse> {
    const searchParams = new URLSearchParams();

    if (!params) {
      const response = await apiClient.get('/customers');
      return response.data;
    }

    // Pagination
    if (params.page) searchParams.append('page', params.page.toString());
    if (params.limit) searchParams.append('limit', params.limit.toString());

    // Search
    if (params.search) searchParams.append('search', params.search);

    // Filters
    if (params.type) searchParams.append('type', params.type);
    if (params.membershipLevel) searchParams.append('membershipLevel', params.membershipLevel);
    if (params.isActive !== undefined) searchParams.append('isActive', params.isActive.toString());

    // Sorting
    if (params.sortBy) searchParams.append('sortBy', params.sortBy);
    if (params.sortOrder) searchParams.append('sortOrder', params.sortOrder);

    const queryString = searchParams.toString();
    const url = queryString ? `/customers?${queryString}` : '/customers';

    const response = await apiClient.get(url);
    return response.data;
  },

  async getCustomer(id: string): Promise<CustomerWithRelations> {
    const response = await apiClient.get(`/customers/${id}`);
    return response.data;
  },

  async createCustomer(data: CreateCustomerDto): Promise<CustomerWithRelations> {
    const response = await apiClient.post('/customers', data);
    return response.data;
  },

  async updateCustomer(id: string, data: UpdateCustomerDto): Promise<CustomerWithRelations> {
    const response = await apiClient.patch(`/customers/${id}`, data);
    return response.data;
  },

  async deleteCustomer(id: string): Promise<void> {
    await apiClient.delete(`/customers/${id}`);
  },

  async activateCustomer(id: string): Promise<Customer> {
    const response = await apiClient.patch(`/customers/${id}/activate`);
    return response.data;
  },

  async deactivateCustomer(id: string): Promise<Customer> {
    const response = await apiClient.patch(`/customers/${id}/deactivate`);
    return response.data;
  },

  // Customer stats
  async getCustomerStats(): Promise<CustomerStats> {
    const response = await apiClient.get('/customers/stats');
    return response.data;
  },

  // Customer code generation
  async generateCustomerCode(type: 'WALK_IN' | 'REGISTERED'): Promise<{ code: string }> {
    const response = await apiClient.get(`/customers/generate-code/${type}`);
    return response.data;
  },

  async validateCustomerCode(code: string): Promise<{ isUnique: boolean }> {
    const response = await apiClient.get(`/customers/validate-code/${code}`);
    return response.data;
  },

  // Search customers for selector components
  async searchCustomers(query: string, limit: number = 20): Promise<CustomerSearchResult[]> {
    const response = await apiClient.get('/customers', {
      params: {
        search: query,
        limit,
        isActive: true,
      },
    });

    // Transform the response to match CustomerSearchResult interface
    return response.data.data.map((customer: Customer) => ({
      id: customer.id,
      name: customer.fullName,
      phone: customer.phoneNumber,
      code: customer.code,
      type: customer.type,
      membershipLevel: customer.membershipLevel,
    }));
  },
};
