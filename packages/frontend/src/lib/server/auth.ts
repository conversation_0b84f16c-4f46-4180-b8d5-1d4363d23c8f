import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { redirect } from 'next/navigation';

/**
 * Get the current session on the server side
 */
export async function getSession() {
  return await getServerSession(authOptions);
}

/**
 * Require authentication for a page - redirects to login if not authenticated
 */
export async function requireAuth() {
  const session = await getSession();

  if (!session) {
    redirect('/auth/login');
  }

  return session;
}

/**
 * Get the authorization header for API requests
 */
export function getAuthHeader(session: any) {
  if (!session?.accessToken) {
    return {};
  }

  return {
    'Authorization': `Bearer ${session.accessToken}`,
  };
}

/**
 * Check if user has required role
 */
export function hasRole(session: any, requiredRole: string) {
  return session?.user?.role === requiredRole;
}
