import { getAuthHeader } from './auth';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';

/**
 * Server-side API client
 */
export class ServerApiClient {
  private baseURL: string;
  private headers: Record<string, string>;

  constructor(session?: any) {
    this.baseURL = API_URL;
    const authHeaders = getAuthHeader(session);
    this.headers = {
      'Content-Type': 'application/json',
    };

    // Only add Authorization header if it exists
    if (authHeaders.Authorization) {
      this.headers.Authorization = authHeaders.Authorization;
    }
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;

    const response = await fetch(url, {
      ...options,
      headers: {
        ...this.headers,
        ...options.headers,
      },
      // Disable caching for dynamic data
      cache: 'no-store',
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`API Error ${response.status}: ${error}`);
    }

    return response.json();
  }

  async get<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'GET' });
  }

  async post<T>(endpoint: string, data: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async patch<T>(endpoint: string, data: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PATCH',
      body: JSON.stringify(data),
    });
  }

  async delete<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }
}

/**
 * Create an authenticated API client
 */
export function createApiClient(session?: any) {
  return new ServerApiClient(session);
}


