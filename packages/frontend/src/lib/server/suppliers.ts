import { createApiClient } from './api';
import {
  Supplier,
  SupplierWithRelations,
  SupplierQueryParams,
  SupplierListResponse,
  SupplierStats,
  PaymentSummary,
} from '@/types/supplier';

/**
 * Server-side supplier API functions
 */
export class ServerSuppliersApi {
  private api;

  constructor(session?: any) {
    this.api = createApiClient(session);
  }

  async getSuppliers(params?: SupplierQueryParams): Promise<SupplierListResponse> {
    const searchParams = new URLSearchParams();
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value));
        }
      });
    }
    
    const queryString = searchParams.toString();
    const endpoint = queryString ? `/suppliers?${queryString}` : '/suppliers';
    
    return this.api.get<SupplierListResponse>(endpoint);
  }

  async getSupplier(id: string): Promise<SupplierWithRelations> {
    return this.api.get<SupplierWithRelations>(`/suppliers/${id}`);
  }

  async getStats(): Promise<SupplierStats> {
    return this.api.get<SupplierStats>('/suppliers/stats');
  }

  async getSupplierPaymentSummary(id: string): Promise<PaymentSummary> {
    return this.api.get<PaymentSummary>(`/suppliers/${id}/payments/summary`);
  }
}

/**
 * Create server-side suppliers API instance
 */
export function createSuppliersApi(session?: any) {
  return new ServerSuppliersApi(session);
}
