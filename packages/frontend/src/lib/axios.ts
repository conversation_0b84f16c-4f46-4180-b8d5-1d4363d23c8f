import axios from 'axios';
import { nullToUndefined } from './utils';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';

export const apiClient = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Response interceptor to transform null values to undefined
// This ensures all API responses are compatible with form types that expect undefined
apiClient.interceptors.response.use(
  (response) => {
    // Only transform JSON responses to avoid breaking binary data, text responses, etc.
    const contentType = response.headers['content-type'] || '';
    const isJsonResponse = contentType.includes('application/json');

    if (response.data && isJsonResponse) {
      try {
        response.data = nullToUndefined(response.data);
      } catch (error) {
        // Log the error but don't break the response
        console.warn('Failed to transform null values to undefined:', error);
      }
    }
    return response;
  },
  (error) => {
    // Also transform error response data if it exists and is JSON
    if (error.response?.data) {
      const contentType = error.response.headers?.['content-type'] || '';
      const isJsonResponse = contentType.includes('application/json');

      if (isJsonResponse) {
        try {
          error.response.data = nullToUndefined(error.response.data);
        } catch (transformError) {
          // Log the error but don't break the error response
          console.warn('Failed to transform error response null values:', transformError);
        }
      }
    }
    return Promise.reject(error);
  }
);

// Token will be set dynamically by the API functions
export const setAuthToken = (token: string | null) => {
  if (token) {
    apiClient.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  } else {
    delete apiClient.defaults.headers.common['Authorization'];
  }
};

export default apiClient;
