// Import Prisma enums directly as the single source of truth
import { ProductType, ProductCategory, MedicineClassification } from '@prisma/client';

// Product Type Options - Single source of truth using Prisma enums
export const PRODUCT_TYPE_OPTIONS = [
  { value: ProductType.MEDICINE, label: 'Obat', description: 'Produk obat-obatan' },
  { value: ProductType.MEDICAL_DEVICE, label: 'Alat Kesehatan', description: 'Alat dan perangkat medis' },
  { value: ProductType.SUPPLEMENT, label: 'Suplemen', description: 'Suplemen dan vitamin' },
  { value: ProductType.COSMETIC, label: 'Kosmetik', description: 'Produk kecantikan dan perawatan' },
  { value: ProductType.GENERAL, label: 'Umum', description: 'Produk umum lainnya' },
];

// Product Category Options - Single source of truth using Prisma enums
export const PRODUCT_CATEGORY_OPTIONS = [
  // Medicine Categories
  { value: ProductCategory.ANALGESIC, label: '<PERSON>bat Pereda Nyeri', description: 'Obat untuk mengurangi rasa nyeri' },
  { value: ProductCategory.ANTIBIOTIC, label: 'Antibiotik', description: 'Obat untuk melawan infeksi bakteri' },
  { value: ProductCategory.ANTACID, label: 'Antasida', description: 'Obat untuk mengatasi asam lambung' },
  { value: ProductCategory.VITAMIN, label: 'Vitamin', description: 'Vitamin dan mineral' },
  { value: ProductCategory.SUPPLEMENT, label: 'Suplemen', description: 'Suplemen makanan dan nutrisi' },
  { value: ProductCategory.COUGH_COLD, label: 'Obat Batuk Pilek', description: 'Obat untuk batuk dan pilek' },
  { value: ProductCategory.DIGESTIVE, label: 'Obat Pencernaan', description: 'Obat untuk gangguan pencernaan' },
  { value: ProductCategory.TOPICAL, label: 'Obat Luar', description: 'Obat untuk penggunaan luar/topikal' },
  { value: ProductCategory.CARDIOVASCULAR, label: 'Obat Jantung', description: 'Obat untuk penyakit jantung dan pembuluh darah' },
  { value: ProductCategory.DIABETES, label: 'Obat Diabetes', description: 'Obat untuk diabetes dan gula darah' },
  { value: ProductCategory.HYPERTENSION, label: 'Obat Hipertensi', description: 'Obat untuk tekanan darah tinggi' },

  // Non-Medicine Categories
  { value: ProductCategory.MEDICAL_DEVICE, label: 'Alat Kesehatan', description: 'Alat dan perangkat medis' },
  { value: ProductCategory.COSMETIC, label: 'Kosmetik', description: 'Produk kecantikan dan kosmetik' },
  { value: ProductCategory.BABY_CARE, label: 'Perawatan Bayi', description: 'Produk perawatan bayi dan anak' },
  { value: ProductCategory.PERSONAL_CARE, label: 'Perawatan Pribadi', description: 'Produk perawatan dan kebersihan pribadi' },
  { value: ProductCategory.FIRST_AID, label: 'P3K', description: 'Produk pertolongan pertama pada kecelakaan' },
  { value: ProductCategory.OTHER, label: 'Lainnya', description: 'Kategori produk lainnya' },
];

// Product Category Options with "All" option for filters
export const PRODUCT_CATEGORY_FILTER_OPTIONS = [
  { value: 'all', label: 'Semua Kategori' },
  ...PRODUCT_CATEGORY_OPTIONS,
];

// Medicine Classification Options - Single source of truth using Prisma enums - Single source of truth
export const MEDICINE_CLASSIFICATION_OPTIONS = [
  {
    value: MedicineClassification.OBAT_BEBAS,
    label: 'Obat Bebas',
    symbol: '🟢',
    description: 'Lingkaran hijau - Obat bebas yang dapat dibeli tanpa resep dokter'
  },
  {
    value: MedicineClassification.OBAT_BEBAS_TERBATAS,
    label: 'Obat Bebas Terbatas',
    symbol: '🔵',
    description: 'Lingkaran biru - Obat bebas terbatas dengan peringatan khusus'
  },
  {
    value: MedicineClassification.OBAT_KERAS,
    label: 'Obat Keras',
    symbol: '🔴K',
    description: 'Lingkaran merah dengan huruf K - Hanya dengan resep dokter'
  },
  {
    value: MedicineClassification.NARKOTIKA,
    label: 'Narkotika',
    symbol: '🔴+',
    description: 'Lingkaran merah dengan tanda plus - Narkotika'
  },
  {
    value: MedicineClassification.PSIKOTROPIKA,
    label: 'Psikotropika',
    symbol: '⚠️',
    description: 'Tanda khusus - Psikotropika'
  },
  {
    value: MedicineClassification.JAMU,
    label: 'Jamu',
    symbol: '🌿',
    description: 'Obat tradisional'
  },
  {
    value: MedicineClassification.OBAT_HERBAL_TERSTANDAR,
    label: 'Obat Herbal Terstandar',
    symbol: '🌿✓',
    description: 'Obat herbal yang telah terstandar'
  },
  {
    value: MedicineClassification.FITOFARMAKA,
    label: 'Fitofarmaka',
    symbol: '🌿⚕️',
    description: 'Obat herbal dengan standar farmasi'
  },
  {
    value: MedicineClassification.NON_MEDICINE,
    label: 'Bukan Obat',
    symbol: '',
    description: 'Produk non-obat'
  },
];

// Status Options
export const PRODUCT_STATUS_OPTIONS = [
  { value: 'all', label: 'Semua Status' },
  { value: 'true', label: 'Aktif' },
  { value: 'false', label: 'Tidak Aktif' },
];

// Color mappings for badges
export const getProductTypeColor = (type: ProductType): string => {
  switch (type) {
    case ProductType.MEDICINE:
      return 'bg-blue-100 text-blue-800 border-blue-200';
    case ProductType.MEDICAL_DEVICE:
      return 'bg-green-100 text-green-800 border-green-200';
    case ProductType.SUPPLEMENT:
      return 'bg-purple-100 text-purple-800 border-purple-200';
    case ProductType.COSMETIC:
      return 'bg-pink-100 text-pink-800 border-pink-200';
    case ProductType.GENERAL:
      return 'bg-gray-100 text-gray-800 border-gray-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

export const getProductCategoryColor = (category: ProductCategory): string => {
  switch (category) {
    case ProductCategory.ANALGESIC:
    case ProductCategory.ANTIBIOTIC:
    case ProductCategory.ANTACID:
      return 'bg-red-100 text-red-800 border-red-200';
    case ProductCategory.VITAMIN:
    case ProductCategory.SUPPLEMENT:
      return 'bg-orange-100 text-orange-800 border-orange-200';
    case ProductCategory.COUGH_COLD:
    case ProductCategory.DIGESTIVE:
      return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    case ProductCategory.TOPICAL:
    case ProductCategory.CARDIOVASCULAR:
      return 'bg-green-100 text-green-800 border-green-200';
    case ProductCategory.DIABETES:
    case ProductCategory.HYPERTENSION:
      return 'bg-blue-100 text-blue-800 border-blue-200';
    case ProductCategory.MEDICAL_DEVICE:
      return 'bg-indigo-100 text-indigo-800 border-indigo-200';
    case ProductCategory.COSMETIC:
    case ProductCategory.PERSONAL_CARE:
      return 'bg-pink-100 text-pink-800 border-pink-200';
    case ProductCategory.BABY_CARE:
      return 'bg-purple-100 text-purple-800 border-purple-200';
    case ProductCategory.FIRST_AID:
      return 'bg-red-100 text-red-800 border-red-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

export const getMedicineClassificationColor = (classification: MedicineClassification): string => {
  switch (classification) {
    case MedicineClassification.OBAT_BEBAS:
      return 'bg-green-100 text-green-800 border-green-200';
    case MedicineClassification.OBAT_BEBAS_TERBATAS:
      return 'bg-blue-100 text-blue-800 border-blue-200';
    case MedicineClassification.OBAT_KERAS:
      return 'bg-red-100 text-red-800 border-red-200';
    case MedicineClassification.NARKOTIKA:
      return 'bg-red-100 text-red-800 border-red-200';
    case MedicineClassification.PSIKOTROPIKA:
      return 'bg-orange-100 text-orange-800 border-orange-200';
    case MedicineClassification.JAMU:
      return 'bg-green-100 text-green-800 border-green-200';
    case MedicineClassification.OBAT_HERBAL_TERSTANDAR:
      return 'bg-green-100 text-green-800 border-green-200';
    case MedicineClassification.FITOFARMAKA:
      return 'bg-green-100 text-green-800 border-green-200';
    case MedicineClassification.NON_MEDICINE:
      return 'bg-gray-100 text-gray-800 border-gray-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

// Table columns configuration
export const PRODUCT_TABLE_COLUMNS = [
  { key: 'code', label: 'Kode', sortable: true },
  { key: 'name', label: 'Nama Produk', sortable: true },
  { key: 'type', label: 'Jenis', sortable: true },
  { key: 'category', label: 'Kategori', sortable: true },
  { key: 'medicineClassification', label: 'Klasifikasi', sortable: true },
  { key: 'manufacturer', label: 'Produsen', sortable: true },
  { key: 'baseUnit', label: 'Unit Dasar', sortable: false },
  { key: 'isActive', label: 'Status', sortable: true },
  { key: 'createdAt', label: 'Dibuat', sortable: true },
  { key: 'actions', label: 'Aksi', sortable: false },
];

export const DEFAULT_PAGINATION = {
  page: 1,
  limit: 10,
  sortBy: 'createdAt',
  sortOrder: 'desc' as const,
};

// Helper functions
export const getProductTypeLabel = (type: ProductType): string => {
  return PRODUCT_TYPE_OPTIONS.find(option => option.value === type)?.label || type;
};

export const getProductCategoryLabel = (category: ProductCategory): string => {
  return PRODUCT_CATEGORY_OPTIONS.find(option => option.value === category)?.label || category;
};

export const getMedicineClassificationLabel = (classification: MedicineClassification): string => {
  return MEDICINE_CLASSIFICATION_OPTIONS.find(option => option.value === classification)?.label || classification;
};

export const getMedicineClassificationSymbol = (classification: MedicineClassification): string => {
  return MEDICINE_CLASSIFICATION_OPTIONS.find(option => option.value === classification)?.symbol || '';
};

export const getMedicineClassificationDescription = (classification: MedicineClassification): string => {
  return MEDICINE_CLASSIFICATION_OPTIONS.find(option => option.value === classification)?.description || '';
};
