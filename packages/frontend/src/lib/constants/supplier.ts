import {
  SupplierType,
  SupplierStatus,
  PaymentMethod,
  DocumentType,
  SupplierTypeOption,
  SupplierStatusOption,
  PaymentMethodOption,
  DocumentTypeOption,
  PaymentStatusOption,
} from '@/types/supplier';

export const SUPPLIER_TYPE_OPTIONS: SupplierTypeOption[] = [
  { value: SupplierType.PBF, label: 'PBF (Pedagang Besar Farmasi)' },
  { value: SupplierType.MANUFACTURER, label: 'Pabrik Farmasi' },
  { value: SupplierType.DISTRIBUTOR, label: 'Distributor' },
  { value: SupplierType.LOCAL, label: 'Supplier Lokal' },
];

export const SUPPLIER_STATUS_OPTIONS: SupplierStatusOption[] = [
  { value: SupplierStatus.ACTIVE, label: 'Aktif' },
  { value: SupplierStatus.INACTIVE, label: 'Tidak Aktif' },
  { value: SupplierStatus.SUSPENDED, label: 'Ditangguh<PERSON>' },
  { value: SupplierStatus.PENDING, label: 'Menunggu' },
];

export const PAYMENT_METHOD_OPTIONS: PaymentMethodOption[] = [
  { value: PaymentMethod.CASH, label: 'Tunai' },
  { value: PaymentMethod.TRANSFER, label: 'Transfer Bank' },
  { value: PaymentMethod.CREDIT, label: 'Kredit' },
  { value: PaymentMethod.GIRO, label: 'Giro' },
];

export const DOCUMENT_TYPE_OPTIONS: DocumentTypeOption[] = [
  { value: DocumentType.LICENSE, label: 'Izin Usaha' },
  { value: DocumentType.CERTIFICATE, label: 'Sertifikat' },
  { value: DocumentType.CONTRACT, label: 'Kontrak' },
  { value: DocumentType.TAX_DOCUMENT, label: 'Dokumen Pajak' },
  { value: DocumentType.OTHER, label: 'Lainnya' },
  // PAYMENT_PROOF is excluded from regular document management
];

export const PAYMENT_STATUS_OPTIONS: PaymentStatusOption[] = [
  { value: 'PENDING', label: 'Menunggu' },
  { value: 'PAID', label: 'Dibayar' },
  { value: 'OVERDUE', label: 'Terlambat' },
  { value: 'CANCELLED', label: 'Dibatalkan' },
];

export const INDONESIAN_PROVINCES = [
  'Aceh',
  'Sumatera Utara',
  'Sumatera Barat',
  'Riau',
  'Kepulauan Riau',
  'Jambi',
  'Sumatera Selatan',
  'Bangka Belitung',
  'Bengkulu',
  'Lampung',
  'DKI Jakarta',
  'Jawa Barat',
  'Jawa Tengah',
  'DI Yogyakarta',
  'Jawa Timur',
  'Banten',
  'Bali',
  'Nusa Tenggara Barat',
  'Nusa Tenggara Timur',
  'Kalimantan Barat',
  'Kalimantan Tengah',
  'Kalimantan Selatan',
  'Kalimantan Timur',
  'Kalimantan Utara',
  'Sulawesi Utara',
  'Sulawesi Tengah',
  'Sulawesi Selatan',
  'Sulawesi Tenggara',
  'Gorontalo',
  'Sulawesi Barat',
  'Maluku',
  'Maluku Utara',
  'Papua',
  'Papua Barat',
  'Papua Selatan',
  'Papua Tengah',
  'Papua Pegunungan',
  'Papua Barat Daya',
];

export const SUPPLIER_TABLE_COLUMNS = [
  { key: 'code', label: 'Kode', sortable: true },
  { key: 'name', label: 'Nama Supplier', sortable: true },
  { key: 'type', label: 'Jenis', sortable: true },
  { key: 'city', label: 'Kota', sortable: true },
  { key: 'phone', label: 'Telepon', sortable: false },
  { key: 'status', label: 'Status', sortable: true },
  { key: 'createdAt', label: 'Dibuat', sortable: true },
  { key: 'actions', label: 'Aksi', sortable: false },
];

export const DEFAULT_PAGINATION = {
  page: 1,
  limit: 10,
  sortBy: 'createdAt',
  sortOrder: 'desc' as const,
};

// Helper functions
export const getSupplierTypeLabel = (type: SupplierType): string => {
  return SUPPLIER_TYPE_OPTIONS.find(option => option.value === type)?.label || type;
};

export const getSupplierStatusLabel = (status: SupplierStatus): string => {
  return SUPPLIER_STATUS_OPTIONS.find(option => option.value === status)?.label || status;
};

export const getPaymentMethodLabel = (method: PaymentMethod): string => {
  return PAYMENT_METHOD_OPTIONS.find(option => option.value === method)?.label || method;
};

export const getDocumentTypeLabel = (type: DocumentType): string => {
  return DOCUMENT_TYPE_OPTIONS.find(option => option.value === type)?.label || type;
};

export const getSupplierStatusColor = (status: SupplierStatus): string => {
  switch (status) {
    case SupplierStatus.ACTIVE:
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
    case SupplierStatus.INACTIVE:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    case SupplierStatus.SUSPENDED:
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
    case SupplierStatus.PENDING:
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
  }
};

export const getSupplierTypeColor = (type: SupplierType): string => {
  switch (type) {
    case SupplierType.PBF:
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
    case SupplierType.MANUFACTURER:
      return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
    case SupplierType.DISTRIBUTOR:
      return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300';
    case SupplierType.LOCAL:
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
  }
};
