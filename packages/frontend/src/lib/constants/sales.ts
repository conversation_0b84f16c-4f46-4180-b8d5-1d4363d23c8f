import { SaleStatus, PaymentMethod, SaleStatusOption, PaymentMethodOption } from '@/types/sales';

// Sale Status Options with "All" option for filters
export const SALES_STATUS_OPTIONS = [
  { value: 'all', label: 'Semua Status' },
  { value: SaleStatus.DRAFT, label: 'Draft' },
  { value: SaleStatus.COMPLETED, label: '<PERSON>les<PERSON>' },
  { value: SaleStatus.CANCELLED, label: '<PERSON><PERSON><PERSON><PERSON>' },
  { value: SaleStatus.REFUNDED, label: 'Dike<PERSON>likan' },
];

// Sale Status Options without "All" for specific use cases
export const SALE_STATUS_OPTIONS: SaleStatusOption[] = [
  { value: SaleStatus.DRAFT, label: 'Draft' },
  { value: SaleStatus.COMPLETED, label: '<PERSON><PERSON><PERSON>' },
  { value: SaleStatus.CANCELLED, label: '<PERSON>batalkan' },
  { value: SaleStatus.REFUNDED, label: '<PERSON><PERSON><PERSON><PERSON><PERSON>' },
];

// Payment Method Options with "All" option for filters
export const PAYMENT_METHODS_OPTIONS = [
  { value: 'all', label: 'Semua Metode' },
  { value: PaymentMethod.CASH, label: 'Tunai' },
  { value: PaymentMethod.TRANSFER, label: 'Transfer' },
  { value: PaymentMethod.CREDIT, label: 'Kredit' },
  { value: PaymentMethod.GIRO, label: 'Giro' },
];

// Payment Method Options without "All" for specific use cases
export const PAYMENT_METHOD_OPTIONS: PaymentMethodOption[] = [
  { value: PaymentMethod.CASH, label: 'Tunai' },
  { value: PaymentMethod.TRANSFER, label: 'Transfer' },
  { value: PaymentMethod.CREDIT, label: 'Kredit' },
  { value: PaymentMethod.GIRO, label: 'Giro' },
];

// Sale Table Columns Configuration
export const SALE_TABLE_COLUMNS = [
  { key: 'saleNumber', label: 'No. Transaksi', sortable: true },
  { key: 'saleDate', label: 'Tanggal', sortable: true },
  { key: 'customerName', label: 'Pelanggan', sortable: true },
  { key: 'cashier', label: 'Kasir', sortable: true },
  { key: 'totalAmount', label: 'Total', sortable: true },
  { key: 'paymentMethod', label: 'Metode Bayar', sortable: true },
  { key: 'status', label: 'Status', sortable: true },
  { key: 'actions', label: 'Aksi', sortable: false },
];

// Default Pagination Settings
export const DEFAULT_SALES_PAGINATION = {
  page: 1,
  limit: 10,
  sortBy: 'createdAt',
  sortOrder: 'desc' as const,
};

// Helper functions
export const getSaleStatusLabel = (status: SaleStatus): string => {
  return SALE_STATUS_OPTIONS.find(option => option.value === status)?.label || status;
};

export const getPaymentMethodLabel = (method: PaymentMethod): string => {
  return PAYMENT_METHOD_OPTIONS.find(option => option.value === method)?.label || method;
};

export const getSaleStatusColor = (status: SaleStatus): string => {
  switch (status) {
    case SaleStatus.DRAFT:
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
    case SaleStatus.COMPLETED:
      return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
    case SaleStatus.CANCELLED:
      return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
    case SaleStatus.REFUNDED:
      return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
  }
};

export const getPaymentMethodColor = (method: PaymentMethod): string => {
  switch (method) {
    case PaymentMethod.CASH:
      return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
    case PaymentMethod.TRANSFER:
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
    case PaymentMethod.CREDIT:
      return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400';
    case PaymentMethod.GIRO:
      return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
  }
};

// Date range presets for filtering
export const DATE_RANGE_PRESETS = [
  {
    label: 'Hari Ini',
    value: 'today',
    getDateRange: () => {
      const today = new Date();
      const start = new Date(today.getFullYear(), today.getMonth(), today.getDate());
      const end = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);
      return { startDate: start.toISOString(), endDate: end.toISOString() };
    },
  },
  {
    label: 'Kemarin',
    value: 'yesterday',
    getDateRange: () => {
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      const start = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate());
      const end = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate(), 23, 59, 59);
      return { startDate: start.toISOString(), endDate: end.toISOString() };
    },
  },
  {
    label: '7 Hari Terakhir',
    value: 'last7days',
    getDateRange: () => {
      const end = new Date();
      const start = new Date();
      start.setDate(start.getDate() - 7);
      return { startDate: start.toISOString(), endDate: end.toISOString() };
    },
  },
  {
    label: '30 Hari Terakhir',
    value: 'last30days',
    getDateRange: () => {
      const end = new Date();
      const start = new Date();
      start.setDate(start.getDate() - 30);
      return { startDate: start.toISOString(), endDate: end.toISOString() };
    },
  },
  {
    label: 'Bulan Ini',
    value: 'thismonth',
    getDateRange: () => {
      const now = new Date();
      const start = new Date(now.getFullYear(), now.getMonth(), 1);
      const end = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59);
      return { startDate: start.toISOString(), endDate: end.toISOString() };
    },
  },
  {
    label: 'Bulan Lalu',
    value: 'lastmonth',
    getDateRange: () => {
      const now = new Date();
      const start = new Date(now.getFullYear(), now.getMonth() - 1, 1);
      const end = new Date(now.getFullYear(), now.getMonth(), 0, 23, 59, 59);
      return { startDate: start.toISOString(), endDate: end.toISOString() };
    },
  },
];

// Search placeholder text
export const SALES_SEARCH_PLACEHOLDER = 'Cari berdasarkan nomor transaksi, nama pelanggan, atau nomor telepon...';

// Export/Import related constants
export const SALES_EXPORT_FORMATS = [
  { value: 'excel', label: 'Excel (.xlsx)' },
  { value: 'csv', label: 'CSV (.csv)' },
  { value: 'pdf', label: 'PDF (.pdf)' },
];

// Currency formatting
export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

// Date formatting
export const formatDate = (date: string | Date): string => {
  return new Intl.DateTimeFormat('id-ID', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(new Date(date));
};

export const formatDateOnly = (date: string | Date): string => {
  return new Intl.DateTimeFormat('id-ID', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  }).format(new Date(date));
};

// Sale number formatting
export const formatSaleNumber = (saleNumber: string): string => {
  return saleNumber.toUpperCase();
};

// Cashier name formatting
export const formatCashierName = (cashier: { firstName: string; lastName: string }): string => {
  return `${cashier.firstName} ${cashier.lastName}`.trim();
};

// Validation helpers
export const isEditableSale = (status: SaleStatus): boolean => {
  return status === SaleStatus.DRAFT;
};

export const isCancellableSale = (status: SaleStatus): boolean => {
  return status === SaleStatus.DRAFT || status === SaleStatus.COMPLETED;
};

export const isRefundableSale = (status: SaleStatus): boolean => {
  return status === SaleStatus.COMPLETED;
};

export const isCompletableSale = (status: SaleStatus): boolean => {
  return status === SaleStatus.DRAFT;
};

export const isDeletableSale = (status: SaleStatus): boolean => {
  return status === SaleStatus.DRAFT || status === SaleStatus.CANCELLED;
};

// Table configuration
export const SALES_TABLE_CONFIG = {
  minWidth: '1400px',
  stickyActionsColumn: true,
  showScrollIndicator: true,
  defaultPageSizes: [10, 25, 50, 100],
  searchDebounceMs: 300,
  refreshInterval: 30000, // 30 seconds
};
