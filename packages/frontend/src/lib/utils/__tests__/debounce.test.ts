import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import {
  useDebounce,
  useDebouncedCallback,
  useDebouncedValue,
  useSearchDebounce,
  useDebouncedValidation,
  DEBOUNCE_DELAYS,
} from '../debounce'

// Mock timers
vi.useFakeTimers()

describe('Debounce Utilities', () => {
  beforeEach(() => {
    vi.clearAllTimers()
  })

  afterEach(() => {
    vi.runOnlyPendingTimers()
    vi.useRealTimers()
    vi.useFakeTimers()
  })

  describe('DEBOUNCE_DELAYS', () => {
    it('should have correct predefined delays', () => {
      expect(DEBOUNCE_DELAYS.SEARCH).toBe(300)
      expect(DEBOUNCE_DELAYS.VALIDATION).toBe(500)
      expect(DEBOUNCE_DELAYS.EXPENSIVE).toBe(1000)
    })
  })

  describe('useDebounce', () => {
    it('should debounce value changes', () => {
      const { result, rerender } = renderHook(
        ({ value }) => useDebounce(value, 300),
        { initialProps: { value: 'initial' } }
      )

      // Initial value should be returned immediately
      expect(result.current).toBe('initial')

      // Update value
      rerender({ value: 'updated' })
      
      // Value should not change immediately
      expect(result.current).toBe('initial')

      // Fast-forward time
      act(() => {
        vi.advanceTimersByTime(300)
      })

      // Value should now be updated
      expect(result.current).toBe('updated')
    })

    it('should use default delay when not specified', () => {
      const { result, rerender } = renderHook(
        ({ value }) => useDebounce(value),
        { initialProps: { value: 'initial' } }
      )

      rerender({ value: 'updated' })
      
      // Should use DEBOUNCE_DELAYS.SEARCH (300ms) as default
      act(() => {
        vi.advanceTimersByTime(299)
      })
      expect(result.current).toBe('initial')

      act(() => {
        vi.advanceTimersByTime(1)
      })
      expect(result.current).toBe('updated')
    })

    it('should handle rapid value changes', () => {
      const { result, rerender } = renderHook(
        ({ value }) => useDebounce(value, 300),
        { initialProps: { value: 'initial' } }
      )

      // Rapid changes
      rerender({ value: 'change1' })
      act(() => { vi.advanceTimersByTime(100) })
      
      rerender({ value: 'change2' })
      act(() => { vi.advanceTimersByTime(100) })
      
      rerender({ value: 'final' })
      
      // Should still be initial value
      expect(result.current).toBe('initial')

      // Complete the debounce
      act(() => {
        vi.advanceTimersByTime(300)
      })

      // Should have the final value
      expect(result.current).toBe('final')
    })
  })

  describe('useDebouncedCallback', () => {
    it('should debounce callback execution', () => {
      const mockCallback = vi.fn()
      const { result } = renderHook(() => 
        useDebouncedCallback(mockCallback, 300)
      )

      // Call the debounced function multiple times
      result.current('arg1')
      result.current('arg2')
      result.current('arg3')

      // Callback should not be called immediately
      expect(mockCallback).not.toHaveBeenCalled()

      // Fast-forward time
      act(() => {
        vi.advanceTimersByTime(300)
      })

      // Callback should be called once with the last arguments
      expect(mockCallback).toHaveBeenCalledTimes(1)
      expect(mockCallback).toHaveBeenCalledWith('arg3')
    })

    it('should use default delay when not specified', () => {
      const mockCallback = vi.fn()
      const { result } = renderHook(() => 
        useDebouncedCallback(mockCallback)
      )

      result.current('test')

      act(() => {
        vi.advanceTimersByTime(299)
      })
      expect(mockCallback).not.toHaveBeenCalled()

      act(() => {
        vi.advanceTimersByTime(1)
      })
      expect(mockCallback).toHaveBeenCalledWith('test')
    })
  })

  describe('useDebouncedValue', () => {
    it('should provide both immediate and debounced values', () => {
      const { result } = renderHook(() => 
        useDebouncedValue('initial', 300)
      )

      // Initial state
      expect(result.current.value).toBe('initial')
      expect(result.current.debouncedValue).toBe('initial')

      // Update value
      act(() => {
        result.current.setValue('updated')
      })

      // Immediate value should change, debounced should not
      expect(result.current.value).toBe('updated')
      expect(result.current.debouncedValue).toBe('initial')

      // Fast-forward time
      act(() => {
        vi.advanceTimersByTime(300)
      })

      // Both values should now be updated
      expect(result.current.value).toBe('updated')
      expect(result.current.debouncedValue).toBe('updated')
    })
  })

  describe('useSearchDebounce', () => {
    it('should provide search functionality with debouncing', () => {
      const { result } = renderHook(() => useSearchDebounce())

      // Initial state
      expect(result.current.searchTerm).toBe('')
      expect(result.current.debouncedSearchTerm).toBe('')

      // Update search term
      act(() => {
        result.current.setSearchTerm('paracetamol')
      })

      // Immediate search term should change
      expect(result.current.searchTerm).toBe('paracetamol')
      expect(result.current.debouncedSearchTerm).toBe('')

      // Fast-forward time
      act(() => {
        vi.advanceTimersByTime(300)
      })

      // Debounced search term should now be updated
      expect(result.current.debouncedSearchTerm).toBe('paracetamol')
    })

    it('should clear search terms', () => {
      const { result } = renderHook(() => useSearchDebounce('initial'))

      act(() => {
        result.current.setSearchTerm('test')
      })

      act(() => {
        result.current.clearSearch()
      })

      expect(result.current.searchTerm).toBe('')
      // Note: debouncedSearchTerm might still be 'initial' until debounce completes
      // In a real scenario, you'd wait for the debounce to complete
    })
  })

  describe('useDebouncedValidation', () => {
    it('should debounce validation function calls', () => {
      const mockValidation = vi.fn().mockResolvedValue('valid')
      const { result } = renderHook(() => 
        useDebouncedValidation(mockValidation, 500)
      )

      // Call validation multiple times
      result.current('value1')
      result.current('value2')
      result.current('value3')

      // Validation should not be called immediately
      expect(mockValidation).not.toHaveBeenCalled()

      // Fast-forward time
      act(() => {
        vi.advanceTimersByTime(500)
      })

      // Validation should be called once with the last value
      expect(mockValidation).toHaveBeenCalledTimes(1)
      expect(mockValidation).toHaveBeenCalledWith('value3')
    })

    it('should use default validation delay', () => {
      const mockValidation = vi.fn()
      const { result } = renderHook(() => 
        useDebouncedValidation(mockValidation)
      )

      result.current('test')

      // Should use DEBOUNCE_DELAYS.VALIDATION (500ms)
      act(() => {
        vi.advanceTimersByTime(499)
      })
      expect(mockValidation).not.toHaveBeenCalled()

      act(() => {
        vi.advanceTimersByTime(1)
      })
      expect(mockValidation).toHaveBeenCalledWith('test')
    })
  })
})
