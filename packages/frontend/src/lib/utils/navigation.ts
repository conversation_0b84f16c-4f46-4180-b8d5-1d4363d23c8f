import { AppRouterInstance } from 'next/dist/shared/lib/app-router-context.shared-runtime';

/**
 * Robust navigation utility that attempts to go back in browser history
 * with a fallback to a specified route when no history is available.
 * 
 * This utility handles edge cases such as:
 * - Direct URL access (no browser history)
 * - Bookmark navigation
 * - Cases where router.back() fails silently
 * - SSR compatibility
 * 
 * @param router - Next.js App Router instance
 * @param fallbackRoute - Route to navigate to if back navigation fails
 * @param options - Optional configuration
 */
export function navigateBackWithFallback(
  router: AppRouterInstance,
  fallbackRoute: string,
  options: {
    /** Timeout in milliseconds before falling back (default: 100ms) */
    fallbackTimeout?: number;
    /** Force fallback instead of attempting back navigation */
    forceFallback?: boolean;
  } = {}
): void {
  const { fallbackTimeout = 100, forceFallback = false } = options;

  // Handle SSR compatibility
  if (typeof window === 'undefined') {
    router.push(fallbackRoute);
    return;
  }

  // Force fallback if requested
  if (forceFallback) {
    router.push(fallbackRoute);
    return;
  }

  // Check if there's meaningful browser history to go back to
  // window.history.length > 1 indicates there's at least one previous entry
  const hasHistory = window.history.length > 1;

  if (!hasHistory) {
    // No history available, go directly to fallback route
    router.push(fallbackRoute);
    return;
  }

  // Attempt to go back with a fallback mechanism
  let navigationHandled = false;

  // Set up a fallback timer in case router.back() fails silently
  const fallbackTimer = setTimeout(() => {
    if (!navigationHandled) {
      navigationHandled = true;
      router.push(fallbackRoute);
    }
  }, fallbackTimeout);

  // Listen for successful navigation (popstate event)
  const handlePopState = () => {
    if (!navigationHandled) {
      navigationHandled = true;
      clearTimeout(fallbackTimer);
      window.removeEventListener('popstate', handlePopState);
    }
  };

  // Add event listener for successful back navigation
  window.addEventListener('popstate', handlePopState);

  // Attempt to go back
  try {
    router.back();
  } catch (error) {
    // If router.back() throws an error, use fallback
    if (!navigationHandled) {
      navigationHandled = true;
      clearTimeout(fallbackTimer);
      window.removeEventListener('popstate', handlePopState);
      router.push(fallbackRoute);
    }
  }
}

/**
 * Common fallback routes for different sections of the application
 */
export const FALLBACK_ROUTES = {
  DASHBOARD: '/dashboard',
  INVENTORY: '/dashboard/inventory',
  PRODUCTS: '/dashboard/products',
  SUPPLIERS: '/dashboard/suppliers',
  PURCHASE_ORDERS: '/dashboard/purchase-orders',
  GOODS_RECEIPTS: '/dashboard/goods-receipts',
  USERS: '/dashboard/users',
  SETTINGS: '/dashboard/settings',
} as const;

/**
 * Convenience function for inventory-related pages
 */
export function navigateBackToInventory(router: AppRouterInstance): void {
  navigateBackWithFallback(router, FALLBACK_ROUTES.INVENTORY);
}

/**
 * Convenience function for product-related pages
 */
export function navigateBackToProducts(router: AppRouterInstance): void {
  navigateBackWithFallback(router, FALLBACK_ROUTES.PRODUCTS);
}

/**
 * Convenience function for supplier-related pages
 */
export function navigateBackToSuppliers(router: AppRouterInstance): void {
  navigateBackWithFallback(router, FALLBACK_ROUTES.SUPPLIERS);
}

/**
 * Convenience function for purchase order-related pages
 */
export function navigateBackToPurchaseOrders(router: AppRouterInstance): void {
  navigateBackWithFallback(router, FALLBACK_ROUTES.PURCHASE_ORDERS);
}

/**
 * Convenience function for goods receipt-related pages
 */
export function navigateBackToGoodsReceipts(router: AppRouterInstance): void {
  navigateBackWithFallback(router, FALLBACK_ROUTES.GOODS_RECEIPTS);
}

/**
 * Convenience function for general dashboard pages
 */
export function navigateBackToDashboard(router: AppRouterInstance): void {
  navigateBackWithFallback(router, FALLBACK_ROUTES.DASHBOARD);
}

/**
 * Type for the navigation utility function
 */
export type NavigateBackWithFallback = typeof navigateBackWithFallback;
