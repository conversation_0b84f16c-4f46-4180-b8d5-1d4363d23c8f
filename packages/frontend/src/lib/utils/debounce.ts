import { useDebounce as useDebounceLib, useDebouncedCallback as useDebouncedCallbackLib } from 'use-debounce';
import { useCallback, useState } from 'react';

/**
 * Predefined debounce delays for consistent usage across the application
 */
export const DEBOUNCE_DELAYS = {
  /** Fast debounce for real-time search and input validation (300ms) */
  SEARCH: 300,
  /** Medium debounce for form validation and API calls (500ms) */
  VALIDATION: 500,
  /** Slow debounce for expensive operations (1000ms) */
  EXPENSIVE: 1000,
} as const;

/**
 * Options for debounce utilities
 */
export interface DebounceOptions {
  /** Leading edge execution */
  leading?: boolean;
  /** Trailing edge execution */
  trailing?: boolean;
  /** Maximum wait time */
  maxWait?: number;
}

/**
 * Custom hook that debounces a value using the use-debounce library
 * 
 * @param value - The value to debounce
 * @param delay - The delay in milliseconds (use DEBOUNCE_DELAYS constants for consistency)
 * @param options - Additional debounce options
 * @returns The debounced value
 * 
 * @example
 * ```tsx
 * const [searchTerm, setSearchTerm] = useState('');
 * const debouncedSearchTerm = useDebounce(searchTerm, DEBOUNCE_DELAYS.SEARCH);
 * ```
 */
export function useDebounce<T>(
  value: T,
  delay: number = DEBOUNCE_DELAYS.SEARCH,
  options?: DebounceOptions
): T {
  const [debouncedValue] = useDebounceLib(value, delay, options);
  return debouncedValue;
}

/**
 * Hook that debounces a callback function
 * 
 * @param callback - The function to debounce
 * @param delay - The delay in milliseconds (use DEBOUNCE_DELAYS constants for consistency)
 * @param options - Additional debounce options
 * @returns The debounced callback function
 * 
 * @example
 * ```tsx
 * const debouncedSearch = useDebouncedCallback(
 *   (searchTerm: string) => {
 *     // Perform search API call
 *     searchProducts(searchTerm);
 *   },
 *   DEBOUNCE_DELAYS.SEARCH
 * );
 * ```
 */
export function useDebouncedCallback<T extends (...args: any[]) => any>(
  callback: T,
  delay: number = DEBOUNCE_DELAYS.SEARCH,
  options?: DebounceOptions
) {
  return useDebouncedCallbackLib(callback, delay, options);
}

/**
 * Hook that provides both debounced value and a setter function
 * Useful for controlled components that need both immediate and debounced values
 * 
 * @param initialValue - Initial value
 * @param delay - The delay in milliseconds (use DEBOUNCE_DELAYS constants for consistency)
 * @param options - Additional debounce options
 * @returns Object with current value, debounced value, and setter function
 * 
 * @example
 * ```tsx
 * const { value, debouncedValue, setValue } = useDebouncedValue('', DEBOUNCE_DELAYS.SEARCH);
 * 
 * // Use value for immediate UI updates
 * <Input value={value} onChange={(e) => setValue(e.target.value)} />
 * 
 * // Use debouncedValue for API calls
 * useEffect(() => {
 *   if (debouncedValue) {
 *     searchAPI(debouncedValue);
 *   }
 * }, [debouncedValue]);
 * ```
 */
export function useDebouncedValue<T>(
  initialValue: T,
  delay: number = DEBOUNCE_DELAYS.SEARCH,
  options?: DebounceOptions
) {
  const [value, setValue] = useState<T>(initialValue);
  const debouncedValue = useDebounce(value, delay, options);

  return {
    /** Current immediate value */
    value,
    /** Debounced value */
    debouncedValue,
    /** Function to update the value */
    setValue,
  };
}

/**
 * Hook for search functionality with common patterns
 * Provides both immediate and debounced search terms with built-in reset functionality
 * 
 * @param initialSearchTerm - Initial search term
 * @param delay - The delay in milliseconds (defaults to DEBOUNCE_DELAYS.SEARCH)
 * @returns Object with search utilities
 * 
 * @example
 * ```tsx
 * const { searchTerm, debouncedSearchTerm, setSearchTerm, clearSearch } = useSearchDebounce();
 * 
 * <Input 
 *   value={searchTerm} 
 *   onChange={(e) => setSearchTerm(e.target.value)}
 *   placeholder="Cari produk..."
 * />
 * 
 * // Use debouncedSearchTerm for API calls
 * const { data } = useQuery({
 *   queryKey: ['products', debouncedSearchTerm],
 *   queryFn: () => searchProducts(debouncedSearchTerm),
 * });
 * ```
 */
export function useSearchDebounce(
  initialSearchTerm: string = '',
  delay: number = DEBOUNCE_DELAYS.SEARCH
) {
  const [searchTerm, setSearchTerm] = useState<string>(initialSearchTerm);
  const debouncedSearchTerm = useDebounce(searchTerm, delay);

  const clearSearch = useCallback(() => {
    setSearchTerm('');
  }, []);

  return {
    /** Current search term (immediate) */
    searchTerm,
    /** Debounced search term for API calls */
    debouncedSearchTerm,
    /** Function to update search term */
    setSearchTerm,
    /** Function to clear search term */
    clearSearch,
  };
}

/**
 * Hook for form validation with debouncing
 * Useful for real-time validation that shouldn't trigger on every keystroke
 * 
 * @param validationFn - Function that performs validation
 * @param delay - The delay in milliseconds (defaults to DEBOUNCE_DELAYS.VALIDATION)
 * @returns Debounced validation function
 * 
 * @example
 * ```tsx
 * const validateProductCode = useDebouncedValidation(
 *   async (code: string) => {
 *     const isUnique = await checkProductCodeUniqueness(code);
 *     return isUnique ? null : 'Kode produk sudah digunakan';
 *   },
 *   DEBOUNCE_DELAYS.VALIDATION
 * );
 * 
 * // Use in form field
 * useEffect(() => {
 *   if (productCode) {
 *     validateProductCode(productCode);
 *   }
 * }, [productCode]);
 * ```
 */
export function useDebouncedValidation<T extends any[], R>(
  validationFn: (...args: T) => R,
  delay: number = DEBOUNCE_DELAYS.VALIDATION
) {
  return useDebouncedCallback(validationFn, delay);
}

// Re-export the original use-debounce hooks for advanced use cases
export {
  useDebounce as useDebounceOriginal,
  useDebouncedCallback as useDebouncedCallbackOriginal
} from 'use-debounce';
