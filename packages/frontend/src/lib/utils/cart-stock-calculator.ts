/**
 * Utility functions for calculating available stock considering cart contents
 * This ensures unit selectors show accurate availability after accounting for items already in cart
 */

export interface CartItem {
  productId: string;
  unitId: string;
  quantity: number;
  conversionFactor: number;
}

export interface UnitHierarchy {
  unitId: string;
  conversionFactor: number;
  level: number;
}

export interface StockInfo {
  quantityOnHand: number;
  quantityAllocated: number;
  availableStock: number;
}

/**
 * Calculate the total quantity in base units for a specific product in the cart
 * @param cartItems - All items in the cart
 * @param productId - The product ID to calculate for
 * @param unitHierarchies - Unit hierarchy information for the product
 * @returns Total quantity in base units
 */
export function calculateCartQuantityInBaseUnits(
  cartItems: CartItem[],
  productId: string,
  unitHierarchies: UnitHierarchy[]
): number {
  // Filter cart items for this specific product
  const productCartItems = cartItems.filter(item => item.productId === productId);

  let totalBaseUnits = 0;

  for (const cartItem of productCartItems) {
    // Find the unit hierarchy for this cart item's unit
    const unitHierarchy = unitHierarchies.find(uh => uh.unitId === cartItem.unitId);

    if (unitHierarchy) {
      // Convert cart item quantity to base units
      const baseUnitsForThisItem = cartItem.quantity * unitHierarchy.conversionFactor;
      totalBaseUnits += baseUnitsForThisItem;
    }
  }

  return totalBaseUnits;
}

/**
 * Calculate available stock for each unit considering cart contents
 * @param originalStockData - Original stock data from API
 * @param cartItems - All items in the cart
 * @param productId - The product ID to calculate for
 * @param unitHierarchies - Unit hierarchy information for the product
 * @returns Updated stock data with cart impact
 */
export function calculateAvailableStockWithCartImpact(
  originalStockData: Record<string, StockInfo>,
  cartItems: CartItem[],
  productId: string,
  unitHierarchies: UnitHierarchy[]
): Record<string, StockInfo> {
  // Calculate total cart quantity in base units
  const cartQuantityInBaseUnits = calculateCartQuantityInBaseUnits(
    cartItems,
    productId,
    unitHierarchies
  );

  // If no items in cart for this product, return original data
  if (cartQuantityInBaseUnits === 0) {
    return originalStockData;
  }

  // Find the base unit (level 0)
  const baseUnitHierarchy = unitHierarchies.find(uh => uh.level === 0);
  if (!baseUnitHierarchy) {
    return originalStockData;
  }

  // Get original base unit stock
  const originalBaseStock = originalStockData[baseUnitHierarchy.unitId];
  if (!originalBaseStock) {
    return originalStockData;
  }

  // Calculate remaining base stock after cart impact
  const remainingBaseStock = Math.max(0, originalBaseStock.availableStock - cartQuantityInBaseUnits);

  // Calculate updated stock for each unit
  const updatedStockData: Record<string, StockInfo> = {};

  for (const unitHierarchy of unitHierarchies) {
    const originalUnitStock = originalStockData[unitHierarchy.unitId];

    if (originalUnitStock) {
      // Calculate how many whole units are available with the remaining base stock
      const availableUnits = Math.floor(remainingBaseStock / unitHierarchy.conversionFactor);

      updatedStockData[unitHierarchy.unitId] = {
        quantityOnHand: originalUnitStock.quantityOnHand, // Keep original for reference
        quantityAllocated: originalUnitStock.quantityAllocated, // Keep original for reference
        availableStock: availableUnits // Updated with cart impact
      };
    }
  }

  return updatedStockData;
}

/**
 * Convert cart items to the format expected by cart stock calculator
 * @param cartItems - Cart items from POS system
 * @returns Converted cart items
 */
export function convertPosCartItemsToCalculatorFormat(
  cartItems: Array<{
    productId: string;
    unitId: string;
    quantity: number;
    availableUnits: Array<{
      id: string;
      conversionFactor?: number;
    }>;
  }>
): CartItem[] {
  return cartItems.map(item => {
    // Find the conversion factor for this unit
    const unitInfo = item.availableUnits.find(unit => unit.id === item.unitId);
    const conversionFactor = unitInfo?.conversionFactor || 1;

    return {
      productId: item.productId,
      unitId: item.unitId,
      quantity: item.quantity,
      conversionFactor
    };
  });
}

/**
 * Get unit hierarchies from product data
 * @param product - Product data with unit hierarchies
 * @returns Unit hierarchies in the format expected by calculator
 */
export function extractUnitHierarchies(
  product: {
    unitHierarchies?: Array<{
      unitId: string;
      conversionFactor: number | string;
      level: number;
    }>;
  }
): UnitHierarchy[] {
  if (!product.unitHierarchies) {
    return [];
  }

  return product.unitHierarchies.map(uh => ({
    unitId: uh.unitId,
    conversionFactor: Number(uh.conversionFactor),
    level: uh.level
  }));
}

/**
 * Debug function to log cart impact calculations
 * @param productId - Product ID
 * @param cartItems - Cart items
 * @param unitHierarchies - Unit hierarchies
 * @param originalStock - Original stock data
 * @param updatedStock - Updated stock data
 */
export function debugCartStockCalculation(
  productId: string,
  cartItems: CartItem[],
  unitHierarchies: UnitHierarchy[],
  originalStock: Record<string, StockInfo>,
  updatedStock: Record<string, StockInfo>
): void {
  if (process.env.NODE_ENV === 'development') {
    const cartQuantity = calculateCartQuantityInBaseUnits(cartItems, productId, unitHierarchies);

    console.group(`🛒 Cart Stock Impact for Product ${productId}`);
    console.log('Cart quantity in base units:', cartQuantity);
    console.log('Unit hierarchies:', unitHierarchies);
    console.log('Original stock:', originalStock);
    console.log('Updated stock:', updatedStock);
    console.groupEnd();
  }
}
