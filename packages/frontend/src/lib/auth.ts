import { NextAuthOptions } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import axios from 'axios';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        try {
          const response = await axios.post(`${API_URL}/auth/login`, {
            email: credentials.email,
            password: credentials.password,
          });

          const { user, accessToken } = response.data;

          if (user && accessToken) {
            return {
              id: user.id,
              email: user.email,
              firstName: user.firstName,
              lastName: user.lastName,
              name: user.firstName && user.lastName ? `${user.firstName} ${user.lastName}` : user.email,
              avatar: user.avatar,
              dateOfBirth: user.dateOfBirth,
              phoneNumber: user.phoneNumber,
              address: user.address,
              role: user.role,
              lastLoginAt: user.lastLoginAt,
              accessToken,
            };
          }

          return null;
        } catch (error) {
          console.error('Authentication error:', error);
          return null;
        }
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id; // Set the user ID in the token
        token.accessToken = (user as any).accessToken;
        token.firstName = (user as any).firstName;
        token.lastName = (user as any).lastName;
        token.avatar = (user as any).avatar;
        token.dateOfBirth = (user as any).dateOfBirth;
        token.phoneNumber = (user as any).phoneNumber;
        token.address = (user as any).address;
        token.role = (user as any).role;
        token.lastLoginAt = (user as any).lastLoginAt;
      }
      return token;
    },
    async session({ session, token }) {
      if (token.accessToken) {
        (session as any).accessToken = token.accessToken;
      }
      if (session.user) {
        (session.user as any).id = token.id; // Set the user ID in the session
        (session.user as any).firstName = token.firstName;
        (session.user as any).lastName = token.lastName;
        (session.user as any).avatar = token.avatar;
        (session.user as any).dateOfBirth = token.dateOfBirth;
        (session.user as any).phoneNumber = token.phoneNumber;
        (session.user as any).address = token.address;
        (session.user as any).role = token.role;
        (session.user as any).lastLoginAt = token.lastLoginAt;
      }
      return session;
    },
  },
  pages: {
    signIn: '/auth/login',
  },
  session: {
    strategy: 'jwt',
  },
  secret: process.env.NEXTAUTH_SECRET,
};
