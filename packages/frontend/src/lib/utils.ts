import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Indonesian formatting utilities
export function formatCurrency(amount: number | null | undefined): string {
  // Handle edge cases
  if (amount === null || amount === undefined || isNaN(amount)) {
    return 'Rp 0';
  }

  // Handle very large numbers (prevent overflow)
  const maxSafeAmount = Number.MAX_SAFE_INTEGER;
  const safeAmount = Math.abs(amount) > maxSafeAmount ? maxSafeAmount : amount;

  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(safeAmount);
}

export function formatDate(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return new Intl.DateTimeFormat('id-ID', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
  }).format(dateObj);
}

export function formatDateTime(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return new Intl.DateTimeFormat('id-ID', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    timeZoneName: 'short',
  }).format(dateObj);
}

export function formatTime(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return new Intl.DateTimeFormat('id-ID', {
    hour: '2-digit',
    minute: '2-digit',
    timeZoneName: 'short',
  }).format(dateObj);
}

export function formatNumber(num: number | null | undefined): string {
  // Handle edge cases
  if (num === null || num === undefined || isNaN(num)) {
    return '0';
  }

  return new Intl.NumberFormat('id-ID').format(num);
}

export function getGreeting(): string {
  const hour = new Date().getHours();
  if (hour < 12) return 'Selamat pagi';
  if (hour < 15) return 'Selamat siang';
  if (hour < 18) return 'Selamat sore';
  return 'Selamat malam';
}

export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Transform null values to undefined recursively
 * This is useful for converting API responses (which uses null) to form data (which expects undefined)
 *
 * Notes:
 * - API has been using null for optional fields, but frontend uses undefined
 * - This function is used to transform null to undefined in API responses as interceptor
 * 
 * Features:
 * - Handles nested objects and arrays
 * - Preserves Date objects and other special types
 * - Optimized for performance with early returns
 * - Handles circular references safely
 */
export function nullToUndefined<T>(obj: T, visited = new WeakSet()): T {
  // Handle primitive values and null
  if (obj === null) {
    return undefined as T;
  }

  // Handle non-object types (primitives, functions, etc.)
  if (typeof obj !== 'object' || obj === undefined) {
    return obj;
  }

  // Handle circular references
  if (visited.has(obj as any)) {
    return obj;
  }
  visited.add(obj as any);

  // Handle arrays
  if (Array.isArray(obj)) {
    const result = obj.map(item => nullToUndefined(item, visited)) as T;
    visited.delete(obj as any);
    return result;
  }

  // Handle Date objects and other special objects that shouldn't be transformed
  if (obj instanceof Date || obj instanceof RegExp || obj instanceof Error) {
    visited.delete(obj as any);
    return obj;
  }

  // Handle plain objects
  const result = {} as T;
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      (result as any)[key] = nullToUndefined((obj as any)[key], visited);
    }
  }

  visited.delete(obj as any);
  return result;
}

/**
 * Type utility that transforms null to undefined in type definitions
 * This reflects what the API interceptor does at runtime
 */
export type ApiTransformed<T> = T extends null
  ? undefined
  : T extends (infer U)[]
  ? ApiTransformed<U>[]
  : T extends Date
  ? T
  : T extends object
  ? { [K in keyof T]: ApiTransformed<T[K]> }
  : T;

