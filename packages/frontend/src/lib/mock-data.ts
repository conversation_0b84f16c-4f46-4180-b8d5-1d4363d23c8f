import { DashboardData, RecentActivity, QuickAction } from '@/types/Dashboard';

export const mockDashboardStats = {
  dailySales: 8750000, // Rp 8.750.000
  lowStock: 12,
  pendingOrders: 5,
  activeCustomers: 156,
};

export const mockRecentActivities: RecentActivity[] = [
  {
    id: '1',
    type: 'sale',
    title: 'Penjualan Paracetamol 500mg',
    description: 'Terjual 3 strip kepada Ibu Sari',
    timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(), // 15 minutes ago
    amount: 45000,
  },
  {
    id: '2',
    type: 'stock',
    title: 'Stok Amoxicillin Menipis',
    description: 'Tersisa 8 box, perlu restok segera',
    timestamp: new Date(Date.now() - 45 * 60 * 1000).toISOString(), // 45 minutes ago
  },
  {
    id: '3',
    type: 'order',
    title: 'Pesanan dari PT Kimia Farma',
    description: 'Pesanan obat generik senilai Rp 2.500.000',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
    amount: 2500000,
  },
  {
    id: '4',
    type: 'customer',
    title: 'Pelanggan Baru Terdaftar',
    description: 'Bapak Ahmad Wijaya bergabung sebagai member',
    timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(), // 3 hours ago
  },
  {
    id: '5',
    type: 'sale',
    title: 'Penjualan Vitamin C',
    description: 'Terjual 5 botol vitamin C 1000mg',
    timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // 4 hours ago
    amount: 125000,
  },
];

export const mockQuickActions: QuickAction[] = [
  {
    id: '1',
    title: 'Tambah Obat Baru',
    description: 'Daftarkan obat baru ke inventori',
    icon: 'Plus',
    href: '/dashboard/obat/tambah',
    variant: 'default',
  },
  {
    id: '2',
    title: 'Proses Penjualan',
    description: 'Mulai transaksi penjualan baru',
    icon: 'ShoppingCart',
    href: '/dashboard/penjualan/baru',
    variant: 'default',
  },
  {
    id: '3',
    title: 'Cek Stok Menipis',
    description: 'Lihat daftar obat yang perlu restok',
    icon: 'AlertTriangle',
    href: '/dashboard/inventori/stok-menipis',
    variant: 'secondary',
  },
  {
    id: '4',
    title: 'Laporan Harian',
    description: 'Buat laporan penjualan hari ini',
    icon: 'FileText',
    href: '/dashboard/laporan/harian',
    variant: 'outline',
  },
];

export const mockDashboardData: DashboardData = {
  stats: mockDashboardStats,
  recentActivities: mockRecentActivities,
  quickActions: mockQuickActions,
};

// Indonesian medicine names and data
export const mockMedicines = [
  { name: 'Paracetamol 500mg', stock: 150, price: 15000 },
  { name: 'Amoxicillin 500mg', stock: 8, price: 25000 },
  { name: 'Vitamin C 1000mg', stock: 75, price: 25000 },
  { name: 'Antasida DOEN', stock: 45, price: 12000 },
  { name: 'OBH Combi', stock: 30, price: 18000 },
  { name: 'Betadine Solution', stock: 20, price: 35000 },
  { name: 'Bodrex Extra', stock: 60, price: 8000 },
  { name: 'Promag Tablet', stock: 40, price: 15000 },
];

export const mockCustomers = [
  'Ibu Sari Dewi',
  'Bapak Ahmad Wijaya',
  'Ibu Rina Sari',
  'Bapak Budi Santoso',
  'Ibu Maya Putri',
  'Bapak Dedi Kurniawan',
  'Ibu Fitri Handayani',
  'Bapak Eko Prasetyo',
];

export const mockSuppliers = [
  'PT Kimia Farma',
  'PT Kalbe Farma',
  'PT Dexa Medica',
  'PT Sanbe Farma',
  'PT Indofarma',
  'PT Pharos Indonesia',
  'PT Bernofarm',
  'PT Combiphar',
];
