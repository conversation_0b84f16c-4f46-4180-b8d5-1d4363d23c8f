import { IntegrationTestSetup, TestContext, expectValidationError, expectUnauthorized, expectForbidden, expectNotFound, expectConflict, expectSuccess } from './test-setup';
import { SaleStatus } from '@prisma/client';

describe('Sale Deletion Integration Tests', () => {
  let testSetup: IntegrationTestSetup;
  let ctx: TestContext;
  let testCustomerId: string;
  let testProductId: string;
  let testUnitId: string;
  let testSupplierId: string;
  
  let draftSaleId: string;
  let cancelledSaleId: string;
  let completedSaleId: string;
  let draftSaleIds: string[] = [];

  beforeAll(async () => {
    testSetup = new IntegrationTestSetup();
    ctx = await testSetup.setup();

    // Create test data needed for sales
    const supplier = await testSetup.createTestSupplier(ctx.users.admin.id);
    testSupplierId = supplier.id;

    const baseUnit = await testSetup.createTestProductUnit(ctx.users.admin.id);
    const product = await testSetup.createTestProduct(baseUnit.id, ctx.users.admin.id);
    testProductId = product.id;
    testUnitId = baseUnit.id;

    // Create inventory item with stock
    await testSetup.createTestInventoryItem(testProductId, testUnitId, testSupplierId, ctx.users.admin.id);

    const customer = await testSetup.createTestCustomer(ctx.users.admin.id);
    testCustomerId = customer.id;

    // Create a draft sale
    const draftSaleResponse = await ctx.request
      .post('/api/sales/draft')
      .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
      .send({
        customerName: 'Draft Sale for Deletion Test',
        paymentMethod: 'CASH',
        amountPaid: 10000,
        items: [
          {
            productId: testProductId,
            unitId: testUnitId,
            quantity: 1,
            unitPrice: 7500,
          },
        ],
      });
    
    draftSaleId = draftSaleResponse.body.id;

    // Create a completed sale
    const completedSaleResponse = await ctx.request
      .post('/api/sales')
      .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
      .send({
        customerName: 'Completed Sale for Deletion Test',
        paymentMethod: 'CASH',
        amountPaid: 10000,
        items: [
          {
            productId: testProductId,
            unitId: testUnitId,
            quantity: 1,
            unitPrice: 7500,
          },
        ],
      });
    
    completedSaleId = completedSaleResponse.body.id;

    // Create a sale that will be cancelled
    const saleForCancellationResponse = await ctx.request
      .post('/api/sales/draft')
      .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
      .send({
        customerName: 'Sale for Cancellation Test',
        paymentMethod: 'CASH',
        amountPaid: 10000,
        items: [
          {
            productId: testProductId,
            unitId: testUnitId,
            quantity: 1,
            unitPrice: 7500,
          },
        ],
      });
    
    const saleForCancellationId = saleForCancellationResponse.body.id;

    // Complete the sale first (since we can only cancel completed sales or drafts)
    await ctx.request
      .patch(`/api/sales/${saleForCancellationId}/complete`)
      .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);
    
    // Then cancel the sale
    const cancelResponse = await ctx.request
      .patch(`/api/sales/${saleForCancellationId}/cancel`)
      .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
      .send({ reason: 'Test cancellation for deletion test' });
    
    cancelledSaleId = cancelResponse.body.id;

    // Create multiple draft sales for bulk deletion test
    for (let i = 0; i < 3; i++) {
      const response = await ctx.request
        .post('/api/sales/draft')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send({
          customerName: `Bulk Draft ${i + 1}`,
          paymentMethod: 'CASH',
          amountPaid: 10000,
          items: [
            {
              productId: testProductId,
              unitId: testUnitId,
              quantity: 1,
              unitPrice: 7500,
            },
          ],
        });
      
      draftSaleIds.push(response.body.id);
    }
  });

  afterAll(async () => {
    await testSetup.teardown();
  });

  describe('DELETE /api/sales/:id', () => {
    it('should delete a draft sale successfully', async () => {
      const response = await ctx.request
        .delete(`/api/sales/${draftSaleId}`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({ reason: 'Test deletion of draft sale' });

      expectSuccess(response);
      expect(response.body.success).toBe(true);
      expect(response.body.deletedSaleId).toBe(draftSaleId);
      
      // Verify sale no longer exists
      const verifyResponse = await ctx.request
        .get(`/api/sales/${draftSaleId}`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);
      
      expectNotFound(verifyResponse);
    });

    it('should delete a cancelled sale successfully and deallocate stock', async () => {
      const response = await ctx.request
        .delete(`/api/sales/${cancelledSaleId}`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({ reason: 'Test deletion of cancelled sale' });

      expectSuccess(response);
      expect(response.body.success).toBe(true);
      expect(response.body.deletedSaleId).toBe(cancelledSaleId);
      
      // Verify sale no longer exists
      const verifyResponse = await ctx.request
        .get(`/api/sales/${cancelledSaleId}`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);
      
      expectNotFound(verifyResponse);
    });

    it('should fail to delete a completed sale', async () => {
      const response = await ctx.request
        .delete(`/api/sales/${completedSaleId}`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({ reason: 'Test deletion of completed sale' });

      expectConflict(response);
      expect(response.body.message).toContain('tidak dapat dihapus');
      
      // Verify sale still exists
      const verifyResponse = await ctx.request
        .get(`/api/sales/${completedSaleId}`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);
      
      expectSuccess(verifyResponse);
    });

    it('should fail to delete a non-existent sale', async () => {
      const nonExistentId = 'non-existent-id';
      const response = await ctx.request
        .delete(`/api/sales/${nonExistentId}`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({ reason: 'Test deletion of non-existent sale' });

      expectNotFound(response);
    });

    it('should fail when deleting a sale without proper permissions', async () => {
      // Create a new draft sale for this test
      const draftSaleResponse = await ctx.request
        .post('/api/sales/draft')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send({
          customerName: 'Permission Test Sale',
          paymentMethod: 'CASH',
          amountPaid: 10000,
          items: [
            {
              productId: testProductId,
              unitId: testUnitId,
              quantity: 1,
              unitPrice: 7500,
            },
          ],
        });
      
      const testSaleId = draftSaleResponse.body.id;
      
      // Cashiers shouldn't be able to delete sales
      const response = await ctx.request
        .delete(`/api/sales/${testSaleId}`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send({ reason: 'Test deletion without permissions' });

      expectForbidden(response);
      
      // Clean up the test sale using admin user
      await ctx.request
        .delete(`/api/sales/${testSaleId}`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({ reason: 'Cleanup' });
    });

    it('should fail without authentication', async () => {
      // Create a new draft sale for this test
      const draftSaleResponse = await ctx.request
        .post('/api/sales/draft')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send({
          customerName: 'Auth Test Sale',
          paymentMethod: 'CASH',
          amountPaid: 10000,
          items: [
            {
              productId: testProductId,
              unitId: testUnitId,
              quantity: 1,
              unitPrice: 7500,
            },
          ],
        });
      
      const testSaleId = draftSaleResponse.body.id;
      
      const response = await ctx.request
        .delete(`/api/sales/${testSaleId}`)
        .send({ reason: 'Test deletion without authentication' });

      expectUnauthorized(response);
      
      // Clean up the test sale using admin user
      await ctx.request
        .delete(`/api/sales/${testSaleId}`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({ reason: 'Cleanup' });
    });
  });

  describe('DELETE /api/sales/bulk', () => {
    it('should bulk delete multiple draft sales', async () => {
      const response = await ctx.request
        .delete(`/api/sales/bulk`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({ 
          saleIds: draftSaleIds,
          reason: 'Test bulk deletion' 
        });

      expectSuccess(response);
      expect(response.body.deleted).toBe(draftSaleIds.length);
      expect(response.body.failed).toBe(0);
      
      // Verify sales no longer exist
      for (const saleId of draftSaleIds) {
        const verifyResponse = await ctx.request
          .get(`/api/sales/${saleId}`)
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);
        
        expectNotFound(verifyResponse);
      }
    });

    it('should fail bulk deletion with empty sales array', async () => {
      const response = await ctx.request
        .delete(`/api/sales/bulk`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({ 
          saleIds: [],
          reason: 'Test empty bulk deletion' 
        });

      expectValidationError(response);
    });

    it('should handle mixed status sales during bulk deletion', async () => {
      // Create a new draft sale and a new completed sale
      const draftResponse = await ctx.request
        .post('/api/sales/draft')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send({
          customerName: 'Mixed Status Test - Draft',
          paymentMethod: 'CASH',
          amountPaid: 10000,
          items: [
            {
              productId: testProductId,
              unitId: testUnitId,
              quantity: 1,
              unitPrice: 7500,
            },
          ],
        });
      
      const completedResponse = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send({
          customerName: 'Mixed Status Test - Completed',
          paymentMethod: 'CASH',
          amountPaid: 10000,
          items: [
            {
              productId: testProductId,
              unitId: testUnitId,
              quantity: 1,
              unitPrice: 7500,
            },
          ],
        });
      
      const mixedSaleIds = [draftResponse.body.id, completedResponse.body.id];
      
      const response = await ctx.request
        .delete(`/api/sales/bulk`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({ 
          saleIds: mixedSaleIds,
          reason: 'Test mixed bulk deletion' 
        });

      expectSuccess(response);
      expect(response.body.deleted).toBe(1); // Only the draft should be deleted
      expect(response.body.failed).toBe(1); // The completed sale should fail
      
      // Verify draft no longer exists
      const verifyDraftResponse = await ctx.request
        .get(`/api/sales/${draftResponse.body.id}`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);
      
      expectNotFound(verifyDraftResponse);
      
      // Verify completed sale still exists
      const verifyCompletedResponse = await ctx.request
        .get(`/api/sales/${completedResponse.body.id}`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);
      
      expectSuccess(verifyCompletedResponse);
    });

    it('should fail bulk deletion without proper permissions', async () => {
      // Create test sales for this test
      const testSaleIds: string[] = [];
      for (let i = 0; i < 2; i++) {
        const response = await ctx.request
          .post('/api/sales/draft')
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send({
            customerName: `Permission Test Bulk ${i + 1}`,
            paymentMethod: 'CASH',
            amountPaid: 10000,
            items: [
              {
                productId: testProductId,
                unitId: testUnitId,
                quantity: 1,
                unitPrice: 7500,
              },
            ],
          });
        
        testSaleIds.push(response.body.id);
      }
      
      // Cashiers shouldn't be able to bulk delete sales
      const response = await ctx.request
        .delete(`/api/sales/bulk`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send({ 
          saleIds: testSaleIds,
          reason: 'Test bulk deletion without permissions' 
        });

      expectForbidden(response);
      
      // Clean up the test sales using admin user
      await ctx.request
        .delete(`/api/sales/bulk`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({ 
          saleIds: testSaleIds,
          reason: 'Cleanup' 
        });
    });

    it('should fail bulk deletion without authentication', async () => {
      // Create test sales for this test
      const testSaleIds: string[] = [];
      for (let i = 0; i < 2; i++) {
        const response = await ctx.request
          .post('/api/sales/draft')
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send({
            customerName: `Auth Test Bulk ${i + 1}`,
            paymentMethod: 'CASH',
            amountPaid: 10000,
            items: [
              {
                productId: testProductId,
                unitId: testUnitId,
                quantity: 1,
                unitPrice: 7500,
              },
            ],
          });
        
        testSaleIds.push(response.body.id);
      }
      
      const response = await ctx.request
        .delete(`/api/sales/bulk`)
        .send({ 
          saleIds: testSaleIds,
          reason: 'Test bulk deletion without authentication' 
        });

      expectUnauthorized(response);
      
      // Clean up the test sales using admin user
      await ctx.request
        .delete(`/api/sales/bulk`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({ 
          saleIds: testSaleIds,
          reason: 'Cleanup' 
        });
    });
  });
}); 