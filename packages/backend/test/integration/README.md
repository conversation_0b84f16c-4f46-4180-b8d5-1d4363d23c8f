# Integration Test Suite

This comprehensive integration test suite tests all backend API endpoints without mocking or patching, using real database interactions to identify bugs and validate business logic.

## 📋 Test Coverage

### Authentication Module (`/api/auth`)
- ✅ User registration with validation
- ✅ User login with credential validation  
- ✅ JWT token generation and validation
- ✅ Profile retrieval and updates
- ✅ Password hashing and security
- ✅ Role-based access control

### Settings Module (`/api/settings`)
- ✅ Pharmacy settings retrieval
- ✅ Settings updates (admin-only)
- ✅ Settings persistence
- ✅ Authorization validation

### Suppliers Module (`/api/suppliers`)
- ✅ Supplier CRUD operations
- ✅ Advanced filtering and search
- ✅ Pagination and sorting
- ✅ Statistics and analytics
- ✅ Role-based permissions
- ✅ Indonesian pharmacy validations (NPWP, PBF types)

### Supplier Documents (`/api/suppliers/:id/documents`)
- ✅ Document upload and management
- ✅ File type validation
- ✅ Document metadata handling
- ✅ Download functionality
- ✅ Expiry date tracking

### Supplier Payments (`/api/suppliers/:id/payments`)
- ✅ Payment creation and updates
- ✅ Payment method validation
- ✅ Amount and date validation
- ✅ Payment status tracking
- ✅ Financial summaries and analytics

### App Root (`/api`)
- ✅ Health check endpoint
- ✅ Rate limiting validation
- ✅ CORS configuration
- ✅ Global validation pipes
- ✅ Error handling
- ✅ Security headers

## 🚀 Running Tests

### Prerequisites
1. PostgreSQL database running
2. Test database configured
3. Environment variables set

### Quick Start
```bash
# Run all integration tests
pnpm test:integration

# Run with coverage report
pnpm test:integration:coverage

# Run in watch mode for development
pnpm test:integration:watch

# Run all tests (unit + integration)
pnpm test:all
```

### Manual Setup
```bash
# 1. Setup test database
createdb apotek_test

# 2. Run migrations
DATABASE_URL="postgresql://postgres:password@localhost:5432/apotek_test" npx prisma migrate deploy

# 3. Run tests
pnpm test:integration
```

## 🗄️ Database Strategy

### Test Database Management
- **Fresh Instance**: Each test run creates a clean database state
- **Isolation**: Tests run sequentially to avoid conflicts
- **Cleanup**: Automatic cleanup after each test suite
- **Migrations**: Automatic migration deployment

### Data Seeding
- **Test Users**: Admin, Pharmacist, and Cashier roles
- **Authentication**: Real JWT tokens for API calls
- **Test Data**: Realistic Indonesian pharmacy data
- **Relationships**: Proper foreign key relationships

## 🔒 Authentication Testing

### User Roles Tested
- **Admin**: Full access to all endpoints
- **Pharmacist**: Manager-level access (create/update)
- **Cashier**: Read-only access to most endpoints

### Security Validations
- JWT token validation
- Role-based access control
- Input validation and sanitization
- Rate limiting protection
- CORS policy enforcement

## 📊 Test Scenarios

### Happy Path Testing
- Valid inputs with expected success responses
- Proper data creation and retrieval
- Correct business logic execution

### Edge Case Testing
- Boundary values and limits
- Empty inputs and null values
- Maximum field lengths
- Date range validations

### Error Case Testing
- Invalid inputs and malformed data
- Unauthorized access attempts
- Non-existent resource requests
- Validation failures

### Indonesian Pharmacy Specific
- NPWP format validation (XX.XXX.XXX.X-XXX.XXX)
- PBF supplier type handling
- Indonesian address formats
- Currency and decimal handling

## 📈 Performance Metrics

### Response Time Validation
- API response times under 1 second
- Database query optimization
- Pagination performance
- Concurrent request handling

### Load Testing
- Multiple simultaneous requests
- Rate limiting behavior
- Memory usage monitoring
- Database connection pooling

## 🐛 Bug Detection

### Common Issues Caught
- Validation bypass attempts
- SQL injection vulnerabilities
- Authorization bypass
- Data consistency issues
- Memory leaks
- Unhandled exceptions

### Business Logic Validation
- Supplier code uniqueness
- Payment amount calculations
- Document expiry tracking
- Status transitions
- Audit trail integrity

## 📝 Test Reports

### Coverage Reports
- Line coverage: Target 90%+
- Branch coverage: Target 85%+
- Function coverage: Target 95%+
- Statement coverage: Target 90%+

### Test Results
- Total endpoints tested: 25+
- Test scenarios: 150+
- Authentication tests: 30+
- Validation tests: 50+
- Error handling tests: 40+

## 🔧 Configuration

### Environment Variables
```env
NODE_ENV=test
DATABASE_URL=postgresql://postgres:password@localhost:5432/apotek_test
JWT_SECRET=test-jwt-secret-key
JWT_EXPIRES_IN=1h
```

### Jest Configuration
- Test timeout: 30 seconds
- Sequential execution (maxWorkers: 1)
- Coverage collection from src/
- Detailed error reporting

## 🚨 Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Ensure PostgreSQL is running
   - Check DATABASE_URL in .env.test
   - Verify test database exists

2. **Tests Timeout**
   - Increase Jest timeout
   - Check database performance
   - Verify network connectivity

3. **Authentication Failures**
   - Check JWT_SECRET configuration
   - Verify user seeding process
   - Validate token generation

4. **Permission Errors**
   - Verify role assignments
   - Check guard implementations
   - Validate authorization logic

### Debug Mode
```bash
# Run with debug output
DEBUG=* pnpm test:integration

# Run specific test file
npx jest test/integration/auth.integration.spec.ts

# Run with verbose output
pnpm test:integration --verbose
```

## 📚 Best Practices

### Test Organization
- Logical grouping by feature
- Descriptive test names
- Proper setup and teardown
- Independent test execution

### Data Management
- Clean database state
- Realistic test data
- Proper foreign key handling
- Transaction management

### Error Handling
- Comprehensive error scenarios
- Proper status code validation
- Error message verification
- Exception handling

### Maintenance
- Regular test updates
- Performance monitoring
- Coverage tracking
- Documentation updates
