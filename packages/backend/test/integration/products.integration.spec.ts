import { IntegrationTestSetup, TestContext, expectValidationError, expectUnauthorized, expectForbidden, expectNotFound, expectConflict, expectSuccess } from './test-setup';
import { ProductType, ProductCategory, MedicineClassification } from '@prisma/client';

describe('Products Integration Tests', () => {
  let testSetup: IntegrationTestSetup;
  let ctx: TestContext;
  let testProductId: string;
  let testUnitId: string;

  beforeAll(async () => {
    testSetup = new IntegrationTestSetup();
    ctx = await testSetup.setup();

    // Create a test unit for products
    const unit = await ctx.prisma.productUnit.create({
      data: {
        name: 'Test Tablet',
        abbreviation: 'tab',
        type: 'COUNT',
        isBaseUnit: true,
        description: 'Test tablet unit',
      },
    });
    testUnitId = unit.id;
  });

  afterAll(async () => {
    // Clean up test data in correct order (delete products first, then units)
    if (testProductId) {
      await ctx.prisma.productUnitHierarchy.deleteMany({
        where: { productId: testProductId },
      });
      await ctx.prisma.product.deleteMany({
        where: { id: testProductId },
      });
    }

    // Delete all products created by test users
    await ctx.prisma.productUnitHierarchy.deleteMany({
      where: {
        product: {
          OR: [
            { createdBy: ctx.users.admin.id },
            { createdBy: ctx.users.pharmacist.id },
            { createdBy: ctx.users.cashier.id },
          ]
        }
      },
    });

    await ctx.prisma.product.deleteMany({
      where: {
        OR: [
          { createdBy: ctx.users.admin.id },
          { createdBy: ctx.users.pharmacist.id },
          { createdBy: ctx.users.cashier.id },
        ]
      },
    });

    // Now safe to delete the test unit
    await ctx.prisma.productUnit.deleteMany({
      where: { id: testUnitId },
    });

    await testSetup.teardown();
  });

  describe('POST /api/products', () => {
    it('should create a new product as admin', async () => {
      const createProductDto = {
        code: 'TEST-001',
        name: 'Test Paracetamol',
        genericName: 'Paracetamol',
        type: ProductType.MEDICINE,
        category: ProductCategory.ANALGESIC,
        manufacturer: 'Test Pharma',
        bpomNumber: 'DKL1234567890A1',
        medicineClassification: MedicineClassification.OBAT_BEBAS,
        regulatorySymbol: 'Lingkaran hijau - Obat bebas',
        baseUnitId: testUnitId,
        minimumStock: 100,
        maximumStock: 1000,
        reorderPoint: 200,
        description: 'Test paracetamol product',
        activeIngredient: 'Paracetamol 500mg',
        strength: '500mg',
        dosageForm: 'Tablet',
        indication: 'Demam, sakit kepala',
        contraindication: 'Hipersensitif terhadap paracetamol',
        sideEffects: 'Jarang terjadi efek samping',
        dosage: 'Dewasa: 1-2 tablet, 3-4 kali sehari',
        storage: 'Simpan di tempat sejuk dan kering',
        notes: 'Test product notes',
        unitHierarchies: [
          {
            unitId: testUnitId,
            conversionFactor: 1,
            level: 0,
            sellingPrice: 5000,
            costPrice: 3000,
          },
        ],
      };

      const response = await ctx.request
        .post('/api/products')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(createProductDto);

      expectSuccess(response, 201);
      expect(response.body).toHaveProperty('id');
      expect(response.body.code).toBe(createProductDto.code);
      expect(response.body.name).toBe(createProductDto.name);
      expect(response.body.type).toBe(createProductDto.type);
      expect(response.body.category).toBe(createProductDto.category);
      expect(response.body.medicineClassification).toBe(createProductDto.medicineClassification);
      expect(response.body.baseUnit).toHaveProperty('id', testUnitId);
      expect(response.body.unitHierarchies).toHaveLength(1);
      expect(response.body.isActive).toBe(true);
      expect(response.body.createdBy).toBe(ctx.users.admin.id);

      testProductId = response.body.id;
    });

    it('should create product as pharmacist', async () => {
      const createProductDto = {
        code: 'TEST-002',
        name: 'Test Ibuprofen',
        type: ProductType.MEDICINE,
        category: ProductCategory.ANALGESIC,
        medicineClassification: MedicineClassification.OBAT_BEBAS,
        baseUnitId: testUnitId,
      };

      const response = await ctx.request
        .post('/api/products')
        .set('Authorization', `Bearer ${ctx.users.pharmacist.accessToken}`)
        .send(createProductDto);

      expectSuccess(response, 201);
      expect(response.body.createdBy).toBe(ctx.users.pharmacist.id);
    });

    it('should fail for cashier (insufficient permissions)', async () => {
      const createProductDto = {
        code: 'TEST-003',
        name: 'Unauthorized Product',
        type: ProductType.MEDICINE,
        category: ProductCategory.ANALGESIC,
        medicineClassification: MedicineClassification.OBAT_BEBAS,
        baseUnitId: testUnitId,
      };

      const response = await ctx.request
        .post('/api/products')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(createProductDto);

      expectForbidden(response);
    });

    it('should not create product with duplicate code', async () => {
      const createProductDto = {
        code: 'TEST-001', // Same code as above
        name: 'Another Test Product',
        type: ProductType.MEDICINE,
        category: ProductCategory.ANALGESIC,
        medicineClassification: MedicineClassification.OBAT_BEBAS,
        baseUnitId: testUnitId,
      };

      const response = await ctx.request
        .post('/api/products')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(createProductDto);

      expectConflict(response);
    });

    it('should not create product without authentication', async () => {
      const createProductDto = {
        code: 'TEST-004',
        name: 'Unauthorized Product',
        type: ProductType.MEDICINE,
        category: ProductCategory.ANALGESIC,
        medicineClassification: MedicineClassification.OBAT_BEBAS,
        baseUnitId: testUnitId,
      };

      const response = await ctx.request
        .post('/api/products')
        .send(createProductDto);

      expectUnauthorized(response);
    });

    it('should fail with missing required fields', async () => {
      const response = await ctx.request
        .post('/api/products')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({});

      expectValidationError(response);
    });

    it('should fail with invalid product type', async () => {
      const createProductDto = {
        code: 'TEST-005',
        name: 'Invalid Type Product',
        type: 'INVALID_TYPE',
        category: ProductCategory.ANALGESIC,
        medicineClassification: MedicineClassification.OBAT_BEBAS,
        baseUnitId: testUnitId,
      };

      const response = await ctx.request
        .post('/api/products')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(createProductDto);

      expectValidationError(response);
    });
  });

  describe('GET /api/products', () => {
    beforeAll(async () => {
      // Create additional test products for filtering tests
      const products = [
        { code: 'MED001', name: 'Medicine Product', type: ProductType.MEDICINE, category: ProductCategory.ANALGESIC, medicineClassification: MedicineClassification.OBAT_BEBAS, baseUnitId: testUnitId },
        { code: 'DEV001', name: 'Medical Device', type: ProductType.MEDICAL_DEVICE, category: ProductCategory.MEDICAL_DEVICE, medicineClassification: MedicineClassification.NON_MEDICINE, baseUnitId: testUnitId },
        { code: 'SUP001', name: 'Supplement Product', type: ProductType.SUPPLEMENT, category: ProductCategory.SUPPLEMENT, medicineClassification: MedicineClassification.NON_MEDICINE, baseUnitId: testUnitId },
      ];

      for (const product of products) {
        await ctx.request
          .post('/api/products')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(product);
      }
    });

    it('should list all products for authenticated user', async () => {
      const response = await ctx.request
        .get('/api/products')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('meta');
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.meta).toHaveProperty('total');
      expect(response.body.meta).toHaveProperty('page');
      expect(response.body.meta).toHaveProperty('limit');
      expect(response.body.meta.total).toBeGreaterThan(0);
    });

    it('should filter products by type', async () => {
      const response = await ctx.request
        .get('/api/products?type=MEDICINE')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      if (response.body.data.length > 0) {
        expect(response.body.data.every((product: any) => product.type === 'MEDICINE')).toBe(true);
      }
    });

    it('should filter products by category', async () => {
      const response = await ctx.request
        .get('/api/products?category=ANALGESIC')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      if (response.body.data.length > 0) {
        expect(response.body.data.every((product: any) => product.category === 'ANALGESIC')).toBe(true);
      }
    });

    it('should filter products by medicine classification', async () => {
      const response = await ctx.request
        .get('/api/products?medicineClassification=OBAT_BEBAS')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      if (response.body.data.length > 0) {
        expect(response.body.data.every((product: any) => product.medicineClassification === 'OBAT_BEBAS')).toBe(true);
      }
    });

    it('should search products by name', async () => {
      const response = await ctx.request
        .get('/api/products?search=Test Paracetamol')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      if (response.body.data.length > 0) {
        expect(response.body.data.some((product: any) => product.name.includes('Test Paracetamol'))).toBe(true);
      }
    });

    it('should paginate results', async () => {
      const response = await ctx.request
        .get('/api/products?page=1&limit=2')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.data.length).toBeLessThanOrEqual(2);
      expect(response.body.meta.page).toBe(1);
      expect(response.body.meta.limit).toBe(2);
    });

    it('should sort products', async () => {
      const response = await ctx.request
        .get('/api/products?sortBy=name&sortOrder=asc')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      if (response.body.data.length > 1) {
        const names = response.body.data.map((product: any) => product.name);
        const sortedNames = [...names].sort();
        expect(names).toEqual(sortedNames);
      }
    });

    it('should fail without authentication', async () => {
      const response = await ctx.request
        .get('/api/products');

      expectUnauthorized(response);
    });

    it('should validate pagination parameters', async () => {
      const response = await ctx.request
        .get('/api/products?page=0&limit=101')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectValidationError(response);
    });
  });

  describe('GET /api/products/stats', () => {
    it('should get product statistics for authenticated user', async () => {
      const response = await ctx.request
        .get('/api/products/stats')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body).toHaveProperty('total');
      expect(response.body).toHaveProperty('active');
      expect(response.body).toHaveProperty('inactive');
      expect(response.body).toHaveProperty('byType');
      expect(response.body).toHaveProperty('byCategory');
      expect(response.body).toHaveProperty('byMedicineClassification');
      expect(typeof response.body.total).toBe('number');
      expect(typeof response.body.active).toBe('number');
      expect(typeof response.body.inactive).toBe('number');
    });

    it('should fail without authentication', async () => {
      const response = await ctx.request
        .get('/api/products/stats');

      expectUnauthorized(response);
    });
  });

  describe('GET /api/products/:id', () => {
    it('should get a single product for authenticated user', async () => {
      const response = await ctx.request
        .get(`/api/products/${testProductId}`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      expect(response.body.id).toBe(testProductId);
      expect(response.body.code).toBe('TEST-001');
      expect(response.body).toHaveProperty('baseUnit');
      expect(response.body).toHaveProperty('unitHierarchies');
      expect(response.body).toHaveProperty('createdByUser');
    });

    it('should return 404 for non-existent product', async () => {
      const response = await ctx.request
        .get('/api/products/non-existent-id')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectNotFound(response);
    });

    it('should fail without authentication', async () => {
      const response = await ctx.request
        .get(`/api/products/${testProductId}`);

      expectUnauthorized(response);
    });
  });

  describe('PATCH /api/products/:id', () => {
    it('should update product as admin', async () => {
      const updateData = {
        name: 'Updated Test Paracetamol',
        description: 'Updated description',
        minimumStock: 150,
      };

      const response = await ctx.request
        .patch(`/api/products/${testProductId}`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(updateData);

      expectSuccess(response);
      expect(response.body.name).toBe(updateData.name);
      expect(response.body.description).toBe(updateData.description);
      expect(response.body.minimumStock).toBe(updateData.minimumStock);
      expect(response.body.updatedBy).toBe(ctx.users.admin.id);
    });

    it('should update product as pharmacist', async () => {
      const updateData = {
        notes: 'Updated by pharmacist',
      };

      const response = await ctx.request
        .patch(`/api/products/${testProductId}`)
        .set('Authorization', `Bearer ${ctx.users.pharmacist.accessToken}`)
        .send(updateData);

      expectSuccess(response);
      expect(response.body.notes).toBe(updateData.notes);
      expect(response.body.updatedBy).toBe(ctx.users.pharmacist.id);
    });

    it('should fail for cashier (insufficient permissions)', async () => {
      const response = await ctx.request
        .patch(`/api/products/${testProductId}`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send({ name: 'Unauthorized Update' });

      expectForbidden(response);
    });

    it('should fail with non-existent product ID', async () => {
      const response = await ctx.request
        .patch('/api/products/non-existent-id')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({ name: 'Test' });

      expectNotFound(response);
    });

    it('should fail without authentication', async () => {
      const response = await ctx.request
        .patch(`/api/products/${testProductId}`)
        .send({ name: 'Test' });

      expectUnauthorized(response);
    });
  });

  describe('PATCH /api/products/:id/deactivate', () => {
    let productToDeactivate: string;

    beforeEach(async () => {
      const uniqueCode = `DEACT${Date.now()}${Math.floor(Math.random() * 1000)}`;
      const response = await ctx.request
        .post('/api/products')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({
          code: uniqueCode,
          name: 'Product to Deactivate',
          type: ProductType.MEDICINE,
          category: ProductCategory.ANALGESIC,
          medicineClassification: MedicineClassification.OBAT_BEBAS,
          baseUnitId: testUnitId,
        });

      if (response.status !== 201) {
        throw new Error(`Failed to create product for test. Status: ${response.status}, Body: ${JSON.stringify(response.body)}`);
      }

      productToDeactivate = response.body.id;
    });

    it('should deactivate product as admin', async () => {
      const response = await ctx.request
        .patch(`/api/products/${productToDeactivate}/deactivate`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.isActive).toBe(false);
      expect(response.body.updatedBy).toBe(ctx.users.admin.id);
    });

    it('should deactivate product as pharmacist', async () => {
      const response = await ctx.request
        .patch(`/api/products/${productToDeactivate}/deactivate`)
        .set('Authorization', `Bearer ${ctx.users.pharmacist.accessToken}`);

      expectSuccess(response);
      expect(response.body.isActive).toBe(false);
    });

    it('should fail for cashier (insufficient permissions)', async () => {
      const response = await ctx.request
        .patch(`/api/products/${productToDeactivate}/deactivate`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectForbidden(response);
    });

    it('should fail without authentication', async () => {
      const response = await ctx.request
        .patch(`/api/products/${productToDeactivate}/deactivate`);

      expectUnauthorized(response);
    });
  });

  describe('PATCH /api/products/:id/activate', () => {
    let productToActivate: string;

    beforeEach(async () => {
      const uniqueCode = `ACT${Date.now()}${Math.floor(Math.random() * 1000)}`;
      const response = await ctx.request
        .post('/api/products')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({
          code: uniqueCode,
          name: 'Product to Activate',
          type: ProductType.MEDICINE,
          category: ProductCategory.ANALGESIC,
          medicineClassification: MedicineClassification.OBAT_BEBAS,
          baseUnitId: testUnitId,
        });

      productToActivate = response.body.id;

      // Deactivate it first
      await ctx.request
        .patch(`/api/products/${productToActivate}/deactivate`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);
    });

    it('should activate product as admin', async () => {
      const response = await ctx.request
        .patch(`/api/products/${productToActivate}/activate`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.isActive).toBe(true);
    });

    it('should activate product as pharmacist', async () => {
      const response = await ctx.request
        .patch(`/api/products/${productToActivate}/activate`)
        .set('Authorization', `Bearer ${ctx.users.pharmacist.accessToken}`);

      expectSuccess(response);
      expect(response.body.isActive).toBe(true);
    });

    it('should fail for cashier (insufficient permissions)', async () => {
      const response = await ctx.request
        .patch(`/api/products/${productToActivate}/activate`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectForbidden(response);
    });
  });

  describe('DELETE /api/products/:id', () => {
    let productToDelete: string;

    beforeAll(async () => {
      // Create a product specifically for deletion test
      const response = await ctx.request
        .post('/api/products')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({
          code: 'DEL001',
          name: 'Product to Delete',
          type: ProductType.MEDICINE,
          category: ProductCategory.ANALGESIC,
          medicineClassification: MedicineClassification.OBAT_BEBAS,
          baseUnitId: testUnitId,
        });
      productToDelete = response.body.id;
    });

    it('should delete product as admin', async () => {
      const response = await ctx.request
        .delete(`/api/products/${productToDelete}`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expect(response.status).toBe(204);

      // Verify product is hard deleted (completely removed from database)
      const getResponse = await ctx.request
        .get(`/api/products/${productToDelete}`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectNotFound(getResponse);
    });

    it('should fail for pharmacist (insufficient permissions)', async () => {
      const response = await ctx.request
        .delete(`/api/products/${testProductId}`)
        .set('Authorization', `Bearer ${ctx.users.pharmacist.accessToken}`);

      expectForbidden(response);
    });

    it('should fail for cashier (insufficient permissions)', async () => {
      const response = await ctx.request
        .delete(`/api/products/${testProductId}`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectForbidden(response);
    });

    it('should fail with non-existent product ID', async () => {
      const response = await ctx.request
        .delete('/api/products/non-existent-id')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectNotFound(response);
    });

    it('should fail without authentication', async () => {
      const response = await ctx.request
        .delete(`/api/products/${testProductId}`);

      expectUnauthorized(response);
    });
  });
});
