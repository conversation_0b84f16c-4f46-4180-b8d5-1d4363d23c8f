import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { PrismaService } from '../../src/prisma/prisma.service';
import { SalesService } from '../../src/sales/sales.service';
import { InventoryService } from '../../src/inventory/inventory.service';
import { UnitConversionService } from '../../src/common/services/unit-conversion.service';
import { IntegrationTestSetup, TestContext } from './test-setup';
import * as request from 'supertest';

describe('Reproduce Exact Bug Integration Tests', () => {
  let ctx: TestContext;
  let testSetup: IntegrationTestSetup;

  beforeAll(async () => {
    testSetup = new IntegrationTestSetup();
    ctx = await testSetup.setup();
  });

  afterAll(async () => {
    await testSetup.teardown();
  });

  describe('Exact Bug Reproduction', () => {
    it('should reproduce the exact bug scenario with real product IDs', async () => {
      // This test uses the exact same payload and product IDs from the user's report
      const salesService = ctx.app.get<SalesService>(SalesService);
      const inventoryService = ctx.app.get<InventoryService>(InventoryService);
      const unitConversionService = ctx.app.get<UnitConversionService>(UnitConversionService);

      // First, let's check if the product exists in the database
      const productId = "cmbvt6d7v00aippgc3gl01whg";
      const boxUnitId = "cmbvt6d6a009nppgcotog2gnk";
      const stripUnitId = "cmbvt6d62009hppgcky4ce6zy";
      const cashierId = "cmbvt6cyh0007ppgcgr61o06d";

      console.log('🔍 Checking if product exists...');
      
      try {
        // Check if product exists
        const product = await ctx.prisma.product.findUnique({
          where: { id: productId },
          include: {
            baseUnit: true,
            unitHierarchies: {
              include: {
                unit: true
              }
            }
          }
        });

        if (!product) {
          console.log('❌ Product not found in database, creating test data...');
          // Product doesn't exist, so the bug might be in a different environment
          // Let's skip this test or create the product
          return;
        }

        console.log('✅ Product found:', product.name);
        console.log('📊 Unit hierarchies:', product.unitHierarchies.map(h => ({
          unitName: h.unit.name,
          conversionFactor: h.conversionFactor,
          level: h.level
        })));

        // Check current stock
        const currentStock = await inventoryService.getAvailableStock(productId);
        console.log('📦 Current available stock:', currentStock, 'tablets');

        // Check unit conversions
        const boxConversion = await unitConversionService.convertToBaseUnit(productId, boxUnitId, 28);
        const stripConversion = await unitConversionService.convertToBaseUnit(productId, stripUnitId, 22);
        
        console.log('🔄 Box conversion (28 boxes):', boxConversion);
        console.log('🔄 Strip conversion (22 strips):', stripConversion);

        if (boxConversion.success && stripConversion.success) {
          const totalDemand = boxConversion.convertedQuantity! + stripConversion.convertedQuantity!;
          console.log('📊 Total demand:', totalDemand, 'tablets');
          console.log('📊 Available vs Demand:', currentStock, 'vs', totalDemand);
          console.log('📊 Should fail?', currentStock < totalDemand);
        }

        // Now try the exact same payload from the user's report
        const createSaleDto = {
          items: [
            {
              productId: "cmbvt6d7v00aippgc3gl01whg",
              unitId: "cmbvt6d6a009nppgcotog2gnk",
              quantity: 28,
              unitPrice: 140000
            },
            {
              productId: "cmbvt6d7v00aippgc3gl01whg",
              unitId: "cmbvt6d62009hppgcky4ce6zy",
              quantity: 22,
              unitPrice: 47500
            }
          ],
          paymentMethod: 'CASH' as any,
          amountPaid: 4965000,
          cashierId: cashierId
        };

        console.log('🚀 Attempting to create sale with exact payload...');

        try {
          const result = await salesService.create(createSaleDto);
          console.log('❌ BUG REPRODUCED! Sale succeeded when it should have failed:', result.saleNumber);
          console.log('📄 Sale details:', {
            id: result.id,
            saleNumber: result.saleNumber,
            status: result.status,
            totalAmount: result.totalAmount
          });

          // This should not happen - the sale should fail
          expect(true).toBe(false); // Force test failure to highlight the bug
        } catch (error) {
          console.log('✅ Sale correctly failed with error:', error.message);
          expect(error.message).toContain('Stok tidak mencukupi');
        }

      } catch (error) {
        console.log('❌ Error during test:', error.message);
        // If cashier doesn't exist, that's expected in test environment
        if (error.message.includes('Cashier ID') || error.message.includes('tidak ditemukan')) {
          console.log('ℹ️  Expected error due to test environment setup');
          return;
        }
        throw error;
      }
    });

    it('should test with API endpoint directly', async () => {
      // Test the exact same scenario using the HTTP API
      console.log('🌐 Testing via HTTP API...');

      const payload = {
        items: [
          {
            productId: "cmbvt6d7v00aippgc3gl01whg",
            unitId: "cmbvt6d6a009nppgcotog2gnk",
            quantity: 28,
            unitPrice: 140000
          },
          {
            productId: "cmbvt6d7v00aippgc3gl01whg",
            unitId: "cmbvt6d62009hppgcky4ce6zy",
            quantity: 22,
            unitPrice: 47500
          }
        ],
        paymentMethod: 'CASH',
        amountPaid: 4965000,
        cashierId: "cmbvt6cyh0007ppgcgr61o06d"
      };

      try {
        const response = await request(ctx.app.getHttpServer())
          .post('/api/sales')
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send(payload);

        console.log('📡 API Response status:', response.status);
        console.log('📡 API Response body:', response.body);

        if (response.status === 201) {
          console.log('❌ BUG REPRODUCED via API! Sale succeeded when it should have failed');
          expect(true).toBe(false); // Force test failure
        } else {
          console.log('✅ API correctly rejected the sale');
          expect(response.status).toBe(400);
          expect(response.body.message).toContain('Stok tidak mencukupi');
        }
      } catch (error) {
        console.log('API test error:', error.message);
      }
    });
  });
});
