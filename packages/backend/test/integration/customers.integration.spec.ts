import { IntegrationTestSetup, TestContext, expectValidationError, expectUnauthorized, expectForbidden, expectNotFound, expectConflict, expectSuccess } from './test-setup';

describe('Customers Integration Tests', () => {
  let testSetup: IntegrationTestSetup;
  let ctx: TestContext;
  let testCustomerId: string;

  beforeAll(async () => {
    testSetup = new IntegrationTestSetup();
    ctx = await testSetup.setup();
  });

  afterAll(async () => {
    await testSetup.teardown();
  });

  describe('POST /api/customers', () => {
    it('should create registered customer with all fields', async () => {
      const customerData = {
        type: 'REGISTERED',
        firstName: 'John',
        lastName: 'Doe',
        fullName: '<PERSON>',
        phoneNumber: '+62 812 3456 7890',
        email: '<EMAIL>',
        dateOfBirth: '1990-01-15',
        gender: 'M',
        address: 'Jl. Test No. 123',
        city: 'Jakarta',
        province: 'DKI Jakarta',
        postalCode: '12345',
        membershipNumber: 'MEM001',
        membershipLevel: 'GOLD',
        loyaltyPoints: 100,
        notes: 'VIP customer',
        isActive: true,
      };

      const response = await ctx.request
        .post('/api/customers')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(customerData);

      expectSuccess(response, 201);
      expect(response.body.type).toBe(customerData.type);
      expect(response.body.firstName).toBe(customerData.firstName);
      expect(response.body.lastName).toBe(customerData.lastName);
      expect(response.body.fullName).toBe(customerData.fullName);
      expect(response.body.phoneNumber).toBe(customerData.phoneNumber);
      expect(response.body.email).toBe(customerData.email);
      expect(response.body.membershipNumber).toBe(customerData.membershipNumber);
      expect(response.body.membershipLevel).toBe(customerData.membershipLevel);
      expect(response.body.loyaltyPoints).toBe(customerData.loyaltyPoints);
      expect(response.body.code).toBeDefined(); // Auto-generated
      expect(response.body.createdBy).toBe(ctx.users.admin.id);
      expect(response.body.isActive).toBe(true);

      testCustomerId = response.body.id;
    });

    it('should create walk-in customer with minimal fields', async () => {
      const customerData = {
        type: 'WALK_IN',
        fullName: 'Walk-in Customer',
      };

      const response = await ctx.request
        .post('/api/customers')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(customerData);

      expectSuccess(response, 201);
      expect(response.body.type).toBe('WALK_IN');
      expect(response.body.fullName).toBe(customerData.fullName);
      expect(response.body.code).toBeDefined();
      expect(response.body.code).toMatch(/^WI\d{7}$/); // Walk-in code pattern: WI + YYMM + 3-digit sequence
      expect(response.body.createdBy).toBe(ctx.users.cashier.id);
    });

    it('should create customer as pharmacist', async () => {
      const customerData = {
        type: 'REGISTERED',
        fullName: 'Pharmacist Customer',
        phoneNumber: '+62 813 1234 5678',
      };

      const response = await ctx.request
        .post('/api/customers')
        .set('Authorization', `Bearer ${ctx.users.pharmacist.accessToken}`)
        .send(customerData);

      expectSuccess(response, 201);
      expect(response.body.createdBy).toBe(ctx.users.pharmacist.id);
    });

    it('should auto-generate customer code if not provided', async () => {
      const customerData = {
        type: 'REGISTERED',
        fullName: 'Auto Code Customer',
      };

      const response = await ctx.request
        .post('/api/customers')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(customerData);

      expectSuccess(response, 201);
      expect(response.body.code).toBeDefined();
      expect(response.body.code).toMatch(/^REG\d{7}$/); // Registered code pattern: REG + YYMM + 3-digit sequence
    });

    it('should accept custom customer code', async () => {
      const customerData = {
        code: 'CUSTOM001',
        type: 'REGISTERED',
        fullName: 'Custom Code Customer',
      };

      const response = await ctx.request
        .post('/api/customers')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(customerData);

      expectSuccess(response, 201);
      expect(response.body.code).toBe('CUSTOM001');
    });

    it('should fail without authentication', async () => {
      const response = await ctx.request
        .post('/api/customers')
        .send({ type: 'WALK_IN', fullName: 'Test' });

      expectUnauthorized(response);
    });

    it('should fail with duplicate customer code', async () => {
      const customerData = {
        code: 'CUSTOM001', // Already exists
        type: 'REGISTERED',
        fullName: 'Duplicate Code Customer',
      };

      const response = await ctx.request
        .post('/api/customers')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(customerData);

      expect(response.status).toBe(400);
      expect(response.body.message).toContain('Kode pelanggan sudah digunakan');
    });

    it('should fail with duplicate membership number', async () => {
      const customerData = {
        type: 'REGISTERED',
        fullName: 'Duplicate Membership Customer',
        membershipNumber: 'MEM001', // Already exists
      };

      const response = await ctx.request
        .post('/api/customers')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(customerData);

      expect(response.status).toBe(400);
      expect(response.body.message).toContain('Nomor keanggotaan sudah digunakan');
    });

    it('should fail with invalid customer type', async () => {
      const customerData = {
        type: 'INVALID_TYPE',
        fullName: 'Invalid Type Customer',
      };

      const response = await ctx.request
        .post('/api/customers')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(customerData);

      expectValidationError(response);
    });

    it('should fail with missing required fields', async () => {
      const response = await ctx.request
        .post('/api/customers')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({});

      expectValidationError(response);
    });

    it('should validate email format', async () => {
      const customerData = {
        type: 'REGISTERED',
        fullName: 'Email Test Customer',
        email: 'invalid-email',
      };

      const response = await ctx.request
        .post('/api/customers')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(customerData);

      expectValidationError(response, 'email');
    });

    it('should validate date format', async () => {
      const customerData = {
        type: 'REGISTERED',
        fullName: 'Date Test Customer',
        dateOfBirth: 'invalid-date',
      };

      const response = await ctx.request
        .post('/api/customers')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(customerData);

      expectValidationError(response);
    });

    it('should validate loyalty points as positive integer', async () => {
      const customerData = {
        type: 'REGISTERED',
        fullName: 'Points Test Customer',
        loyaltyPoints: -10,
      };

      const response = await ctx.request
        .post('/api/customers')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(customerData);

      expectValidationError(response);
    });
  });

  describe('GET /api/customers', () => {
    beforeAll(async () => {
      // Create additional test customers for filtering tests
      const customers = [
        {
          type: 'REGISTERED',
          fullName: 'Alice Johnson',
          phoneNumber: '+62 811 1111 1111',
          email: '<EMAIL>',
          city: 'Jakarta',
          membershipLevel: 'SILVER',
          isActive: true
        },
        {
          type: 'WALK_IN',
          fullName: 'Bob Smith',
          phoneNumber: '+62 812 2222 2222',
          city: 'Surabaya',
          isActive: true
        },
        {
          type: 'REGISTERED',
          fullName: 'Charlie Brown',
          email: '<EMAIL>',
          city: 'Jakarta',
          membershipLevel: 'BRONZE',
          isActive: false
        },
      ];

      for (const customer of customers) {
        await ctx.request
          .post('/api/customers')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(customer);
      }
    });

    it('should list all customers for authenticated user', async () => {
      const response = await ctx.request
        .get('/api/customers')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      expect(response.body.data).toBeInstanceOf(Array);
      expect(response.body.meta).toBeDefined();
      expect(response.body.meta.total).toBeGreaterThan(0);
      expect(response.body.meta.page).toBe(1);
      expect(response.body.meta.limit).toBe(10);
    });

    it('should filter customers by type', async () => {
      const response = await ctx.request
        .get('/api/customers?type=REGISTERED')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.data.every((c: any) => c.type === 'REGISTERED')).toBe(true);
    });

    it('should filter customers by membership level', async () => {
      const response = await ctx.request
        .get('/api/customers?membershipLevel=SILVER')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.data.every((c: any) => c.membershipLevel === 'SILVER')).toBe(true);
    });

    it('should filter customers by active status', async () => {
      const response = await ctx.request
        .get('/api/customers?isActive=true')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.data.every((c: any) => c.isActive === true)).toBe(true);
    });

    it('should search customers by name', async () => {
      const response = await ctx.request
        .get('/api/customers?search=Alice')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.data.some((c: any) => c.fullName.includes('Alice'))).toBe(true);
    });

    it('should search customers by phone number', async () => {
      const response = await ctx.request
        .get('/api/customers?search=1111')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.data.some((c: any) => c.phoneNumber && c.phoneNumber.includes('1111'))).toBe(true);
    });

    it('should search customers by email', async () => {
      const response = await ctx.request
        .get('/api/customers?search=<EMAIL>')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.data.some((c: any) => c.email && c.email.includes('alice'))).toBe(true);
    });

    it('should paginate results', async () => {
      const response = await ctx.request
        .get('/api/customers?page=1&limit=2')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.data.length).toBeLessThanOrEqual(2);
      expect(response.body.meta.page).toBe(1);
      expect(response.body.meta.limit).toBe(2);
    });

    it('should sort customers', async () => {
      const response = await ctx.request
        .get('/api/customers?sortBy=fullName&sortOrder=asc')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      const names = response.body.data.map((c: any) => c.fullName);
      const sortedNames = [...names].sort();
      expect(names).toEqual(sortedNames);
    });

    it('should fail without authentication', async () => {
      const response = await ctx.request
        .get('/api/customers');

      expectUnauthorized(response);
    });

    it('should validate pagination parameters', async () => {
      const response = await ctx.request
        .get('/api/customers?page=0&limit=101')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectValidationError(response);
    });
  });

  describe('GET /api/customers/stats', () => {
    it('should return customer statistics for authenticated user', async () => {
      const response = await ctx.request
        .get('/api/customers/stats')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body).toHaveProperty('total');
      expect(response.body).toHaveProperty('walkIn');
      expect(response.body).toHaveProperty('registered');
      expect(response.body).toHaveProperty('active');
      expect(response.body).toHaveProperty('inactive');
      expect(typeof response.body.total).toBe('number');
      expect(typeof response.body.walkIn).toBe('number');
      expect(typeof response.body.registered).toBe('number');
      expect(typeof response.body.active).toBe('number');
      expect(typeof response.body.inactive).toBe('number');
    });

    it('should fail without authentication', async () => {
      const response = await ctx.request
        .get('/api/customers/stats');

      expectUnauthorized(response);
    });
  });

  describe('GET /api/customers/generate-code/:type', () => {
    it('should generate walk-in customer code', async () => {
      const response = await ctx.request
        .get('/api/customers/generate-code/WALK_IN')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      expect(response.body.code).toBeDefined();
      expect(response.body.code).toMatch(/^WI\d{7}$/);
    });

    it('should generate registered customer code', async () => {
      const response = await ctx.request
        .get('/api/customers/generate-code/REGISTERED')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.code).toBeDefined();
      expect(response.body.code).toMatch(/^REG\d{7}$/);
    });

    it('should fail with invalid customer type', async () => {
      const response = await ctx.request
        .get('/api/customers/generate-code/INVALID')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expect(response.status).toBe(400);
      expect(response.body.message).toContain('Jenis pelanggan tidak valid');
    });

    it('should fail without authentication', async () => {
      const response = await ctx.request
        .get('/api/customers/generate-code/WALK_IN');

      expectUnauthorized(response);
    });
  });

  describe('GET /api/customers/validate-code/:code', () => {
    it('should validate unique customer code', async () => {
      const response = await ctx.request
        .get('/api/customers/validate-code/UNIQUE123')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.isUnique).toBe(true);
    });

    it('should validate existing customer code', async () => {
      const response = await ctx.request
        .get('/api/customers/validate-code/CUSTOM001')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.isUnique).toBe(false);
    });

    it('should fail without authentication', async () => {
      const response = await ctx.request
        .get('/api/customers/validate-code/TEST123');

      expectUnauthorized(response);
    });
  });

  describe('GET /api/customers/:id', () => {
    it('should return customer details for authenticated user', async () => {
      const response = await ctx.request
        .get(`/api/customers/${testCustomerId}`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      expect(response.body.id).toBe(testCustomerId);
      expect(response.body.firstName).toBe('John');
      expect(response.body.lastName).toBe('Doe');
      expect(response.body.fullName).toBe('John Doe');
      expect(response.body.createdByUser).toBeDefined();
      expect(response.body._count).toBeDefined();
      expect(response.body._count.sales).toBeDefined();
    });

    it('should fail with non-existent customer ID', async () => {
      const response = await ctx.request
        .get('/api/customers/non-existent-id')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectNotFound(response);
      expect(response.body.message).toContain('Pelanggan tidak ditemukan');
    });

    it('should fail without authentication', async () => {
      const response = await ctx.request
        .get(`/api/customers/${testCustomerId}`);

      expectUnauthorized(response);
    });
  });

  describe('PATCH /api/customers/:id', () => {
    it('should update customer as admin', async () => {
      const updateData = {
        fullName: 'John Updated Doe',
        phoneNumber: '+62 813 9999 8888',
        email: '<EMAIL>',
        membershipLevel: 'PLATINUM',
        loyaltyPoints: 500,
        notes: 'Updated customer notes',
      };

      const response = await ctx.request
        .patch(`/api/customers/${testCustomerId}`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(updateData);

      expectSuccess(response);
      expect(response.body.fullName).toBe(updateData.fullName);
      expect(response.body.phoneNumber).toBe(updateData.phoneNumber);
      expect(response.body.email).toBe(updateData.email);
      expect(response.body.membershipLevel).toBe(updateData.membershipLevel);
      expect(response.body.loyaltyPoints).toBe(updateData.loyaltyPoints);
      expect(response.body.notes).toBe(updateData.notes);
      expect(response.body.updatedBy).toBe(ctx.users.admin.id);
    });

    it('should update customer as pharmacist', async () => {
      const updateData = {
        notes: 'Updated by pharmacist',
      };

      const response = await ctx.request
        .patch(`/api/customers/${testCustomerId}`)
        .set('Authorization', `Bearer ${ctx.users.pharmacist.accessToken}`)
        .send(updateData);

      expectSuccess(response);
      expect(response.body.notes).toBe(updateData.notes);
      expect(response.body.updatedBy).toBe(ctx.users.pharmacist.id);
    });

    it('should update customer as cashier', async () => {
      const updateData = {
        phoneNumber: '+62 814 1111 2222',
      };

      const response = await ctx.request
        .patch(`/api/customers/${testCustomerId}`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(updateData);

      expectSuccess(response);
      expect(response.body.phoneNumber).toBe(updateData.phoneNumber);
      expect(response.body.updatedBy).toBe(ctx.users.cashier.id);
    });

    it('should fail with non-existent customer ID', async () => {
      const response = await ctx.request
        .patch('/api/customers/non-existent-id')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({ fullName: 'Test' });

      expectNotFound(response);
    });

    it('should fail without authentication', async () => {
      const response = await ctx.request
        .patch(`/api/customers/${testCustomerId}`)
        .send({ fullName: 'Test' });

      expectUnauthorized(response);
    });

    it('should validate email format in update', async () => {
      const response = await ctx.request
        .patch(`/api/customers/${testCustomerId}`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({ email: 'invalid-email' });

      expectValidationError(response, 'email');
    });

    it('should fail with duplicate customer code', async () => {
      // First, get another customer's code
      const customersResponse = await ctx.request
        .get('/api/customers?limit=5')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      const otherCustomer = customersResponse.body.data.find((c: any) => c.id !== testCustomerId);

      if (otherCustomer) {
        const response = await ctx.request
          .patch(`/api/customers/${testCustomerId}`)
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send({ code: otherCustomer.code });

        expect(response.status).toBe(400);
        expect(response.body.message).toContain('Kode pelanggan sudah digunakan');
      }
    });

    it('should fail with duplicate membership number', async () => {
      // First, get another customer's membership number
      const customersResponse = await ctx.request
        .get('/api/customers?limit=5')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      const otherCustomer = customersResponse.body.data.find((c: any) => c.id !== testCustomerId && c.membershipNumber);

      if (otherCustomer) {
        const response = await ctx.request
          .patch(`/api/customers/${testCustomerId}`)
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send({ membershipNumber: otherCustomer.membershipNumber });

        expect(response.status).toBe(400);
        expect(response.body.message).toContain('Nomor keanggotaan sudah digunakan');
      }
    });
  });

  describe('PATCH /api/customers/:id/deactivate', () => {
    let customerToDeactivate: string;

    beforeEach(async () => {
      const uniqueCode = `DEACT${Date.now()}${Math.floor(Math.random() * 1000)}`;
      const response = await ctx.request
        .post('/api/customers')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({
          code: uniqueCode,
          type: 'REGISTERED',
          fullName: 'Customer to Deactivate',
        });

      if (response.status !== 201) {
        throw new Error(`Failed to create customer for test. Status: ${response.status}, Body: ${JSON.stringify(response.body)}`);
      }

      customerToDeactivate = response.body.id;
    });

    it('should deactivate customer as admin', async () => {
      const response = await ctx.request
        .patch(`/api/customers/${customerToDeactivate}/deactivate`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.isActive).toBe(false);
      expect(response.body.updatedBy).toBe(ctx.users.admin.id);
    });

    it('should deactivate customer as pharmacist', async () => {
      const response = await ctx.request
        .patch(`/api/customers/${customerToDeactivate}/deactivate`)
        .set('Authorization', `Bearer ${ctx.users.pharmacist.accessToken}`);

      expectSuccess(response);
      expect(response.body.isActive).toBe(false);
      expect(response.body.updatedBy).toBe(ctx.users.pharmacist.id);
    });

    it('should fail for cashier (insufficient permissions)', async () => {
      const response = await ctx.request
        .patch(`/api/customers/${customerToDeactivate}/deactivate`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectForbidden(response);
    });

    it('should fail with non-existent customer ID', async () => {
      const response = await ctx.request
        .patch('/api/customers/non-existent-id/deactivate')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectNotFound(response);
    });

    it('should fail without authentication', async () => {
      const response = await ctx.request
        .patch(`/api/customers/${customerToDeactivate}/deactivate`);

      expectUnauthorized(response);
    });
  });

  describe('PATCH /api/customers/:id/activate', () => {
    let customerToActivate: string;

    beforeEach(async () => {
      const uniqueCode = `ACT${Date.now()}${Math.floor(Math.random() * 1000)}`;
      const response = await ctx.request
        .post('/api/customers')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({
          code: uniqueCode,
          type: 'REGISTERED',
          fullName: 'Customer to Activate',
          isActive: false,
        });

      if (response.status !== 201) {
        throw new Error(`Failed to create customer for test. Status: ${response.status}, Body: ${JSON.stringify(response.body)}`);
      }

      customerToActivate = response.body.id;
    });

    it('should activate customer as admin', async () => {
      const response = await ctx.request
        .patch(`/api/customers/${customerToActivate}/activate`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.isActive).toBe(true);
      expect(response.body.updatedBy).toBe(ctx.users.admin.id);
    });

    it('should activate customer as pharmacist', async () => {
      const response = await ctx.request
        .patch(`/api/customers/${customerToActivate}/activate`)
        .set('Authorization', `Bearer ${ctx.users.pharmacist.accessToken}`);

      expectSuccess(response);
      expect(response.body.isActive).toBe(true);
      expect(response.body.updatedBy).toBe(ctx.users.pharmacist.id);
    });

    it('should fail for cashier (insufficient permissions)', async () => {
      const response = await ctx.request
        .patch(`/api/customers/${customerToActivate}/activate`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectForbidden(response);
    });

    it('should fail with non-existent customer ID', async () => {
      const response = await ctx.request
        .patch('/api/customers/non-existent-id/activate')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectNotFound(response);
    });

    it('should fail without authentication', async () => {
      const response = await ctx.request
        .patch(`/api/customers/${customerToActivate}/activate`);

      expectUnauthorized(response);
    });
  });

  describe('DELETE /api/customers/:id', () => {
    let customerToDelete: string;

    beforeEach(async () => {
      const uniqueCode = `DEL${Date.now()}${Math.floor(Math.random() * 1000)}`;
      const response = await ctx.request
        .post('/api/customers')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({
          code: uniqueCode,
          type: 'WALK_IN',
          fullName: 'Customer to Delete',
        });

      if (response.status !== 201) {
        throw new Error(`Failed to create customer for test. Status: ${response.status}, Body: ${JSON.stringify(response.body)}`);
      }

      customerToDelete = response.body.id;
    });

    it('should delete customer as admin', async () => {
      const response = await ctx.request
        .delete(`/api/customers/${customerToDelete}`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expect(response.status).toBe(204);

      // Verify customer is deleted
      const getResponse = await ctx.request
        .get(`/api/customers/${customerToDelete}`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectNotFound(getResponse);
    });

    it('should delete customer as pharmacist', async () => {
      const response = await ctx.request
        .delete(`/api/customers/${customerToDelete}`)
        .set('Authorization', `Bearer ${ctx.users.pharmacist.accessToken}`);

      expect(response.status).toBe(204);
    });

    it('should fail for cashier (insufficient permissions)', async () => {
      const response = await ctx.request
        .delete(`/api/customers/${customerToDelete}`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectForbidden(response);
    });

    it('should fail with non-existent customer ID', async () => {
      const response = await ctx.request
        .delete('/api/customers/non-existent-id')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectNotFound(response);
    });

    it('should fail without authentication', async () => {
      const response = await ctx.request
        .delete(`/api/customers/${customerToDelete}`);

      expectUnauthorized(response);
    });

    it('should fail to delete customer with sales history', async () => {
      // Create a customer for this test
      const customerResponse = await ctx.request
        .post('/api/customers')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({
          type: 'REGISTERED',
          fullName: 'Customer with Sales History',
          phoneNumber: '+62 815 5555 5555',
          email: '<EMAIL>',
        });

      if (customerResponse.status !== 201) {
        console.log('Customer creation failed:', customerResponse.status, customerResponse.body);
      }
      expectSuccess(customerResponse, 201);
      const customerWithSales = customerResponse.body.id;

      // Create test data needed for sale (unit, product, supplier, inventory)
      // First create a unit directly in database (units are not created via API)
      const testUnit = await ctx.prisma.productUnit.create({
        data: {
          name: 'Tablet Customer Test',
          abbreviation: 'TabCT',
          type: 'COUNT',
          isBaseUnit: true,
          description: 'Test tablet unit for customer integration test',
        },
      });

      // Then create product with the unit
      const productResponse = await ctx.request
        .post('/api/products')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({
          code: 'PROD-CUSTOMER-TEST',
          name: 'Product for Customer Test',
          type: 'MEDICINE',
          category: 'ANALGESIC',
          medicineClassification: 'OBAT_BEBAS',
          baseUnitId: testUnit.id,
          manufacturer: 'Test Pharma',
          description: 'Test product for customer integration test',
        });

      if (productResponse.status !== 201) {
        console.log('Product creation failed:', productResponse.status, productResponse.body);
      }
      expectSuccess(productResponse, 201);
      const testProduct = productResponse.body;

      // Create supplier for inventory
      const supplierResponse = await ctx.request
        .post('/api/suppliers')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({
          code: 'SUP-CUSTOMER-TEST',
          name: 'Supplier for Customer Test',
          type: 'DISTRIBUTOR',
        });

      if (supplierResponse.status !== 201) {
        console.log('Supplier creation failed:', supplierResponse.status, supplierResponse.body);
      }
      expectSuccess(supplierResponse, 201);
      const testSupplier = supplierResponse.body;

      // Create inventory for the product
      const inventoryResponse = await ctx.request
        .post('/api/inventory')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({
          productId: testProduct.id,
          unitId: testUnit.id,
          supplierId: testSupplier.id,
          batchNumber: 'BATCH-CUSTOMER-TEST',
          quantityOnHand: 100,
          costPrice: 5000,
          sellingPrice: 7500,
          expiryDate: '2025-12-31',
          location: 'Rak Customer Test',
        });

      if (inventoryResponse.status !== 201) {
        console.log('Inventory creation failed:', inventoryResponse.status, inventoryResponse.body);
      }
      expectSuccess(inventoryResponse, 201);

      // Create a sale for this customer
      const saleResponse = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send({
          customerId: customerWithSales,
          paymentMethod: 'CASH',
          amountPaid: 10000,
          items: [
            {
              productId: testProduct.id,
              unitId: testUnit.id,
              quantity: 1,
              unitPrice: 7500,
            },
          ],
        });

      expectSuccess(saleResponse, 201);
      expect(saleResponse.body.customerId).toBe(customerWithSales);

      // Now attempt to delete the customer - this should fail
      const deleteResponse = await ctx.request
        .delete(`/api/customers/${customerWithSales}`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expect(deleteResponse.status).toBe(400);
      expect(deleteResponse.body.message).toContain('Tidak dapat menghapus pelanggan yang memiliki riwayat transaksi');

      // Verify customer still exists
      const getCustomerResponse = await ctx.request
        .get(`/api/customers/${customerWithSales}`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(getCustomerResponse);
      expect(getCustomerResponse.body.id).toBe(customerWithSales);
      expect(getCustomerResponse.body._count.sales).toBe(1);
    });

    it('should successfully delete customer without sales history', async () => {
      // Create a customer without any sales
      const customerResponse = await ctx.request
        .post('/api/customers')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({
          type: 'WALK_IN',
          fullName: 'Customer without Sales',
          phoneNumber: '+62 816 6666 6666',
        });

      expectSuccess(customerResponse, 201);
      const customerWithoutSales = customerResponse.body.id;

      // Verify customer exists and has no sales
      const getCustomerResponse = await ctx.request
        .get(`/api/customers/${customerWithoutSales}`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(getCustomerResponse);
      expect(getCustomerResponse.body._count.sales).toBe(0);

      // Delete the customer - this should succeed
      const deleteResponse = await ctx.request
        .delete(`/api/customers/${customerWithoutSales}`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expect(deleteResponse.status).toBe(204);

      // Verify customer is deleted
      const getDeletedCustomerResponse = await ctx.request
        .get(`/api/customers/${customerWithoutSales}`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectNotFound(getDeletedCustomerResponse);
      expect(getDeletedCustomerResponse.body.message).toContain('Pelanggan tidak ditemukan');
    });
  });

  describe('Edge Cases and Boundary Conditions', () => {
    it('should handle very long customer names', async () => {
      const longName = 'A'.repeat(255); // Test maximum length
      const customerData = {
        type: 'REGISTERED',
        fullName: longName,
      };

      const response = await ctx.request
        .post('/api/customers')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(customerData);

      expectSuccess(response, 201);
      expect(response.body.fullName).toBe(longName);
    });

    it('should handle special characters in customer data', async () => {
      const customerData = {
        type: 'REGISTERED',
        fullName: 'José María Ñoño',
        address: 'Jl. Raya No. 123 RT/RW 01/02',
        notes: 'Customer with special chars: àáâãäåæçèéêë',
      };

      const response = await ctx.request
        .post('/api/customers')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(customerData);

      expectSuccess(response, 201);
      expect(response.body.fullName).toBe(customerData.fullName);
      expect(response.body.address).toBe(customerData.address);
      expect(response.body.notes).toBe(customerData.notes);
    });

    it('should handle maximum loyalty points', async () => {
      const customerData = {
        type: 'REGISTERED',
        fullName: 'Max Points Customer',
        loyaltyPoints: 999999,
      };

      const response = await ctx.request
        .post('/api/customers')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(customerData);

      expectSuccess(response, 201);
      expect(response.body.loyaltyPoints).toBe(999999);
    });

    it('should handle empty search queries gracefully', async () => {
      const response = await ctx.request
        .get('/api/customers?search=')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.data).toBeInstanceOf(Array);
    });

    it('should handle large page numbers gracefully', async () => {
      const response = await ctx.request
        .get('/api/customers?page=999999&limit=10')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.data).toBeInstanceOf(Array);
      expect(response.body.data.length).toBe(0);
      expect(response.body.meta.page).toBe(999999);
    });
  });
});
