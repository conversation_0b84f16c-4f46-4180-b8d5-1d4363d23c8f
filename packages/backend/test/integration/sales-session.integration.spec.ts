import { IntegrationTestSetup, TestContext, expectValidationError, expectUnauthorized, expectForbidden, expectNotFound, expectConflict, expectSuccess } from './test-setup';
import { StockMovementType, SaleStatus, PaymentMethod, ReferenceType } from '@prisma/client';

describe('Sales Session Integration Tests', () => {
  let testSetup: IntegrationTestSetup;
  let ctx: TestContext;
  let testCustomerId: string;
  let testProductId: string;
  let testUnitId: string;
  let testSupplierId: string;

  // Session test data
  let session1StartTime: Date;
  let session2StartTime: Date;
  let session1EndTime: Date;
  let sale1Id: string;
  let sale2Id: string;
  let sale3Id: string;

  beforeAll(async () => {
    testSetup = new IntegrationTestSetup();
    ctx = await testSetup.setup();

    // Create test data needed for sales
    const supplier = await testSetup.createTestSupplier(ctx.users.admin.id);
    testSupplierId = supplier.id;

    const baseUnit = await testSetup.createTestProductUnit(ctx.users.admin.id);
    const product = await testSetup.createTestProduct(baseUnit.id, ctx.users.admin.id);
    testProductId = product.id;
    testUnitId = baseUnit.id;

    await testSetup.createTestInventoryItem(testProductId, testUnitId, testSupplierId, ctx.users.admin.id);

    const customer = await testSetup.createTestCustomer(ctx.users.admin.id);
    testCustomerId = customer.id;

    // Set up session times for testing
    const now = new Date();
    session1StartTime = new Date(now.getTime() - 4 * 60 * 60 * 1000); // 4 hours ago
    session1EndTime = new Date(now.getTime() - 2 * 60 * 60 * 1000); // 2 hours ago
    session2StartTime = new Date(now.getTime() - 1 * 60 * 60 * 1000); // 1 hour ago

    // Create test sales for different sessions
    await createTestSalesForSessions();
  });

  afterAll(async () => {
    await testSetup.teardown();
  });

  async function createTestSalesForSessions() {
    // Sale 1: Session 1 (closed session)
    const sale1 = await ctx.prisma.sale.create({
      data: {
        saleNumber: 'TRX-SESSION1-001',
        customerId: testCustomerId,
        cashierId: ctx.users.cashier.id,
        status: SaleStatus.COMPLETED,
        subtotal: 15000,
        taxAmount: 1500,
        totalAmount: 16500,
        paymentMethod: PaymentMethod.CASH,
        amountPaid: 20000,
        changeAmount: 3500,
        saleDate: new Date(session1StartTime.getTime() + 30 * 60 * 1000), // 30 min after session start
        customerName: 'Session 1 Customer',
      },
    });
    sale1Id = sale1.id;

    // Sale 2: Session 1 (closed session) - different time
    const sale2 = await ctx.prisma.sale.create({
      data: {
        saleNumber: 'TRX-SESSION1-002',
        customerId: testCustomerId,
        cashierId: ctx.users.cashier.id,
        status: SaleStatus.COMPLETED,
        subtotal: 25000,
        taxAmount: 2500,
        totalAmount: 27500,
        paymentMethod: PaymentMethod.TRANSFER,
        amountPaid: 27500,
        changeAmount: 0,
        saleDate: new Date(session1StartTime.getTime() + 90 * 60 * 1000), // 90 min after session start
        customerName: 'Session 1 Customer 2',
      },
    });
    sale2Id = sale2.id;

    // Sale 3: Session 2 (active session)
    const sale3 = await ctx.prisma.sale.create({
      data: {
        saleNumber: 'TRX-SESSION2-001',
        customerId: testCustomerId,
        cashierId: ctx.users.cashier.id,
        status: SaleStatus.COMPLETED,
        subtotal: 12000,
        taxAmount: 1200,
        totalAmount: 13200,
        paymentMethod: PaymentMethod.CASH,
        amountPaid: 15000,
        changeAmount: 1800,
        saleDate: new Date(session2StartTime.getTime() + 15 * 60 * 1000), // 15 min after session start
        customerName: 'Session 2 Customer',
      },
    });
    sale3Id = sale3.id;

    // Create sale items for all sales
    await ctx.prisma.saleItem.createMany({
      data: [
        {
          saleId: sale1Id,
          productId: testProductId,
          unitId: testUnitId,
          quantity: 2,
          unitPrice: 7500,
          totalPrice: 15000,
        },
        {
          saleId: sale2Id,
          productId: testProductId,
          unitId: testUnitId,
          quantity: 3,
          unitPrice: 8333.33,
          totalPrice: 25000,
        },
        {
          saleId: sale3Id,
          productId: testProductId,
          unitId: testUnitId,
          quantity: 1,
          unitPrice: 12000,
          totalPrice: 12000,
        },
      ],
    });
  }

  describe('GET /api/sales/session/:cashierId/:sessionStartTime', () => {
    it('should get sales for closed session with end time', async () => {
      const sessionStartTimeEncoded = encodeURIComponent(session1StartTime.toISOString());
      const sessionEndTimeEncoded = encodeURIComponent(session1EndTime.toISOString());

      const response = await ctx.request
        .get(`/api/sales/session/${ctx.users.cashier.id}/${sessionStartTimeEncoded}?sessionEndTime=${sessionEndTimeEncoded}`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      expect(response.body.data).toHaveLength(2);
      expect(response.body.meta.total).toBe(2);
      expect(response.body.meta.cashierId).toBe(ctx.users.cashier.id);
      expect(response.body.meta.sessionStartTime).toBe(session1StartTime.toISOString());

      // Verify sales are from session 1
      const saleNumbers = response.body.data.map((sale: any) => sale.saleNumber);
      expect(saleNumbers).toContain('TRX-SESSION1-001');
      expect(saleNumbers).toContain('TRX-SESSION1-002');
      expect(saleNumbers).not.toContain('TRX-SESSION2-001');
    });

    it('should get sales for active session without end time', async () => {
      const sessionStartTimeEncoded = encodeURIComponent(session2StartTime.toISOString());

      const response = await ctx.request
        .get(`/api/sales/session/${ctx.users.cashier.id}/${sessionStartTimeEncoded}`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      expect(response.body.data).toHaveLength(1);
      expect(response.body.meta.total).toBe(1);
      expect(response.body.data[0].saleNumber).toBe('TRX-SESSION2-001');
    });

    it('should return empty data for session with no sales', async () => {
      const emptySessionStart = new Date(Date.now() - 10 * 60 * 1000); // 10 minutes ago
      const sessionStartTimeEncoded = encodeURIComponent(emptySessionStart.toISOString());

      const response = await ctx.request
        .get(`/api/sales/session/${ctx.users.cashier.id}/${sessionStartTimeEncoded}`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      expect(response.body.data).toHaveLength(0);
      expect(response.body.meta.total).toBe(0);
    });

    it('should include complete sale data structure', async () => {
      const sessionStartTimeEncoded = encodeURIComponent(session1StartTime.toISOString());

      const response = await ctx.request
        .get(`/api/sales/session/${ctx.users.cashier.id}/${sessionStartTimeEncoded}`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      const sale = response.body.data[0];

      // Verify sale structure
      expect(sale).toHaveProperty('id');
      expect(sale).toHaveProperty('saleNumber');
      expect(sale).toHaveProperty('customerId');
      expect(sale).toHaveProperty('cashierId');
      expect(sale).toHaveProperty('saleDate');
      expect(sale).toHaveProperty('totalAmount');
      expect(sale).toHaveProperty('paymentMethod');
      expect(sale).toHaveProperty('customer');
      expect(sale).toHaveProperty('saleItems');

      // Verify customer data
      expect(sale.customer).toHaveProperty('id');
      expect(sale.customer).toHaveProperty('fullName');
      expect(sale.customer).toHaveProperty('code');
      expect(sale.customer).toHaveProperty('type');

      // Verify sale items
      expect(sale.saleItems).toBeInstanceOf(Array);
      expect(sale.saleItems.length).toBeGreaterThan(0);

      const saleItem = sale.saleItems[0];
      expect(saleItem).toHaveProperty('id');
      expect(saleItem).toHaveProperty('quantity');
      expect(saleItem).toHaveProperty('unitPrice');
      expect(saleItem).toHaveProperty('totalPrice');
      expect(saleItem).toHaveProperty('product');
      expect(saleItem).toHaveProperty('unit');
    });

    it('should fail without authentication', async () => {
      const sessionStartTimeEncoded = encodeURIComponent(session1StartTime.toISOString());

      const response = await ctx.request
        .get(`/api/sales/session/${ctx.users.cashier.id}/${sessionStartTimeEncoded}`);

      expectUnauthorized(response);
    });

    it('should fail with invalid session start time format', async () => {
      const response = await ctx.request
        .get(`/api/sales/session/${ctx.users.cashier.id}/invalid-date`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expect(response.status).toBe(400);
    });

    it('should fail with non-existent cashier ID', async () => {
      const sessionStartTimeEncoded = encodeURIComponent(session1StartTime.toISOString());

      const response = await ctx.request
        .get(`/api/sales/session/non-existent-cashier/${sessionStartTimeEncoded}`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      expect(response.body.data).toHaveLength(0);
    });
  });

  describe('GET /api/sales/session-stats/:cashierId/:sessionStartTime/stats', () => {
    it('should get statistics for closed session with end time', async () => {
      const sessionStartTimeEncoded = encodeURIComponent(session1StartTime.toISOString());
      const sessionEndTimeEncoded = encodeURIComponent(session1EndTime.toISOString());

      const response = await ctx.request
        .get(`/api/sales/session-stats/${ctx.users.cashier.id}/${sessionStartTimeEncoded}/stats?sessionEndTime=${sessionEndTimeEncoded}`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);

      // Verify statistics structure
      expect(response.body).toHaveProperty('sessionStartTime');
      expect(response.body).toHaveProperty('cashierId');
      expect(response.body).toHaveProperty('totalSalesCount');
      expect(response.body).toHaveProperty('totalRevenue');
      expect(response.body).toHaveProperty('averageTransactionValue');
      expect(response.body).toHaveProperty('paymentMethodBreakdown');
      expect(response.body).toHaveProperty('hourlyBreakdown');
      expect(response.body).toHaveProperty('firstSaleTime');
      expect(response.body).toHaveProperty('lastSaleTime');

      // Verify calculated values
      expect(response.body.totalSalesCount).toBe(2);
      expect(response.body.totalRevenue).toBe(44000); // 16500 + 27500
      expect(response.body.averageTransactionValue).toBe(22000); // 44000 / 2
      expect(response.body.cashierId).toBe(ctx.users.cashier.id);

      // Verify payment method breakdown
      expect(response.body.paymentMethodBreakdown).toHaveProperty('CASH');
      expect(response.body.paymentMethodBreakdown).toHaveProperty('TRANSFER');
      expect(response.body.paymentMethodBreakdown.CASH.count).toBe(1);
      expect(response.body.paymentMethodBreakdown.CASH.amount).toBe(16500);
      expect(response.body.paymentMethodBreakdown.TRANSFER.count).toBe(1);
      expect(response.body.paymentMethodBreakdown.TRANSFER.amount).toBe(27500);

      // Verify time boundaries
      expect(response.body.firstSaleTime).toBeDefined();
      expect(response.body.lastSaleTime).toBeDefined();
      expect(response.body.firstSaleTime).toBeLessThanOrEqual(response.body.lastSaleTime);
    });

    it('should get statistics for active session without end time', async () => {
      const sessionStartTimeEncoded = encodeURIComponent(session2StartTime.toISOString());

      const response = await ctx.request
        .get(`/api/sales/session-stats/${ctx.users.cashier.id}/${sessionStartTimeEncoded}/stats`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      expect(response.body.totalSalesCount).toBe(1);
      expect(response.body.totalRevenue).toBe(13200);
      expect(response.body.averageTransactionValue).toBe(13200);
    });

    it('should return zero statistics for empty session', async () => {
      const emptySessionStart = new Date(Date.now() - 5 * 60 * 1000); // 5 minutes ago
      const sessionStartTimeEncoded = encodeURIComponent(emptySessionStart.toISOString());

      const response = await ctx.request
        .get(`/api/sales/session-stats/${ctx.users.cashier.id}/${sessionStartTimeEncoded}/stats`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      expect(response.body.totalSalesCount).toBe(0);
      expect(response.body.totalRevenue).toBe(0);
      expect(response.body.averageTransactionValue).toBe(0);
      expect(response.body.paymentMethodBreakdown).toEqual({});
      expect(response.body.hourlyBreakdown).toEqual({});
      expect(response.body.firstSaleTime).toBeNull();
      expect(response.body.lastSaleTime).toBeNull();
    });

    it('should verify hourly breakdown calculation', async () => {
      const sessionStartTimeEncoded = encodeURIComponent(session1StartTime.toISOString());

      const response = await ctx.request
        .get(`/api/sales/session-stats/${ctx.users.cashier.id}/${sessionStartTimeEncoded}/stats`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);

      // Verify hourly breakdown structure
      expect(response.body.hourlyBreakdown).toBeDefined();
      expect(typeof response.body.hourlyBreakdown).toBe('object');

      // Check that hours are represented as numbers and have count/amount
      Object.keys(response.body.hourlyBreakdown).forEach(hour => {
        const hourData = response.body.hourlyBreakdown[hour];
        expect(hourData).toHaveProperty('count');
        expect(hourData).toHaveProperty('amount');
        expect(typeof hourData.count).toBe('number');
        expect(typeof hourData.amount).toBe('number');
        expect(hourData.count).toBeGreaterThan(0);
        expect(hourData.amount).toBeGreaterThan(0);
      });
    });

    it('should fail without authentication', async () => {
      const sessionStartTimeEncoded = encodeURIComponent(session1StartTime.toISOString());

      const response = await ctx.request
        .get(`/api/sales/session-stats/${ctx.users.cashier.id}/${sessionStartTimeEncoded}/stats`);

      expectUnauthorized(response);
    });

    it('should fail with invalid session start time format', async () => {
      const response = await ctx.request
        .get(`/api/sales/session-stats/${ctx.users.cashier.id}/invalid-date/stats`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expect(response.status).toBe(400);
    });
  });

  describe('Session Isolation Tests', () => {
    it('should properly isolate sales between different sessions on same day', async () => {
      // Create two sessions on the same day but different times
      const today = new Date();
      today.setHours(9, 0, 0, 0); // 9 AM
      const morningSession = today;

      const afternoonSession = new Date(today);
      afternoonSession.setHours(14, 0, 0, 0); // 2 PM

      // Create sales for morning session
      const morningSale = await ctx.prisma.sale.create({
        data: {
          saleNumber: 'TRX-MORNING-001',
          customerId: testCustomerId,
          cashierId: ctx.users.cashier.id,
          status: SaleStatus.COMPLETED,
          subtotal: 10000,
          totalAmount: 10000,
          paymentMethod: PaymentMethod.CASH,
          amountPaid: 10000,
          changeAmount: 0,
          saleDate: new Date(morningSession.getTime() + 30 * 60 * 1000), // 30 min after start
          customerName: 'Morning Customer',
        },
      });

      // Create sales for afternoon session
      const afternoonSale = await ctx.prisma.sale.create({
        data: {
          saleNumber: 'TRX-AFTERNOON-001',
          customerId: testCustomerId,
          cashierId: ctx.users.cashier.id,
          status: SaleStatus.COMPLETED,
          subtotal: 20000,
          totalAmount: 20000,
          paymentMethod: PaymentMethod.TRANSFER,
          amountPaid: 20000,
          changeAmount: 0,
          saleDate: new Date(afternoonSession.getTime() + 30 * 60 * 1000), // 30 min after start
          customerName: 'Afternoon Customer',
        },
      });

      // Test morning session isolation
      const morningSessionEncoded = encodeURIComponent(morningSession.toISOString());
      const morningEndTime = new Date(morningSession.getTime() + 4 * 60 * 60 * 1000); // 4 hours later
      const morningEndEncoded = encodeURIComponent(morningEndTime.toISOString());

      const morningResponse = await ctx.request
        .get(`/api/sales/session/${ctx.users.cashier.id}/${morningSessionEncoded}?sessionEndTime=${morningEndEncoded}`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(morningResponse);
      expect(morningResponse.body.data).toHaveLength(1);
      expect(morningResponse.body.data[0].saleNumber).toBe('TRX-MORNING-001');

      // Test afternoon session isolation
      const afternoonSessionEncoded = encodeURIComponent(afternoonSession.toISOString());
      const afternoonEndTime = new Date(afternoonSession.getTime() + 4 * 60 * 60 * 1000); // 4 hours later
      const afternoonEndEncoded = encodeURIComponent(afternoonEndTime.toISOString());

      const afternoonResponse = await ctx.request
        .get(`/api/sales/session/${ctx.users.cashier.id}/${afternoonSessionEncoded}?sessionEndTime=${afternoonEndEncoded}`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(afternoonResponse);
      expect(afternoonResponse.body.data).toHaveLength(1);
      expect(afternoonResponse.body.data[0].saleNumber).toBe('TRX-AFTERNOON-001');

      // Cleanup
      await ctx.prisma.sale.deleteMany({
        where: { id: { in: [morningSale.id, afternoonSale.id] } }
      });
    });

    it('should handle sessions spanning midnight correctly', async () => {
      // Create a session that starts before midnight and has sales after midnight
      const beforeMidnight = new Date();
      beforeMidnight.setHours(23, 30, 0, 0); // 11:30 PM

      const afterMidnight = new Date(beforeMidnight);
      afterMidnight.setDate(afterMidnight.getDate() + 1);
      afterMidnight.setHours(0, 30, 0, 0); // 12:30 AM next day

      // Create sale before midnight
      const saleBefore = await ctx.prisma.sale.create({
        data: {
          saleNumber: 'TRX-BEFORE-MIDNIGHT',
          customerId: testCustomerId,
          cashierId: ctx.users.cashier.id,
          status: SaleStatus.COMPLETED,
          subtotal: 15000,
          totalAmount: 15000,
          paymentMethod: PaymentMethod.CASH,
          amountPaid: 15000,
          changeAmount: 0,
          saleDate: new Date(beforeMidnight.getTime() + 15 * 60 * 1000), // 15 min after session start
          customerName: 'Before Midnight Customer',
        },
      });

      // Create sale after midnight
      const saleAfter = await ctx.prisma.sale.create({
        data: {
          saleNumber: 'TRX-AFTER-MIDNIGHT',
          customerId: testCustomerId,
          cashierId: ctx.users.cashier.id,
          status: SaleStatus.COMPLETED,
          subtotal: 25000,
          totalAmount: 25000,
          paymentMethod: PaymentMethod.TRANSFER,
          amountPaid: 25000,
          changeAmount: 0,
          saleDate: afterMidnight,
          customerName: 'After Midnight Customer',
        },
      });

      // Test session that spans midnight
      const sessionStartEncoded = encodeURIComponent(beforeMidnight.toISOString());
      const sessionEndTime = new Date(afterMidnight.getTime() + 60 * 60 * 1000); // 1 hour after midnight sale
      const sessionEndEncoded = encodeURIComponent(sessionEndTime.toISOString());

      const response = await ctx.request
        .get(`/api/sales/session/${ctx.users.cashier.id}/${sessionStartEncoded}?sessionEndTime=${sessionEndEncoded}`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      expect(response.body.data).toHaveLength(2);

      const saleNumbers = response.body.data.map((sale: any) => sale.saleNumber);
      expect(saleNumbers).toContain('TRX-BEFORE-MIDNIGHT');
      expect(saleNumbers).toContain('TRX-AFTER-MIDNIGHT');

      // Cleanup
      await ctx.prisma.sale.deleteMany({
        where: { id: { in: [saleBefore.id, saleAfter.id] } }
      });
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle very short sessions (less than 1 minute)', async () => {
      const shortSessionStart = new Date();
      const shortSessionEnd = new Date(shortSessionStart.getTime() + 30 * 1000); // 30 seconds later

      const sessionStartEncoded = encodeURIComponent(shortSessionStart.toISOString());
      const sessionEndEncoded = encodeURIComponent(shortSessionEnd.toISOString());

      const response = await ctx.request
        .get(`/api/sales/session/${ctx.users.cashier.id}/${sessionStartEncoded}?sessionEndTime=${sessionEndEncoded}`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      expect(response.body.data).toHaveLength(0);
      expect(response.body.meta.total).toBe(0);
    });

    it('should handle session end time before start time', async () => {
      const sessionStart = new Date();
      const sessionEnd = new Date(sessionStart.getTime() - 60 * 60 * 1000); // 1 hour before start

      const sessionStartEncoded = encodeURIComponent(sessionStart.toISOString());
      const sessionEndEncoded = encodeURIComponent(sessionEnd.toISOString());

      const response = await ctx.request
        .get(`/api/sales/session/${ctx.users.cashier.id}/${sessionStartEncoded}?sessionEndTime=${sessionEndEncoded}`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      expect(response.body.data).toHaveLength(0);
    });

    it('should handle future session start time', async () => {
      const futureSession = new Date(Date.now() + 60 * 60 * 1000); // 1 hour in future
      const sessionStartEncoded = encodeURIComponent(futureSession.toISOString());

      const response = await ctx.request
        .get(`/api/sales/session/${ctx.users.cashier.id}/${sessionStartEncoded}`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      expect(response.body.data).toHaveLength(0);
    });

    it('should handle malformed session end time query parameter', async () => {
      const sessionStartEncoded = encodeURIComponent(session1StartTime.toISOString());

      const response = await ctx.request
        .get(`/api/sales/session/${ctx.users.cashier.id}/${sessionStartEncoded}?sessionEndTime=invalid-date`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expect(response.status).toBe(400);
    });

    it('should handle URL encoding edge cases', async () => {
      // Test with special characters in date string
      const sessionStart = new Date('2024-01-01T12:00:00.000Z');
      const sessionStartEncoded = encodeURIComponent(sessionStart.toISOString());

      const response = await ctx.request
        .get(`/api/sales/session/${ctx.users.cashier.id}/${sessionStartEncoded}`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
    });
  });

  describe('Authorization and Access Control', () => {
    it('should allow admin to access any cashier session data', async () => {
      const sessionStartEncoded = encodeURIComponent(session1StartTime.toISOString());

      const response = await ctx.request
        .get(`/api/sales/session/${ctx.users.cashier.id}/${sessionStartEncoded}`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.data.length).toBeGreaterThan(0);
    });

    it('should allow pharmacist to access any cashier session data', async () => {
      const sessionStartEncoded = encodeURIComponent(session1StartTime.toISOString());

      const response = await ctx.request
        .get(`/api/sales/session/${ctx.users.cashier.id}/${sessionStartEncoded}`)
        .set('Authorization', `Bearer ${ctx.users.pharmacist.accessToken}`);

      expectSuccess(response);
      expect(response.body.data.length).toBeGreaterThan(0);
    });

    it('should allow cashier to access their own session data', async () => {
      const sessionStartEncoded = encodeURIComponent(session1StartTime.toISOString());

      const response = await ctx.request
        .get(`/api/sales/session/${ctx.users.cashier.id}/${sessionStartEncoded}`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      expect(response.body.data.length).toBeGreaterThan(0);
    });
  });

  describe('Data Consistency Verification', () => {
    it('should verify sales data matches statistics calculations', async () => {
      const sessionStartEncoded = encodeURIComponent(session1StartTime.toISOString());
      const sessionEndEncoded = encodeURIComponent(session1EndTime.toISOString());

      // Get sales data
      const salesResponse = await ctx.request
        .get(`/api/sales/session/${ctx.users.cashier.id}/${sessionStartEncoded}?sessionEndTime=${sessionEndEncoded}`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      // Get statistics
      const statsResponse = await ctx.request
        .get(`/api/sales/session-stats/${ctx.users.cashier.id}/${sessionStartEncoded}/stats?sessionEndTime=${sessionEndEncoded}`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(salesResponse);
      expectSuccess(statsResponse);

      const sales = salesResponse.body.data;
      const stats = statsResponse.body;

      // Verify count matches
      expect(stats.totalSalesCount).toBe(sales.length);

      // Verify total revenue matches
      const calculatedRevenue = sales.reduce((sum: number, sale: any) => {
        const amount = typeof sale.totalAmount === 'string' ? parseFloat(sale.totalAmount) : Number(sale.totalAmount);
        return sum + amount;
      }, 0);
      expect(stats.totalRevenue).toBe(calculatedRevenue);

      // Verify average transaction value
      const expectedAverage = sales.length > 0 ? calculatedRevenue / sales.length : 0;
      expect(stats.averageTransactionValue).toBe(expectedAverage);

      // Verify payment method breakdown
      const paymentMethodCounts: Record<string, { count: number; amount: number }> = {};
      sales.forEach((sale: any) => {
        if (!paymentMethodCounts[sale.paymentMethod]) {
          paymentMethodCounts[sale.paymentMethod] = { count: 0, amount: 0 };
        }
        paymentMethodCounts[sale.paymentMethod].count += 1;
        const amount = typeof sale.totalAmount === 'string' ? parseFloat(sale.totalAmount) : Number(sale.totalAmount);
        paymentMethodCounts[sale.paymentMethod].amount += amount;
      });

      Object.keys(paymentMethodCounts).forEach(method => {
        expect(stats.paymentMethodBreakdown[method]).toEqual(paymentMethodCounts[method]);
      });
    });

    it('should verify time boundaries are respected', async () => {
      const sessionStartEncoded = encodeURIComponent(session1StartTime.toISOString());
      const sessionEndEncoded = encodeURIComponent(session1EndTime.toISOString());

      const response = await ctx.request
        .get(`/api/sales/session/${ctx.users.cashier.id}/${sessionStartEncoded}?sessionEndTime=${sessionEndEncoded}`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);

      response.body.data.forEach((sale: any) => {
        const saleDate = new Date(sale.saleDate);
        expect(saleDate.getTime()).toBeGreaterThanOrEqual(session1StartTime.getTime());
        expect(saleDate.getTime()).toBeLessThanOrEqual(session1EndTime.getTime());
      });
    });
  });
});
