import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { PrismaService } from '../../src/prisma/prisma.service';
import { SalesService } from '../../src/sales/sales.service';
import { InventoryService } from '../../src/inventory/inventory.service';
import { UnitConversionService } from '../../src/common/services/unit-conversion.service';
import { IntegrationTestSetup, TestContext } from './test-setup';
import { UnitType, ProductType, ProductCategory, MedicineClassification } from '@prisma/client';

describe('Stock Validation Bug Integration Tests', () => {
  let ctx: TestContext;
  let testSetup: IntegrationTestSetup;
  let testData: any;

  beforeAll(async () => {
    testSetup = new IntegrationTestSetup();
    ctx = await testSetup.setup();

    // Create test data
    testData = await setupIntegrationTestData();
  });

  afterAll(async () => {
    await cleanupTestData();
    await testSetup.teardown();
  });

  async function setupIntegrationTestData() {
    // Clean up any existing test data
    await cleanupTestData();

    // Create test units
    const units = await createTestUnits();

    // Create test products with unit hierarchies
    const products = await createTestProducts(units);

    // Create test users
    const users = await createTestUsers();

    // Create test inventory items
    const inventoryItems = await createTestInventoryItems(products, units);

    return { units, products, inventoryItems, users };
  }

  async function createTestUnits() {
    const unitData = [
      { name: 'Tablet', abbreviation: 'tab', type: UnitType.COUNT, isBaseUnit: true },
      { name: 'Strip', abbreviation: 'strip', type: UnitType.PACKAGE, isBaseUnit: false },
      { name: 'Box', abbreviation: 'box', type: UnitType.PACKAGE, isBaseUnit: false },
    ];

    const units: any = {};

    for (const data of unitData) {
      const unit = await ctx.prisma.productUnit.create({ data });
      units[data.name.toLowerCase()] = unit;
    }

    return units;
  }

  async function createTestProducts(units: any) {
    const products: any = {};

    // Alprazolam (Tablet → Strip → Box) - matches the bug scenario exactly
    const alprazolam = await ctx.prisma.product.create({
      data: {
        code: 'MED-009',
        name: 'Alprazolam 0.5mg',
        genericName: 'Alprazolam',
        type: ProductType.MEDICINE,
        category: ProductCategory.OTHER,
        manufacturer: 'PT Dexa Medica',
        bpomNumber: 'DKL9012345678I9',
        medicineClassification: MedicineClassification.PSIKOTROPIKA,
        regulatorySymbol: 'Lingkaran biru dengan tanda silang merah - Obat psikotropika yang memerlukan resep dokter dan pencatatan khusus',
        baseUnitId: units.tablet.id,
        minimumStock: 20,
        maximumStock: 100,
        reorderPoint: 30,
        description: 'Obat psikotropika untuk gangguan kecemasan',
        activeIngredient: 'Alprazolam 0.5mg',
        strength: '0.5mg',
        dosageForm: 'Tablet',
        indication: 'Gangguan kecemasan, gangguan panik',
        contraindication: 'Hipersensitif terhadap benzodiazepine, glaukoma sudut sempit, myasthenia gravis',
        sideEffects: 'Mengantuk, pusing, gangguan koordinasi, ketergantungan',
        dosage: 'Dewasa: 0.25-0.5mg, 2-3 kali sehari sesuai petunjuk dokter',
        storage: 'Simpan di tempat sejuk dan kering, jauh dari jangkauan anak-anak, dalam lemari khusus psikotropika',
        isActive: true,
      }
    });

    // Create unit hierarchies for Alprazolam - exact same as the bug scenario
    await ctx.prisma.productUnitHierarchy.createMany({
      data: [
        {
          productId: alprazolam.id,
          unitId: units.tablet.id,
          conversionFactor: 1,
          level: 0,
          sellingPrice: 5000,
          costPrice: 3000,
          isActive: true,
        },
        {
          productId: alprazolam.id,
          unitId: units.strip.id,
          conversionFactor: 10, // 1 strip = 10 tablets
          level: 1,
          sellingPrice: 47500,
          costPrice: 28500,
          isActive: true,
        },
        {
          productId: alprazolam.id,
          unitId: units.box.id,
          conversionFactor: 30, // 1 box = 30 tablets
          level: 2,
          sellingPrice: 140000,
          costPrice: 85000,
          isActive: true,
        },
      ]
    });

    products.alprazolam = alprazolam;

    return products;
  }

  async function createTestUsers() {
    // Use the existing users from the test context
    return { admin: ctx.users.admin, cashier: ctx.users.cashier };
  }

  async function createTestInventoryItems(products: any, units: any) {
    const inventoryItems: any = {};

    // Create inventory for Alprazolam - exact same stock as the bug scenario
    inventoryItems.alprazolam = await ctx.prisma.inventoryItem.create({
      data: {
        productId: products.alprazolam.id,
        unitId: units.tablet.id, // Base unit
        batchNumber: 'ALPRA-BATCH-001',
        quantityOnHand: 858, // 858 tablets (from original bug scenario)
        quantityAllocated: 0,
        costPrice: 3000,
        expiryDate: new Date('2025-12-31'),
        receivedDate: new Date('2024-01-01'),
        isActive: true,
      }
    });

    return inventoryItems;
  }

  async function cleanupTestData() {
    // Clean up in correct order to respect foreign key constraints
    try {
      await ctx.prisma.saleItem.deleteMany();
      await ctx.prisma.sale.deleteMany();
      await ctx.prisma.inventoryItem.deleteMany();
      await ctx.prisma.productUnitHierarchy.deleteMany();
      await ctx.prisma.product.deleteMany();
      await ctx.prisma.productUnit.deleteMany();
    } catch (error) {
      // Ignore errors during cleanup
    }
  }

  describe('Stock Validation Bug Reproduction', () => {
    it('should fail when attempting to sell more than available stock (mixed units)', async () => {
      // This test reproduces the exact bug scenario:
      // - Product: Alprazolam 0.5mg (ID: cmbvt6d7v00aippgc3gl01whg)
      // - Available stock: 858 tablets
      // - Attempting to sell: 28 boxes + 22 strips = 28*30 + 22*10 = 840 + 220 = 1060 tablets
      // - Expected: Should fail with insufficient stock error
      // - Actual bug: Sale succeeds when it should fail

      const salesService = ctx.app.get<SalesService>(SalesService);
      const inventoryService = ctx.app.get<InventoryService>(InventoryService);
      const unitConversionService = ctx.app.get<UnitConversionService>(UnitConversionService);

      const productId = testData.products.alprazolam.id;
      const boxUnitId = testData.units.box.id;
      const stripUnitId = testData.units.strip.id;
      const tabletUnitId = testData.units.tablet.id;

      // Verify initial stock
      const initialStock = await inventoryService.getAvailableStock(productId);
      expect(initialStock).toBe(858);

      // Prepare sale items that exceed available stock
      const saleItems = [
        {
          productId: productId,
          unitId: boxUnitId,
          quantity: 28,
          unitPrice: 140000
        },
        {
          productId: productId,
          unitId: stripUnitId,
          quantity: 22,
          unitPrice: 47500
        }
      ];

      // Validate each item's conversion to base units
      let totalDemandInBaseUnits = 0;

      for (const item of saleItems) {
        const conversionResult = await unitConversionService.convertToBaseUnit(
          item.productId,
          item.unitId,
          item.quantity
        );

        expect(conversionResult.success).toBe(true);
        totalDemandInBaseUnits += conversionResult.convertedQuantity!;
      }

      // Verify the conversion calculations
      expect(totalDemandInBaseUnits).toBe(1060); // 28*30 + 22*10 = 840 + 220 = 1060

      // Check available stock
      const availableStock = await inventoryService.getAvailableStock(productId);
      expect(availableStock).toBe(858);

      // Verify that demand exceeds available stock
      expect(totalDemandInBaseUnits > availableStock).toBe(true);

      // Prepare sale data
      const createSaleDto = {
        items: saleItems,
        paymentMethod: 'CASH' as any,
        amountPaid: 4965000,
        cashierId: testData.users.cashier.id
      };

      // This should fail with insufficient stock error (and it does - stock validation works correctly)
      await expect(salesService.create(createSaleDto)).rejects.toThrow(
        'Stok tidak mencukupi untuk produk Alprazolam 0.5mg. Tersedia: 858 Tablet, Diminta: 1060 Tablet'
      );
    });

    it('should correctly validate stock for individual units', async () => {
      // Test individual unit validation to ensure the conversion logic works correctly
      const unitConversionService = ctx.app.get<UnitConversionService>(UnitConversionService);

      const productId = testData.products.alprazolam.id;
      const boxUnitId = testData.units.box.id;
      const stripUnitId = testData.units.strip.id;
      const tabletUnitId = testData.units.tablet.id;

      // Test box conversion (30 tablets per box)
      const boxConversion = await unitConversionService.convertToBaseUnit(productId, boxUnitId, 1);
      expect(boxConversion.success).toBe(true);
      expect(boxConversion.convertedQuantity).toBe(30);

      // Test strip conversion (10 tablets per strip)
      const stripConversion = await unitConversionService.convertToBaseUnit(productId, stripUnitId, 1);
      expect(stripConversion.success).toBe(true);
      expect(stripConversion.convertedQuantity).toBe(10);

      // Test tablet conversion (1 tablet per tablet)
      const tabletConversion = await unitConversionService.convertToBaseUnit(productId, tabletUnitId, 1);
      expect(tabletConversion.success).toBe(true);
      expect(tabletConversion.convertedQuantity).toBe(1);
    });

    it('should allow sales when stock is sufficient', async () => {
      // Test that sales work correctly when stock is sufficient
      const salesService = ctx.app.get<SalesService>(SalesService);

      const productId = testData.products.alprazolam.id;
      const tabletUnitId = testData.units.tablet.id;

      // Try to sell a small amount that should succeed
      const createSaleDto = {
        items: [{
          productId: productId,
          unitId: tabletUnitId,
          quantity: 10, // Only 10 tablets, well within the 858 available
          unitPrice: 5000
        }],
        paymentMethod: 'CASH' as any,
        amountPaid: 50000,
        cashierId: testData.users.cashier.id
      };

      // This should succeed
      const result = await salesService.create(createSaleDto);
      expect(result).toBeDefined();
      expect(result.status).toBe('COMPLETED');
      // Note: The sales service doesn't return saleItems in the response,
      // so we'll just check the basic properties
    });

    it('should allow draft sales even with insufficient stock (potential bug source)', async () => {
      // This test checks if the user might have used the draft endpoint by mistake
      const salesService = ctx.app.get<SalesService>(SalesService);

      const productId = testData.products.alprazolam.id;
      const boxUnitId = testData.units.box.id;
      const stripUnitId = testData.units.strip.id;

      // Prepare sale items that exceed available stock (same as the bug scenario)
      const saleItems = [
        {
          productId: productId,
          unitId: boxUnitId,
          quantity: 28,
          unitPrice: 140000
        },
        {
          productId: productId,
          unitId: stripUnitId,
          quantity: 22,
          unitPrice: 47500
        }
      ];

      // Prepare sale data
      const createSaleDto = {
        items: saleItems,
        paymentMethod: 'CASH' as any,
        amountPaid: 4965000,
        cashierId: testData.users.cashier.id
      };

      // Draft sales should succeed even with insufficient stock (this might be the bug source)
      const result = await salesService.createDraft(createSaleDto);
      expect(result).toBeDefined();
      expect(result.status).toBe('DRAFT');

      // This demonstrates that draft sales bypass stock validation
      // If the user accidentally used POST /api/sales/draft instead of POST /api/sales,
      // they would see a successful transaction creation
    });

    it('should fail when completing a draft sale with insufficient stock (bug fix verification)', async () => {
      // This test verifies the fix for the real bug: completing draft sales should validate stock
      const salesService = ctx.app.get<SalesService>(SalesService);

      const productId = testData.products.alprazolam.id;
      const boxUnitId = testData.units.box.id;
      const stripUnitId = testData.units.strip.id;

      // Prepare sale items that exceed available stock (same as the bug scenario)
      const saleItems = [
        {
          productId: productId,
          unitId: boxUnitId,
          quantity: 28,
          unitPrice: 140000
        },
        {
          productId: productId,
          unitId: stripUnitId,
          quantity: 22,
          unitPrice: 47500
        }
      ];

      // Prepare sale data
      const createSaleDto = {
        items: saleItems,
        paymentMethod: 'CASH' as any,
        amountPaid: 4965000,
        cashierId: testData.users.cashier.id
      };

      // First, create a draft sale (this should succeed)
      const draftSale = await salesService.createDraft(createSaleDto);
      expect(draftSale).toBeDefined();
      expect(draftSale.status).toBe('DRAFT');

      // Now try to complete the draft sale - this should fail with stock validation error
      await expect(salesService.completeSale(draftSale.id, testData.users.cashier.id)).rejects.toThrow(
        /Stok tidak mencukupi untuk produk Alprazolam 0\.5mg/
      );

      // Verify the sale is still in DRAFT status
      const saleAfterFailedCompletion = await ctx.prisma.sale.findUnique({
        where: { id: draftSale.id }
      });
      expect(saleAfterFailedCompletion?.status).toBe('DRAFT');
    });

    it('should handle null/undefined stock values correctly (parseInt bug fix)', async () => {
      // This test verifies the fix for the parseInt(null) bug in getAvailableStock
      const inventoryService = ctx.app.get<InventoryService>(InventoryService);

      // Create a product with no inventory items (should return 0, not NaN)
      const testProduct = await ctx.prisma.product.create({
        data: {
          code: 'TEST-NULL-STOCK',
          name: 'Test Product with No Stock',
          genericName: 'Test Generic',
          type: 'MEDICINE',
          category: 'OTHER',
          manufacturer: 'Test Manufacturer',
          baseUnitId: testData.units.tablet.id,
          minimumStock: 10,
          maximumStock: 100,
          reorderPoint: 20,
          isActive: true,
        }
      });

      // Get available stock for product with no inventory items
      const availableStock = await inventoryService.getAvailableStock(testProduct.id);

      // Should return 0, not NaN or undefined
      expect(availableStock).toBe(0);
      expect(typeof availableStock).toBe('number');
      expect(isNaN(availableStock)).toBe(false);

      // Clean up
      await ctx.prisma.product.delete({ where: { id: testProduct.id } });
    });
  });
});
