import { IntegrationTestSetup, TestContext, expectValidationError, expectUnauthorized, expectForbidden, expectNotFound, expectSuccess } from './test-setup';
import * as fs from 'fs';
import * as path from 'path';

describe('Product Import/Export Integration Tests', () => {
  let testSetup: IntegrationTestSetup;
  let ctx: TestContext;
  let baseUnitId: string;

  beforeAll(async () => {
    testSetup = new IntegrationTestSetup();
    ctx = await testSetup.setup();

    // Create a test base unit for product imports
    const baseUnit = await ctx.prisma.productUnit.create({
      data: {
        name: 'Test Unit',
        abbreviation: 'tu',
        type: 'COUNT',
        isBaseUnit: true,
        description: 'Test unit for import',
        isActive: true,
      },
    });
    baseUnitId = baseUnit.id;
  });

  afterAll(async () => {
    await testSetup.teardown();
  });

  describe('GET /api/products/template', () => {
    it('should download CSV template as admin', async () => {
      const response = await ctx.request
        .get('/api/products/template')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.headers['content-type']).toContain('text/csv');
      expect(response.headers['content-disposition']).toContain('attachment; filename="template-product.csv"');
      expect(response.text).toContain('Kode Produk');
      expect(response.text).toContain('Nama Produk');
      expect(response.text).toContain('Jenis Produk');
    });

    it('should download CSV template as pharmacist', async () => {
      const response = await ctx.request
        .get('/api/products/template')
        .set('Authorization', `Bearer ${ctx.users.pharmacist.accessToken}`);

      expectSuccess(response);
      expect(response.headers['content-type']).toContain('text/csv');
    });

    it('should require authentication', async () => {
      const response = await ctx.request
        .get('/api/products/template');

      expectUnauthorized(response);
    });

    it('should require manager role or higher', async () => {
      const response = await ctx.request
        .get('/api/products/template')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectForbidden(response);
    });
  });

  describe('POST /api/products/import', () => {
    const createTestCSV = (data: any[]) => {
      const headers = [
        'Kode Produk', 'Nama Produk', 'Nama Generik', 'Jenis Produk', 'Kategori',
        'Produsen', 'Nomor BPOM', 'Klasifikasi Obat', 'Simbol Regulasi', 'ID Unit Dasar',
        'Stok Minimum', 'Stok Maksimum', 'Titik Reorder', 'Deskripsi', 'Bahan Aktif',
        'Kekuatan', 'Bentuk Sediaan', 'Indikasi', 'Kontraindikasi', 'Efek Samping',
        'Dosis', 'Penyimpanan', 'Catatan'
      ];

      const csvContent = [
        headers.join(','),
        ...data.map(row => headers.map(header => {
          const value = row[header] || '';
          return `"${value.toString().replace(/"/g, '""')}"`;
        }).join(','))
      ].join('\n');

      return csvContent;
    };

    it('should import valid products successfully as admin', async () => {
      const testData = [
        {
          'Kode Produk': 'TEST-001',
          'Nama Produk': 'Test Product 1',
          'Nama Generik': 'Test Generic',
          'Jenis Produk': 'MEDICINE',
          'Kategori': 'ANALGESIC',
          'Produsen': 'Test Pharma',
          'Nomor BPOM': 'DKL1234567890A1',
          'Klasifikasi Obat': 'OBAT_BEBAS',
          'Simbol Regulasi': 'Lingkaran Hijau',
          'ID Unit Dasar': baseUnitId,
          'Stok Minimum': '10',
          'Stok Maksimum': '100',
          'Titik Reorder': '20',
          'Deskripsi': 'Test product description',
          'Bahan Aktif': 'Test Active Ingredient',
          'Kekuatan': '500mg',
          'Bentuk Sediaan': 'Tablet',
          'Indikasi': 'Test indication',
          'Kontraindikasi': 'Test contraindication',
          'Efek Samping': 'Test side effects',
          'Dosis': 'Test dosage',
          'Penyimpanan': 'Test storage',
          'Catatan': 'Test notes'
        }
      ];

      const csvContent = createTestCSV(testData);
      const tempFilePath = path.join(__dirname, 'temp-import.csv');
      fs.writeFileSync(tempFilePath, csvContent);

      try {
        const response = await ctx.request
          .post('/api/products/import')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .attach('file', tempFilePath);

        expectSuccess(response, 201);
        expect(response.body.success).toBe(true);
        expect(response.body.totalRows).toBe(1);
        expect(response.body.successfulImports).toBe(1);
        expect(response.body.failedImports).toBe(0);
        expect(response.body.importedProducts).toHaveLength(1);

        // Verify product was created in database
        const product = await ctx.prisma.product.findUnique({
          where: { code: 'TEST-001' }
        });
        expect(product).toBeTruthy();
        expect(product!.name).toBe('Test Product 1');
      } finally {
        if (fs.existsSync(tempFilePath)) {
          fs.unlinkSync(tempFilePath);
        }
      }
    });

    it('should handle validation errors', async () => {
      const testData = [
        {
          'Kode Produk': '', // Missing required field
          'Nama Produk': 'Test Product 2',
          'Jenis Produk': 'INVALID_TYPE', // Invalid enum
          'Kategori': 'ANALGESIC',
          'Klasifikasi Obat': 'OBAT_BEBAS',
          'ID Unit Dasar': 'invalid-unit-id', // Invalid unit ID
        }
      ];

      const csvContent = createTestCSV(testData);
      const tempFilePath = path.join(__dirname, 'temp-import-invalid.csv');
      fs.writeFileSync(tempFilePath, csvContent);

      try {
        const response = await ctx.request
          .post('/api/products/import')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .attach('file', tempFilePath);

        expectSuccess(response, 201);
        expect(response.body.success).toBe(false);
        expect(response.body.totalRows).toBe(1);
        expect(response.body.successfulImports).toBe(0);
        expect(response.body.failedImports).toBe(1);
        expect(response.body.errors).toHaveLength(1);
        expect(response.body.errors[0].errors).toContain('Kode produk wajib diisi');
      } finally {
        if (fs.existsSync(tempFilePath)) {
          fs.unlinkSync(tempFilePath);
        }
      }
    });

    it('should require authentication', async () => {
      const response = await ctx.request
        .post('/api/products/import');

      expectUnauthorized(response);
    });

    it('should require manager role or higher', async () => {
      const response = await ctx.request
        .post('/api/products/import')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectForbidden(response);
    });

    it('should reject non-CSV files', async () => {
      const tempFilePath = path.join(__dirname, 'temp-import.txt');
      fs.writeFileSync(tempFilePath, 'This is not a CSV file');

      try {
        const response = await ctx.request
          .post('/api/products/import')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .attach('file', tempFilePath);

        expectValidationError(response);
      } finally {
        if (fs.existsSync(tempFilePath)) {
          fs.unlinkSync(tempFilePath);
        }
      }
    });
  });

  describe('GET /api/products/export', () => {
    beforeAll(async () => {
      // Create test products for export
      await ctx.prisma.product.createMany({
        data: [
          {
            code: 'EXPORT-001',
            name: 'Export Test Product 1',
            type: 'MEDICINE',
            category: 'ANALGESIC',
            medicineClassification: 'OBAT_BEBAS',
            baseUnitId: baseUnitId,
            isActive: true,
            createdBy: ctx.users.admin.id,
            updatedBy: ctx.users.admin.id,
          },
          {
            code: 'EXPORT-002',
            name: 'Export Test Product 2',
            type: 'MEDICAL_DEVICE',
            category: 'MEDICAL_DEVICE',
            medicineClassification: 'NON_MEDICINE',
            baseUnitId: baseUnitId,
            isActive: false,
            createdBy: ctx.users.admin.id,
            updatedBy: ctx.users.admin.id,
          }
        ]
      });
    });

    it('should export all products as CSV', async () => {
      const response = await ctx.request
        .get('/api/products/export?format=csv')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.headers['content-type']).toContain('text/csv');
      expect(response.headers['content-disposition']).toContain('attachment; filename="products-export-');
      expect(response.text).toContain('Kode Produk');
      expect(response.text).toContain('EXPORT-001');
      expect(response.text).toContain('EXPORT-002');
    });

    it('should export filtered products', async () => {
      const response = await ctx.request
        .get('/api/products/export?format=csv&type=MEDICINE&isActive=true')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.text).toContain('EXPORT-001');
      expect(response.text).not.toContain('EXPORT-002'); // Should be filtered out (inactive)
    });

    it('should export as XLSX format', async () => {
      const response = await ctx.request
        .get('/api/products/export?format=xlsx')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.headers['content-type']).toBe('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      expect(response.headers['content-disposition']).toContain('attachment; filename="products-export-');
    });

    it('should require authentication', async () => {
      const response = await ctx.request
        .get('/api/products/export');

      expectUnauthorized(response);
    });

    it('should require manager role or higher', async () => {
      const response = await ctx.request
        .get('/api/products/export')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectForbidden(response);
    });
  });
});
