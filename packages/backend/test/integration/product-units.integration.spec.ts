import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../../src/app.module';
import { PrismaService } from '../../src/prisma/prisma.service';
import { JwtService } from '@nestjs/jwt';

describe('Product Units API (e2e)', () => {
  let app: INestApplication;
  let prisma: PrismaService;
  let jwtService: JwtService;
  let authToken: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    prisma = moduleFixture.get<PrismaService>(PrismaService);
    jwtService = moduleFixture.get<JwtService>(JwtService);

    // Configure validation pipe with transformation (same as main.ts)
    app.useGlobalPipes(
      new ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
      }),
    );

    // Set global prefix to match main application
    app.setGlobalPrefix('api');

    await app.init();

    // Create test user and get auth token
    const testUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        password: 'hashedpassword',
        role: 'ADMIN',
      },
    });

    authToken = jwtService.sign({
      sub: testUser.id,
      email: testUser.email,
      role: testUser.role,
    });

    // Create test units
    await prisma.productUnit.createMany({
      data: [
        {
          name: 'Tablet',
          abbreviation: 'tab',
          type: 'COUNT',
          isBaseUnit: true,
          description: 'Tablet - bentuk sediaan padat bulat atau oval',
          isActive: true,
        },
        {
          name: 'Kapsul',
          abbreviation: 'kaps',
          type: 'COUNT',
          isBaseUnit: true,
          description: 'Kapsul - bentuk sediaan berupa cangkang keras atau lunak',
          isActive: true,
        },
        {
          name: 'Mililiter',
          abbreviation: 'ml',
          type: 'VOLUME',
          isBaseUnit: true,
          description: 'Mililiter - satuan volume untuk sediaan cair',
          isActive: true,
        },
        {
          name: 'Strip',
          abbreviation: 'strip',
          type: 'PACKAGE',
          isBaseUnit: false,
          description: 'Strip/Blister - kemasan lembaran untuk tablet/kapsul',
          isActive: true,
        },
        {
          name: 'Kaplet',
          abbreviation: 'kaplet',
          type: 'COUNT',
          isBaseUnit: true,
          description: 'Kaplet - tablet salut selaput berbentuk kapsul',
          isActive: true,
        },
        {
          name: 'Puyer',
          abbreviation: 'puyer',
          type: 'COUNT',
          isBaseUnit: true,
          description: 'Puyer - serbuk obat yang dibungkus kertas perkamen',
          isActive: true,
        },
        {
          name: 'Supositoria',
          abbreviation: 'supp',
          type: 'COUNT',
          isBaseUnit: true,
          description: 'Supositoria - sediaan padat untuk rektal/vaginal',
          isActive: true,
        },
        {
          name: 'Ovula',
          abbreviation: 'ovul',
          type: 'COUNT',
          isBaseUnit: true,
          description: 'Ovula - supositoria khusus untuk vaginal',
          isActive: true,
        },
        {
          name: 'Gram',
          abbreviation: 'g',
          type: 'WEIGHT',
          isBaseUnit: true,
          description: 'Gram - satuan berat untuk sediaan semi padat',
          isActive: true,
        },
        {
          name: 'Inactive Unit',
          abbreviation: 'ina',
          type: 'COUNT',
          isBaseUnit: true,
          description: 'Inactive unit for testing',
          isActive: false,
        },
      ],
    });
  });

  afterAll(async () => {
    await prisma.user.deleteMany({
      where: { email: '<EMAIL>' },
    });
    await prisma.productUnit.deleteMany({
      where: {
        name: {
          in: ['Tablet', 'Kapsul', 'Mililiter', 'Strip', 'Kaplet', 'Puyer', 'Supositoria', 'Ovula', 'Gram', 'Inactive Unit']
        }
      },
    });
    await app.close();
  });

  describe('GET /api/product-units', () => {
    it('should return all product units', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/product-units')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
      
      // Check structure of first unit
      const firstUnit = response.body[0];
      expect(firstUnit).toHaveProperty('id');
      expect(firstUnit).toHaveProperty('name');
      expect(firstUnit).toHaveProperty('abbreviation');
      expect(firstUnit).toHaveProperty('type');
      expect(firstUnit).toHaveProperty('isBaseUnit');
      expect(firstUnit).toHaveProperty('isActive');
    });

    it('should filter by active status', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/product-units?isActive=true')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      response.body.forEach(unit => {
        expect(unit.isActive).toBe(true);
      });
    });

    it('should filter by base unit status', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/product-units?isBaseUnit=true')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      response.body.forEach(unit => {
        expect(unit.isBaseUnit).toBe(true);
      });
    });

    it('should search by name', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/product-units?search=tablet')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
      
      // Should find tablet unit
      const tabletUnit = response.body.find(unit => 
        unit.name.toLowerCase().includes('tablet') || 
        unit.abbreviation.toLowerCase().includes('tab')
      );
      expect(tabletUnit).toBeDefined();
    });

    it('should sort by name', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/product-units?sortBy=name&sortOrder=asc')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      
      // Check if sorted alphabetically
      for (let i = 1; i < response.body.length; i++) {
        expect(response.body[i].name >= response.body[i - 1].name).toBe(true);
      }
    });

    it('should require authentication', async () => {
      await request(app.getHttpServer())
        .get('/api/product-units')
        .expect(401);
    });
  });

  describe('GET /api/product-units/base-units', () => {
    it('should return only base units', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/product-units/base-units')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
      
      // All units should be base units and active
      response.body.forEach(unit => {
        expect(unit.isBaseUnit).toBe(true);
        expect(unit.isActive).toBe(true);
      });
    });

    it('should include common base units', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/product-units/base-units')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      const unitNames = response.body.map(unit => unit.name.toLowerCase());
      
      // Should include common pharmaceutical base units
      expect(unitNames).toContain('tablet');
      expect(unitNames).toContain('kapsul');
      expect(unitNames).toContain('mililiter');
    });

    it('should require authentication', async () => {
      await request(app.getHttpServer())
        .get('/api/product-units/base-units')
        .expect(401);
    });
  });

  describe('GET /api/product-units/by-type', () => {
    it('should return units by type', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/product-units/by-type?type=COUNT')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
      
      // All units should be COUNT type
      response.body.forEach(unit => {
        expect(unit.type).toBe('COUNT');
        expect(unit.isActive).toBe(true);
      });
    });

    it('should return all active units when no type specified', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/product-units/by-type')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
      
      // All units should be active
      response.body.forEach(unit => {
        expect(unit.isActive).toBe(true);
      });
    });

    it('should handle invalid type gracefully', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/product-units/by-type?type=INVALID')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      // Should return all active units when invalid type provided
    });

    it('should require authentication', async () => {
      await request(app.getHttpServer())
        .get('/api/product-units/by-type')
        .expect(401);
    });
  });

  describe('Unit Types Coverage', () => {
    it('should have units for all major types', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/product-units')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      const types = [...new Set(response.body.map(unit => unit.type))];
      
      // Should have major unit types for pharmacy
      expect(types).toContain('COUNT');
      expect(types).toContain('VOLUME');
      expect(types).toContain('WEIGHT');
      expect(types).toContain('PACKAGE');
    });

    it('should have proper Indonesian pharmaceutical units', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/product-units/base-units')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      const unitNames = response.body.map(unit => unit.name.toLowerCase());
      
      // Indonesian pharmaceutical specific units
      expect(unitNames).toContain('kaplet');
      expect(unitNames).toContain('puyer');
      expect(unitNames).toContain('supositoria');
      expect(unitNames).toContain('ovula');
    });
  });
});
