import { IntegrationTestSetup, TestContext } from './test-setup';
import { TaxConfigurationService } from '../../src/procurement/services/tax-configuration.service';
import { TaxCalculationService } from '../../src/procurement/services/tax-calculation.service';
import { TaxType } from '../../src/procurement/dto/tax-configuration.dto';
import { Prisma } from '@prisma/client';

describe('Tax Calculation Integration Tests', () => {
  let testContext: TestContext;
  let testSetup: IntegrationTestSetup;
  let taxConfigService: TaxConfigurationService;
  let taxCalculationService: TaxCalculationService;

  beforeAll(async () => {
    testSetup = new IntegrationTestSetup();
    testContext = await testSetup.setup();

    // Create mock logger
    const mockLogger = {
      error: jest.fn(),
      warn: jest.fn(),
      info: jest.fn(),
      debug: jest.fn(),
      trace: jest.fn(),
      fatal: jest.fn(),
    } as any;

    // Initialize tax services
    taxConfigService = new TaxConfigurationService(testContext.prisma, mockLogger);
    taxCalculationService = new TaxCalculationService(taxConfigService, mockLogger);
  });

  afterAll(async () => {
    await testSetup.teardown();
  });

  beforeEach(async () => {
    // Clean up tax settings before each test
    await testContext.prisma.appSettings.deleteMany({
      where: {
        settingKey: {
          startsWith: 'tax.',
        },
      },
    });
  });

  describe('Tax Configuration Service', () => {
    it('should return default PPN configuration when no settings exist', async () => {
      const config = await taxConfigService.getCurrentPPNConfiguration();

      expect(config.taxType).toBe('PPN');
      expect(config.taxRate).toBe(11); // Default current rate
      expect(config.isActive).toBe(true);
      expect(config.description).toBe('Pajak Pertambahan Nilai (PPN)');
    });

    it('should update PPN configuration correctly', async () => {
      const updateData = {
        taxRate: 12,
        isActive: true,
        effectiveFrom: new Date('2025-01-01'),
        description: 'PPN 12% mulai 2025',
      };

      const updatedConfig = await taxConfigService.updatePPNConfiguration(updateData);

      expect(updatedConfig.taxRate).toBe(12);
      expect(updatedConfig.isActive).toBe(true);
      expect(updatedConfig.effectiveFrom).toEqual(new Date('2025-01-01'));
      expect(updatedConfig.description).toBe('PPN 12% mulai 2025');
    });

    it('should handle tax calculation mode settings', async () => {
      // Test default mode
      const defaultMode = await taxConfigService.getDefaultTaxCalculationMode();
      expect(defaultMode).toBe('exclusive');

      // Test setting mode
      await taxConfigService.setDefaultTaxCalculationMode('inclusive');
      const newMode = await taxConfigService.getDefaultTaxCalculationMode();
      expect(newMode).toBe('inclusive');
    });

    it('should check PPN active status correctly', async () => {
      // Set up active PPN with a wide date range to ensure it's active during tests
      const now = new Date();
      const pastDate = new Date(now.getFullYear() - 1, 0, 1); // January 1st of last year
      const futureDate = new Date(now.getFullYear() + 1, 11, 31); // December 31st of next year

      await taxConfigService.updatePPNConfiguration({
        taxRate: 11,
        isActive: true,
        effectiveFrom: pastDate,
        effectiveTo: futureDate,
      });

      const isActive = await taxConfigService.isPPNActive();
      expect(isActive).toBe(true);

      // Test inactive PPN
      await taxConfigService.updatePPNConfiguration({
        isActive: false,
      });

      const isInactive = await taxConfigService.isPPNActive();
      expect(isInactive).toBe(false);
    });
  });

  describe('Tax Calculation Service', () => {
    beforeEach(async () => {
      // Set up standard PPN configuration for tests with wide date range
      const now = new Date();
      const pastDate = new Date(now.getFullYear() - 1, 0, 1); // January 1st of last year
      const futureDate = new Date(now.getFullYear() + 1, 11, 31); // December 31st of next year

      await taxConfigService.updatePPNConfiguration({
        taxRate: 11,
        isActive: true,
        effectiveFrom: pastDate,
        effectiveTo: futureDate,
      });
    });

    it('should calculate exclusive PPN correctly', async () => {
      const subtotal = new Prisma.Decimal(100000); // Rp 100,000

      const result = await taxCalculationService.calculatePPN(subtotal, {
        isInclusive: false,
      });

      expect(result.subtotal).toBeCloseTo(100000, 2);
      expect(result.taxAmount).toBeCloseTo(11000, 2); // 11% of 100,000
      expect(result.totalAmount).toBeCloseTo(111000, 2);
      expect(result.taxRate).toBeCloseTo(11, 2);
      expect(result.isInclusive).toBe(false);
    });

    it('should calculate inclusive PPN correctly', async () => {
      const totalWithTax = new Prisma.Decimal(111000); // Rp 111,000 including tax

      const result = await taxCalculationService.calculatePPN(totalWithTax, {
        isInclusive: true,
      });

      expect(result.subtotal).toBeCloseTo(100000, 2); // Should extract the base amount
      expect(result.taxAmount).toBeCloseTo(11000, 2); // Tax portion
      expect(result.totalAmount).toBeCloseTo(111000, 2); // Original amount
      expect(result.taxRate).toBeCloseTo(11, 2);
      expect(result.isInclusive).toBe(true);
    });

    it('should handle zero tax when PPN is inactive', async () => {
      // Deactivate PPN
      await taxConfigService.updatePPNConfiguration({
        isActive: false,
      });

      const subtotal = new Prisma.Decimal(100000);
      const result = await taxCalculationService.calculatePPN(subtotal);

      expect(result.subtotal).toBe(100000);
      expect(result.taxAmount).toBe(0);
      expect(result.totalAmount).toBe(100000);
      expect(result.taxRate).toBe(0);
    });

    it('should calculate tax with discount correctly', async () => {
      const subtotal = new Prisma.Decimal(100000);
      const discount = new Prisma.Decimal(10000); // Rp 10,000 discount

      const result = await taxCalculationService.calculateWithDiscount(
        subtotal,
        discount,
        { isInclusive: false }
      );

      expect(result.subtotal).toBeCloseTo(90000, 2); // 100,000 - 10,000
      expect(result.taxAmount).toBeCloseTo(9900, 2); // 11% of 90,000
      expect(result.totalAmount).toBeCloseTo(99900, 2); // 90,000 + 9,900
    });

    it('should handle multiple items calculation', async () => {
      const items = [
        { subtotal: new Prisma.Decimal(50000), options: { isInclusive: false } },
        { subtotal: new Prisma.Decimal(30000), options: { isInclusive: false } },
        { subtotal: new Prisma.Decimal(20000), options: { isInclusive: false } },
      ];

      const result = await taxCalculationService.calculateMultipleItems(items);

      expect(result.subtotal).toBeCloseTo(100000, 2); // 50,000 + 30,000 + 20,000
      expect(result.taxAmount).toBeCloseTo(11000, 2); // 11% of 100,000
      expect(result.totalAmount).toBeCloseTo(111000, 2);
    });

    it('should provide detailed tax breakdown', async () => {
      const subtotal = new Prisma.Decimal(100000);

      const breakdown = await taxCalculationService.getTaxBreakdown(subtotal, {
        isInclusive: false,
      });

      expect(breakdown.subtotal).toBeCloseTo(100000, 2);
      expect(breakdown.taxRate).toBeCloseTo(11, 2);
      expect(breakdown.taxAmount).toBeCloseTo(11000, 2);
      expect(breakdown.totalAmount).toBeCloseTo(111000, 2);
      expect(breakdown.taxDescription).toContain('Pajak Pertambahan Nilai');
      expect(breakdown.isInclusive).toBe(false);
    });

    it('should handle edge case: very small amounts', async () => {
      const subtotal = new Prisma.Decimal(1); // Rp 1

      const result = await taxCalculationService.calculatePPN(subtotal, {
        isInclusive: false,
      });

      expect(result.subtotal).toBeCloseTo(1, 2);
      expect(result.taxAmount).toBeCloseTo(0.11, 2); // Should round to 2 decimal places
      expect(result.totalAmount).toBeCloseTo(1.11, 2);
    });

    it('should handle edge case: zero amount', async () => {
      const subtotal = new Prisma.Decimal(0);

      const result = await taxCalculationService.calculatePPN(subtotal, {
        isInclusive: false,
      });

      expect(result.subtotal).toBe(0);
      expect(result.taxAmount).toBe(0);
      expect(result.totalAmount).toBe(0);
    });

    it('should handle discount larger than subtotal', async () => {
      const subtotal = new Prisma.Decimal(50000);
      const discount = new Prisma.Decimal(60000); // Discount larger than subtotal

      const result = await taxCalculationService.calculateWithDiscount(
        subtotal,
        discount,
        { isInclusive: false }
      );

      expect(result.subtotal).toBe(0); // Should not go negative
      expect(result.taxAmount).toBe(0);
      expect(result.totalAmount).toBe(0);
    });
  });

  describe('Tax Rate Changes', () => {
    it('should handle transition from 11% to 12% PPN rate', async () => {
      // Test with 11% rate (current period)
      const now = new Date();
      const pastDate = new Date(now.getFullYear() - 1, 0, 1);
      const futureDate = new Date(now.getFullYear() + 1, 11, 31);

      await taxConfigService.updatePPNConfiguration({
        taxRate: 11,
        isActive: true,
        effectiveFrom: pastDate,
        effectiveTo: futureDate,
      });

      const subtotal = new Prisma.Decimal(100000);
      const result11 = await taxCalculationService.calculatePPN(subtotal);
      expect(result11.taxAmount).toBe(11000);

      // Test with 12% rate (update current configuration)
      await taxConfigService.updatePPNConfiguration({
        taxRate: 12,
        effectiveFrom: pastDate,
        effectiveTo: futureDate,
      });

      const result12 = await taxCalculationService.calculatePPN(subtotal);
      expect(result12.taxAmount).toBe(12000);
    });

    it('should get effective rate for specific dates', async () => {
      const testDate = new Date('2024-06-15');
      const beforeDate = new Date('2023-06-15');

      await taxConfigService.updatePPNConfiguration({
        taxRate: 11,
        isActive: true,
        effectiveFrom: new Date('2024-01-01'),
        effectiveTo: new Date('2024-12-31'),
      });

      const rateWithinRange = await taxConfigService.getEffectivePPNRate(testDate);
      expect(rateWithinRange).toBe(11);

      // Test date outside effective range
      const rateOutside = await taxConfigService.getEffectivePPNRate(beforeDate);
      expect(rateOutside).toBe(0);
    });
  });
});
