import { IntegrationTestSetup, TestContext, expectValidationError, expectUnauthorized, expectForbidden, expectNotFound, expectConflict, expectSuccess } from './test-setup';
import { AllocationMethod } from '../../src/inventory/dto/stock-allocation.dto';
import { StockAllocationMethod } from '../../src/inventory/dto/stock-consumption.dto';

describe('Inventory Integration Tests', () => {
  let testSetup: IntegrationTestSetup;
  let ctx: TestContext;
  let testInventoryItemId: string;
  let testProductId: string;
  let testUnitId: string;
  let testSupplierId: string;

  beforeAll(async () => {
    testSetup = new IntegrationTestSetup();
    ctx = await testSetup.setup();

    // Create test dependencies in correct order
    const testUnit = await testSetup.createTestProductUnit(ctx.users.admin.id);
    testUnitId = testUnit.id;

    const testProduct = await testSetup.createTestProduct(testUnitId, ctx.users.admin.id);
    testProductId = testProduct.id;

    const testSupplier = await testSetup.createTestSupplier(ctx.users.admin.id);
    testSupplierId = testSupplier.id;

    console.log('Test dependencies created:', {
      testUnitId,
      testProductId,
      testSupplierId,
    });
  });

  afterAll(async () => {
    await testSetup.teardown();
  });

  describe('POST /api/inventory', () => {
    it('should create inventory item as admin', async () => {
      const inventoryData = {
        productId: testProductId,
        unitId: testUnitId,
        batchNumber: 'BATCH001',
        expiryDate: '2025-12-31T00:00:00.000Z',
        quantityOnHand: 100,
        costPrice: 5000,
        sellingPrice: 7500,
        location: 'Rak A-1',
        receivedDate: '2024-01-01T00:00:00.000Z',
        supplierId: testSupplierId,
        isActive: true,
        notes: 'Test inventory item',
      };

      const response = await ctx.request
        .post('/api/inventory')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(inventoryData);

      expectSuccess(response, 201);
      expect(response.body.productId).toBe(inventoryData.productId);
      expect(response.body.unitId).toBe(inventoryData.unitId);
      expect(response.body.batchNumber).toBe(inventoryData.batchNumber);
      expect(response.body.quantityOnHand).toBe(inventoryData.quantityOnHand);
      expect(response.body.costPrice).toBe(inventoryData.costPrice);
      expect(response.body.sellingPrice).toBe(inventoryData.sellingPrice);
      expect(response.body.location).toBe(inventoryData.location);
      expect(response.body.supplierId).toBe(inventoryData.supplierId);
      expect(response.body.isActive).toBe(inventoryData.isActive);
      expect(response.body.notes).toBe(inventoryData.notes);
      expect(response.body.createdBy).toBe(ctx.users.admin.id);
      expect(response.body.product).toBeDefined();
      expect(response.body.unit).toBeDefined();
      expect(response.body.supplier).toBeDefined();

      testInventoryItemId = response.body.id;
      console.log('Created testInventoryItemId:', testInventoryItemId);
    });

    it('should create inventory item as pharmacist', async () => {
      const inventoryData = {
        productId: testProductId,
        unitId: testUnitId,
        batchNumber: 'BATCH002',
        quantityOnHand: 50,
        costPrice: 4000,
        isActive: true,
      };

      const response = await ctx.request
        .post('/api/inventory')
        .set('Authorization', `Bearer ${ctx.users.pharmacist.accessToken}`)
        .send(inventoryData);

      expectSuccess(response, 201);
      expect(response.body.createdBy).toBe(ctx.users.pharmacist.id);
    });

    it('should fail to create inventory item as cashier', async () => {
      const inventoryData = {
        productId: testProductId,
        unitId: testUnitId,
        quantityOnHand: 25,
        costPrice: 3000,
      };

      const response = await ctx.request
        .post('/api/inventory')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(inventoryData);

      expectForbidden(response);
    });

    it('should fail without authentication', async () => {
      const inventoryData = {
        productId: testProductId,
        unitId: testUnitId,
        quantityOnHand: 25,
        costPrice: 3000,
      };

      const response = await ctx.request
        .post('/api/inventory')
        .send(inventoryData);

      expectUnauthorized(response);
    });

    it('should fail with invalid product ID', async () => {
      const inventoryData = {
        productId: 'invalid-product-id',
        unitId: testUnitId,
        quantityOnHand: 25,
        costPrice: 3000,
      };

      const response = await ctx.request
        .post('/api/inventory')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(inventoryData);

      expectNotFound(response);
    });

    it('should fail with invalid unit ID', async () => {
      const inventoryData = {
        productId: testProductId,
        unitId: 'invalid-unit-id',
        quantityOnHand: 25,
        costPrice: 3000,
      };

      const response = await ctx.request
        .post('/api/inventory')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(inventoryData);

      expectNotFound(response);
    });

    it('should fail with invalid supplier ID', async () => {
      const inventoryData = {
        productId: testProductId,
        unitId: testUnitId,
        supplierId: 'invalid-supplier-id',
        quantityOnHand: 25,
        costPrice: 3000,
      };

      const response = await ctx.request
        .post('/api/inventory')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(inventoryData);

      expectNotFound(response);
    });

    it('should fail with duplicate batch number for same product', async () => {
      // First create a successful inventory item
      const firstInventoryData = {
        productId: testProductId,
        unitId: testUnitId,
        batchNumber: 'BATCH_DUPLICATE_TEST',
        quantityOnHand: 25,
        costPrice: 3000,
      };

      const firstResponse = await ctx.request
        .post('/api/inventory')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(firstInventoryData);

      expect(firstResponse.status).toBe(201);

      // Now try to create another with the same batch number
      const duplicateInventoryData = {
        productId: testProductId,
        unitId: testUnitId,
        batchNumber: 'BATCH_DUPLICATE_TEST', // Same batch number
        quantityOnHand: 25,
        costPrice: 3000,
      };

      const response = await ctx.request
        .post('/api/inventory')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(duplicateInventoryData);

      expectConflict(response);
    });

    it('should fail with negative quantity', async () => {
      const inventoryData = {
        productId: testProductId,
        unitId: testUnitId,
        quantityOnHand: -10,
        costPrice: 3000,
      };

      const response = await ctx.request
        .post('/api/inventory')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(inventoryData);

      expectValidationError(response, 'quantityOnHand');
    });

    it('should fail with negative cost price', async () => {
      const inventoryData = {
        productId: testProductId,
        unitId: testUnitId,
        quantityOnHand: 25,
        costPrice: -1000,
      };

      const response = await ctx.request
        .post('/api/inventory')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(inventoryData);

      expectValidationError(response, 'costPrice');
    });

    it('should fail with selling price lower than cost price', async () => {
      const inventoryData = {
        productId: testProductId,
        unitId: testUnitId,
        quantityOnHand: 25,
        costPrice: 5000,
        sellingPrice: 3000, // Lower than cost price
      };

      const response = await ctx.request
        .post('/api/inventory')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(inventoryData);

      expect(response.status).toBe(400);
      expect(response.body.message).toContain('Selling price must be greater than cost price');
    });

    it('should fail with expiry date in the past', async () => {
      const pastDate = new Date();
      pastDate.setFullYear(pastDate.getFullYear() - 1);

      const inventoryData = {
        productId: testProductId,
        unitId: testUnitId,
        quantityOnHand: 25,
        costPrice: 3000,
        expiryDate: pastDate.toISOString(),
      };

      const response = await ctx.request
        .post('/api/inventory')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(inventoryData);

      expect(response.status).toBe(400);
      expect(response.body.message).toContain('Tanggal kedaluwarsa harus di masa depan');
    });

    it('should fail with received date in the future', async () => {
      const futureDate = new Date();
      futureDate.setFullYear(futureDate.getFullYear() + 1);

      const inventoryData = {
        productId: testProductId,
        unitId: testUnitId,
        quantityOnHand: 25,
        costPrice: 3000,
        receivedDate: futureDate.toISOString(),
      };

      const response = await ctx.request
        .post('/api/inventory')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(inventoryData);

      expect(response.status).toBe(400);
      expect(response.body.message).toContain('Received date cannot be in the future');
    });

    it('should fail with missing required fields', async () => {
      const inventoryData = {
        // Missing productId, unitId, quantityOnHand, costPrice
      };

      const response = await ctx.request
        .post('/api/inventory')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(inventoryData);

      expectValidationError(response);
    });
  });

  describe('GET /api/inventory', () => {
    beforeEach(async () => {
      // Create additional test inventory items for filtering tests
      const inventoryItems = [
        {
          productId: testProductId,
          unitId: testUnitId,
          batchNumber: 'FILTER001',
          quantityOnHand: 200,
          costPrice: 6000,
          location: 'Rak B-1',
          isActive: true,
        },
        {
          productId: testProductId,
          unitId: testUnitId,
          batchNumber: 'FILTER002',
          quantityOnHand: 5, // Low stock
          costPrice: 7000,
          location: 'Rak C-1',
          isActive: false,
        },
        {
          productId: testProductId,
          unitId: testUnitId,
          batchNumber: 'FILTER003',
          quantityOnHand: 150,
          costPrice: 5500,
          expiryDate: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(), // Expires in 15 days
          location: 'Rak D-1',
          isActive: true,
        },
      ];

      for (const item of inventoryItems) {
        await ctx.request
          .post('/api/inventory')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(item);
      }
    });

    it('should list all inventory items for authenticated user', async () => {
      const response = await ctx.request
        .get('/api/inventory')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('meta');
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.meta).toHaveProperty('total');
      expect(response.body.meta).toHaveProperty('page');
      expect(response.body.meta).toHaveProperty('limit');
      expect(response.body.meta).toHaveProperty('totalPages');
      expect(response.body.meta).toHaveProperty('hasNextPage');
      expect(response.body.meta).toHaveProperty('hasPreviousPage');
      expect(response.body.meta.total).toBeGreaterThan(0);
    });

    it('should filter inventory items by product ID', async () => {
      const response = await ctx.request
        .get(`/api/inventory?productId=${testProductId}`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.data.every((item: any) => item.productId === testProductId)).toBe(true);
    });

    it('should filter inventory items by supplier ID', async () => {
      const response = await ctx.request
        .get(`/api/inventory?supplierId=${testSupplierId}`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.data.every((item: any) => item.supplierId === testSupplierId)).toBe(true);
    });

    it('should filter inventory items by location', async () => {
      const response = await ctx.request
        .get('/api/inventory?location=Rak A')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.data.every((item: any) => item.location?.includes('Rak A'))).toBe(true);
    });

    it('should filter inventory items by batch number', async () => {
      const response = await ctx.request
        .get('/api/inventory?batchNumber=FILTER')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.data.every((item: any) => item.batchNumber?.includes('FILTER'))).toBe(true);
    });

    it('should filter inventory items by active status', async () => {
      const response = await ctx.request
        .get('/api/inventory?isActive=true')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.data.every((item: any) => item.isActive === true)).toBe(true);
    });

    it('should filter inventory items by inactive status', async () => {
      const response = await ctx.request
        .get('/api/inventory?isActive=false')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.data.every((item: any) => item.isActive === false)).toBe(true);
    });

    it('should filter low stock items', async () => {
      const response = await ctx.request
        .get('/api/inventory?lowStock=true')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.data.every((item: any) => item.quantityOnHand < 10)).toBe(true);
    });

    it('should filter expiring soon items', async () => {
      const response = await ctx.request
        .get('/api/inventory?expiringSoon=true')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      // Should include items expiring within 30 days
      const thirtyDaysFromNow = new Date();
      thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
      
      response.body.data.forEach((item: any) => {
        if (item.expiryDate) {
          const expiryDate = new Date(item.expiryDate);
          expect(expiryDate.getTime()).toBeLessThanOrEqual(thirtyDaysFromNow.getTime());
          expect(expiryDate.getTime()).toBeGreaterThan(new Date().getTime());
        }
      });
    });

    it('should search inventory items by product name', async () => {
      const response = await ctx.request
        .get('/api/inventory?search=Test Product')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.data.every((item: any) => 
        item.product.name.toLowerCase().includes('test product')
      )).toBe(true);
    });

    it('should search inventory items by batch number', async () => {
      const response = await ctx.request
        .get('/api/inventory?search=BATCH001')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.data.some((item: any) => 
        item.batchNumber?.includes('BATCH001')
      )).toBe(true);
    });

    it('should sort inventory items by quantity', async () => {
      const response = await ctx.request
        .get('/api/inventory?sortBy=quantityOnHand&sortOrder=asc')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      const quantities = response.body.data.map((item: any) => item.quantityOnHand);
      const sortedQuantities = [...quantities].sort((a, b) => a - b);
      expect(quantities).toEqual(sortedQuantities);
    });

    it('should sort inventory items by cost price descending', async () => {
      const response = await ctx.request
        .get('/api/inventory?sortBy=costPrice&sortOrder=desc')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      const prices = response.body.data.map((item: any) => item.costPrice);
      const sortedPrices = [...prices].sort((a, b) => b - a);
      expect(prices).toEqual(sortedPrices);
    });

    it('should paginate inventory items', async () => {
      const response = await ctx.request
        .get('/api/inventory?page=1&limit=2')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.data.length).toBeLessThanOrEqual(2);
      expect(response.body.meta.page).toBe(1);
      expect(response.body.meta.limit).toBe(2);
    });

    it('should fail without authentication', async () => {
      const response = await ctx.request
        .get('/api/inventory');

      expectUnauthorized(response);
    });
  });

  describe('GET /api/inventory/stats', () => {
    it('should return inventory statistics for authenticated user', async () => {
      const response = await ctx.request
        .get('/api/inventory/stats')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      expect(response.body).toHaveProperty('totalItems');
      expect(response.body).toHaveProperty('activeItems');
      expect(response.body).toHaveProperty('inactiveItems');
      expect(response.body).toHaveProperty('lowStockItems');
      expect(response.body).toHaveProperty('expiredItems');
      expect(response.body).toHaveProperty('expiringSoonItems');
      expect(response.body).toHaveProperty('zeroStockItems');
      expect(response.body).toHaveProperty('totalValue');
      expect(response.body).toHaveProperty('averageCostPrice');
      expect(response.body).toHaveProperty('totalQuantity');
      expect(response.body).toHaveProperty('recentMovements');
      expect(typeof response.body.totalItems).toBe('number');
      expect(typeof response.body.activeItems).toBe('number');
      expect(typeof response.body.totalValue).toBe('number');
    });

    it('should fail without authentication', async () => {
      const response = await ctx.request
        .get('/api/inventory/stats');

      expectUnauthorized(response);
    });
  });

  describe('GET /api/inventory/:id', () => {
    it('should return inventory item details for authenticated user', async () => {
      const response = await ctx.request
        .get(`/api/inventory/${testInventoryItemId}`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      expect(response.body.id).toBe(testInventoryItemId);
      expect(response.body.batchNumber).toBe('BATCH001');
      expect(response.body.quantityOnHand).toBe(100);
      expect(response.body).toHaveProperty('product');
      expect(response.body).toHaveProperty('unit');
      expect(response.body).toHaveProperty('supplier');
      expect(response.body).toHaveProperty('stockMovements');
    });

    it('should fail with non-existent inventory item ID', async () => {
      const response = await ctx.request
        .get('/api/inventory/non-existent-id')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectNotFound(response);
    });

    it('should fail without authentication', async () => {
      const response = await ctx.request
        .get(`/api/inventory/${testInventoryItemId}`);

      expectUnauthorized(response);
    });
  });

  describe('PATCH /api/inventory/:id', () => {
    it('should update inventory item as admin', async () => {
      const updateData = {
        batchNumber: 'BATCH001-UPDATED',
        quantityOnHand: 120,
        costPrice: 5500,
        sellingPrice: 8000,
        location: 'Rak A-2',
        notes: 'Updated test inventory item',
      };

      const response = await ctx.request
        .patch(`/api/inventory/${testInventoryItemId}`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(updateData);

      expectSuccess(response);
      expect(response.body.batchNumber).toBe(updateData.batchNumber);
      expect(response.body.costPrice).toBe(updateData.costPrice);
      expect(response.body.sellingPrice).toBe(updateData.sellingPrice);
      expect(response.body.location).toBe(updateData.location);
      expect(response.body.notes).toBe(updateData.notes);
      expect(response.body.updatedBy).toBe(ctx.users.admin.id);
    });

    it('should update inventory item as pharmacist', async () => {
      const updateData = {
        location: 'Rak A-3',
        notes: 'Updated by pharmacist',
      };

      const response = await ctx.request
        .patch(`/api/inventory/${testInventoryItemId}`)
        .set('Authorization', `Bearer ${ctx.users.pharmacist.accessToken}`)
        .send(updateData);

      expectSuccess(response);
      expect(response.body.location).toBe(updateData.location);
      expect(response.body.notes).toBe(updateData.notes);
      expect(response.body.updatedBy).toBe(ctx.users.pharmacist.id);
    });

    it('should fail to update inventory item as cashier', async () => {
      const updateData = {
        location: 'Rak A-4',
      };

      const response = await ctx.request
        .patch(`/api/inventory/${testInventoryItemId}`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(updateData);

      expectForbidden(response);
    });

    it('should fail with non-existent inventory item ID', async () => {
      const updateData = {
        location: 'Rak A-5',
      };

      const response = await ctx.request
        .patch('/api/inventory/non-existent-id')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(updateData);

      expectNotFound(response);
    });

    it('should fail with invalid unit ID', async () => {
      const updateData = {
        unitId: 'invalid-unit-id',
      };

      const response = await ctx.request
        .patch(`/api/inventory/${testInventoryItemId}`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(updateData);

      expectNotFound(response);
    });

    it('should fail with invalid supplier ID', async () => {
      const updateData = {
        supplierId: 'invalid-supplier-id',
      };

      const response = await ctx.request
        .patch(`/api/inventory/${testInventoryItemId}`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(updateData);

      expectNotFound(response);
    });

    it('should fail without authentication', async () => {
      const updateData = {
        location: 'Rak A-6',
      };

      const response = await ctx.request
        .patch(`/api/inventory/${testInventoryItemId}`)
        .send(updateData);

      expectUnauthorized(response);
    });
  });

  describe('PATCH /api/inventory/:id/adjust-stock', () => {
    it('should adjust stock increase as admin', async () => {
      const adjustmentData = {
        quantity: 50, // Increase by 50
        reason: 'Stock replenishment',
        notes: 'Received new stock from supplier',
      };

      const response = await ctx.request
        .patch(`/api/inventory/${testInventoryItemId}/adjust-stock`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(adjustmentData);

      expectSuccess(response);
      expect(response.body.quantityOnHand).toBe(170); // 120 + 50 from previous update
      expect(response.body.updatedBy).toBe(ctx.users.admin.id);
    });

    it('should adjust stock decrease as pharmacist', async () => {
      const adjustmentData = {
        quantity: -20, // Decrease by 20
        reason: 'Damaged goods',
        notes: 'Items damaged during handling',
      };

      const response = await ctx.request
        .patch(`/api/inventory/${testInventoryItemId}/adjust-stock`)
        .set('Authorization', `Bearer ${ctx.users.pharmacist.accessToken}`)
        .send(adjustmentData);

      expectSuccess(response);
      expect(response.body.quantityOnHand).toBe(150); // 170 - 20
      expect(response.body.updatedBy).toBe(ctx.users.pharmacist.id);
    });

    it('should fail to adjust stock as cashier', async () => {
      const adjustmentData = {
        quantity: 10,
        reason: 'Test adjustment',
      };

      const response = await ctx.request
        .patch(`/api/inventory/${testInventoryItemId}/adjust-stock`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(adjustmentData);

      expectForbidden(response);
    });

    it('should fail with insufficient stock for negative adjustment', async () => {
      const adjustmentData = {
        quantity: -200, // More than available stock (150)
        reason: 'Test insufficient stock',
      };

      const response = await ctx.request
        .patch(`/api/inventory/${testInventoryItemId}/adjust-stock`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(adjustmentData);

      expect(response.status).toBe(400);
      expect(response.body.message).toContain('Stok tidak mencukupi');
    });

    it('should fail with non-existent inventory item ID', async () => {
      const adjustmentData = {
        quantity: 10,
        reason: 'Test adjustment',
      };

      const response = await ctx.request
        .patch('/api/inventory/non-existent-id/adjust-stock')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(adjustmentData);

      expectNotFound(response);
    });

    it('should fail with missing reason', async () => {
      const adjustmentData = {
        quantity: 10,
        // Missing reason
      };

      const response = await ctx.request
        .patch(`/api/inventory/${testInventoryItemId}/adjust-stock`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(adjustmentData);

      expectValidationError(response, 'reason');
    });

    it('should fail without authentication', async () => {
      const adjustmentData = {
        quantity: 10,
        reason: 'Test adjustment',
      };

      const response = await ctx.request
        .patch(`/api/inventory/${testInventoryItemId}/adjust-stock`)
        .send(adjustmentData);

      expectUnauthorized(response);
    });
  });

  describe('PATCH /api/inventory/:id/activate', () => {
    let inventoryToActivate: string;

    beforeEach(async () => {
      // Create an inventory item and deactivate it for testing activation
      const createResponse = await ctx.request
        .post('/api/inventory')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({
          productId: testProductId,
          unitId: testUnitId,
          batchNumber: `ACT${Date.now()}`,
          quantityOnHand: 75,
          costPrice: 4500,
          isActive: true,
        });

      inventoryToActivate = createResponse.body.id;

      // Deactivate the inventory item so we can test activation
      const deactivateResponse = await ctx.request
        .patch(`/api/inventory/${inventoryToActivate}/deactivate`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({
          reason: 'Test deactivation for activation test',
        });

      if (deactivateResponse.status !== 200) {
        throw new Error(`Failed to deactivate inventory item for test. Status: ${deactivateResponse.status}, Body: ${JSON.stringify(deactivateResponse.body)}`);
      }
    });

    it('should activate inventory item as admin', async () => {
      const activateData = {
        reason: 'Reactivating for use',
        notes: 'Item is now ready for use',
      };

      const response = await ctx.request
        .patch(`/api/inventory/${inventoryToActivate}/activate`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(activateData);

      expectSuccess(response);
      expect(response.body.isActive).toBe(true);
      expect(response.body.updatedBy).toBe(ctx.users.admin.id);
    });

    it('should activate inventory item as pharmacist', async () => {
      // First deactivate again
      await ctx.request
        .patch(`/api/inventory/${inventoryToActivate}/deactivate`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({ reason: 'Test deactivation' });

      const activateData = {
        reason: 'Pharmacist activation',
      };

      const response = await ctx.request
        .patch(`/api/inventory/${inventoryToActivate}/activate`)
        .set('Authorization', `Bearer ${ctx.users.pharmacist.accessToken}`)
        .send(activateData);

      expectSuccess(response);
      expect(response.body.isActive).toBe(true);
      expect(response.body.updatedBy).toBe(ctx.users.pharmacist.id);
    });

    it('should fail to activate inventory item as cashier', async () => {
      // First deactivate again
      await ctx.request
        .patch(`/api/inventory/${inventoryToActivate}/deactivate`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({ reason: 'Test deactivation' });

      const activateData = {
        reason: 'Cashier activation attempt',
      };

      const response = await ctx.request
        .patch(`/api/inventory/${inventoryToActivate}/activate`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(activateData);

      expectForbidden(response);
    });

    it('should fail to activate already active inventory item', async () => {
      // First ensure the item is active
      await ctx.request
        .patch(`/api/inventory/${inventoryToActivate}/activate`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({ reason: 'Ensure active' });

      // Now try to activate it again
      const activateData = {
        reason: 'Already active test',
      };

      const response = await ctx.request
        .patch(`/api/inventory/${inventoryToActivate}/activate`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(activateData);

      expect(response.status).toBe(400);
      expect(response.body.message).toContain('sudah aktif');
    });

    it('should fail with non-existent inventory item ID', async () => {
      const activateData = {
        reason: 'Non-existent test',
      };

      const response = await ctx.request
        .patch('/api/inventory/non-existent-id/activate')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(activateData);

      expectNotFound(response);
    });

    it('should fail without authentication', async () => {
      const activateData = {
        reason: 'Unauthenticated test',
      };

      const response = await ctx.request
        .patch(`/api/inventory/${inventoryToActivate}/activate`)
        .send(activateData);

      expectUnauthorized(response);
    });
  });

  describe('PATCH /api/inventory/:id/deactivate', () => {
    it('should deactivate inventory item as admin', async () => {
      const deactivateData = {
        reason: 'Item expired',
        notes: 'Removing from active inventory due to expiration',
      };

      const response = await ctx.request
        .patch(`/api/inventory/${testInventoryItemId}/deactivate`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(deactivateData);

      expectSuccess(response);
      expect(response.body.isActive).toBe(false);
      expect(response.body.updatedBy).toBe(ctx.users.admin.id);
    });

    it('should deactivate inventory item as pharmacist', async () => {
      // First activate the item again
      await ctx.request
        .patch(`/api/inventory/${testInventoryItemId}/activate`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({ reason: 'Reactivate for test' });

      const deactivateData = {
        reason: 'Pharmacist deactivation',
      };

      const response = await ctx.request
        .patch(`/api/inventory/${testInventoryItemId}/deactivate`)
        .set('Authorization', `Bearer ${ctx.users.pharmacist.accessToken}`)
        .send(deactivateData);

      expectSuccess(response);
      expect(response.body.isActive).toBe(false);
      expect(response.body.updatedBy).toBe(ctx.users.pharmacist.id);
    });

    it('should fail to deactivate inventory item as cashier', async () => {
      // First activate the item again
      await ctx.request
        .patch(`/api/inventory/${testInventoryItemId}/activate`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({ reason: 'Reactivate for test' });

      const deactivateData = {
        reason: 'Cashier deactivation attempt',
      };

      const response = await ctx.request
        .patch(`/api/inventory/${testInventoryItemId}/deactivate`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(deactivateData);

      expectForbidden(response);
    });

    it('should fail to deactivate already inactive inventory item', async () => {
      // First ensure the item is inactive
      await ctx.request
        .patch(`/api/inventory/${testInventoryItemId}/deactivate`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({ reason: 'Ensure inactive' });

      // Now try to deactivate it again
      const deactivateData = {
        reason: 'Already inactive test',
      };

      const response = await ctx.request
        .patch(`/api/inventory/${testInventoryItemId}/deactivate`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(deactivateData);

      expect(response.status).toBe(400);
      expect(response.body.message).toContain('sudah tidak aktif');
    });

    it('should fail without authentication', async () => {
      const deactivateData = {
        reason: 'Unauthenticated test',
      };

      const response = await ctx.request
        .patch(`/api/inventory/${testInventoryItemId}/deactivate`)
        .send(deactivateData);

      expectUnauthorized(response);
    });
  });

  describe('GET /api/inventory/:id/stock-movements', () => {
    it('should return stock movements for inventory item', async () => {
      const response = await ctx.request
        .get(`/api/inventory/${testInventoryItemId}/stock-movements`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('meta');
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.meta).toHaveProperty('total');
      expect(response.body.meta).toHaveProperty('page');
      expect(response.body.meta).toHaveProperty('limit');
      expect(response.body.meta).toHaveProperty('totalPages');
      expect(response.body.meta).toHaveProperty('hasNextPage');
      expect(response.body.meta).toHaveProperty('hasPreviousPage');

      // Should have stock movements from creation, updates, and adjustments
      expect(response.body.data.length).toBeGreaterThan(0);
      response.body.data.forEach((movement: any) => {
        expect(movement).toHaveProperty('id');
        expect(movement).toHaveProperty('type');
        expect(movement).toHaveProperty('quantity');
        expect(movement).toHaveProperty('reason');
        expect(movement).toHaveProperty('movementDate');
        expect(movement.inventoryItemId).toBe(testInventoryItemId);
      });
    });

    it('should paginate stock movements', async () => {
      const response = await ctx.request
        .get(`/api/inventory/${testInventoryItemId}/stock-movements?page=1&limit=2`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.data.length).toBeLessThanOrEqual(2);
      expect(response.body.meta.page).toBe(1);
      expect(response.body.meta.limit).toBe(2);
    });

    it('should fail with non-existent inventory item ID', async () => {
      const response = await ctx.request
        .get('/api/inventory/non-existent-id/stock-movements')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response); // Should return empty data, not fail
      expect(response.body.data).toHaveLength(0);
    });

    it('should fail without authentication', async () => {
      const response = await ctx.request
        .get(`/api/inventory/${testInventoryItemId}/stock-movements`);

      expectUnauthorized(response);
    });
  });

  describe('DELETE /api/inventory/:id', () => {
    let inventoryToDelete: string;

    beforeEach(async () => {
      // Create an inventory item specifically for deletion testing
      const createResponse = await ctx.request
        .post('/api/inventory')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({
          productId: testProductId,
          unitId: testUnitId,
          batchNumber: `DEL${Date.now()}`,
          quantityOnHand: 25,
          costPrice: 3000,
        });

      inventoryToDelete = createResponse.body.id;
    });

    it('should delete inventory item as admin', async () => {
      const response = await ctx.request
        .delete(`/api/inventory/${inventoryToDelete}`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expect(response.status).toBe(204);

      // Verify item is deleted
      const getResponse = await ctx.request
        .get(`/api/inventory/${inventoryToDelete}`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectNotFound(getResponse);
    });

    it('should fail to delete inventory item as pharmacist', async () => {
      const response = await ctx.request
        .delete(`/api/inventory/${inventoryToDelete}`)
        .set('Authorization', `Bearer ${ctx.users.pharmacist.accessToken}`);

      expectForbidden(response);
    });

    it('should fail to delete inventory item as cashier', async () => {
      const response = await ctx.request
        .delete(`/api/inventory/${inventoryToDelete}`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectForbidden(response);
    });

    it('should fail with non-existent inventory item ID', async () => {
      const response = await ctx.request
        .delete('/api/inventory/non-existent-id')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectNotFound(response);
    });

    it('should fail without authentication', async () => {
      const response = await ctx.request
        .delete(`/api/inventory/${inventoryToDelete}`);

      expectUnauthorized(response);
    });
  });

  describe('Enhanced Filtering and Search', () => {
    beforeEach(async () => {
      // Create additional test data for enhanced filtering
      const enhancedTestData = [
        {
          productId: testProductId,
          unitId: testUnitId,
          batchNumber: 'ENHANCED001',
          quantityOnHand: 5, // Low stock
          costPrice: 1000,
          sellingPrice: 1500,
          location: 'Rak Enhanced-1',
          expiryDate: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(), // Expires in 15 days
          isActive: true,
        },
        {
          productId: testProductId,
          unitId: testUnitId,
          batchNumber: 'ENHANCED002',
          quantityOnHand: 200,
          costPrice: 2500,
          sellingPrice: 3500,
          location: 'Rak Enhanced-2',
          receivedDate: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(), // 60 days ago
          isActive: true,
        },
      ];

      for (const data of enhancedTestData) {
        await ctx.request
          .post('/api/inventory')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(data);
      }
    });

    it('should filter by quantity range', async () => {
      const response = await ctx.request
        .get('/api/inventory?quantityMin=100&quantityMax=200')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      expect(response.body.data.length).toBeGreaterThan(0);
      response.body.data.forEach((item: any) => {
        expect(item.quantityOnHand).toBeGreaterThanOrEqual(100);
        expect(item.quantityOnHand).toBeLessThanOrEqual(200);
      });
    });

    it('should filter by cost price range', async () => {
      const response = await ctx.request
        .get('/api/inventory?costPriceMin=2000&costPriceMax=3000')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      expect(response.body.data.length).toBeGreaterThan(0);
      response.body.data.forEach((item: any) => {
        expect(item.costPrice).toBeGreaterThanOrEqual(2000);
        expect(item.costPrice).toBeLessThanOrEqual(3000);
      });
    });

    it('should filter by custom low stock threshold', async () => {
      const response = await ctx.request
        .get('/api/inventory?lowStock=true&lowStockThreshold=10')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      expect(response.body.data.length).toBeGreaterThan(0);
      response.body.data.forEach((item: any) => {
        expect(item.quantityOnHand).toBeLessThanOrEqual(10);
      });
    });

    it('should filter by custom expiring soon days', async () => {
      const response = await ctx.request
        .get('/api/inventory?expiringSoon=true&expiringSoonDays=20')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      expect(response.body.data.length).toBeGreaterThan(0);
      response.body.data.forEach((item: any) => {
        if (item.expiryDate) {
          const expiryDate = new Date(item.expiryDate);
          const now = new Date();
          const daysUntilExpiry = Math.ceil((expiryDate.getTime() - now.getTime()) / (24 * 60 * 60 * 1000));
          expect(daysUntilExpiry).toBeLessThanOrEqual(20);
          expect(daysUntilExpiry).toBeGreaterThan(0);
        }
      });
    });

    it('should filter by date ranges', async () => {
      const fromDate = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString();
      const toDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString();

      const response = await ctx.request
        .get(`/api/inventory?receivedDateFrom=${fromDate}&receivedDateTo=${toDate}`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      response.body.data.forEach((item: any) => {
        const receivedDate = new Date(item.receivedDate);
        expect(receivedDate.getTime()).toBeGreaterThanOrEqual(new Date(fromDate).getTime());
        expect(receivedDate.getTime()).toBeLessThanOrEqual(new Date(toDate).getTime());
      });
    });

    it('should support multiple product IDs', async () => {
      const response = await ctx.request
        .get(`/api/inventory?productIds=${testProductId}&productIds=non-existent-id`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      response.body.data.forEach((item: any) => {
        expect([testProductId, 'non-existent-id']).toContain(item.productId);
      });
    });

    it('should support search operators', async () => {
      const response = await ctx.request
        .get('/api/inventory?search=ENHANCED&searchOperator=startsWith')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      response.body.data.forEach((item: any) => {
        expect(item.batchNumber.toLowerCase()).toMatch(/^enhanced/);
      });
    });
  });

  describe('Enhanced Sorting', () => {
    it('should sort by product name', async () => {
      const response = await ctx.request
        .get('/api/inventory?sortBy=productName&sortOrder=asc')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      expect(response.body.data.length).toBeGreaterThan(1);

      for (let i = 1; i < response.body.data.length; i++) {
        const current = response.body.data[i].product.name.toLowerCase();
        const previous = response.body.data[i - 1].product.name.toLowerCase();
        expect(current >= previous).toBe(true);
      }
    });

    it('should sort by supplier name', async () => {
      const response = await ctx.request
        .get('/api/inventory?sortBy=supplierName&sortOrder=desc')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      response.body.data.forEach((item: any) => {
        if (item.supplier) {
          expect(item.supplier.name).toBeDefined();
        }
      });
    });

    it('should sort by expiry proximity', async () => {
      const response = await ctx.request
        .get('/api/inventory?sortBy=expiryProximity&sortOrder=asc')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      // Items with earlier expiry dates should come first
      const itemsWithExpiry = response.body.data.filter((item: any) => item.expiryDate);
      for (let i = 1; i < itemsWithExpiry.length; i++) {
        const current = new Date(itemsWithExpiry[i].expiryDate);
        const previous = new Date(itemsWithExpiry[i - 1].expiryDate);
        expect(current.getTime() >= previous.getTime()).toBe(true);
      }
    });

    it('should support multi-field sorting', async () => {
      const response = await ctx.request
        .get('/api/inventory?sortFields=productName&sortFields=costPrice&sortOrder=asc')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      expect(response.body.data.length).toBeGreaterThan(0);
    });

    it('should sort by stock velocity with actual movement calculation', async () => {
      // First, create some inventory items with different movement patterns
      const testItems: string[] = [];

      // Create items with different movement patterns
      for (let i = 0; i < 3; i++) {
        const itemResponse = await ctx.request
          .post('/api/inventory')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send({
            productId: testProductId,
            unitId: testUnitId,
            batchNumber: `VELOCITY-${i}`,
            quantityOnHand: 100,
            costPrice: 1000 + i * 100,
            sellingPrice: 1500 + i * 150,
            location: `Velocity-${i}`,
            isActive: true,
          });

        expectSuccess(itemResponse, 201); // Create endpoints return 201
        testItems.push(itemResponse.body.id);
      }

      // Create different movement patterns for each item
      // Item 0: High velocity (many movements)
      for (let j = 0; j < 5; j++) {
        await ctx.request
          .patch(`/api/inventory/${testItems[0]}/adjust-stock`)
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send({
            quantity: j % 2 === 0 ? 10 : -5, // Alternating in/out movements
            reason: `High velocity movement ${j}`,
          });
      }

      // Item 1: Medium velocity (some movements)
      for (let j = 0; j < 2; j++) {
        await ctx.request
          .patch(`/api/inventory/${testItems[1]}/adjust-stock`)
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send({
            quantity: 5,
            reason: `Medium velocity movement ${j}`,
          });
      }

      // Item 2: Low velocity (no additional movements - only creation)

      // Wait a moment to ensure movements are recorded
      await new Promise(resolve => setTimeout(resolve, 100));

      // Test stock velocity sorting (descending - highest velocity first)
      const response = await ctx.request
        .get('/api/inventory?sortBy=stockVelocity&sortOrder=desc&search=VELOCITY&searchOperator=startsWith')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      expect(response.body.data.length).toBeGreaterThanOrEqual(3);

      // Verify velocity metadata is included
      expect(response.body.meta.velocityCalculation).toBeDefined();
      expect(response.body.meta.velocityCalculation.method).toBe('movements_per_day');
      expect(response.body.meta.velocityCalculation.periodDays).toBe(30);

      // Verify each item has velocity information
      response.body.data.forEach((item: any) => {
        expect(item._velocity).toBeDefined();
        expect(item._velocity.movementsPerDay).toBeDefined();
        expect(item._velocity.totalQuantityMoved).toBeDefined();
        expect(item._velocity.calculationPeriodDays).toBe(30);
        expect(typeof item._velocity.movementsPerDay).toBe('number');
        expect(typeof item._velocity.totalQuantityMoved).toBe('number');
      });

      // Verify sorting order: items should be sorted by actual movement velocity
      // Item 0 (high velocity) should come first, Item 2 (low velocity) should come last
      const velocities = response.body.data.map((item: any) => item._velocity.movementsPerDay);

      // Check that velocities are in descending order
      for (let i = 1; i < velocities.length; i++) {
        expect(velocities[i]).toBeLessThanOrEqual(velocities[i - 1]);
      }

      // The first item should have the highest velocity (most movements)
      expect(velocities[0]).toBeGreaterThan(0);

      // Filter to only our test items
      const testItemsInResponse = response.body.data.filter((item: any) =>
        item.batchNumber.startsWith('VELOCITY-')
      );

      expect(testItemsInResponse.length).toBe(3);

      // Verify that the high-velocity item is first among our test items
      const firstTestItem = testItemsInResponse[0];
      expect(firstTestItem.batchNumber).toBe('VELOCITY-0'); // Should be the high-velocity item
      expect(firstTestItem._velocity.movementsPerDay).toBeGreaterThan(0);
    });

    it('should handle stock velocity sorting with ascending order', async () => {
      const response = await ctx.request
        .get('/api/inventory?sortBy=stockVelocity&sortOrder=asc&search=VELOCITY')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);

      if (response.body.data.length > 1) {
        const velocities = response.body.data.map((item: any) => item._velocity.movementsPerDay);

        // Check that velocities are in ascending order
        for (let i = 1; i < velocities.length; i++) {
          expect(velocities[i]).toBeGreaterThanOrEqual(velocities[i - 1]);
        }
      }
    });

    it('should fallback gracefully when stock velocity calculation fails', async () => {
      // Test with a malformed query that might cause raw SQL to fail
      const response = await ctx.request
        .get('/api/inventory?sortBy=stockVelocity&sortOrder=desc&limit=1')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);

      // Should still return data, either with velocity calculation or fallback
      expect(response.body.data).toBeDefined();
      expect(Array.isArray(response.body.data)).toBe(true);

      // Check if fallback was used
      if (response.body.meta.fallbackUsed) {
        expect(response.body.meta.fallbackReason).toBeDefined();
        expect(typeof response.body.meta.fallbackReason).toBe('string');
      }
    });

    it('should include velocity calculation metadata in response', async () => {
      const response = await ctx.request
        .get('/api/inventory?sortBy=stockVelocity&sortOrder=desc&limit=1')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);

      // Verify velocity calculation metadata
      if (!response.body.meta.fallbackUsed) {
        expect(response.body.meta.velocityCalculation).toBeDefined();
        expect(response.body.meta.velocityCalculation.periodDays).toBe(30);
        expect(response.body.meta.velocityCalculation.startDate).toBeDefined();
        expect(response.body.meta.velocityCalculation.method).toBe('movements_per_day');

        // Verify startDate is a valid ISO string
        const startDate = new Date(response.body.meta.velocityCalculation.startDate);
        expect(startDate).toBeInstanceOf(Date);
        expect(isNaN(startDate.getTime())).toBe(false);
      }
    });
  });

  describe('Enhanced Pagination', () => {
    it('should support cursor-based pagination', async () => {
      // Get first page with smaller limit to ensure there are more items
      const firstResponse = await ctx.request
        .get('/api/inventory?paginationType=cursor&limit=1')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(firstResponse);
      expect(firstResponse.body.meta.paginationType).toBe('cursor');
      expect(firstResponse.body.data.length).toBe(1);

      // Only test second page if there's a next cursor
      if (firstResponse.body.meta.nextCursor) {
        // Get next page using cursor
        const secondResponse = await ctx.request
          .get(`/api/inventory?paginationType=cursor&limit=1&cursor=${firstResponse.body.meta.nextCursor}`)
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

        expectSuccess(secondResponse);

        // Ensure no overlap between pages if second page has data
        if (secondResponse.body.data.length > 0) {
          const firstPageIds = firstResponse.body.data.map((item: any) => item.id);
          const secondPageIds = secondResponse.body.data.map((item: any) => item.id);
          const overlap = firstPageIds.filter((id: string) => secondPageIds.includes(id));
          expect(overlap.length).toBe(0);
        }
      } else {
        // If no next cursor, it means we have all items in first page
        expect(firstResponse.body.meta.hasNextPage).toBe(false);
      }
    });

    it('should include aggregations when requested', async () => {
      const response = await ctx.request
        .get('/api/inventory?includeAggregations=true')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      expect(response.body.aggregations).toBeDefined();
      expect(response.body.aggregations.totalQuantity).toBeDefined();
      expect(response.body.aggregations.totalValue).toBeDefined();
      expect(response.body.aggregations.averagePrice).toBeDefined();
      expect(response.body.aggregations.minPrice).toBeDefined();
      expect(response.body.aggregations.maxPrice).toBeDefined();
      expect(typeof response.body.aggregations.totalQuantity).toBe('number');
      expect(typeof response.body.aggregations.totalValue).toBe('number');
    });

    it('should include movement stats when requested', async () => {
      const response = await ctx.request
        .get('/api/inventory?includeMovementStats=true')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      expect(response.body.movementStats).toBeDefined();
      expect(Array.isArray(response.body.movementStats)).toBe(true);
      response.body.movementStats.forEach((stat: any) => {
        expect(stat.type).toBeDefined();
        expect(typeof stat.totalQuantity).toBe('number');
        expect(typeof stat.totalMovements).toBe('number');
      });
    });
  });

  describe('Enhanced Statistics', () => {
    it('should return comprehensive analytics in stats', async () => {
      const response = await ctx.request
        .get('/api/inventory/stats')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);

      // Basic metrics
      expect(response.body.totalItems).toBeDefined();
      expect(response.body.activeItems).toBeDefined();
      expect(response.body.inactiveItems).toBeDefined();
      expect(response.body.lowStockItems).toBeDefined();
      expect(response.body.expiredItems).toBeDefined();
      expect(response.body.expiringSoonItems).toBeDefined();
      expect(response.body.zeroStockItems).toBeDefined();
      expect(response.body.totalValue).toBeDefined();
      expect(response.body.averageCostPrice).toBeDefined();
      expect(response.body.totalQuantity).toBeDefined();

      // Advanced analytics
      expect(response.body.topProducts).toBeDefined();
      expect(Array.isArray(response.body.topProducts)).toBe(true);
      expect(response.body.supplierPerformance).toBeDefined();
      expect(Array.isArray(response.body.supplierPerformance)).toBe(true);
      expect(response.body.categoryBreakdown).toBeDefined();
      expect(Array.isArray(response.body.categoryBreakdown)).toBe(true);
      expect(response.body.stockAging).toBeDefined();
      expect(response.body.turnoverAnalysis).toBeDefined();
      expect(response.body.expiryAlerts).toBeDefined();
      expect(response.body.stockVelocity).toBeDefined();
      expect(response.body.trendAnalysis).toBeDefined();

      // Calculated metrics
      expect(response.body.averageStockValue).toBeDefined();
      expect(response.body.stockTurnoverRate).toBeDefined();
      expect(response.body.inventoryHealth).toBeDefined();
      expect(response.body.inventoryHealth.score).toBeDefined();
      expect(response.body.inventoryHealth.status).toBeDefined();
      expect(Array.isArray(response.body.inventoryHealth.issues)).toBe(true);

      // Verify data types
      expect(typeof response.body.totalItems).toBe('number');
      expect(typeof response.body.totalValue).toBe('number');
      expect(typeof response.body.averageStockValue).toBe('number');
      expect(typeof response.body.stockTurnoverRate).toBe('number');
      expect(typeof response.body.inventoryHealth.score).toBe('number');
      expect(typeof response.body.inventoryHealth.status).toBe('string');
    });

    it('should return detailed top products analysis', async () => {
      const response = await ctx.request
        .get('/api/inventory/stats')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      expect(response.body.topProducts).toBeDefined();

      if (response.body.topProducts.length > 0) {
        const topProduct = response.body.topProducts[0];
        expect(topProduct.productId).toBeDefined();
        expect(topProduct.productName).toBeDefined();
        expect(topProduct.productCode).toBeDefined();
        expect(topProduct.totalQuantity).toBeDefined();
        expect(topProduct.totalValue).toBeDefined();
        expect(topProduct.itemCount).toBeDefined();
        expect(typeof topProduct.totalValue).toBe('number');
        expect(typeof topProduct.totalQuantity).toBe('number');
      }
    });

    it('should return supplier performance metrics', async () => {
      const response = await ctx.request
        .get('/api/inventory/stats')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      expect(response.body.supplierPerformance).toBeDefined();

      if (response.body.supplierPerformance.length > 0) {
        const supplier = response.body.supplierPerformance[0];
        expect(supplier.supplierId).toBeDefined();
        expect(supplier.supplierName).toBeDefined();
        expect(supplier.totalItems).toBeDefined();
        expect(supplier.totalValue).toBeDefined();
        expect(supplier.averagePrice).toBeDefined();
        expect(typeof supplier.totalItems).toBe('number');
        expect(typeof supplier.totalValue).toBe('number');
        expect(typeof supplier.averagePrice).toBe('number');
      }
    });

    it('should return stock aging analysis', async () => {
      const response = await ctx.request
        .get('/api/inventory/stats')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      expect(response.body.stockAging).toBeDefined();
      expect(response.body.stockAging.recent).toBeDefined();
      expect(response.body.stockAging.medium).toBeDefined();
      expect(response.body.stockAging.old).toBeDefined();
      expect(response.body.stockAging.veryOld).toBeDefined();

      // Verify structure of aging periods
      ['recent', 'medium', 'old', 'veryOld'].forEach(period => {
        const agingData = response.body.stockAging[period];
        expect(agingData.period).toBeDefined();
        expect(agingData.count).toBeDefined();
        expect(agingData.quantity).toBeDefined();
        expect(agingData.value).toBeDefined();
        expect(typeof agingData.count).toBe('number');
        expect(typeof agingData.quantity).toBe('number');
        expect(typeof agingData.value).toBe('number');
      });
    });

    it('should return expiry alerts with detailed breakdown', async () => {
      const response = await ctx.request
        .get('/api/inventory/stats')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      expect(response.body.expiryAlerts).toBeDefined();
      expect(response.body.expiryAlerts.critical).toBeDefined();
      expect(response.body.expiryAlerts.warning).toBeDefined();
      expect(response.body.expiryAlerts.notice).toBeDefined();
      expect(response.body.expiryAlerts.expired).toBeDefined();

      // Verify structure of expiry alerts
      ['critical', 'warning', 'notice', 'expired'].forEach(level => {
        const alertData = response.body.expiryAlerts[level];
        expect(alertData.period).toBeDefined();
        expect(alertData.count).toBeDefined();
        expect(Array.isArray(alertData.items)).toBe(true);
        expect(typeof alertData.count).toBe('number');
      });
    });

    it('should return turnover analysis', async () => {
      const response = await ctx.request
        .get('/api/inventory/stats')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      expect(response.body.turnoverAnalysis).toBeDefined();
      expect(response.body.turnoverAnalysis.averageTurnoverRate).toBeDefined();
      expect(response.body.turnoverAnalysis.topPerformers).toBeDefined();
      expect(response.body.turnoverAnalysis.slowMovers).toBeDefined();
      expect(response.body.turnoverAnalysis.totalItemsAnalyzed).toBeDefined();
      expect(Array.isArray(response.body.turnoverAnalysis.topPerformers)).toBe(true);
      expect(Array.isArray(response.body.turnoverAnalysis.slowMovers)).toBe(true);
      expect(typeof response.body.turnoverAnalysis.averageTurnoverRate).toBe('number');
      expect(typeof response.body.turnoverAnalysis.totalItemsAnalyzed).toBe('number');
    });

    it('should return stock velocity metrics', async () => {
      const response = await ctx.request
        .get('/api/inventory/stats')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      expect(response.body.stockVelocity).toBeDefined();
      expect(response.body.stockVelocity.fastMoving).toBeDefined();
      expect(response.body.stockVelocity.slowMoving).toBeDefined();
      expect(response.body.stockVelocity.averageVelocity).toBeDefined();
      expect(Array.isArray(response.body.stockVelocity.fastMoving)).toBe(true);
      expect(Array.isArray(response.body.stockVelocity.slowMoving)).toBe(true);
      expect(typeof response.body.stockVelocity.averageVelocity).toBe('number');
    });

    it('should return trend analysis', async () => {
      const response = await ctx.request
        .get('/api/inventory/stats')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      expect(response.body.trendAnalysis).toBeDefined();
      expect(response.body.trendAnalysis.movementTrend).toBeDefined();
      expect(response.body.trendAnalysis.totalMovements).toBeDefined();
      expect(response.body.trendAnalysis.averageMovementsPerDay).toBeDefined();

      const movementTrend = response.body.trendAnalysis.movementTrend;
      expect(movementTrend.percentage).toBeDefined();
      expect(movementTrend.direction).toBeDefined();
      expect(movementTrend.firstPeriodMovements).toBeDefined();
      expect(movementTrend.secondPeriodMovements).toBeDefined();
      expect(typeof movementTrend.percentage).toBe('number');
      expect(['increasing', 'decreasing', 'stable']).toContain(movementTrend.direction);
    });
  });

  describe('FIFO/FEFO Stock Allocation Integration Tests', () => {
    let fifoTestProductId: string;
    let fifoTestUnitId: string;
    let fifoTestSupplierId: string;

    // Helper function to create inventory batches for testing
    async function createInventoryBatches(batches: Array<{
      batchNumber: string;
      receivedDate: string;
      quantity: number;
      expiryDate?: string;
    }>) {
      for (const batch of batches) {
        await ctx.request
          .post('/api/inventory')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send({
            productId: fifoTestProductId,
            unitId: fifoTestUnitId,
            supplierId: fifoTestSupplierId,
            batchNumber: batch.batchNumber,
            quantityOnHand: batch.quantity,
            costPrice: 10000,
            sellingPrice: 15000,
            receivedDate: batch.receivedDate,
            expiryDate: batch.expiryDate,
            location: 'Test Location',
          });
      }
    }

    beforeAll(async () => {
      // Create dedicated test dependencies for FIFO/FEFO tests
      const fifoUnit = await ctx.prisma.productUnit.create({
        data: {
          name: 'FIFO Test Unit',
          abbreviation: 'FTU',
          type: 'COUNT',
          isBaseUnit: true,
          description: 'Unit for FIFO/FEFO testing',
          isActive: true,
        },
      });
      fifoTestUnitId = fifoUnit.id;

      const fifoProduct = await ctx.prisma.product.create({
        data: {
          code: 'FIFO-TEST-001',
          name: 'FIFO Test Medicine',
          type: 'MEDICINE',
          category: 'ANALGESIC',
          medicineClassification: 'OBAT_BEBAS',
          baseUnitId: fifoTestUnitId,
          isActive: true,
          createdBy: ctx.users.admin.id,
        },
      });
      fifoTestProductId = fifoProduct.id;

      const fifoSupplier = await ctx.prisma.supplier.create({
        data: {
          code: 'FIFO-SUP-001',
          name: 'FIFO Test Supplier',
          type: 'PBF',
          status: 'ACTIVE',
          address: 'FIFO Test Address',
          city: 'Jakarta',
          province: 'DKI Jakarta',
          postalCode: '12345',
          phone: '+62 21 1234 5678',
          email: '<EMAIL>',
          npwp: '12.345.678.9-012.345',
          createdBy: ctx.users.admin.id,
        },
      });
      fifoTestSupplierId = fifoSupplier.id;

      console.log('FIFO/FEFO test dependencies created:', {
        fifoTestProductId,
        fifoTestUnitId,
        fifoTestSupplierId,
      });
    });

    describe('FIFO (First In First Out) Allocation Tests', () => {
      beforeEach(async () => {
        // Reset all test data before each test
        await ctx.prisma.inventoryItem.deleteMany({
          where: {
            productId: fifoTestProductId
          }
        });
      });

      it('should allocate from oldest received batch first (FIFO)', async () => {
        // Set up test data - create multiple batches with different received dates
        await createInventoryBatches([
          { batchNumber: 'FIFO-BATCH-001', receivedDate: '2023-01-01', quantity: 100 },
          { batchNumber: 'FIFO-BATCH-002', receivedDate: '2023-02-01', quantity: 150 },
          { batchNumber: 'FIFO-BATCH-003', receivedDate: '2023-03-01', quantity: 200 }
        ]);

        // Allocate 80 units with FIFO method
        const response = await ctx.request
          .post(`/api/inventory/allocate`)
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send({
            productId: fifoTestProductId,
            requestedQuantity: 80,
            method: 'FIFO',
            reason: 'FIFO allocation test'
          });

        expectSuccess(response, 201);
        expect(response.body.success).toBe(true);
        expect(response.body.allocatedQuantity).toBe(80);
        
        // Verify the oldest batch (FIFO-BATCH-001) was allocated from first
        expect(response.body.batches[0].batchNumber).toBe('FIFO-BATCH-001');
        
        // Check that the batch has been updated correctly
        const updatedBatch = await ctx.prisma.inventoryItem.findFirst({
          where: { batchNumber: 'FIFO-BATCH-001' }
        });
        
        // With the updated allocation logic, quantityOnHand remains unchanged, but quantityAllocated is incremented
        expect(updatedBatch?.quantityOnHand).toBe(100); // Unchanged
        expect(updatedBatch?.quantityAllocated).toBe(80); // 80 units allocated
      });

      it('should allocate across multiple batches when single batch insufficient (FIFO)', async () => {
        // Set up test data - create multiple batches with different received dates
        await createInventoryBatches([
          { batchNumber: 'FIFO-BATCH-001', receivedDate: '2023-01-01', quantity: 100 },
          { batchNumber: 'FIFO-BATCH-002', receivedDate: '2023-02-01', quantity: 150 }
        ]);

        // Allocate 180 units with FIFO method (more than first batch)
        const response = await ctx.request
          .post(`/api/inventory/allocate`)
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send({
            productId: fifoTestProductId,
            requestedQuantity: 180,
            method: 'FIFO',
            reason: 'FIFO multi-batch allocation test'
          });

        expectSuccess(response, 201);
        expect(response.body.success).toBe(true);
        expect(response.body.allocatedQuantity).toBe(180);
        
        // Verify batches were allocated in correct order
        expect(response.body.batches[0].batchNumber).toBe('FIFO-BATCH-001');
        expect(response.body.batches[1].batchNumber).toBe('FIFO-BATCH-002');
        
        // Check that both batches have been updated correctly
        const batch1 = await ctx.prisma.inventoryItem.findFirst({
          where: { batchNumber: 'FIFO-BATCH-001' }
        });
        
        const batch2 = await ctx.prisma.inventoryItem.findFirst({
          where: { batchNumber: 'FIFO-BATCH-002' }
        });
        
        // With the updated allocation logic, quantityOnHand remains unchanged, but quantityAllocated is incremented
        expect(batch1?.quantityOnHand).toBe(100); // Unchanged
        expect(batch1?.quantityAllocated).toBe(100); // Fully allocated
        expect(batch2?.quantityOnHand).toBe(150); // Unchanged
        expect(batch2?.quantityAllocated).toBe(80); // 80 units allocated (180 - 100)
      });

      it('should handle tie-breaking with createdAt when received dates are equal', async () => {
        // Ensure clean state - delete all existing batches first
        await ctx.prisma.inventoryItem.deleteMany({
          where: { productId: fifoTestProductId }
        });

        // Create two batches with same received date but different creation times
        const sameReceivedDate = new Date(Date.now() - 45 * 24 * 60 * 60 * 1000);

        const batch1 = await ctx.prisma.inventoryItem.create({
          data: {
            productId: fifoTestProductId,
            unitId: fifoTestUnitId,
            supplierId: fifoTestSupplierId,
            batchNumber: 'FIFO-TIE-001',
            quantityOnHand: 50,
            costPrice: 5000,
            receivedDate: sameReceivedDate,
            expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
            location: 'Rak TIE-A1',
            isActive: true,
            createdBy: ctx.users.admin.id,
          },
        });

        // Wait a moment to ensure different createdAt
        await new Promise(resolve => setTimeout(resolve, 50));

        const batch2 = await ctx.prisma.inventoryItem.create({
          data: {
            productId: fifoTestProductId,
            unitId: fifoTestUnitId,
            supplierId: fifoTestSupplierId,
            batchNumber: 'FIFO-TIE-002',
            quantityOnHand: 75,
            costPrice: 5100,
            receivedDate: sameReceivedDate, // Same received date
            expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
            location: 'Rak TIE-A2',
            isActive: true,
            createdBy: ctx.users.admin.id,
          },
        });

        const allocationData = {
          productId: fifoTestProductId,
          requestedQuantity: 40,
          method: AllocationMethod.FIFO,
          allowPartialAllocation: true,
          reason: 'Test FIFO tie-breaking',
        };

        const response = await ctx.request
          .post('/api/inventory/allocate')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(allocationData);

        expectSuccess(response, 201);
        expect(response.body.success).toBe(true);
        expect(response.body.allocatedQuantity).toBe(40);
        expect(response.body.batches).toHaveLength(1);

        // Should allocate from the batch created first (earlier createdAt)
        expect(response.body.batches[0].batchNumber).toBe('FIFO-TIE-001');
      });
    });

    describe('FEFO (First Expired First Out) Allocation Tests', () => {
      beforeEach(async () => {
        // Clean up any existing test batches
        await ctx.prisma.inventoryItem.deleteMany({
          where: { productId: fifoTestProductId }
        });
      });

      it('should allocate from earliest expiry batch first (FEFO)', async () => {
        // Set up test data - create multiple batches with different expiry dates
        console.log('Creating FEFO test batches...');
        
        // Get future dates for expiry
        const now = new Date();
        const futureDate1 = new Date(now);
        futureDate1.setMonth(futureDate1.getMonth() + 3); // 3 months from now
        
        const futureDate2 = new Date(now);
        futureDate2.setMonth(futureDate2.getMonth() + 6); // 6 months from now
        
        const futureDate3 = new Date(now);
        futureDate3.setMonth(futureDate3.getMonth() + 12); // 12 months from now
        
        // Create batches one by one to ensure they're created properly
        const batch1Response = await ctx.request
          .post('/api/inventory')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send({
            productId: fifoTestProductId,
            unitId: fifoTestUnitId,
            supplierId: fifoTestSupplierId,
            batchNumber: 'FEFO-BATCH-001',
            quantityOnHand: 100,
            costPrice: 10000,
            sellingPrice: 15000,
            receivedDate: new Date().toISOString().split('T')[0], // Today
            expiryDate: futureDate1.toISOString().split('T')[0], // 3 months from now
            location: 'Test Location'
          });
        
        console.log('Batch 1 creation response:', batch1Response.status, batch1Response.body);
        
        const batch2Response = await ctx.request
          .post('/api/inventory')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send({
            productId: fifoTestProductId,
            unitId: fifoTestUnitId,
            supplierId: fifoTestSupplierId,
            batchNumber: 'FEFO-BATCH-002',
            quantityOnHand: 150,
            costPrice: 10000,
            sellingPrice: 15000,
            receivedDate: new Date().toISOString().split('T')[0], // Today
            expiryDate: futureDate2.toISOString().split('T')[0], // 6 months from now
            location: 'Test Location'
          });
        
        console.log('Batch 2 creation response:', batch2Response.status, batch2Response.body);
        
        const batch3Response = await ctx.request
          .post('/api/inventory')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send({
            productId: fifoTestProductId,
            unitId: fifoTestUnitId,
            supplierId: fifoTestSupplierId,
            batchNumber: 'FEFO-BATCH-003',
            quantityOnHand: 200,
            costPrice: 10000,
            sellingPrice: 15000,
            receivedDate: new Date().toISOString().split('T')[0], // Today
            expiryDate: futureDate3.toISOString().split('T')[0], // 12 months from now
            location: 'Test Location'
          });
        
        console.log('Batch 3 creation response:', batch3Response.status, batch3Response.body);
        
        // Verify inventory items were created
        const inventoryItems = await ctx.prisma.inventoryItem.findMany({
          where: {
            productId: fifoTestProductId
          }
        });
        
        console.log(`Found ${inventoryItems.length} inventory items for product:`, 
          inventoryItems.map(item => ({ 
            id: item.id, 
            batchNumber: item.batchNumber, 
            quantityOnHand: item.quantityOnHand,
            expiryDate: item.expiryDate
          }))
        );

        // Allocate 80 units with FEFO method
        const response = await ctx.request
          .post(`/api/inventory/allocate`)
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send({
            productId: fifoTestProductId,
            requestedQuantity: 80,
            method: AllocationMethod.FEFO,
            reason: 'FEFO allocation test'
          });

        console.log('FEFO test response:', response.body);
        
        expectSuccess(response, 201);
        expect(response.body.success).toBe(true);
        expect(response.body.allocatedQuantity).toBe(80);
        
        // Verify the earliest expiry batch (FEFO-BATCH-001) was allocated from first
        expect(response.body.batches[0].batchNumber).toBe('FEFO-BATCH-001');
        
        // Check that the batch has been updated correctly
        const updatedBatch = await ctx.prisma.inventoryItem.findFirst({
          where: { batchNumber: 'FEFO-BATCH-001' }
        });
        
        // With the updated allocation logic, quantityOnHand remains unchanged, but quantityAllocated is incremented
        expect(updatedBatch?.quantityOnHand).toBe(100); // Unchanged
        expect(updatedBatch?.quantityAllocated).toBe(80); // 80 units allocated
      });

      it('should allocate across multiple batches when single batch insufficient (FEFO)', async () => {
        // Set up test data - create multiple batches with different expiry dates
        console.log('Creating FEFO multi-batch test...');
        
        // Get future dates for expiry
        const now = new Date();
        const futureDate1 = new Date(now);
        futureDate1.setMonth(futureDate1.getMonth() + 3); // 3 months from now
        
        const futureDate2 = new Date(now);
        futureDate2.setMonth(futureDate2.getMonth() + 6); // 6 months from now
        
        // Create batches one by one to ensure they're created properly
        const batch1Response = await ctx.request
          .post('/api/inventory')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send({
            productId: fifoTestProductId,
            unitId: fifoTestUnitId,
            supplierId: fifoTestSupplierId,
            batchNumber: 'FEFO-MULTI-001',
            quantityOnHand: 100,
            costPrice: 10000,
            sellingPrice: 15000,
            receivedDate: new Date().toISOString().split('T')[0], // Today
            expiryDate: futureDate1.toISOString().split('T')[0], // 3 months from now
            location: 'Test Location'
          });
        
        console.log('Multi-batch 1 creation response:', batch1Response.status, batch1Response.body);
        
        const batch2Response = await ctx.request
          .post('/api/inventory')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send({
            productId: fifoTestProductId,
            unitId: fifoTestUnitId,
            supplierId: fifoTestSupplierId,
            batchNumber: 'FEFO-MULTI-002',
            quantityOnHand: 150,
            costPrice: 10000,
            sellingPrice: 15000,
            receivedDate: new Date().toISOString().split('T')[0], // Today
            expiryDate: futureDate2.toISOString().split('T')[0], // 6 months from now
            location: 'Test Location'
          });
        
        console.log('Multi-batch 2 creation response:', batch2Response.status, batch2Response.body);
        
        // Verify inventory items were created
        const inventoryItems = await ctx.prisma.inventoryItem.findMany({
          where: {
            productId: fifoTestProductId,
            batchNumber: { startsWith: 'FEFO-MULTI-' }
          }
        });
        
        console.log(`Found ${inventoryItems.length} multi-batch inventory items:`, 
          inventoryItems.map(item => ({ 
            id: item.id, 
            batchNumber: item.batchNumber, 
            quantityOnHand: item.quantityOnHand,
            expiryDate: item.expiryDate
          }))
        );

        // Allocate 180 units with FEFO method (more than first batch)
        const response = await ctx.request
          .post(`/api/inventory/allocate`)
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send({
            productId: fifoTestProductId,
            requestedQuantity: 180,
            method: AllocationMethod.FEFO,
            reason: 'FEFO multi-batch allocation test'
          });

        console.log('FEFO multi-batch test response:', response.body);
        
        expectSuccess(response, 201);
        expect(response.body.success).toBe(true);
        expect(response.body.allocatedQuantity).toBe(180);
        
        // Verify batches were allocated in correct order
        expect(response.body.batches[0].batchNumber).toBe('FEFO-MULTI-001');
        expect(response.body.batches[1].batchNumber).toBe('FEFO-MULTI-002');
        
        // Check that both batches have been updated correctly
        const batch1 = await ctx.prisma.inventoryItem.findFirst({
          where: { batchNumber: 'FEFO-MULTI-001' }
        });
        
        const batch2 = await ctx.prisma.inventoryItem.findFirst({
          where: { batchNumber: 'FEFO-MULTI-002' }
        });
        
        // With the updated allocation logic, quantityOnHand remains unchanged, but quantityAllocated is incremented
        expect(batch1?.quantityOnHand).toBe(100); // Unchanged
        expect(batch1?.quantityAllocated).toBe(100); // Fully allocated
        expect(batch2?.quantityOnHand).toBe(150); // Unchanged
        expect(batch2?.quantityAllocated).toBe(80); // 80 units allocated (180 - 100)
      });

      it('should fallback to FIFO when expiry dates are equal', async () => {
        // Ensure clean state - delete all existing batches first
        await ctx.prisma.inventoryItem.deleteMany({
          where: { productId: fifoTestProductId }
        });

        // Create two batches with same expiry date but different received dates
        const sameExpiryDate = new Date(Date.now() + 120 * 24 * 60 * 60 * 1000);

        await ctx.prisma.inventoryItem.create({
          data: {
            productId: fifoTestProductId,
            unitId: fifoTestUnitId,
            supplierId: fifoTestSupplierId,
            batchNumber: 'FEFO-EQUAL-001',
            quantityOnHand: 50,
            costPrice: 5000,
            receivedDate: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000), // Older received
            expiryDate: sameExpiryDate,
            location: 'Rak EQUAL-A1',
            isActive: true,
            createdBy: ctx.users.admin.id,
          },
        });

        await ctx.prisma.inventoryItem.create({
          data: {
            productId: fifoTestProductId,
            unitId: fifoTestUnitId,
            supplierId: fifoTestSupplierId,
            batchNumber: 'FEFO-EQUAL-002',
            quantityOnHand: 75,
            costPrice: 5100,
            receivedDate: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000), // Newer received
            expiryDate: sameExpiryDate, // Same expiry date
            location: 'Rak EQUAL-A2',
            isActive: true,
            createdBy: ctx.users.admin.id,
          },
        });

        const allocationData = {
          productId: fifoTestProductId,
          requestedQuantity: 40,
          method: AllocationMethod.FEFO,
          allowPartialAllocation: true,
          reason: 'Test FEFO fallback to FIFO',
        };

        const response = await ctx.request
          .post('/api/inventory/allocate')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(allocationData);

        console.log('FEFO test response:', response.body);
        
        expectSuccess(response, 201);
        expect(response.body.success).toBe(true);
        expect(response.body.allocatedQuantity).toBe(40);
        expect(response.body.batches).toHaveLength(1);

        // Should allocate from the batch received first (FIFO fallback)
        expect(response.body.batches[0].batchNumber).toBe('FEFO-EQUAL-001');
      });

      it('should provide near-expiry warnings for FEFO allocation', async () => {
        // Create a batch that expires soon
        const nearExpiryBatch = await ctx.prisma.inventoryItem.create({
          data: {
            productId: fifoTestProductId,
            unitId: fifoTestUnitId,
            supplierId: fifoTestSupplierId,
            batchNumber: 'FEFO-NEAR-EXPIRY',
            quantityOnHand: 100,
            costPrice: 5000,
            receivedDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
            expiryDate: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000), // Expires in 15 days
            location: 'Rak NEAR-A1',
            isActive: true,
            createdBy: ctx.users.admin.id,
          },
        });

        const allocationData = {
          productId: fifoTestProductId,
          requestedQuantity: 50,
          method: AllocationMethod.FEFO,
          allowPartialAllocation: true,
          nearExpiryWarningDays: 30, // Warn for items expiring within 30 days
          reason: 'Test FEFO near-expiry warning',
        };

        const response = await ctx.request
          .post('/api/inventory/allocate')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(allocationData);

        console.log('FEFO test response:', response.body);
        
        expectSuccess(response, 201);
        expect(response.body.success).toBe(true);
        expect(response.body.warnings).toBeDefined();
        expect(response.body.warnings.some((warning: string) =>
          warning.includes('kedaluwarsa dalam') && warning.includes('15')
        )).toBe(true);
      });
    });

    describe('Edge Cases and Error Conditions', () => {
      beforeEach(async () => {
        // Clean up any existing test batches
        await ctx.prisma.inventoryItem.deleteMany({
          where: { productId: fifoTestProductId }
        });
      });

      it('should handle insufficient total stock across all batches', async () => {
        // Create batches with limited total stock
        const batches = [
          {
            batchNumber: 'INSUFFICIENT-001',
            quantityOnHand: 30,
            costPrice: 5000,
            receivedDate: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000),
            expiryDate: new Date(Date.now() + 180 * 24 * 60 * 60 * 1000),
            location: 'Rak INS-A1',
          },
          {
            batchNumber: 'INSUFFICIENT-002',
            quantityOnHand: 40,
            costPrice: 5200,
            receivedDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
            expiryDate: new Date(Date.now() + 120 * 24 * 60 * 60 * 1000),
            location: 'Rak INS-A2',
          },
        ];

        for (const batch of batches) {
          await ctx.prisma.inventoryItem.create({
            data: {
              productId: fifoTestProductId,
              unitId: fifoTestUnitId,
              supplierId: fifoTestSupplierId,
              ...batch,
              isActive: true,
              createdBy: ctx.users.admin.id,
            },
          });
        }

        const allocationData = {
          productId: fifoTestProductId,
          requestedQuantity: 100, // More than total available (70)
          method: AllocationMethod.FEFO,
          allowPartialAllocation: true,
          reason: 'Test insufficient stock',
        };

        const response = await ctx.request
          .post('/api/inventory/allocate')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(allocationData);

        expectSuccess(response, 201);
        expect(response.body.success).toBe(true);
        expect(response.body.allocatedQuantity).toBe(70); // Total available
        expect(response.body.shortfall).toBe(30); // 100 - 70
        expect(response.body.warnings.some((warning: string) =>
          warning.includes('Alokasi sebagian')
        )).toBe(true);
      });

      it('should fail when insufficient stock and partial allocation not allowed', async () => {
        // Create batch with limited stock
        await ctx.prisma.inventoryItem.create({
          data: {
            productId: fifoTestProductId,
            unitId: fifoTestUnitId,
            supplierId: fifoTestSupplierId,
            batchNumber: 'LIMITED-STOCK',
            quantityOnHand: 50,
            costPrice: 5000,
            receivedDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
            expiryDate: new Date(Date.now() + 180 * 24 * 60 * 60 * 1000),
            location: 'Rak LIM-A1',
            isActive: true,
            createdBy: ctx.users.admin.id,
          },
        });

        const allocationData = {
          productId: fifoTestProductId,
          requestedQuantity: 100, // More than available (50)
          method: AllocationMethod.FIFO,
          allowPartialAllocation: false, // Don't allow partial
          reason: 'Test insufficient stock - no partial',
        };

        const response = await ctx.request
          .post('/api/inventory/allocate')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(allocationData);

        expectSuccess(response, 201);
        expect(response.body.success).toBe(false);
        expect(response.body.allocatedQuantity).toBe(0); // No allocation when partial not allowed
        expect(response.body.errors.some((error: string) =>
          error.includes('Stok tidak mencukupi')
        )).toBe(true);
      });

      it('should skip inactive inventory items', async () => {
        // Create active and inactive batches
        await ctx.prisma.inventoryItem.create({
          data: {
            productId: fifoTestProductId,
            unitId: fifoTestUnitId,
            supplierId: fifoTestSupplierId,
            batchNumber: 'INACTIVE-BATCH',
            quantityOnHand: 100,
            costPrice: 5000,
            receivedDate: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000),
            expiryDate: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // Earlier expiry
            location: 'Rak INACTIVE-A1',
            isActive: false, // Inactive
            createdBy: ctx.users.admin.id,
          },
        });

        await ctx.prisma.inventoryItem.create({
          data: {
            productId: fifoTestProductId,
            unitId: fifoTestUnitId,
            supplierId: fifoTestSupplierId,
            batchNumber: 'ACTIVE-BATCH',
            quantityOnHand: 80,
            costPrice: 5200,
            receivedDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
            expiryDate: new Date(Date.now() + 180 * 24 * 60 * 60 * 1000), // Later expiry
            location: 'Rak ACTIVE-A1',
            isActive: true, // Active
            createdBy: ctx.users.admin.id,
          },
        });

        const allocationData = {
          productId: fifoTestProductId,
          requestedQuantity: 50,
          method: AllocationMethod.FEFO,
          allowPartialAllocation: true,
          reason: 'Test skip inactive items',
        };

        const response = await ctx.request
          .post('/api/inventory/allocate')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(allocationData);

        expectSuccess(response, 201);
        expect(response.body.success).toBe(true);
        expect(response.body.allocatedQuantity).toBe(50);
        expect(response.body.batches).toHaveLength(1);

        // Should allocate from active batch only, even though inactive has earlier expiry
        expect(response.body.batches[0].batchNumber).toBe('ACTIVE-BATCH');
      });

      it('should skip zero quantity batches', async () => {
        // Create zero quantity and normal batches
        await ctx.prisma.inventoryItem.create({
          data: {
            productId: fifoTestProductId,
            unitId: fifoTestUnitId,
            supplierId: fifoTestSupplierId,
            batchNumber: 'ZERO-QUANTITY',
            quantityOnHand: 0, // Zero quantity
            costPrice: 5000,
            receivedDate: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000),
            expiryDate: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000),
            location: 'Rak ZERO-A1',
            isActive: true,
            createdBy: ctx.users.admin.id,
          },
        });

        await ctx.prisma.inventoryItem.create({
          data: {
            productId: fifoTestProductId,
            unitId: fifoTestUnitId,
            supplierId: fifoTestSupplierId,
            batchNumber: 'NORMAL-QUANTITY',
            quantityOnHand: 100,
            costPrice: 5200,
            receivedDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
            expiryDate: new Date(Date.now() + 180 * 24 * 60 * 60 * 1000),
            location: 'Rak NORMAL-A1',
            isActive: true,
            createdBy: ctx.users.admin.id,
          },
        });

        const allocationData = {
          productId: fifoTestProductId,
          requestedQuantity: 50,
          method: AllocationMethod.FIFO,
          allowPartialAllocation: true,
          reason: 'Test skip zero quantity',
        };

        const response = await ctx.request
          .post('/api/inventory/allocate')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(allocationData);

        expectSuccess(response, 201);
        expect(response.body.success).toBe(true);
        expect(response.body.allocatedQuantity).toBe(50);
        expect(response.body.batches).toHaveLength(1);

        // Should allocate from normal quantity batch only
        expect(response.body.batches[0].batchNumber).toBe('NORMAL-QUANTITY');
      });

      it('should skip expired items in both FIFO and FEFO', async () => {
        // Create two batches - one expired and one valid
        console.log('Creating expired/valid test batches...');
        
        const now = new Date();
        const pastDate = new Date(now);
        pastDate.setDate(pastDate.getDate() - 10); // 10 days ago
        
        const futureDate = new Date(now);
        futureDate.setDate(futureDate.getDate() + 90); // 90 days from now
        
        // Create expired batch
        const expiredResponse = await ctx.request
          .post('/api/inventory')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send({
            productId: fifoTestProductId,
            unitId: fifoTestUnitId,
            supplierId: fifoTestSupplierId,
            batchNumber: 'EXPIRED-BATCH',
            quantityOnHand: 100,
            costPrice: 10000,
            sellingPrice: 15000,
            receivedDate: new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 90 days ago
            expiryDate: pastDate.toISOString().split('T')[0], // 10 days ago (expired)
            location: 'Test Location'
          });
        
        console.log('Expired batch response:', expiredResponse.status, expiredResponse.body);
        
        // We need to bypass validation to create an expired batch
        if (expiredResponse.status === 400) {
          console.log('Creating expired batch directly in database...');
          await ctx.prisma.inventoryItem.create({
            data: {
              productId: fifoTestProductId,
              unitId: fifoTestUnitId,
              supplierId: fifoTestSupplierId,
              batchNumber: 'EXPIRED-BATCH',
              quantityOnHand: 100,
              quantityAllocated: 0,
              costPrice: 10000,
              sellingPrice: 15000,
              receivedDate: new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000),
              expiryDate: pastDate,
              location: 'Test Location',
              isActive: true,
              createdBy: ctx.users.admin.id
            }
          });
        }
        
        // Create valid batch
        const validResponse = await ctx.request
          .post('/api/inventory')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send({
            productId: fifoTestProductId,
            unitId: fifoTestUnitId,
            supplierId: fifoTestSupplierId,
            batchNumber: 'VALID-BATCH',
            quantityOnHand: 100,
            costPrice: 10000,
            sellingPrice: 15000,
            receivedDate: new Date().toISOString().split('T')[0], // Today
            expiryDate: futureDate.toISOString().split('T')[0], // 90 days from now
            location: 'Test Location'
          });
        
        console.log('Valid batch response:', validResponse.status, validResponse.body);
        
        // Verify inventory items were created
        const inventoryItems = await ctx.prisma.inventoryItem.findMany({
          where: {
            productId: fifoTestProductId,
            batchNumber: { in: ['EXPIRED-BATCH', 'VALID-BATCH'] }
          }
        });
        
        console.log(`Found ${inventoryItems.length} expired/valid inventory items:`, 
          inventoryItems.map(item => ({ 
            id: item.id, 
            batchNumber: item.batchNumber, 
            quantityOnHand: item.quantityOnHand,
            expiryDate: item.expiryDate
          }))
        );

        // Test FIFO allocation - should skip expired batch
        const fifoResponse = await ctx.request
          .post('/api/inventory/allocate')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send({
            productId: fifoTestProductId,
            requestedQuantity: 50,
            method: AllocationMethod.FIFO,
            reason: 'Test expired batch skipping'
          });
        
        console.log('FIFO expired test response:', fifoResponse.body);

        expectSuccess(fifoResponse, 201);
        expect(fifoResponse.body.success).toBe(true);
        expect(fifoResponse.body.batches[0].batchNumber).toBe('VALID-BATCH');

        // Reset stock for FEFO test
        await ctx.prisma.inventoryItem.updateMany({
          where: { productId: fifoTestProductId },
          data: { quantityAllocated: 0 }
        });

        // Test FEFO allocation - should skip expired batch
        const fefoResponse = await ctx.request
          .post('/api/inventory/allocate')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send({
            productId: fifoTestProductId,
            requestedQuantity: 50,
            method: AllocationMethod.FEFO,
            reason: 'Test expired batch skipping'
          });
        
        console.log('FEFO expired test response:', fefoResponse.body);

        expectSuccess(fefoResponse, 201);
        expect(fefoResponse.body.success).toBe(true);
        expect(fefoResponse.body.batches[0].batchNumber).toBe('VALID-BATCH');
      });
    });

    describe('Integration with Stock Adjustment Functionality', () => {
      let adjustmentTestBatches: string[] = [];

      beforeEach(async () => {
        // Clean up any existing test batches
        await ctx.prisma.inventoryItem.deleteMany({
          where: { productId: fifoTestProductId }
        });
        adjustmentTestBatches = [];

        // Create test batches for adjustment integration
        const now = new Date();
        const batches = [
          {
            batchNumber: 'ADJ-BATCH-001',
            quantityOnHand: 100,
            costPrice: 5000,
            receivedDate: new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000),
            expiryDate: new Date(now.getTime() + 120 * 24 * 60 * 60 * 1000),
            location: 'Rak ADJ-A1',
          },
          {
            batchNumber: 'ADJ-BATCH-002',
            quantityOnHand: 150,
            costPrice: 5200,
            receivedDate: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000),
            expiryDate: new Date(now.getTime() + 90 * 24 * 60 * 60 * 1000),
            location: 'Rak ADJ-A2',
          },
        ];

        for (const batch of batches) {
          const createdBatch = await ctx.prisma.inventoryItem.create({
            data: {
              productId: fifoTestProductId,
              unitId: fifoTestUnitId,
              supplierId: fifoTestSupplierId,
              ...batch,
              isActive: true,
              createdBy: ctx.users.admin.id,
            },
          });
          adjustmentTestBatches.push(createdBatch.id);
        }
      });

      it('should maintain FIFO/FEFO behavior after stock adjustments', async () => {
        // First, make a stock adjustment to one of the batches
        const adjustmentResponse = await ctx.request
          .patch(`/api/inventory/${adjustmentTestBatches[0]}/adjust-stock`)
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send({
            quantity: 50, // Add 50 units
            reason: 'Test adjustment before allocation',
          });

        expectSuccess(adjustmentResponse);

        // Now test FEFO allocation - should still prioritize by expiry date
        const allocationData = {
          productId: fifoTestProductId,
          requestedQuantity: 80,
          method: AllocationMethod.FEFO,
          allowPartialAllocation: true,
          reason: 'Test FEFO after adjustment',
        };

        const allocationResponse = await ctx.request
          .post('/api/inventory/allocate')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(allocationData);

        expectSuccess(allocationResponse, 201);
        expect(allocationResponse.body.success).toBe(true);
        expect(allocationResponse.body.allocatedQuantity).toBe(80);

        // Should still allocate from ADJ-BATCH-002 (earlier expiry) despite adjustment to ADJ-BATCH-001
        expect(allocationResponse.body.batches[0].batchNumber).toBe('ADJ-BATCH-002');
      });

      it('should create proper audit trail for allocation and adjustments', async () => {
        // Perform allocation
        const allocationData = {
          productId: fifoTestProductId,
          requestedQuantity: 75,
          method: AllocationMethod.FIFO,
          allowPartialAllocation: true,
          reason: 'Test audit trail allocation',
        };

        const allocationResponse = await ctx.request
          .post('/api/inventory/allocate')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(allocationData);

        expectSuccess(allocationResponse, 201);

        // Perform stock adjustment
        const adjustmentResponse = await ctx.request
          .patch(`/api/inventory/${adjustmentTestBatches[1]}/adjust-stock`)
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send({
            quantity: -25, // Reduce 25 units
            reason: 'Test audit trail adjustment',
          });

        expectSuccess(adjustmentResponse);

        // Verify stock movements were created for both operations
        const stockMovements = await ctx.prisma.stockMovement.findMany({
          where: {
            inventoryItemId: { in: adjustmentTestBatches }
          },
          orderBy: { createdAt: 'desc' },
          include: {
            inventoryItem: {
              select: { batchNumber: true }
            }
          }
        });

        // Should have movements for both allocation and adjustment
        expect(stockMovements.length).toBeGreaterThanOrEqual(2);

        // Check for allocation movement
        const allocationMovement = stockMovements.find(m => m.type === 'ALLOCATION');
        expect(allocationMovement).toBeDefined();
        expect(allocationMovement?.reason).toContain('Test audit trail allocation');

        // Check for adjustment movement
        const adjustmentMovement = stockMovements.find(m => m.type === 'OUT');
        expect(adjustmentMovement).toBeDefined();
        expect(adjustmentMovement?.reason).toContain('Test audit trail adjustment');
      });

      it('should handle concurrent allocation and adjustment operations', async () => {
        // This test ensures that FIFO/FEFO allocation works correctly even when
        // stock adjustments happen concurrently (simulated by rapid sequential operations)

        const operations = [
          // Allocation operation
          ctx.request
            .post('/api/inventory/allocate')
            .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
            .send({
              productId: fifoTestProductId,
              requestedQuantity: 50,
              method: AllocationMethod.FEFO,
              allowPartialAllocation: true,
              reason: 'Concurrent test allocation',
            }),

          // Stock adjustment operation
          ctx.request
            .patch(`/api/inventory/${adjustmentTestBatches[0]}/adjust-stock`)
            .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
            .send({
              quantity: 25,
              reason: 'Concurrent test adjustment',
            }),
        ];

        // Execute operations concurrently
        const results = await Promise.all(operations);

        // Both operations should succeed
        results.forEach(result => {
          expect(result.status).toBeGreaterThanOrEqual(200);
          expect(result.status).toBeLessThan(300);
        });

        // Verify final stock levels are consistent
        const finalBatches = await ctx.prisma.inventoryItem.findMany({
          where: { id: { in: adjustmentTestBatches } },
          orderBy: { batchNumber: 'asc' }
        });

        // Stock levels should be mathematically consistent
        expect(finalBatches[0].quantityOnHand).toBeGreaterThanOrEqual(0);
        expect(finalBatches[1].quantityOnHand).toBeGreaterThanOrEqual(0);

        // Total stock should account for allocation and adjustment
        const totalStock = finalBatches.reduce((sum, batch) => sum + batch.quantityOnHand, 0);
        expect(totalStock).toBeGreaterThan(0);
      });

      it('should integrate with stock consumption API endpoints', async () => {
        // Test the new stock consumption endpoints work with existing inventory
        const consumptionData = {
          productId: fifoTestProductId,
          requestedQuantity: 60,
          method: StockAllocationMethod.FEFO,
          allowPartialAllocation: true,
          reason: 'Test consumption API integration',
        };

        const consumptionResponse = await ctx.request
          .post('/api/inventory/consume-stock')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(consumptionData);

        expectSuccess(consumptionResponse, 201);
        expect(consumptionResponse.body.success).toBe(true);
        expect(consumptionResponse.body.allocatedQuantity).toBe(60);

        // Verify stock availability check works
        const availabilityData = {
          productId: fifoTestProductId,
          requestedQuantity: 100,
        };

        const availabilityResponse = await ctx.request
          .post('/api/inventory/validate-stock-availability')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(availabilityData);

        expectSuccess(availabilityResponse);
        expect(availabilityResponse.body.isAvailable).toBeDefined();
        expect(availabilityResponse.body.totalAvailable).toBeDefined();

        // Verify preview functionality works
        const previewData = {
          productId: fifoTestProductId,
          requestedQuantity: 40,
          method: StockAllocationMethod.FIFO,
        };

        const previewResponse = await ctx.request
          .post('/api/inventory/preview-stock-consumption')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(previewData);

        expectSuccess(previewResponse, 201);
        expect(previewResponse.body.previewOnly).toBe(true);
        expect(previewResponse.body.allocatedQuantity).toBeGreaterThan(0);
      });
    });
  });
});
