import { IntegrationTestSetup, TestContext, expectValidationError, expectUnauthorized, expectForbidden, expectNotFound, expectSuccess } from './test-setup';
import { StockMovementType } from '@prisma/client';
import * as fs from 'fs';
import * as path from 'path';

// Helper function for testing multiple possible status codes
const expectOneOf = (response: any, statusCodes: number[]) => {
  expect(statusCodes).toContain(response.status);
};

describe('Inventory Reports Integration Tests', () => {
  let testSetup: IntegrationTestSetup;
  let ctx: TestContext;
  let testInventoryItemId: string;
  let testProductId: string;
  let testUnitId: string;
  let testSupplierId: string;

  beforeAll(async () => {
    testSetup = new IntegrationTestSetup();
    ctx = await testSetup.setup();

    // Create test dependencies
    const testUnit = await testSetup.createTestProductUnit(ctx.users.admin.id);
    testUnitId = testUnit.id;

    const testProduct = await testSetup.createTestProduct(testUnitId, ctx.users.admin.id);
    testProductId = testProduct.id;

    const testSupplier = await testSetup.createTestSupplier(ctx.users.admin.id);
    testSupplierId = testSupplier.id;

    // Create test inventory items for reports
    const testInventoryItem = await testSetup.createTestInventoryItem(
      testProductId,
      testUnitId,
      testSupplierId,
      ctx.users.admin.id
    );
    testInventoryItemId = testInventoryItem.id;

    // Create additional test data for comprehensive reports
    await createAdditionalTestData();

    console.log('Test dependencies created:', {
      testUnitId,
      testProductId,
      testSupplierId,
      testInventoryItemId,
    });
  });

  afterAll(async () => {
    await testSetup.teardown();
  });

  async function createAdditionalTestData() {
    // Create multiple inventory items with different characteristics
    const inventoryItems = [
      {
        productId: testProductId,
        unitId: testUnitId,
        supplierId: testSupplierId,
        batchNumber: 'LOW_STOCK_001',
        quantityOnHand: 5, // Low stock
        costPrice: 3000,
        sellingPrice: 4500,
        location: 'Rak A-2',
        isActive: true,
        createdBy: ctx.users.admin.id,
      },
      {
        productId: testProductId,
        unitId: testUnitId,
        supplierId: testSupplierId,
        batchNumber: 'EXPIRING_001',
        quantityOnHand: 50,
        costPrice: 4000,
        sellingPrice: 6000,
        location: 'Rak B-1',
        expiryDate: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000), // Expires in 10 days
        isActive: true,
        createdBy: ctx.users.admin.id,
      },
      {
        productId: testProductId,
        unitId: testUnitId,
        supplierId: testSupplierId,
        batchNumber: 'EXPIRED_001',
        quantityOnHand: 25,
        costPrice: 2000,
        sellingPrice: 3000,
        location: 'Rak C-1',
        expiryDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // Expired 5 days ago
        isActive: true,
        createdBy: ctx.users.admin.id,
      },
    ];

    for (const item of inventoryItems) {
      await ctx.prisma.inventoryItem.create({ data: item });
    }

    // Create stock movements for allocation history and turnover analysis
    const stockMovements = [
      {
        inventoryItemId: testInventoryItemId,
        type: StockMovementType.ALLOCATION,
        quantity: -10,
        reason: 'Test allocation',
        createdBy: ctx.users.admin.id,
      },
      {
        inventoryItemId: testInventoryItemId,
        type: StockMovementType.OUT,
        quantity: -5,
        reason: 'Test consumption',
        createdBy: ctx.users.admin.id,
      },
      {
        inventoryItemId: testInventoryItemId,
        type: StockMovementType.ADJUSTMENT,
        quantity: 15,
        reason: 'Stock adjustment',
        createdBy: ctx.users.admin.id,
      },
    ];

    for (const movement of stockMovements) {
      await ctx.prisma.stockMovement.create({ data: movement });
    }
  }

  describe('POST /api/inventory/reports/generate', () => {
    describe('Authentication and Authorization', () => {
      it('should fail without authentication', async () => {
        const reportRequest = {
          type: 'stock_levels',
          format: 'pdf',
          language: 'id',
          filters: {},
          includeDetails: true,
          includeSummary: true,
        };

        const response = await ctx.request
          .post('/api/inventory/reports/generate')
          .send(reportRequest);

        expectUnauthorized(response);
      });

      it('should fail as cashier (insufficient permissions)', async () => {
        const reportRequest = {
          type: 'stock_levels',
          format: 'pdf',
          language: 'id',
          filters: {},
          includeDetails: true,
          includeSummary: true,
        };

        const response = await ctx.request
          .post('/api/inventory/reports/generate')
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send(reportRequest);

        expectForbidden(response);
      });

      it('should succeed as pharmacist', async () => {
        const reportRequest = {
          type: 'stock_levels',
          format: 'pdf',
          language: 'id',
          filters: {},
          includeDetails: true,
          includeSummary: true,
        };

        const response = await ctx.request
          .post('/api/inventory/reports/generate')
          .set('Authorization', `Bearer ${ctx.users.pharmacist.accessToken}`)
          .send(reportRequest);

        expectSuccess(response);
        expect(response.body).toHaveProperty('reportUrl');
        expect(response.body).toHaveProperty('reportId');
        expect(response.body).toHaveProperty('fileName');
        expect(response.body).toHaveProperty('fileSize');
        expect(response.body).toHaveProperty('expiresAt');
        expect(response.body.format).toBe('pdf');
        expect(response.body.type).toBe('stock_levels');
      });

      it('should succeed as admin', async () => {
        const reportRequest = {
          type: 'stock_levels',
          format: 'pdf',
          language: 'id',
          filters: {},
          includeDetails: true,
          includeSummary: true,
        };

        const response = await ctx.request
          .post('/api/inventory/reports/generate')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(reportRequest);

        expectSuccess(response);
        expect(response.body).toHaveProperty('reportUrl');
        expect(response.body).toHaveProperty('reportId');
        expect(response.body).toHaveProperty('fileName');
        expect(response.body).toHaveProperty('fileSize');
        expect(response.body).toHaveProperty('expiresAt');
        expect(response.body.format).toBe('pdf');
        expect(response.body.type).toBe('stock_levels');
      });
    });

    describe('Validation', () => {
      it('should fail with invalid report type', async () => {
        const reportRequest = {
          type: 'invalid_type',
          format: 'pdf',
          language: 'id',
          filters: {},
          includeDetails: true,
          includeSummary: true,
        };

        const response = await ctx.request
          .post('/api/inventory/reports/generate')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(reportRequest);

        expectValidationError(response, 'type');
      });

      it('should fail with invalid format', async () => {
        const reportRequest = {
          type: 'stock_levels',
          format: 'invalid_format',
          language: 'id',
          filters: {},
          includeDetails: true,
          includeSummary: true,
        };

        const response = await ctx.request
          .post('/api/inventory/reports/generate')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(reportRequest);

        expectValidationError(response, 'format');
      });

      it('should fail with invalid language', async () => {
        const reportRequest = {
          type: 'stock_levels',
          format: 'pdf',
          language: 'invalid_language',
          filters: {},
          includeDetails: true,
          includeSummary: true,
        };

        const response = await ctx.request
          .post('/api/inventory/reports/generate')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(reportRequest);

        expectValidationError(response, 'language');
      });

      it('should fail with invalid date range (start date after end date)', async () => {
        const reportRequest = {
          type: 'stock_movements',
          format: 'pdf',
          language: 'id',
          filters: {
            startDate: '2024-12-31',
            endDate: '2024-01-01',
          },
          includeDetails: true,
          includeSummary: true,
        };

        const response = await ctx.request
          .post('/api/inventory/reports/generate')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(reportRequest);

        expect(response.status).toBe(400);
        expect(response.body.message).toContain('Tanggal mulai tidak boleh lebih besar dari tanggal akhir');
      });

      it('should fail with future start date', async () => {
        const futureDate = new Date();
        futureDate.setFullYear(futureDate.getFullYear() + 1);

        const reportRequest = {
          type: 'stock_movements',
          format: 'pdf',
          language: 'id',
          filters: {
            startDate: futureDate.toISOString().split('T')[0],
          },
          includeDetails: true,
          includeSummary: true,
        };

        const response = await ctx.request
          .post('/api/inventory/reports/generate')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(reportRequest);

        expect(response.status).toBe(400);
        expect(response.body.message).toContain('Tanggal mulai tidak boleh di masa depan');
      });

      it('should fail with invalid quantity range', async () => {
        const reportRequest = {
          type: 'stock_levels',
          format: 'pdf',
          language: 'id',
          filters: {
            minQuantity: 100,
            maxQuantity: 50,
          },
          includeDetails: true,
          includeSummary: true,
        };

        const response = await ctx.request
          .post('/api/inventory/reports/generate')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(reportRequest);

        expect(response.status).toBe(400);
        expect(response.body.message).toContain('Jumlah minimum tidak boleh lebih besar dari jumlah maksimum');
      });

      it('should fail with invalid expiry days range', async () => {
        const reportRequest = {
          type: 'expiry_report',
          format: 'pdf',
          language: 'id',
          filters: {
            expiringSoonDays: 500, // Invalid: > 365
          },
          includeDetails: true,
          includeSummary: true,
        };

        const response = await ctx.request
          .post('/api/inventory/reports/generate')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(reportRequest);

        expect(response.status).toBe(400);
        // The validation message might be in an array or have a prefix
        const message = Array.isArray(response.body.message) ? response.body.message.join(' ') : response.body.message;
        expect(message).toContain('Hari kedaluwarsa harus antara 1-365 hari');
      });
    });
  });

  describe('Report Types', () => {
    const reportTypes = [
      'stock_levels',
      'stock_movements',
      'low_stock',
      'expiry_report',
      'allocation_history',
      'supplier_performance',
      'category_analysis',
      'stock_aging',
      'turnover_analysis',
    ];

    reportTypes.forEach((reportType) => {
      describe(`${reportType} reports`, () => {
        it('should generate PDF report', async () => {
          const reportRequest = {
            type: reportType,
            format: 'pdf',
            language: 'id',
            filters: {},
            includeDetails: true,
            includeSummary: true,
          };

          const response = await ctx.request
            .post('/api/inventory/reports/generate')
            .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
            .send(reportRequest);

          expectSuccess(response);
          expect(response.body.type).toBe(reportType);
          expect(response.body.format).toBe('pdf');
          expect(response.body.fileName).toContain('.pdf');
          expect(response.body.fileSize).toBeGreaterThan(0);
        });

        it('should generate Excel report', async () => {
          const reportRequest = {
            type: reportType,
            format: 'excel',
            language: 'id',
            filters: {},
            includeDetails: true,
            includeSummary: true,
          };

          const response = await ctx.request
            .post('/api/inventory/reports/generate')
            .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
            .send(reportRequest);

          expectSuccess(response);
          expect(response.body.type).toBe(reportType);
          expect(response.body.format).toBe('excel');
          expect(response.body.fileName).toContain('.xlsx');
          expect(response.body.fileSize).toBeGreaterThan(0);
        });

        it('should generate CSV report', async () => {
          const reportRequest = {
            type: reportType,
            format: 'csv',
            language: 'id',
            filters: {},
            includeDetails: true,
            includeSummary: true,
          };

          const response = await ctx.request
            .post('/api/inventory/reports/generate')
            .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
            .send(reportRequest);

          expectSuccess(response);
          expect(response.body.type).toBe(reportType);
          expect(response.body.format).toBe('csv');
          expect(response.body.fileName).toContain('.csv');
          expect(response.body.fileSize).toBeGreaterThan(0);
        });
      });
    });
  });

  describe('Report Filtering', () => {
    it('should filter stock levels by product ID', async () => {
      const reportRequest = {
        type: 'stock_levels',
        format: 'pdf',
        language: 'id',
        filters: {
          productId: testProductId,
        },
        includeDetails: true,
        includeSummary: true,
      };

      const response = await ctx.request
        .post('/api/inventory/reports/generate')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(reportRequest);

      expectSuccess(response);
      expect(response.body.fileName).toContain('Laporan_Tingkat_Stok');
    });

    it('should filter stock levels by supplier ID', async () => {
      const reportRequest = {
        type: 'stock_levels',
        format: 'pdf',
        language: 'id',
        filters: {
          supplierId: testSupplierId,
        },
        includeDetails: true,
        includeSummary: true,
      };

      const response = await ctx.request
        .post('/api/inventory/reports/generate')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(reportRequest);

      expectSuccess(response);
    });

    it('should filter stock levels by location', async () => {
      const reportRequest = {
        type: 'stock_levels',
        format: 'pdf',
        language: 'id',
        filters: {
          location: 'Rak A',
        },
        includeDetails: true,
        includeSummary: true,
      };

      const response = await ctx.request
        .post('/api/inventory/reports/generate')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(reportRequest);

      expectSuccess(response);
    });

    it('should filter stock levels by active status', async () => {
      const reportRequest = {
        type: 'stock_levels',
        format: 'pdf',
        language: 'id',
        filters: {
          isActive: true,
        },
        includeDetails: true,
        includeSummary: true,
      };

      const response = await ctx.request
        .post('/api/inventory/reports/generate')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(reportRequest);

      expectSuccess(response);
    });

    it('should filter stock levels by quantity range', async () => {
      const reportRequest = {
        type: 'stock_levels',
        format: 'pdf',
        language: 'id',
        filters: {
          minQuantity: 10,
          maxQuantity: 200,
        },
        includeDetails: true,
        includeSummary: true,
      };

      const response = await ctx.request
        .post('/api/inventory/reports/generate')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(reportRequest);

      expectSuccess(response);
    });

    it('should filter stock movements by date range', async () => {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - 30);
      const endDate = new Date();

      const reportRequest = {
        type: 'stock_movements',
        format: 'pdf',
        language: 'id',
        filters: {
          startDate: startDate.toISOString().split('T')[0],
          endDate: endDate.toISOString().split('T')[0],
        },
        includeDetails: true,
        includeSummary: true,
      };

      const response = await ctx.request
        .post('/api/inventory/reports/generate')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(reportRequest);

      expectSuccess(response);
      expect(response.body.fileName).toContain('Laporan_Pergerakan_Stok');
    });

    it('should filter expiry report by expiring soon days', async () => {
      const reportRequest = {
        type: 'expiry_report',
        format: 'pdf',
        language: 'id',
        filters: {
          expiringSoonDays: 15,
        },
        includeDetails: true,
        includeSummary: true,
      };

      const response = await ctx.request
        .post('/api/inventory/reports/generate')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(reportRequest);

      expectSuccess(response);
      expect(response.body.fileName).toContain('Laporan_Kedaluwarsa');
    });
  });

  describe('Report Options', () => {
    it('should generate report without details', async () => {
      const reportRequest = {
        type: 'stock_levels',
        format: 'pdf',
        language: 'id',
        filters: {},
        includeDetails: false,
        includeSummary: true,
      };

      const response = await ctx.request
        .post('/api/inventory/reports/generate')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(reportRequest);

      expectSuccess(response);
    });

    it('should generate report without summary', async () => {
      const reportRequest = {
        type: 'stock_levels',
        format: 'pdf',
        language: 'id',
        filters: {},
        includeDetails: true,
        includeSummary: false,
      };

      const response = await ctx.request
        .post('/api/inventory/reports/generate')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(reportRequest);

      expectSuccess(response);
    });

    it('should generate report with charts', async () => {
      const reportRequest = {
        type: 'stock_levels',
        format: 'pdf',
        language: 'id',
        filters: {},
        includeDetails: true,
        includeSummary: true,
        includeCharts: true,
      };

      const response = await ctx.request
        .post('/api/inventory/reports/generate')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(reportRequest);

      expectSuccess(response);
    });

    it('should generate report in English', async () => {
      const reportRequest = {
        type: 'stock_levels',
        format: 'pdf',
        language: 'en',
        filters: {},
        includeDetails: true,
        includeSummary: true,
      };

      const response = await ctx.request
        .post('/api/inventory/reports/generate')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(reportRequest);

      expectSuccess(response);
      expect(response.body.fileName).toContain('Report_Stock_Levels');
    });
  });

  describe('File Download', () => {
    let reportFileName: string;

    beforeAll(async () => {
      // Generate a report to test download
      const reportRequest = {
        type: 'stock_levels',
        format: 'pdf',
        language: 'id',
        filters: {},
        includeDetails: true,
        includeSummary: true,
      };

      const response = await ctx.request
        .post('/api/inventory/reports/generate')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(reportRequest);

      expectSuccess(response);
      reportFileName = response.body.fileName;
    });

    it('should download generated report file', async () => {
      const response = await ctx.request
        .get(`/api/reports/download/${reportFileName}`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expect(response.status).toBe(200);
      expect(response.headers['content-type']).toContain('application/pdf');
      expect(response.headers['content-disposition']).toContain(`attachment; filename="${reportFileName}"`);
      expect(response.body).toBeDefined();
    });

    it('should fail to download non-existent file', async () => {
      const response = await ctx.request
        .get('/api/reports/download/non-existent-file.pdf')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectNotFound(response);
    });

    it('should fail to download without authentication', async () => {
      const response = await ctx.request
        .get(`/api/reports/download/${reportFileName}`);

      expectUnauthorized(response);
    });
  });

  describe('Import Functionality', () => {
    describe('POST /api/inventory/import', () => {
      it('should fail without authentication', async () => {
        const response = await ctx.request
          .post('/api/inventory/import')
          .attach('file', Buffer.from('test,data'), 'test.csv');

        expectUnauthorized(response);
      });

      it('should fail as cashier (insufficient permissions)', async () => {
        const response = await ctx.request
          .post('/api/inventory/import')
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .attach('file', Buffer.from('test,data'), 'test.csv');

        expectForbidden(response);
      });

      it('should fail without file', async () => {
        const response = await ctx.request
          .post('/api/inventory/import')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send({ validateOnly: false });

        expect(response.status).toBe(400);
        expect(response.body.message).toContain('File tidak ditemukan');
      });

      it('should fail with invalid file type', async () => {
        const response = await ctx.request
          .post('/api/inventory/import')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .attach('file', Buffer.from('invalid content'), 'test.txt');

        expect(response.status).toBe(400);
        expect(response.body.message).toContain('Format file tidak didukung');
      });

      it('should fail with oversized file', async () => {
        // Create a buffer larger than 10MB
        const largeBuffer = Buffer.alloc(11 * 1024 * 1024, 'a');

        const response = await ctx.request
          .post('/api/inventory/import')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .attach('file', largeBuffer, 'large.csv');

        expect(response.status).toBe(400);
        expect(response.body.message).toContain('Ukuran file terlalu besar');
      });

      it('should validate CSV file structure', async () => {
        const csvContent = 'productCode,batchNumber,quantityOnHand,costPrice,supplierCode\nTEST001,BATCH001,100,5000,SUP001';

        const response = await ctx.request
          .post('/api/inventory/import')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .field('validateOnly', 'true')
          .attach('file', Buffer.from(csvContent), 'test.csv');

        // This might fail due to missing products/suppliers, but should validate structure
        expectOneOf(response, [200, 201, 400]);
        expect(response.body).toHaveProperty('success');
        expect(response.body).toHaveProperty('message');
      });

      it('should validate Excel file structure', async () => {
        // Create a minimal Excel file buffer (this is a simplified test)
        const excelContent = 'productCode,batchNumber,quantityOnHand,costPrice,supplierCode\nTEST001,BATCH001,100,5000,SUP001';

        const response = await ctx.request
          .post('/api/inventory/import')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .field('validateOnly', 'true')
          .attach('file', Buffer.from(excelContent), 'test.xlsx');

        // This might fail due to Excel parsing, but should handle gracefully
        expectOneOf(response, [200, 201, 400]);
        expect(response.body).toHaveProperty('success');
        expect(response.body).toHaveProperty('message');
      });
    });

    describe('GET /api/inventory/import-template', () => {
      it('should fail without authentication', async () => {
        const response = await ctx.request
          .get('/api/inventory/import-template');

        expectUnauthorized(response);
      });

      it('should fail as cashier (insufficient permissions)', async () => {
        const response = await ctx.request
          .get('/api/inventory/import-template')
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

        expectForbidden(response);
      });

      it('should download CSV template as admin', async () => {
        const response = await ctx.request
          .get('/api/inventory/import-template?format=csv')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

        expect(response.status).toBe(200);
        expect(response.headers['content-type']).toContain('text/csv');
        expect(response.headers['content-disposition']).toContain('attachment');
        expect(response.headers['content-disposition']).toContain('Template_Import_Inventori');
        expect(response.text).toContain('productCode,batchNumber,quantityOnHand');
      });

      it('should download Excel template as pharmacist', async () => {
        const response = await ctx.request
          .get('/api/inventory/import-template?format=xlsx')
          .set('Authorization', `Bearer ${ctx.users.pharmacist.accessToken}`);

        expect(response.status).toBe(200);
        expect(response.headers['content-type']).toContain('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        expect(response.headers['content-disposition']).toContain('attachment');
        expect(response.headers['content-disposition']).toContain('Template_Import_Inventori');
      });

      it('should default to Excel format when no format specified', async () => {
        const response = await ctx.request
          .get('/api/inventory/import-template')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

        expect(response.status).toBe(200);
        expect(response.headers['content-type']).toContain('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      });

      it('should include dynamic supplier and product data in CSV template', async () => {
        const response = await ctx.request
          .get('/api/inventory/import-template?format=csv')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

        expect(response.status).toBe(200);
        expect(response.headers['content-type']).toBe('text/csv');

        const csvContent = response.text;

        // Should include usage instructions
        expect(csvContent).toContain('PETUNJUK PENGGUNAAN');
        expect(csvContent).toContain('Anda dapat menggunakan KODE atau NAMA');

        // Should include supplier reference if suppliers exist
        expect(csvContent).toContain('DAFTAR SUPPLIER TERSEDIA');

        // Should include product reference if products exist
        expect(csvContent).toContain('DAFTAR PRODUK TERSEDIA');

        // Should include examples with both codes and names
        expect(csvContent).toContain('productCode,batchNumber,quantityOnHand,costPrice');
      });

      it('should include reference sheets in Excel template', async () => {
        const response = await ctx.request
          .get('/api/inventory/import-template?format=xlsx')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

        expect(response.status).toBe(200);
        expect(response.headers['content-type']).toBe('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');

        // The Excel file should be larger than a basic template due to reference sheets
        // For binary responses, check the content-length header or response body size
        const contentLength = response.headers['content-length'];
        if (contentLength) {
          expect(parseInt(contentLength)).toBeGreaterThan(5000);
        } else {
          // Fallback: just check that we got a response
          expect(response.body).toBeDefined();
        }
      });
    });

    describe('Enhanced Import Functionality', () => {
      it('should import inventory using supplier codes', async () => {
        // Get the actual product and supplier codes from the database
        const product = await ctx.prisma.product.findUnique({
          where: { id: testProductId },
          select: { code: true }
        });

        const supplier = await ctx.prisma.supplier.findUnique({
          where: { id: testSupplierId },
          select: { code: true }
        });

        if (!product || !supplier) {
          throw new Error('Test data not found');
        }

        const csvContent = [
          'productCode,batchNumber,quantityOnHand,costPrice,supplierCode,location',
          `${product.code},BATCH-CODE-001,100,5000,${supplier.code},Gudang Test`
        ].join('\n');

        const response = await ctx.request
          .post('/api/inventory/import')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .attach('file', Buffer.from(csvContent), 'test-codes.csv')
          .field('validateOnly', 'false');

        expect(response.status).toBe(201); // Import endpoint returns 201 Created
        expect(response.body.success).toBe(true);
        expect(response.body.importedCount).toBe(1);
        expect(response.body.skippedCount).toBe(0);
      });

      it('should import inventory using supplier names', async () => {
        // First get the supplier name
        const supplier = await ctx.prisma.supplier.findUnique({
          where: { id: testSupplierId },
          select: { name: true }
        });

        const product = await ctx.prisma.product.findUnique({
          where: { id: testProductId },
          select: { name: true }
        });

        if (!supplier || !product) {
          throw new Error('Test data not found');
        }

        const csvContent = [
          'productCode,batchNumber,quantityOnHand,costPrice,supplierCode,location',
          `"${product.name}",BATCH-NAME-001,50,7500,"${supplier.name}",Gudang Test`
        ].join('\n');

        const response = await ctx.request
          .post('/api/inventory/import')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .attach('file', Buffer.from(csvContent), 'test-names.csv')
          .field('validateOnly', 'false');

        expect(response.status).toBe(201); // Import endpoint returns 201 Created
        expect(response.body.success).toBe(true);
        expect(response.body.importedCount).toBe(1);
        expect(response.body.skippedCount).toBe(0);
      });

      it('should import inventory using mixed codes and names', async () => {
        const supplier = await ctx.prisma.supplier.findUnique({
          where: { id: testSupplierId },
          select: { name: true, code: true }
        });

        const product = await ctx.prisma.product.findUnique({
          where: { id: testProductId },
          select: { name: true, code: true }
        });

        if (!supplier || !product) {
          throw new Error('Test data not found');
        }

        const csvContent = [
          'productCode,batchNumber,quantityOnHand,costPrice,supplierCode,location',
          `${product.code},BATCH-MIXED-001,75,6000,"${supplier.name}",Gudang Test`,
          `"${product.name}",BATCH-MIXED-002,25,6500,${supplier.code},Gudang Test`
        ].join('\n');

        const response = await ctx.request
          .post('/api/inventory/import')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .attach('file', Buffer.from(csvContent), 'test-mixed.csv')
          .field('validateOnly', 'false');

        expect(response.status).toBe(201); // Import endpoint returns 201 Created
        expect(response.body.success).toBe(true);
        expect(response.body.importedCount).toBe(2);
        expect(response.body.skippedCount).toBe(0);
      });

      it('should provide helpful suggestions for invalid supplier names', async () => {
        // Get the actual product code
        const product = await ctx.prisma.product.findUnique({
          where: { id: testProductId },
          select: { code: true }
        });

        if (!product) {
          throw new Error('Test product not found');
        }

        const csvContent = [
          'productCode,batchNumber,quantityOnHand,costPrice,supplierCode,location',
          `${product.code},BATCH-INVALID-001,100,5000,"Invalid Supplier Name",Gudang Test`
        ].join('\n');

        const response = await ctx.request
          .post('/api/inventory/import')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .attach('file', Buffer.from(csvContent), 'test-invalid-supplier.csv')
          .field('validateOnly', 'false');

        expect(response.status).toBe(201); // Import endpoint returns 201 even for failures
        expect(response.body.success).toBe(false);
        expect(response.body.importedCount).toBe(0);
        expect(response.body.skippedCount).toBe(1);
        expect(response.body.errors).toBeDefined();
        expect(response.body.errors.length).toBeGreaterThan(0);
        expect(response.body.errors[0]).toContain('tidak ditemukan');
      });

      it('should provide helpful suggestions for invalid product names', async () => {
        // Get the actual supplier code
        const supplier = await ctx.prisma.supplier.findUnique({
          where: { id: testSupplierId },
          select: { code: true }
        });

        if (!supplier) {
          throw new Error('Test supplier not found');
        }

        const csvContent = [
          'productCode,batchNumber,quantityOnHand,costPrice,supplierCode,location',
          `"Invalid Product Name",BATCH-INVALID-002,100,5000,${supplier.code},Gudang Test`
        ].join('\n');

        const response = await ctx.request
          .post('/api/inventory/import')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .attach('file', Buffer.from(csvContent), 'test-invalid-product.csv')
          .field('validateOnly', 'false');

        expect(response.status).toBe(201); // Import endpoint returns 201 even for failures
        expect(response.body.success).toBe(false);
        expect(response.body.importedCount).toBe(0);
        expect(response.body.skippedCount).toBe(1);
        expect(response.body.errors).toBeDefined();
        expect(response.body.errors.length).toBeGreaterThan(0);
        expect(response.body.errors[0]).toContain('tidak ditemukan');
      });

      it('should handle partial success with mixed valid and invalid entries', async () => {
        const supplier = await ctx.prisma.supplier.findUnique({
          where: { id: testSupplierId },
          select: { name: true }
        });

        const product = await ctx.prisma.product.findUnique({
          where: { id: testProductId },
          select: { code: true }
        });

        if (!supplier || !product) {
          throw new Error('Test data not found');
        }

        const csvContent = [
          'productCode,batchNumber,quantityOnHand,costPrice,supplierCode,location',
          `${product.code},BATCH-VALID-001,100,5000,"${supplier.name}",Gudang Test`,
          `"Invalid Product",BATCH-INVALID-001,50,7500,"${supplier.name}",Gudang Test`,
          `${product.code},BATCH-VALID-002,75,6000,"Invalid Supplier",Gudang Test`,
          `${product.code},BATCH-VALID-003,25,5500,"${supplier.name}",Gudang Test`
        ].join('\n');

        const response = await ctx.request
          .post('/api/inventory/import')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .attach('file', Buffer.from(csvContent), 'test-partial.csv')
          .field('validateOnly', 'false');

        expect(response.status).toBe(201); // Import endpoint returns 201
        expect(response.body.success).toBe(true); // Partial success
        expect(response.body.importedCount).toBe(2); // 2 valid entries
        expect(response.body.skippedCount).toBe(2); // 2 invalid entries
        expect(response.body.errors).toBeDefined();
        expect(response.body.errors.length).toBe(2); // 2 error messages
        expect(response.body.message).toContain('sebagian berhasil');
      });

      it('should handle case-insensitive matching', async () => {
        const supplier = await ctx.prisma.supplier.findUnique({
          where: { id: testSupplierId },
          select: { name: true }
        });

        const product = await ctx.prisma.product.findUnique({
          where: { id: testProductId },
          select: { name: true }
        });

        if (!supplier || !product) {
          throw new Error('Test data not found');
        }

        const csvContent = [
          'productCode,batchNumber,quantityOnHand,costPrice,supplierCode,location',
          `"${product.name.toUpperCase()}",BATCH-CASE-001,100,5000,"${supplier.name.toLowerCase()}",Gudang Test`
        ].join('\n');

        const response = await ctx.request
          .post('/api/inventory/import')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .attach('file', Buffer.from(csvContent), 'test-case.csv')
          .field('validateOnly', 'false');

        expect(response.status).toBe(201); // Import endpoint returns 201 Created
        expect(response.body.success).toBe(true);
        expect(response.body.importedCount).toBe(1);
        expect(response.body.skippedCount).toBe(0);
      });

      it('should limit error messages to prevent overwhelming users', async () => {
        // Create CSV with many invalid entries
        const csvLines = ['productCode,batchNumber,quantityOnHand,costPrice,supplierCode,location'];

        // Add 15 invalid entries
        for (let i = 1; i <= 15; i++) {
          csvLines.push(`"Invalid Product ${i}",BATCH-${i},100,5000,"Invalid Supplier ${i}",Gudang Test`);
        }

        const csvContent = csvLines.join('\n');

        const response = await ctx.request
          .post('/api/inventory/import')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .attach('file', Buffer.from(csvContent), 'test-many-errors.csv')
          .field('validateOnly', 'false');

        expect(response.status).toBe(201); // Import endpoint returns 201
        expect(response.body.success).toBe(false);
        expect(response.body.importedCount).toBe(0);
        expect(response.body.skippedCount).toBe(15);
        expect(response.body.errors).toBeDefined();
        expect(response.body.errors.length).toBeLessThanOrEqual(10); // Should limit to 10 errors
        expect(response.body.message).toContain('dari 15 error'); // Should mention total count (15 invalid entries)
      });
    });
  });

  describe('Progress Tracking', () => {
    describe('GET /api/inventory/reports/:reportId/progress', () => {
      it('should fail without authentication', async () => {
        const response = await ctx.request
          .get('/api/inventory/reports/test-report-id/progress');

        expectUnauthorized(response);
      });

      it('should fail as cashier (insufficient permissions)', async () => {
        const response = await ctx.request
          .get('/api/inventory/reports/test-report-id/progress')
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

        expectForbidden(response);
      });

      it('should return progress for valid report ID', async () => {
        const response = await ctx.request
          .get('/api/inventory/reports/test-report-id/progress')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

        expectSuccess(response);
        expect(response.body).toHaveProperty('reportId');
        expect(response.body).toHaveProperty('status');
        expect(response.body).toHaveProperty('progress');
        expect(response.body).toHaveProperty('message');
        expect(response.body.reportId).toBe('test-report-id');
      });
    });
  });

  describe('Error Scenarios', () => {
    it('should handle database connection errors gracefully', async () => {
      // This test would require mocking database failures
      // For now, we'll test with invalid filters that might cause database errors
      const reportRequest = {
        type: 'stock_levels',
        format: 'pdf',
        language: 'id',
        filters: {
          productId: 'non-existent-product-id',
        },
        includeDetails: true,
        includeSummary: true,
      };

      const response = await ctx.request
        .post('/api/inventory/reports/generate')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(reportRequest);

      // Should still succeed but with empty data
      expectSuccess(response);
    });

    it('should handle empty data sets gracefully', async () => {
      const reportRequest = {
        type: 'stock_levels',
        format: 'pdf',
        language: 'id',
        filters: {
          minQuantity: 999999, // Filter that will return no results
        },
        includeDetails: true,
        includeSummary: true,
      };

      const response = await ctx.request
        .post('/api/inventory/reports/generate')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(reportRequest);

      expectSuccess(response);
      expect(response.body.fileSize).toBeGreaterThan(0); // Should still generate a report with headers
    });

    it('should handle concurrent report generation requests', async () => {
      const reportRequest = {
        type: 'stock_levels',
        format: 'pdf',
        language: 'id',
        filters: {},
        includeDetails: true,
        includeSummary: true,
      };

      // Send multiple concurrent requests
      const promises = Array(3).fill(null).map(() =>
        ctx.request
          .post('/api/inventory/reports/generate')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(reportRequest)
      );

      const responses = await Promise.all(promises);

      // All should succeed
      responses.forEach(response => {
        expectSuccess(response);
        expect(response.body).toHaveProperty('reportId');
        expect(response.body).toHaveProperty('fileName');
      });

      // All should have unique report IDs and file names
      const reportIds = responses.map(r => r.body.reportId);
      const fileNames = responses.map(r => r.body.fileName);

      expect(new Set(reportIds).size).toBe(reportIds.length);
      expect(new Set(fileNames).size).toBe(fileNames.length);
    });
  });
});
