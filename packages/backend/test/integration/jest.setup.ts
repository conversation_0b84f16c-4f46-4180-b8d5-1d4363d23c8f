import { config } from 'dotenv';
import { join } from 'path';

// CRITICAL: Load test environment variables FIRST and ensure they take precedence
// This must happen before any NestJS modules are loaded
config({ path: join(__dirname, '../../.env.test'), override: true });

// Set test environment
process.env.NODE_ENV = 'test';

// SAFETY CHECK: Ensure we're using the test database
if (!process.env.DATABASE_URL?.includes('_test')) {
  throw new Error(
    `CRITICAL: Test environment is not using a test database!\n` +
    `Current DATABASE_URL: ${process.env.DATABASE_URL}\n` +
    `Expected: A database URL containing '_test'\n` +
    `This prevents accidental data loss in development/production databases.`
  );
}

// Ensure we have required environment variables for testing
if (!process.env.DATABASE_URL) {
  process.env.DATABASE_URL = 'postgresql://postgres:password@localhost:5432/apotek_test';
}

if (!process.env.JWT_SECRET) {
  process.env.JWT_SECRET = 'test-jwt-secret-key-for-integration-tests';
}

if (!process.env.JWT_EXPIRES_IN) {
  process.env.JWT_EXPIRES_IN = '1h';
}

// Increase timeout for database operations
jest.setTimeout(30000);

// Global test setup
beforeAll(async () => {
  console.log('🧪 Starting Integration Test Suite');
  console.log(`📊 Database: ${process.env.DATABASE_URL}`);
  console.log(`🔑 JWT Secret: ${process.env.JWT_SECRET?.substring(0, 10)}...`);
});

afterAll(async () => {
  console.log('✅ Integration Test Suite Completed');
});

// Global error handling
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});
