import { IntegrationTestSetup, TestContext, expectSuccess, expectValidationError, expectUnauthorized } from './test-setup';

describe('Supplier Code Generator Integration Tests', () => {
  let testSetup: IntegrationTestSetup;
  let ctx: TestContext;

  beforeAll(async () => {
    testSetup = new IntegrationTestSetup();
    ctx = await testSetup.setup();
  });

  afterAll(async () => {
    await testSetup.teardown();
  });

  describe('GET /api/suppliers/generate-code/:type', () => {
    it('should generate PBF supplier code', async () => {
      const response = await ctx.request
        .get('/api/suppliers/generate-code/PBF')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.code).toMatch(/^PBF-\d{3}$/);
      expect(response.body.code).toBe('PBF-001'); // First PBF code
    });

    it('should generate DISTRIBUTOR supplier code', async () => {
      const response = await ctx.request
        .get('/api/suppliers/generate-code/DISTRIBUTOR')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.code).toMatch(/^DIST-\d{3}$/);
      expect(response.body.code).toBe('DIST-001'); // First DISTRIBUTOR code
    });

    it('should generate MANUFACTURER supplier code', async () => {
      const response = await ctx.request
        .get('/api/suppliers/generate-code/MANUFACTURER')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.code).toMatch(/^MFG-\d{3}$/);
      expect(response.body.code).toBe('MFG-001'); // First MANUFACTURER code
    });

    it('should generate LOCAL supplier code', async () => {
      const response = await ctx.request
        .get('/api/suppliers/generate-code/LOCAL')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.code).toMatch(/^SUP-\d{3}$/);
      expect(response.body.code).toBe('SUP-001'); // First LOCAL code
    });

    it('should increment sequence numbers correctly', async () => {
      // Create a supplier with PBF-001
      await ctx.request
        .post('/api/suppliers')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({
          code: 'PBF-001',
          name: 'Test PBF Supplier',
          type: 'PBF',
        });

      // Generate next PBF code
      const response = await ctx.request
        .get('/api/suppliers/generate-code/PBF')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.code).toBe('PBF-002'); // Should increment to 002
    });

    it('should handle gaps in sequence numbers', async () => {
      // Create suppliers with non-sequential codes
      await ctx.request
        .post('/api/suppliers')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({
          code: 'DIST-005',
          name: 'Test Distributor 5',
          type: 'DISTRIBUTOR',
        });

      await ctx.request
        .post('/api/suppliers')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({
          code: 'DIST-003',
          name: 'Test Distributor 3',
          type: 'DISTRIBUTOR',
        });

      // Generate next DISTRIBUTOR code - should use highest + 1
      const response = await ctx.request
        .get('/api/suppliers/generate-code/DISTRIBUTOR')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.code).toBe('DIST-006'); // Should be highest (5) + 1
    });

    it('should fail with invalid supplier type', async () => {
      const response = await ctx.request
        .get('/api/suppliers/generate-code/INVALID_TYPE')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectValidationError(response);
      expect(response.body.message).toContain('Jenis supplier tidak valid');
    });

    it('should fail without authentication', async () => {
      const response = await ctx.request
        .get('/api/suppliers/generate-code/PBF');

      expectUnauthorized(response);
    });
  });

  describe('GET /api/suppliers/validate-code/:code', () => {
    beforeAll(async () => {
      // Create a supplier for validation tests
      await ctx.request
        .post('/api/suppliers')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({
          code: 'TEST-001',
          name: 'Test Validation Supplier',
          type: 'LOCAL',
        });
    });

    it('should validate unique supplier code', async () => {
      const response = await ctx.request
        .get('/api/suppliers/validate-code/UNIQUE-001')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.isUnique).toBe(true);
    });

    it('should detect duplicate supplier code', async () => {
      const response = await ctx.request
        .get('/api/suppliers/validate-code/TEST-001')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.isUnique).toBe(false);
    });

    it('should handle URL encoded codes', async () => {
      const response = await ctx.request
        .get('/api/suppliers/validate-code/' + encodeURIComponent('TEST-002'))
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.isUnique).toBe(true);
    });

    it('should fail without authentication', async () => {
      const response = await ctx.request
        .get('/api/suppliers/validate-code/TEST-999');

      expectUnauthorized(response);
    });
  });

  describe('Integration with supplier creation', () => {
    it('should create supplier with auto-generated code', async () => {
      // Generate a code first
      const codeResponse = await ctx.request
        .get('/api/suppliers/generate-code/MANUFACTURER')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      const generatedCode = codeResponse.body.code;

      // Create supplier with the generated code
      const supplierResponse = await ctx.request
        .post('/api/suppliers')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({
          code: generatedCode,
          name: 'Auto-Generated Code Supplier',
          type: 'MANUFACTURER',
        });

      expectSuccess(supplierResponse, 201);
      expect(supplierResponse.body.code).toBe(generatedCode);
      expect(supplierResponse.body.type).toBe('MANUFACTURER');
    });

    it('should prevent duplicate codes even with concurrent requests', async () => {
      // This test simulates the scenario where two users try to create suppliers
      // with the same generated code simultaneously
      
      const codeResponse = await ctx.request
        .get('/api/suppliers/generate-code/LOCAL')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      const generatedCode = codeResponse.body.code;

      // Create first supplier
      const firstResponse = await ctx.request
        .post('/api/suppliers')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({
          code: generatedCode,
          name: 'First Supplier',
          type: 'LOCAL',
        });

      expectSuccess(firstResponse, 201);

      // Try to create second supplier with same code - should fail
      const secondResponse = await ctx.request
        .post('/api/suppliers')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({
          code: generatedCode,
          name: 'Second Supplier',
          type: 'LOCAL',
        });

      expect(secondResponse.status).toBe(409);
      expect(secondResponse.body.message).toContain('Kode supplier sudah digunakan');
    });
  });
});
