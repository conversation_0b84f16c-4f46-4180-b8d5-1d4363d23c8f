import { IntegrationTestSetup, TestContext, expectValidationError, expectUnauthorized, expectSuccess } from './test-setup';

describe('Authentication Integration Tests', () => {
  let testSetup: IntegrationTestSetup;
  let ctx: TestContext;

  beforeAll(async () => {
    testSetup = new IntegrationTestSetup();
    ctx = await testSetup.setup();
  });

  afterAll(async () => {
    await testSetup.teardown();
  });

  describe('POST /api/auth/register', () => {
    it('should register a new user with valid data', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'New',
        lastName: 'User',
        role: 'CASHIER',
      };

      const response = await ctx.request
        .post('/api/auth/register')
        .send(userData);

      expectSuccess(response, 201);
      expect(response.body.user).toBeDefined();
      expect(response.body.accessToken).toBeDefined();
      expect(response.body.user.email).toBe(userData.email);
      expect(response.body.user.firstName).toBe(userData.firstName);
      expect(response.body.user.lastName).toBe(userData.lastName);
      expect(response.body.user.role).toBe(userData.role);
      expect(response.body.user.password).toBeUndefined(); // Password should not be returned
    });

    it('should fail with duplicate email', async () => {
      const userData = {
        email: '<EMAIL>', // Already exists
        password: 'password123',
        firstName: 'Duplicate',
        lastName: 'User',
      };

      const response = await ctx.request
        .post('/api/auth/register')
        .send(userData);

      expect(response.status).toBe(409);
      expect(response.body.message).toContain('already exists');
    });

    it('should fail with invalid email format', async () => {
      const userData = {
        email: 'invalid-email',
        password: 'password123',
        firstName: 'Test',
        lastName: 'User',
      };

      const response = await ctx.request
        .post('/api/auth/register')
        .send(userData);

      expectValidationError(response, 'email');
    });

    it('should fail with short password', async () => {
      const userData = {
        email: '<EMAIL>',
        password: '123', // Too short
        firstName: 'Test',
        lastName: 'User',
      };

      const response = await ctx.request
        .post('/api/auth/register')
        .send(userData);

      expectValidationError(response, 'Password must be at least 8 characters long');
    });

    it('should fail with missing required fields', async () => {
      const response = await ctx.request
        .post('/api/auth/register')
        .send({});

      expectValidationError(response);
    });

    it('should register with optional fields', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Full',
        lastName: 'User',
        phoneNumber: '+62 812 3456 7890',
        address: 'Test Address 123',
        dateOfBirth: '1990-01-01',
        role: 'PHARMACIST',
      };

      const response = await ctx.request
        .post('/api/auth/register')
        .send(userData);

      expectSuccess(response, 201);
      expect(response.body.user.phoneNumber).toBe(userData.phoneNumber);
      expect(response.body.user.address).toBe(userData.address);
      expect(response.body.user.role).toBe(userData.role);
    });
  });

  describe('POST /api/auth/login', () => {
    it('should login with valid credentials', async () => {
      const response = await ctx.request
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'password123',
        });

      expectSuccess(response);
      expect(response.body.user).toBeDefined();
      expect(response.body.accessToken).toBeDefined();
      expect(response.body.user.email).toBe('<EMAIL>');
      expect(response.body.user.role).toBe('ADMIN');
    });

    it('should fail with invalid email', async () => {
      const response = await ctx.request
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'password123',
        });

      expectUnauthorized(response);
    });

    it('should fail with invalid password', async () => {
      const response = await ctx.request
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'wrongpassword',
        });

      expectUnauthorized(response);
    });

    it('should fail with missing credentials', async () => {
      const response = await ctx.request
        .post('/api/auth/login')
        .send({});

      expectValidationError(response);
    });

    it('should fail with invalid email format', async () => {
      const response = await ctx.request
        .post('/api/auth/login')
        .send({
          email: 'invalid-email',
          password: 'password123',
        });

      expectValidationError(response, 'email');
    });
  });

  describe('POST /api/auth/logout', () => {
    it('should logout successfully', async () => {
      const response = await ctx.request
        .post('/api/auth/logout');

      expectSuccess(response);
      expect(response.body.message).toBe('Logged out successfully');
    });
  });

  describe('GET /api/auth/me', () => {
    it('should return current user with valid token', async () => {
      const response = await ctx.request
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.id).toBe(ctx.users.admin.id);
      expect(response.body.email).toBe(ctx.users.admin.email);
      expect(response.body.role).toBe(ctx.users.admin.role);
    });

    it('should fail without token', async () => {
      const response = await ctx.request
        .get('/api/auth/me');

      expectUnauthorized(response);
    });

    it('should fail with invalid token', async () => {
      const response = await ctx.request
        .get('/api/auth/me')
        .set('Authorization', 'Bearer invalid-token');

      expectUnauthorized(response);
    });
  });

  describe('GET /api/auth/profile', () => {
    it('should return extended profile with valid token', async () => {
      const response = await ctx.request
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.id).toBe(ctx.users.admin.id);
      expect(response.body.email).toBe(ctx.users.admin.email);
      expect(response.body.firstName).toBe(ctx.users.admin.firstName);
      expect(response.body.lastName).toBe(ctx.users.admin.lastName);
    });

    it('should fail without token', async () => {
      const response = await ctx.request
        .get('/api/auth/profile');

      expectUnauthorized(response);
    });
  });

  describe('PUT /api/auth/profile', () => {
    it('should update profile with valid data', async () => {
      const updateData = {
        firstName: 'Updated',
        lastName: 'Name',
        phoneNumber: '+62 812 9999 8888',
        address: 'Updated Address 456',
      };

      const response = await ctx.request
        .put('/api/auth/profile')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(updateData);

      expectSuccess(response);
      expect(response.body.firstName).toBe(updateData.firstName);
      expect(response.body.lastName).toBe(updateData.lastName);
      expect(response.body.phoneNumber).toBe(updateData.phoneNumber);
      expect(response.body.address).toBe(updateData.address);
    });

    it('should fail without token', async () => {
      const response = await ctx.request
        .put('/api/auth/profile')
        .send({ firstName: 'Test' });

      expectUnauthorized(response);
    });

    it('should reject unknown fields', async () => {
      const response = await ctx.request
        .put('/api/auth/profile')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send({ email: 'invalid-email', unknownField: 'test' });

      expectValidationError(response, 'property');
    });
  });
});
