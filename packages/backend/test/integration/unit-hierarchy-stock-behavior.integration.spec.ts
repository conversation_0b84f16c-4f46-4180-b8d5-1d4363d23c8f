import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { PrismaService } from '../../src/prisma/prisma.service';
import { SalesService } from '../../src/sales/sales.service';
import { InventoryService } from '../../src/inventory/inventory.service';
import { UnitConversionService } from '../../src/common/services/unit-conversion.service';
import { IntegrationTestSetup, TestContext } from './test-setup';
import { UnitType, ProductType, ProductCategory } from '@prisma/client';

describe('Unit Hierarchy Stock Behavior Integration Tests', () => {
  let ctx: TestContext;
  let testSetup: IntegrationTestSetup;
  let testData: any;

  beforeAll(async () => {
    testSetup = new IntegrationTestSetup();
    ctx = await testSetup.setup();

    // Create test data
    testData = await setupTestData();
  });

  afterAll(async () => {
    await cleanupTestData();
    await testSetup.teardown();
  });

  async function setupTestData() {
    // Clean up any existing test data
    await cleanupTestData();

    // Create test units
    const units = await createTestUnits();

    // Create test product with unit hierarchy
    const product = await createTestProduct(units);

    // Create test inventory items
    const inventoryItems = await createTestInventoryItems(product, units);

    // Create test users
    const users = { admin: ctx.users.admin, cashier: ctx.users.cashier };

    return { units, product, inventoryItems, users };
  }

  async function createTestUnits() {
    const unitData = [
      { name: 'Tablet', abbreviation: 'tab', type: UnitType.COUNT, isBaseUnit: true },
      { name: 'Strip', abbreviation: 'strip', type: UnitType.PACKAGE, isBaseUnit: false },
      { name: 'Box', abbreviation: 'box', type: UnitType.PACKAGE, isBaseUnit: false },
    ];

    const units: any = {};

    for (const data of unitData) {
      const unit = await ctx.prisma.productUnit.create({ data });
      units[data.name.toLowerCase()] = unit;
    }

    return units;
  }

  async function createTestProduct(units: any) {
    // Create product with unit hierarchy: Box → Strip → Tablet
    const product = await ctx.prisma.product.create({
      data: {
        code: 'TEST-HIERARCHY-001',
        name: 'Test Medicine with Hierarchy',
        genericName: 'Test Generic',
        type: ProductType.MEDICINE,
        category: ProductCategory.OTHER,
        manufacturer: 'Test Manufacturer',
        baseUnitId: units.tablet.id,
        minimumStock: 10,
        maximumStock: 1000,
        reorderPoint: 50,
        isActive: true,
      }
    });

    // Create unit hierarchies
    // Tablet (base unit, level 0, factor 1)
    await ctx.prisma.productUnitHierarchy.create({
      data: {
        productId: product.id,
        unitId: units.tablet.id,
        conversionFactor: 1,
        level: 0,
        sellingPrice: 1000,
        costPrice: 600,
        isActive: true,
      }
    });

    // Strip (level 1, factor 10 - 1 strip = 10 tablets)
    await ctx.prisma.productUnitHierarchy.create({
      data: {
        productId: product.id,
        unitId: units.strip.id,
        conversionFactor: 10,
        level: 1,
        sellingPrice: 9500,
        costPrice: 5700,
        isActive: true,
      }
    });

    // Box (level 2, factor 100 - 1 box = 100 tablets = 10 strips)
    await ctx.prisma.productUnitHierarchy.create({
      data: {
        productId: product.id,
        unitId: units.box.id,
        conversionFactor: 100,
        level: 2,
        sellingPrice: 90000,
        costPrice: 54000,
        isActive: true,
      }
    });

    return product;
  }

  async function createTestInventoryItems(product: any, units: any) {
    // Create inventory with exactly 10 boxes worth of stock (1000 tablets)
    const inventoryItem = await ctx.prisma.inventoryItem.create({
      data: {
        productId: product.id,
        unitId: units.tablet.id, // Base unit
        batchNumber: 'HIERARCHY-BATCH-001',
        quantityOnHand: 1000, // 1000 tablets = 100 strips = 10 boxes
        quantityAllocated: 0,
        costPrice: 600,
        expiryDate: new Date('2025-12-31'),
        receivedDate: new Date('2024-01-01'),
        isActive: true,
      }
    });

    return { inventoryItem };
  }

  async function cleanupTestData() {
    try {
      await ctx.prisma.saleItem.deleteMany();
      await ctx.prisma.sale.deleteMany();
      await ctx.prisma.inventoryItem.deleteMany();
      await ctx.prisma.productUnitHierarchy.deleteMany();
      await ctx.prisma.product.deleteMany();
      await ctx.prisma.productUnit.deleteMany();
    } catch (error) {
      // Ignore errors during cleanup
    }
  }

  describe('Unit Hierarchy Stock Behavior Analysis', () => {
    it('should demonstrate current stock calculation behavior across unit levels', async () => {
      const inventoryService = ctx.app.get<InventoryService>(InventoryService);
      const unitConversionService = ctx.app.get<UnitConversionService>(UnitConversionService);

      console.log('\n🔍 INITIAL STOCK ANALYSIS');
      console.log('=========================');

      // Get base stock (tablets)
      const baseStock = await inventoryService.getAvailableStock(testData.product.id);
      console.log(`📦 Base stock (tablets): ${baseStock}`);

      // Calculate available units for each level
      const boxStock = Math.floor(baseStock / 100); // 1 box = 100 tablets
      const stripStock = Math.floor(baseStock / 10); // 1 strip = 10 tablets
      const tabletStock = baseStock; // 1 tablet = 1 tablet

      console.log(`📦 Available boxes: ${boxStock}`);
      console.log(`📦 Available strips: ${stripStock}`);
      console.log(`📦 Available tablets: ${tabletStock}`);

      expect(baseStock).toBe(1000);
      expect(boxStock).toBe(10);
      expect(stripStock).toBe(100);
      expect(tabletStock).toBe(1000);
    });

    it('should test selling maximum boxes and check remaining stock', async () => {
      const salesService = ctx.app.get<SalesService>(SalesService);
      const inventoryService = ctx.app.get<InventoryService>(InventoryService);

      console.log('\n🛒 SELLING MAXIMUM BOXES TEST');
      console.log('=============================');

      // Sell exactly 10 boxes (all available boxes)
      const createSaleDto = {
        items: [{
          productId: testData.product.id,
          unitId: testData.units.box.id,
          quantity: 10, // All 10 boxes
          unitPrice: 90000
        }],
        paymentMethod: 'CASH' as any,
        amountPaid: 900000,
        cashierId: testData.users.cashier.id
      };

      console.log('🛒 Attempting to sell 10 boxes...');
      const sale = await salesService.create(createSaleDto);
      console.log(`✅ Sale successful: ${sale.saleNumber}`);

      // Check stock after sale
      const stockAfterSale = await inventoryService.getAvailableStock(testData.product.id);
      console.log(`📦 Base stock after sale: ${stockAfterSale} tablets`);

      // Calculate available units for each level after sale
      const boxStockAfter = Math.floor(stockAfterSale / 100);
      const stripStockAfter = Math.floor(stockAfterSale / 10);
      const tabletStockAfter = stockAfterSale;

      console.log(`📦 Available boxes after sale: ${boxStockAfter}`);
      console.log(`📦 Available strips after sale: ${stripStockAfter}`);
      console.log(`📦 Available tablets after sale: ${tabletStockAfter}`);

      // This is the key question: What should happen here?
      expect(stockAfterSale).toBe(0); // All stock consumed
      expect(boxStockAfter).toBe(0); // No boxes available
      expect(stripStockAfter).toBe(0); // No strips available
      expect(tabletStockAfter).toBe(0); // No tablets available
    });

    it('should test attempting to sell after all boxes are consumed', async () => {
      const salesService = ctx.app.get<SalesService>(SalesService);

      console.log('\n🚫 TESTING SALES AFTER STOCK DEPLETION');
      console.log('======================================');

      // Try to sell 1 box (should fail)
      const boxSaleDto = {
        items: [{
          productId: testData.product.id,
          unitId: testData.units.box.id,
          quantity: 1,
          unitPrice: 90000
        }],
        paymentMethod: 'CASH' as any,
        amountPaid: 90000,
        cashierId: testData.users.cashier.id
      };

      console.log('🚫 Attempting to sell 1 box after depletion...');
      await expect(salesService.create(boxSaleDto)).rejects.toThrow(/Stok tidak mencukupi/);
      console.log('✅ Box sale correctly rejected');

      // Try to sell 1 strip (should fail)
      const stripSaleDto = {
        items: [{
          productId: testData.product.id,
          unitId: testData.units.strip.id,
          quantity: 1,
          unitPrice: 9500
        }],
        paymentMethod: 'CASH' as any,
        amountPaid: 9500,
        cashierId: testData.users.cashier.id
      };

      console.log('🚫 Attempting to sell 1 strip after depletion...');
      await expect(salesService.create(stripSaleDto)).rejects.toThrow(/Stok tidak mencukupi/);
      console.log('✅ Strip sale correctly rejected');

      // Try to sell 1 tablet (should fail)
      const tabletSaleDto = {
        items: [{
          productId: testData.product.id,
          unitId: testData.units.tablet.id,
          quantity: 1,
          unitPrice: 1000
        }],
        paymentMethod: 'CASH' as any,
        amountPaid: 1000,
        cashierId: testData.users.cashier.id
      };

      console.log('🚫 Attempting to sell 1 tablet after depletion...');
      await expect(salesService.create(tabletSaleDto)).rejects.toThrow(/Stok tidak mencukupi/);
      console.log('✅ Tablet sale correctly rejected');
    });

    it('should test partial box consumption scenario', async () => {
      // Reset inventory for this test
      await ctx.prisma.inventoryItem.updateMany({
        where: { productId: testData.product.id },
        data: { quantityOnHand: 1000, quantityAllocated: 0 }
      });

      const salesService = ctx.app.get<SalesService>(SalesService);
      const inventoryService = ctx.app.get<InventoryService>(InventoryService);

      console.log('\n🧪 PARTIAL BOX CONSUMPTION TEST');
      console.log('===============================');

      // Sell 9.5 boxes worth of stock (950 tablets)
      // This should leave 50 tablets = 5 strips = 0.5 boxes
      const createSaleDto = {
        items: [
          {
            productId: testData.product.id,
            unitId: testData.units.box.id,
            quantity: 9, // 9 boxes = 900 tablets
            unitPrice: 90000
          },
          {
            productId: testData.product.id,
            unitId: testData.units.strip.id,
            quantity: 5, // 5 strips = 50 tablets
            unitPrice: 9500
          }
        ],
        paymentMethod: 'CASH' as any,
        amountPaid: 857500, // 9*90000 + 5*9500
        cashierId: testData.users.cashier.id
      };

      console.log('🛒 Selling 9 boxes + 5 strips (950 tablets total)...');
      const sale = await salesService.create(createSaleDto);
      console.log(`✅ Sale successful: ${sale.saleNumber}`);

      // Check remaining stock
      const remainingStock = await inventoryService.getAvailableStock(testData.product.id);
      console.log(`📦 Remaining base stock: ${remainingStock} tablets`);

      const remainingBoxes = Math.floor(remainingStock / 100);
      const remainingStrips = Math.floor(remainingStock / 10);
      const remainingTablets = remainingStock;

      console.log(`📦 Remaining boxes: ${remainingBoxes}`);
      console.log(`📦 Remaining strips: ${remainingStrips}`);
      console.log(`📦 Remaining tablets: ${remainingTablets}`);

      // Now test what happens when we try to sell units
      console.log('\n🧪 TESTING SALES WITH PARTIAL STOCK');
      console.log('===================================');

      // Try to sell 1 box (should fail - not enough for a full box)
      try {
        await salesService.create({
          items: [{
            productId: testData.product.id,
            unitId: testData.units.box.id,
            quantity: 1,
            unitPrice: 90000
          }],
          paymentMethod: 'CASH' as any,
          amountPaid: 90000,
          cashierId: testData.users.cashier.id
        });
        console.log('❌ Box sale unexpectedly succeeded');
      } catch (error) {
        console.log('✅ Box sale correctly rejected (insufficient for full box)');
      }

      // Try to sell 5 strips (should succeed - exactly what we have)
      try {
        const stripSale = await salesService.create({
          items: [{
            productId: testData.product.id,
            unitId: testData.units.strip.id,
            quantity: 5,
            unitPrice: 9500
          }],
          paymentMethod: 'CASH' as any,
          amountPaid: 47500,
          cashierId: testData.users.cashier.id
        });
        console.log(`✅ Strip sale succeeded: ${stripSale.saleNumber}`);
      } catch (error) {
        console.log(`❌ Strip sale failed: ${error.message}`);
      }

      // Check final stock
      const finalStock = await inventoryService.getAvailableStock(testData.product.id);
      console.log(`📦 Final stock: ${finalStock} tablets`);

      expect(remainingStock).toBe(50);
      expect(remainingBoxes).toBe(0);
      expect(remainingStrips).toBe(5);
      expect(finalStock).toBe(0);
    });
  });
});
