/**
 * Unit Conversion Integration Tests
 * 
 * This test suite validates the unit conversion service within the broader
 * sales workflow, ensuring proper integration with stock validation,
 * allocation, and consumption processes.
 */

import { IntegrationTestSetup, TestContext } from './test-setup';
import { UnitConversionService } from '../../src/common/services/unit-conversion.service';
import { InventoryService } from '../../src/inventory/inventory.service';
import { UnitType, ProductType, ProductCategory, MedicineClassification } from '@prisma/client';

describe('Unit Conversion Integration Tests', () => {
  let testSetup: IntegrationTestSetup;
  let ctx: TestContext;
  let unitConversionService: UnitConversionService;
  let inventoryService: InventoryService;

  // Test data IDs
  let testData: {
    units: any;
    products: any;
    inventoryItems: any;
    users: any;
  };

  beforeAll(async () => {
    testSetup = new IntegrationTestSetup();
    ctx = await testSetup.setup();

    // Get services from the app context
    unitConversionService = ctx.app.get<UnitConversionService>(UnitConversionService);
    inventoryService = ctx.app.get<InventoryService>(InventoryService);

    // Setup test data
    await setupIntegrationTestData();
  });

  afterAll(async () => {
    await cleanupTestData();
    await testSetup.teardown();
  });

  async function setupIntegrationTestData() {
    // Clean up any existing test data
    await cleanupTestData();

    // Create test units
    const units = await createTestUnits();

    // Create test products with unit hierarchies
    const products = await createTestProducts(units);

    // Create test users
    const users = await createTestUsers();

    // Create test inventory items
    const inventoryItems = await createTestInventoryItems(products, units);

    testData = { units, products, inventoryItems, users };
  }

  async function createTestUnits() {
    const unitData = [
      { name: 'Tablet', abbreviation: 'tab', type: UnitType.COUNT, isBaseUnit: true },
      { name: 'Strip', abbreviation: 'strip', type: UnitType.PACKAGE, isBaseUnit: false },
      { name: 'Box', abbreviation: 'box', type: UnitType.PACKAGE, isBaseUnit: false },
      { name: 'Mililiter', abbreviation: 'ml', type: UnitType.VOLUME, isBaseUnit: true },
      { name: 'Botol', abbreviation: 'btl', type: UnitType.PACKAGE, isBaseUnit: false },
      { name: 'Gram', abbreviation: 'g', type: UnitType.WEIGHT, isBaseUnit: true },
      { name: 'Tube', abbreviation: 'tube', type: UnitType.PACKAGE, isBaseUnit: false },
    ];

    const units: any = {};
    for (const unit of unitData) {
      const created = await ctx.prisma.productUnit.create({ data: unit });
      units[unit.name.toLowerCase()] = created;
    }
    return units;
  }

  async function createTestProducts(units: any) {
    const products: any = {};

    // Paracetamol (Tablet → Strip → Box)
    const paracetamol = await ctx.prisma.product.create({
      data: {
        code: 'TEST-PARA-001',
        name: 'Test Paracetamol 500mg',
        genericName: 'Paracetamol',
        type: ProductType.MEDICINE,
        category: ProductCategory.ANALGESIC,
        manufacturer: 'Test Pharma',
        bpomNumber: 'TEST123456',
        medicineClassification: MedicineClassification.OBAT_BEBAS,
        regulatorySymbol: 'Test symbol',
        baseUnitId: units.tablet.id,
        minimumStock: 100,
        maximumStock: 1000,
        reorderPoint: 200,
        description: 'Test paracetamol',
        activeIngredient: 'Paracetamol 500mg',
        strength: '500mg',
        dosageForm: 'Tablet',
        indication: 'Test indication',
        contraindication: 'Test contraindication',
        sideEffects: 'Test side effects',
        dosage: 'Test dosage',
        storage: 'Test storage',
      }
    });

    // Create unit hierarchies for Paracetamol
    await ctx.prisma.productUnitHierarchy.createMany({
      data: [
        {
          productId: paracetamol.id,
          unitId: units.tablet.id,
          conversionFactor: 1,
          level: 0,
          sellingPrice: 500,
          costPrice: 300,
        },
        {
          productId: paracetamol.id,
          unitId: units.strip.id,
          conversionFactor: 10, // 1 strip = 10 tablets
          level: 1,
          sellingPrice: 4500,
          costPrice: 2800,
        },
        {
          productId: paracetamol.id,
          unitId: units.box.id,
          conversionFactor: 100, // 1 box = 100 tablets
          level: 2,
          sellingPrice: 42000,
          costPrice: 26000,
        },
      ]
    });

    products.paracetamol = paracetamol;

    // Alprazolam (Tablet → Strip → Box) - smaller box
    const alprazolam = await ctx.prisma.product.create({
      data: {
        code: 'TEST-ALPRA-001',
        name: 'Test Alprazolam 0.5mg',
        genericName: 'Alprazolam',
        type: ProductType.MEDICINE,
        category: ProductCategory.OTHER,
        manufacturer: 'Test Pharma',
        bpomNumber: 'TEST789012',
        medicineClassification: MedicineClassification.PSIKOTROPIKA,
        regulatorySymbol: 'Test symbol',
        baseUnitId: units.tablet.id,
        minimumStock: 20,
        maximumStock: 100,
        reorderPoint: 30,
        description: 'Test alprazolam',
        activeIngredient: 'Alprazolam 0.5mg',
        strength: '0.5mg',
        dosageForm: 'Tablet',
        indication: 'Test indication',
        contraindication: 'Test contraindication',
        sideEffects: 'Test side effects',
        dosage: 'Test dosage',
        storage: 'Test storage',
      }
    });

    // Create unit hierarchies for Alprazolam
    await ctx.prisma.productUnitHierarchy.createMany({
      data: [
        {
          productId: alprazolam.id,
          unitId: units.tablet.id,
          conversionFactor: 1,
          level: 0,
          sellingPrice: 5000,
          costPrice: 3000,
        },
        {
          productId: alprazolam.id,
          unitId: units.strip.id,
          conversionFactor: 10, // 1 strip = 10 tablets
          level: 1,
          sellingPrice: 47500,
          costPrice: 28500,
        },
        {
          productId: alprazolam.id,
          unitId: units.box.id,
          conversionFactor: 30, // 1 box = 30 tablets (smaller box)
          level: 2,
          sellingPrice: 140000,
          costPrice: 85000,
        },
      ]
    });

    products.alprazolam = alprazolam;

    // OBH Combi (ml → Botol)
    const obhCombi = await ctx.prisma.product.create({
      data: {
        code: 'TEST-OBH-001',
        name: 'Test OBH Combi',
        genericName: 'Test Cough Syrup',
        type: ProductType.MEDICINE,
        category: ProductCategory.COUGH_COLD,
        manufacturer: 'Test Pharma',
        bpomNumber: 'TEST345678',
        medicineClassification: MedicineClassification.OBAT_BEBAS_TERBATAS,
        regulatorySymbol: 'Test symbol',
        baseUnitId: units.mililiter.id,
        minimumStock: 500,
        maximumStock: 2000,
        reorderPoint: 800,
        description: 'Test cough syrup',
        activeIngredient: 'Test ingredients',
        strength: 'Test strength',
        dosageForm: 'Sirup',
        indication: 'Test indication',
        contraindication: 'Test contraindication',
        sideEffects: 'Test side effects',
        dosage: 'Test dosage',
        storage: 'Test storage',
      }
    });

    // Create unit hierarchies for OBH Combi
    await ctx.prisma.productUnitHierarchy.createMany({
      data: [
        {
          productId: obhCombi.id,
          unitId: units.mililiter.id,
          conversionFactor: 1,
          level: 0,
          sellingPrice: 300,
          costPrice: 200,
        },
        {
          productId: obhCombi.id,
          unitId: units.botol.id,
          conversionFactor: 60, // 1 bottle = 60ml
          level: 1,
          sellingPrice: 16500,
          costPrice: 11000,
        },
      ]
    });

    products.obhCombi = obhCombi;

    return products;
  }

  async function createTestUsers() {
    // Use the existing users from the test context
    return { admin: ctx.users.admin, cashier: ctx.users.cashier };
  }

  async function createTestInventoryItems(products: any, units: any) {
    const inventoryItems: any = {};

    // Create inventory for Paracetamol
    inventoryItems.paracetamol = await ctx.prisma.inventoryItem.create({
      data: {
        productId: products.paracetamol.id,
        unitId: units.tablet.id, // Base unit
        batchNumber: 'PARA-BATCH-001',
        quantityOnHand: 5000, // 5000 tablets
        quantityAllocated: 0,
        costPrice: 300,
        expiryDate: new Date('2025-12-31'),
        receivedDate: new Date('2024-01-01'),
        isActive: true,
      }
    });

    // Create inventory for Alprazolam
    inventoryItems.alprazolam = await ctx.prisma.inventoryItem.create({
      data: {
        productId: products.alprazolam.id,
        unitId: units.tablet.id, // Base unit
        batchNumber: 'ALPRA-BATCH-001',
        quantityOnHand: 858, // 858 tablets (from original bug scenario)
        quantityAllocated: 0,
        costPrice: 3000,
        expiryDate: new Date('2025-12-31'),
        receivedDate: new Date('2024-01-01'),
        isActive: true,
      }
    });

    // Create inventory for OBH Combi
    inventoryItems.obhCombi = await ctx.prisma.inventoryItem.create({
      data: {
        productId: products.obhCombi.id,
        unitId: units.mililiter.id, // Base unit
        batchNumber: 'OBH-BATCH-001',
        quantityOnHand: 3000, // 3000 ml
        quantityAllocated: 0,
        costPrice: 200,
        expiryDate: new Date('2025-12-31'),
        receivedDate: new Date('2024-01-01'),
        isActive: true,
      }
    });

    return inventoryItems;
  }

  async function cleanupTestData() {
    // Delete in reverse order of dependencies
    await ctx.prisma.stockMovement.deleteMany({ where: { reason: { contains: 'TEST' } } });
    await ctx.prisma.saleItem.deleteMany({});
    await ctx.prisma.sale.deleteMany({});
    await ctx.prisma.inventoryItem.deleteMany({ where: { batchNumber: { contains: 'TEST' } } });
    await ctx.prisma.productUnitHierarchy.deleteMany({});
    await ctx.prisma.product.deleteMany({ where: { code: { startsWith: 'TEST-' } } });
    await ctx.prisma.productUnit.deleteMany({ where: { name: { in: ['Tablet', 'Strip', 'Box', 'Mililiter', 'Botol', 'Gram', 'Tube'] } } });
    await ctx.prisma.user.deleteMany({ where: { email: { contains: 'test-' } } });
  }

  describe('Unit Conversion in Sales Workflow', () => {
    describe('Stock Validation with Mixed Units', () => {
      it('should correctly validate stock for mixed unit sales', async () => {
        // Test the original bug scenario: 28 boxes + 22 strips of Alprazolam
        // Available: 858 tablets, Demand: 1060 tablets (should fail)

        const saleItems = [
          {
            productId: testData.products.alprazolam.id,
            unitId: testData.units.box.id,
            quantity: 28,
            unitPrice: 140000,
          },
          {
            productId: testData.products.alprazolam.id,
            unitId: testData.units.strip.id,
            quantity: 22,
            unitPrice: 47500,
          },
        ];

        // Validate each item's conversion to base units
        let totalDemandInBaseUnits = 0;

        for (const item of saleItems) {
          const conversionResult = await unitConversionService.convertToBaseUnit(
            item.productId,
            item.unitId,
            item.quantity
          );

          expect(conversionResult.success).toBe(true);
          totalDemandInBaseUnits += conversionResult.convertedQuantity!;
        }

        // Verify the conversion calculations
        expect(totalDemandInBaseUnits).toBe(1060); // 28*30 + 22*10 = 840 + 220 = 1060

        // Check available stock
        const availableStock = await inventoryService.getAvailableStock(testData.products.alprazolam.id);
        expect(availableStock).toBe(858);

        // Verify that demand exceeds available stock
        expect(totalDemandInBaseUnits > availableStock).toBe(true);
      });

      it('should correctly validate stock for successful mixed unit sales', async () => {
        // Test a scenario that should succeed: 5 boxes + 3 strips of Paracetamol
        // Available: 5000 tablets, Demand: 530 tablets (should succeed)

        const saleItems = [
          {
            productId: testData.products.paracetamol.id,
            unitId: testData.units.box.id,
            quantity: 5,
            unitPrice: 42000,
          },
          {
            productId: testData.products.paracetamol.id,
            unitId: testData.units.strip.id,
            quantity: 3,
            unitPrice: 4500,
          },
        ];

        let totalDemandInBaseUnits = 0;

        for (const item of saleItems) {
          const conversionResult = await unitConversionService.convertToBaseUnit(
            item.productId,
            item.unitId,
            item.quantity
          );

          expect(conversionResult.success).toBe(true);
          totalDemandInBaseUnits += conversionResult.convertedQuantity!;
        }

        // Verify the conversion calculations
        expect(totalDemandInBaseUnits).toBe(530); // 5*100 + 3*10 = 500 + 30 = 530

        // Check available stock
        const availableStock = await inventoryService.getAvailableStock(testData.products.paracetamol.id);
        expect(availableStock).toBe(5000);

        // Verify that demand is within available stock
        expect(totalDemandInBaseUnits <= availableStock).toBe(true);
      });
    });

    describe('Liquid Medicine Conversions', () => {
      it('should handle liquid medicine unit conversions correctly', async () => {
        // Test OBH Combi: 2.5 bottles = 150ml
        const conversionResult = await unitConversionService.convertToBaseUnit(
          testData.products.obhCombi.id,
          testData.units.botol.id,
          2.5
        );

        expect(conversionResult.success).toBe(true);
        expect(conversionResult.convertedQuantity).toBe(150); // 2.5 * 60 = 150ml

        // Verify available stock
        const availableStock = await inventoryService.getAvailableStock(testData.products.obhCombi.id);
        expect(availableStock).toBe(3000);

        // Verify demand is within stock
        expect(conversionResult.convertedQuantity! <= availableStock).toBe(true);
      });
    });

    describe('Fractional Quantity Handling', () => {
      it('should handle fractional quantities correctly', async () => {
        // Test 0.5 boxes of Paracetamol = 50 tablets
        const conversionResult = await unitConversionService.convertToBaseUnit(
          testData.products.paracetamol.id,
          testData.units.box.id,
          0.5
        );

        expect(conversionResult.success).toBe(true);
        expect(conversionResult.convertedQuantity).toBe(50); // 0.5 * 100 = 50 tablets
      });

      it('should handle very small fractional quantities', async () => {
        // Test 0.1 strips of Paracetamol = 1 tablet
        const conversionResult = await unitConversionService.convertToBaseUnit(
          testData.products.paracetamol.id,
          testData.units.strip.id,
          0.1
        );

        expect(conversionResult.success).toBe(true);
        expect(conversionResult.convertedQuantity).toBe(1); // 0.1 * 10 = 1 tablet
      });
    });

    describe('Round-trip Conversion Accuracy', () => {
      it('should maintain accuracy in round-trip conversions', async () => {
        // Convert 100 tablets to strips, then back to tablets
        const toStrips = await unitConversionService.convertUnits(
          testData.products.paracetamol.id,
          testData.units.tablet.id,
          testData.units.strip.id,
          100
        );

        expect(toStrips.success).toBe(true);
        expect(toStrips.convertedQuantity).toBe(10); // 100 tablets = 10 strips

        const backToTablets = await unitConversionService.convertUnits(
          testData.products.paracetamol.id,
          testData.units.strip.id,
          testData.units.tablet.id,
          toStrips.convertedQuantity!
        );

        expect(backToTablets.success).toBe(true);
        expect(backToTablets.convertedQuantity).toBe(100); // Should return to original value
      });
    });

    describe('Complex Multi-level Conversions', () => {
      it('should handle conversions between non-adjacent levels', async () => {
        // Convert directly from boxes to tablets (skipping strips)
        const boxToTablets = await unitConversionService.convertUnits(
          testData.products.paracetamol.id,
          testData.units.box.id,
          testData.units.tablet.id,
          2.5
        );

        expect(boxToTablets.success).toBe(true);
        expect(boxToTablets.convertedQuantity).toBe(250); // 2.5 boxes * 100 tablets/box = 250 tablets

        // Convert directly from tablets to boxes
        const tabletsToBox = await unitConversionService.convertUnits(
          testData.products.paracetamol.id,
          testData.units.tablet.id,
          testData.units.box.id,
          250
        );

        expect(tabletsToBox.success).toBe(true);
        expect(tabletsToBox.convertedQuantity).toBe(2.5); // 250 tablets / 100 tablets/box = 2.5 boxes
      });
    });
  });

  describe('Error Handling in Integration Context', () => {
    it('should handle invalid product IDs gracefully', async () => {
      const result = await unitConversionService.convertToBaseUnit(
        'invalid-product-id',
        testData.units.tablet.id,
        10
      );

      expect(result.success).toBe(false);
      expect(result.error).toContain('Product not found');
    });

    it('should handle invalid unit IDs gracefully', async () => {
      const result = await unitConversionService.convertUnits(
        testData.products.paracetamol.id,
        'invalid-unit-id',
        testData.units.strip.id,
        10
      );

      expect(result.success).toBe(false);
      expect(result.error).toContain('No conversion path found');
    });

    it('should handle zero quantities', async () => {
      const result = await unitConversionService.convertToBaseUnit(
        testData.products.paracetamol.id,
        testData.units.strip.id,
        0
      );

      expect(result.success).toBe(true);
      expect(result.convertedQuantity).toBe(0);
    });
  });

  describe('Performance and Precision Tests', () => {
    it('should handle large quantities without precision loss', async () => {
      // Test with 10,000 tablets
      const result = await unitConversionService.convertUnits(
        testData.products.paracetamol.id,
        testData.units.tablet.id,
        testData.units.strip.id,
        10000
      );

      expect(result.success).toBe(true);
      expect(result.convertedQuantity).toBe(1000); // 10,000 tablets = 1,000 strips
    });

    it('should maintain precision with decimal calculations', async () => {
      // Test with 33.33 tablets (should result in 3.333 strips)
      const result = await unitConversionService.convertUnits(
        testData.products.paracetamol.id,
        testData.units.tablet.id,
        testData.units.strip.id,
        33.33
      );

      expect(result.success).toBe(true);
      expect(result.convertedQuantity).toBe(3.333); // Rounded to 4 decimal places
    });
  });
});
