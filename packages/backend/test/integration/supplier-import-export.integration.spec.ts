import { IntegrationTestSetup, TestContext, expectValidationError, expectUnauthorized, expectForbidden, expectNotFound, expectSuccess } from './test-setup';
import * as fs from 'fs';
import * as path from 'path';

describe('Supplier Import/Export Integration Tests', () => {
  let testSetup: IntegrationTestSetup;
  let ctx: TestContext;
  let testCsvPath: string;
  let invalidCsvPath: string;
  let largeCsvPath: string;

  beforeAll(async () => {
    testSetup = new IntegrationTestSetup();
    ctx = await testSetup.setup();

    // Create necessary upload directories
    const fs = require('fs');
    const uploadDirs = [
      'uploads/templates',
      'uploads/imports',
      'uploads/exports'
    ];

    uploadDirs.forEach(dir => {
      const fullPath = path.join(process.cwd(), dir);
      if (!fs.existsSync(fullPath)) {
        fs.mkdirSync(fullPath, { recursive: true });
      }
    });

    // Create test CSV files
    await createTestCsvFiles();
  });

  afterAll(async () => {
    await testSetup.teardown();
    
    // Clean up test files
    cleanupTestFiles();
  });

  async function createTestCsvFiles() {
    const testDir = path.join(process.cwd(), 'test-files');
    if (!fs.existsSync(testDir)) {
      fs.mkdirSync(testDir, { recursive: true });
    }

    // Valid CSV file
    testCsvPath = path.join(testDir, 'test-suppliers.csv');
    const validCsvContent = `Kode Supplier,Nama Supplier,Jenis (PBF/DISTRIBUTOR/MANUFACTURER/LOCAL),Alamat,Kota,Provinsi,Kode Pos,Telepon,Email,Website,NPWP,Nomor Izin,Izin Apotek/PBF,Limit Kredit,Termin Pembayaran (hari),Metode Pembayaran (CASH/TRANSFER/CREDIT),Nama Bank,Nomor Rekening,Nama Pemegang Rekening,Catatan
IMP001,PT Test Import 1,PBF,Jl. Import Test No. 1,Jakarta,DKI Jakarta,12345,+62 21 1234567,<EMAIL>,https://import1.com,01.234.567.8-901.234,IMP001/2024,SIA001/2024,5000000,30,TRANSFER,Bank Mandiri,**********,PT Test Import 1,Test import supplier 1
IMP002,PT Test Import 2,DISTRIBUTOR,Jl. Import Test No. 2,Bandung,Jawa Barat,40123,+62 22 7654321,<EMAIL>,https://import2.com,02.345.678.9-012.345,IMP002/2024,,3000000,45,CASH,Bank BCA,**********,PT Test Import 2,Test import supplier 2
IMP003,CV Test Local,LOCAL,Jl. Local Test No. 3,Surabaya,Jawa Timur,60123,+62 31 1122334,<EMAIL>,,03.456.789.0-123.456,LOC001/2024,,1000000,15,CREDIT,Bank BRI,**********,CV Test Local,Local supplier test`;
    fs.writeFileSync(testCsvPath, validCsvContent);

    // Invalid CSV file (missing required fields, invalid formats)
    invalidCsvPath = path.join(testDir, 'invalid-suppliers.csv');
    const invalidCsvContent = `Kode Supplier,Nama Supplier,Jenis (PBF/DISTRIBUTOR/MANUFACTURER/LOCAL),Alamat,Kota,Provinsi,Kode Pos,Telepon,Email,Website,NPWP,Nomor Izin,Izin Apotek/PBF,Limit Kredit,Termin Pembayaran (hari),Metode Pembayaran (CASH/TRANSFER/CREDIT),Nama Bank,Nomor Rekening,Nama Pemegang Rekening,Catatan
,Missing Code Supplier,PBF,Jl. Test,Jakarta,DKI Jakarta,12345,+62 21 1234567,<EMAIL>,https://test.com,01.234.567.8-901.234,TEST001,SIA001,5000000,30,TRANSFER,Bank Test,**********,Test Supplier,Test
INV001,,INVALID_TYPE,Jl. Test,Jakarta,DKI Jakarta,12345,invalid-phone,invalid-email,invalid-url,invalid-npwp,TEST002,SIA002,invalid-amount,invalid-terms,INVALID_PAYMENT,Bank Test,**********,Test Supplier,Test
DUP001,Duplicate Code Test,PBF,Jl. Test,Jakarta,DKI Jakarta,12345,+62 21 1234567,<EMAIL>,https://dup.com,01.234.567.8-901.234,DUP001,SIA003,5000000,30,TRANSFER,Bank Test,**********,Duplicate Test,Test`;
    fs.writeFileSync(invalidCsvPath, invalidCsvContent);

    // Large CSV file (for testing file size limits)
    largeCsvPath = path.join(testDir, 'large-suppliers.csv');
    let largeCsvContent = `Kode Supplier,Nama Supplier,Jenis (PBF/DISTRIBUTOR/MANUFACTURER/LOCAL),Alamat,Kota,Provinsi,Kode Pos,Telepon,Email,Website,NPWP,Nomor Izin,Izin Apotek/PBF,Limit Kredit,Termin Pembayaran (hari),Metode Pembayaran (CASH/TRANSFER/CREDIT),Nama Bank,Nomor Rekening,Nama Pemegang Rekening,Catatan\n`;

    // Add many rows to make file large (reduce to 100 for faster testing)
    for (let i = 1; i <= 100; i++) {
      largeCsvContent += `LARGE${i.toString().padStart(3, '0')},PT Large Supplier ${i},PBF,Jl. Large Test No. ${i},Jakarta,DKI Jakarta,12345,+62 21 1234567,large${i}@test.com,https://large${i}.com,01.234.567.8-901.234,LARGE${i}/2024,SIA${i}/2024,5000000,30,TRANSFER,Bank Test,**********,PT Large Supplier ${i},Large supplier ${i}\n`;
    }
    fs.writeFileSync(largeCsvPath, largeCsvContent);
  }

  function cleanupTestFiles() {
    const testFiles = [testCsvPath, invalidCsvPath, largeCsvPath];
    testFiles.forEach(filePath => {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    });

    const testDir = path.join(process.cwd(), 'test-files');
    if (fs.existsSync(testDir)) {
      fs.rmSync(testDir, { recursive: true, force: true });
    }
  }

  describe('GET /api/suppliers/template', () => {
    it('should download CSV template as admin', async () => {
      const response = await ctx.request
        .get('/api/suppliers/template')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.headers['content-type']).toContain('text/csv');
      expect(response.headers['content-disposition']).toContain('attachment');
      expect(response.headers['content-disposition']).toContain('template-supplier.csv');
      
      // Check that response contains CSV headers
      const csvContent = response.text;
      expect(csvContent).toContain('Kode Supplier');
      expect(csvContent).toContain('Nama Supplier');
      expect(csvContent).toContain('Jenis (PBF/DISTRIBUTOR/MANUFACTURER/LOCAL)');
      expect(csvContent).toContain('NPWP');
      
      // Check sample data
      expect(csvContent).toContain('PT Kimia Farma');
      expect(csvContent).toContain('PT Kalbe Farma');
    });

    it('should download CSV template as pharmacist', async () => {
      const response = await ctx.request
        .get('/api/suppliers/template')
        .set('Authorization', `Bearer ${ctx.users.pharmacist.accessToken}`);

      expectSuccess(response);
      expect(response.headers['content-type']).toContain('text/csv');
    });

    it('should fail without authentication', async () => {
      const response = await ctx.request
        .get('/api/suppliers/template');

      expectUnauthorized(response);
    });

    it('should fail as cashier (insufficient permissions)', async () => {
      const response = await ctx.request
        .get('/api/suppliers/template')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectForbidden(response);
    });
  });

  describe('POST /api/suppliers/import', () => {
    it('should import valid CSV file as admin', async () => {
      const response = await ctx.request
        .post('/api/suppliers/import')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .attach('file', testCsvPath);

      expectSuccess(response, 201);
      expect(response.body.success).toBe(true);
      expect(response.body.totalRows).toBe(3);
      expect(response.body.successfulImports).toBe(3);
      expect(response.body.failedImports).toBe(0);
      expect(response.body.errors).toHaveLength(0);
      expect(response.body.importedSuppliers).toHaveLength(3);

      // Verify suppliers were created in database
      const supplier1 = await ctx.prisma.supplier.findUnique({
        where: { code: 'IMP001' }
      });
      expect(supplier1).toBeDefined();
      expect(supplier1?.name).toBe('PT Test Import 1');
      expect(supplier1?.type).toBe('PBF');
      expect(supplier1?.npwp).toBe('01.234.567.8-901.234');

      const supplier2 = await ctx.prisma.supplier.findUnique({
        where: { code: 'IMP002' }
      });
      expect(supplier2).toBeDefined();
      expect(supplier2?.name).toBe('PT Test Import 2');
      expect(supplier2?.type).toBe('DISTRIBUTOR');

      const supplier3 = await ctx.prisma.supplier.findUnique({
        where: { code: 'IMP003' }
      });
      expect(supplier3).toBeDefined();
      expect(supplier3?.name).toBe('CV Test Local');
      expect(supplier3?.type).toBe('LOCAL');
    });

    it('should import valid CSV file as pharmacist', async () => {
      // Create a different CSV to avoid duplicate codes
      const pharmacistCsvPath = path.join(process.cwd(), 'test-files', 'pharmacist-import.csv');
      const csvContent = `Kode Supplier,Nama Supplier,Jenis (PBF/DISTRIBUTOR/MANUFACTURER/LOCAL),Alamat,Kota,Provinsi,Kode Pos,Telepon,Email,Website,NPWP,Nomor Izin,Izin Apotek/PBF,Limit Kredit,Termin Pembayaran (hari),Metode Pembayaran (CASH/TRANSFER/CREDIT),Nama Bank,Nomor Rekening,Nama Pemegang Rekening,Catatan
PHARM001,PT Pharmacist Import,MANUFACTURER,Jl. Pharmacist Test,Jakarta,DKI Jakarta,12345,+62 21 1234567,<EMAIL>,https://pharm.com,04.567.890.1-234.567,PHARM001/2024,SIA004/2024,2000000,30,TRANSFER,Bank Test,**********,PT Pharmacist Import,Pharmacist import test`;
      fs.writeFileSync(pharmacistCsvPath, csvContent);

      const response = await ctx.request
        .post('/api/suppliers/import')
        .set('Authorization', `Bearer ${ctx.users.pharmacist.accessToken}`)
        .attach('file', pharmacistCsvPath);

      expectSuccess(response, 201);
      expect(response.body.success).toBe(true);
      expect(response.body.successfulImports).toBe(1);

      // Clean up
      fs.unlinkSync(pharmacistCsvPath);
    });

    it('should handle invalid CSV data with detailed errors', async () => {
      const response = await ctx.request
        .post('/api/suppliers/import')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .attach('file', invalidCsvPath);

      expectSuccess(response, 201);
      expect(response.body.success).toBe(true);
      expect(response.body.totalRows).toBe(3);
      expect(response.body.successfulImports).toBeLessThan(3);
      expect(response.body.failedImports).toBeGreaterThan(0);
      expect(response.body.errors.length).toBeGreaterThan(0);

      // Check specific error messages
      const errors = response.body.errors;
      expect(errors.some((e: any) => e.errors.includes('Kode supplier wajib diisi'))).toBe(true);
      expect(errors.some((e: any) => e.errors.includes('Jenis supplier tidak valid. Harus salah satu dari: PBF, DISTRIBUTOR, MANUFACTURER, LOCAL'))).toBe(true);
    });

    it('should handle duplicate supplier codes', async () => {
      // First import
      await ctx.request
        .post('/api/suppliers/import')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .attach('file', testCsvPath);

      // Second import with same codes should fail
      const response = await ctx.request
        .post('/api/suppliers/import')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .attach('file', testCsvPath);

      expectSuccess(response, 201);
      expect(response.body.success).toBe(true);
      expect(response.body.successfulImports).toBe(0);
      expect(response.body.failedImports).toBe(3);
      expect(response.body.errors.every((e: any) => 
        e.errors.includes('Kode supplier sudah digunakan')
      )).toBe(true);
    });

    it('should fail without file attachment', async () => {
      const response = await ctx.request
        .post('/api/suppliers/import')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expect(response.status).toBe(400);
      expect(response.body.message).toContain('File CSV wajib diunggah');
    });

    it('should fail with non-CSV file', async () => {
      const txtFilePath = path.join(process.cwd(), 'test-files', 'test.txt');
      fs.writeFileSync(txtFilePath, 'This is not a CSV file');

      const response = await ctx.request
        .post('/api/suppliers/import')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .attach('file', txtFilePath);

      expect(response.status).toBe(400);
      expect(response.body.message).toContain('Hanya file CSV yang diizinkan');

      // Clean up
      fs.unlinkSync(txtFilePath);
    });

    it('should fail without authentication', async () => {
      const response = await ctx.request
        .post('/api/suppliers/import')
        .attach('file', testCsvPath);

      expectUnauthorized(response);
    });

    it('should fail as cashier (insufficient permissions)', async () => {
      const response = await ctx.request
        .post('/api/suppliers/import')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .attach('file', testCsvPath);

      expectForbidden(response);
    });

    it('should handle large CSV files within limits', async () => {
      const response = await ctx.request
        .post('/api/suppliers/import')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .attach('file', largeCsvPath);

      // Should either succeed or fail gracefully
      expect([200, 201, 400, 413]).toContain(response.status);
      
      if (response.status === 201) {
        expect(response.body.success).toBe(true);
        expect(response.body.totalRows).toBe(100);
      }
    });
  });

  describe('GET /api/suppliers/export', () => {
    beforeEach(async () => {
      // Create test suppliers for export
      await ctx.prisma.supplier.createMany({
        data: [
          {
            code: 'EXP001',
            name: 'PT Export Test 1',
            type: 'PBF',
            status: 'ACTIVE',
            address: 'Jl. Export Test No. 1',
            city: 'Jakarta',
            province: 'DKI Jakarta',
            postalCode: '12345',
            phone: '+62 21 1234567',
            email: '<EMAIL>',
            website: 'https://export1.com',
            npwp: '01.234.567.8-901.234',
            licenseNumber: 'EXP001/2024',
            pharmacyLicense: 'SIA001/2024',
            creditLimit: 5000000,
            paymentTerms: 30,
            preferredPayment: 'TRANSFER',
            bankName: 'Bank Export',
            bankAccount: '**********',
            bankAccountName: 'PT Export Test 1',
            notes: 'Export test supplier 1',
            createdBy: ctx.users.admin.id,
            updatedBy: ctx.users.admin.id,
          },
          {
            code: 'EXP002',
            name: 'PT Export Test 2',
            type: 'DISTRIBUTOR',
            status: 'ACTIVE',
            address: 'Jl. Export Test No. 2',
            city: 'Bandung',
            province: 'Jawa Barat',
            postalCode: '40123',
            phone: '+62 22 7654321',
            email: '<EMAIL>',
            creditLimit: 3000000,
            paymentTerms: 45,
            preferredPayment: 'CASH',
            notes: 'Export test supplier 2',
            createdBy: ctx.users.admin.id,
            updatedBy: ctx.users.admin.id,
          },
          {
            code: 'EXP003',
            name: 'CV Export Local',
            type: 'LOCAL',
            status: 'INACTIVE',
            address: 'Jl. Local Export No. 3',
            city: 'Surabaya',
            province: 'Jawa Timur',
            postalCode: '60123',
            phone: '+62 31 1122334',
            email: '<EMAIL>',
            creditLimit: 1000000,
            paymentTerms: 15,
            preferredPayment: 'CREDIT',
            notes: 'Local export supplier',
            createdBy: ctx.users.admin.id,
            updatedBy: ctx.users.admin.id,
          },
        ],
      });
    });

    afterEach(async () => {
      // Clean up test suppliers
      await ctx.prisma.supplier.deleteMany({
        where: {
          code: {
            in: ['EXP001', 'EXP002', 'EXP003'],
          },
        },
      });
    });

    it('should export all suppliers as CSV by default', async () => {
      const response = await ctx.request
        .get('/api/suppliers/export')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.headers['content-type']).toContain('text/csv');
      expect(response.headers['content-disposition']).toContain('attachment');
      expect(response.headers['content-disposition']).toContain('.csv');

      const csvContent = response.text;
      expect(csvContent).toContain('Kode Supplier,Nama Supplier');
      expect(csvContent).toContain('EXP001,PT Export Test 1');
      expect(csvContent).toContain('EXP002,PT Export Test 2');
      expect(csvContent).toContain('EXP003,CV Export Local');
    });

    it('should export suppliers as XLSX format', async () => {
      const response = await ctx.request
        .get('/api/suppliers/export?format=xlsx')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .buffer(true)
        .parse((res, callback) => {
          let data = Buffer.alloc(0);
          res.on('data', (chunk) => {
            data = Buffer.concat([data, chunk]);
          });
          res.on('end', () => {
            callback(null, data);
          });
        });

      expectSuccess(response);
      expect(response.headers['content-type']).toContain('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      expect(response.headers['content-disposition']).toContain('attachment');
      expect(response.headers['content-disposition']).toContain('.xlsx');

      // XLSX files are binary, so we just check that we got data
      expect(response.body).toBeDefined();
      expect(Buffer.isBuffer(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
    });

    it('should export filtered suppliers by type', async () => {
      const response = await ctx.request
        .get('/api/suppliers/export?type=PBF')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      const csvContent = response.text;
      expect(csvContent).toContain('EXP001,PT Export Test 1');
      expect(csvContent).not.toContain('EXP002,PT Export Test 2'); // DISTRIBUTOR
      expect(csvContent).not.toContain('EXP003,CV Export Local'); // LOCAL
    });

    it('should export filtered suppliers by status', async () => {
      const response = await ctx.request
        .get('/api/suppliers/export?status=ACTIVE')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      const csvContent = response.text;
      expect(csvContent).toContain('EXP001,PT Export Test 1');
      expect(csvContent).toContain('EXP002,PT Export Test 2');
      expect(csvContent).not.toContain('EXP003,CV Export Local'); // INACTIVE
    });

    it('should export filtered suppliers by province', async () => {
      const response = await ctx.request
        .get('/api/suppliers/export?province=DKI Jakarta')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      const csvContent = response.text;
      expect(csvContent).toContain('EXP001,PT Export Test 1');
      expect(csvContent).not.toContain('EXP002,PT Export Test 2'); // Jawa Barat
      expect(csvContent).not.toContain('EXP003,CV Export Local'); // Jawa Timur
    });

    it('should export filtered suppliers by search term', async () => {
      const response = await ctx.request
        .get('/api/suppliers/export?search=Local')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      const csvContent = response.text;
      expect(csvContent).not.toContain('EXP001,PT Export Test 1');
      expect(csvContent).not.toContain('EXP002,PT Export Test 2');
      expect(csvContent).toContain('EXP003,CV Export Local');
    });

    it('should export with multiple filters combined', async () => {
      const response = await ctx.request
        .get('/api/suppliers/export?type=PBF&status=ACTIVE&province=DKI Jakarta')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      const csvContent = response.text;
      expect(csvContent).toContain('EXP001,PT Export Test 1');
      expect(csvContent).not.toContain('EXP002,PT Export Test 2');
      expect(csvContent).not.toContain('EXP003,CV Export Local');
    });

    it('should export as pharmacist', async () => {
      const response = await ctx.request
        .get('/api/suppliers/export')
        .set('Authorization', `Bearer ${ctx.users.pharmacist.accessToken}`);

      expectSuccess(response);
      expect(response.headers['content-type']).toContain('text/csv');
    });

    it('should fail without authentication', async () => {
      const response = await ctx.request
        .get('/api/suppliers/export');

      expectUnauthorized(response);
    });

    it('should fail as cashier (insufficient permissions)', async () => {
      const response = await ctx.request
        .get('/api/suppliers/export')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectForbidden(response);
    });

    it('should handle empty result set', async () => {
      const response = await ctx.request
        .get('/api/suppliers/export?search=DEFINITELY_NONEXISTENT_SUPPLIER_12345&type=MANUFACTURER&province=NONEXISTENT_PROVINCE')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      const csvContent = response.text;
      // Should contain headers but no data rows
      expect(csvContent).toContain('Kode Supplier,Nama Supplier');
      const lines = csvContent.split('\n').filter(line => line.trim() !== '');
      expect(lines.length).toBeLessThanOrEqual(1); // Only header line
    });

    it('should include all expected CSV columns', async () => {
      const response = await ctx.request
        .get('/api/suppliers/export')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      const csvContent = response.text;
      const headers = csvContent.split('\n')[0];

      // Check for all expected columns
      expect(headers).toContain('Kode Supplier');
      expect(headers).toContain('Nama Supplier');
      expect(headers).toContain('Jenis');
      expect(headers).toContain('Status');
      expect(headers).toContain('Alamat');
      expect(headers).toContain('Kota');
      expect(headers).toContain('Provinsi');
      expect(headers).toContain('Kode Pos');
      expect(headers).toContain('Telepon');
      expect(headers).toContain('Email');
      expect(headers).toContain('Website');
      expect(headers).toContain('NPWP');
      expect(headers).toContain('Nomor Izin');
      expect(headers).toContain('Izin Apotek/PBF');
      expect(headers).toContain('Limit Kredit');
      expect(headers).toContain('Termin Pembayaran');
      expect(headers).toContain('Metode Pembayaran');
      expect(headers).toContain('Nama Bank');
      expect(headers).toContain('Nomor Rekening');
      expect(headers).toContain('Nama Pemegang Rekening');
      expect(headers).toContain('Jumlah Kontak');
      expect(headers).toContain('Jumlah Dokumen');
      expect(headers).toContain('Jumlah Pembayaran');
      expect(headers).toContain('Dibuat Oleh');
      expect(headers).toContain('Tanggal Dibuat');
      expect(headers).toContain('Catatan');
    });
  });
});
