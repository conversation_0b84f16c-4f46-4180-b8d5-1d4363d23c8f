import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { PrismaService } from '../../src/prisma/prisma.service';
import { StockAllocationService } from '../../src/inventory/services/stock-allocation.service';
import { AllocationMethod } from '../../src/inventory/dto/stock-allocation.dto';
import { StockMovementType, ProductCategory, MedicineClassification, ReferenceType } from '@prisma/client';
import { IntegrationTestSetup, TestContext } from '../integration/test-setup';

describe('Pre-allocated Sale Workflow Integration Test', () => {
  let testContext: TestContext;
  let app: INestApplication;
  let prisma: PrismaService;
  let stockAllocationService: StockAllocationService;
  let userId: string;
  let productId: string;
  let unitId: string;
  let batchAId: string;
  let batchBId: string;
  let testSetup: IntegrationTestSetup;

  beforeAll(async () => {
    testSetup = new IntegrationTestSetup();
    testContext = await testSetup.setup();
    app = testContext.app;
    prisma = testContext.prisma;
    
    // Get the stock allocation service from the app
    stockAllocationService = app.get<StockAllocationService>(StockAllocationService);
    
    // Use the admin user for our tests
    userId = testContext.users.admin.id;
  });

  afterAll(async () => {
    await testSetup.teardown();
  });

  beforeEach(async () => {
    // Clean up any previous test data
    await prisma.stockMovement.deleteMany();
    await prisma.inventoryItem.deleteMany();
    await prisma.product.deleteMany();
    await prisma.productUnit.deleteMany();

    // Create test data: Product unit
    const unit = await prisma.productUnit.create({
      data: {
        name: 'Test Unit',
        abbreviation: 'TU',
        type: 'COUNT',
        description: 'Test unit',
        isActive: true,
      },
    });
    unitId = unit.id;

    // Create test data: Product
    const product = await prisma.product.create({
      data: {
        name: 'Test Product',
        code: 'TEST-001',
        type: 'MEDICINE',
        baseUnitId: unit.id,
        category: ProductCategory.ANALGESIC,
        medicineClassification: MedicineClassification.OBAT_BEBAS,
        isActive: true,
        unitHierarchies: {
          create: {
            unitId: unit.id,
            conversionFactor: 1,
            level: 0,
          },
        },
      },
    });
    productId = product.id;

    // Create two inventory batches with different expiry dates
    // Batch A expires sooner
    const batchA = await prisma.inventoryItem.create({
      data: {
        productId,
        unitId,
        batchNumber: 'BATCH-A',
        expiryDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        quantityOnHand: 50,
        costPrice: 10,
        receivedDate: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000), // 60 days ago
        isActive: true,
      },
    });
    batchAId = batchA.id;

    // Batch B expires later
    const batchB = await prisma.inventoryItem.create({
      data: {
        productId,
        unitId,
        batchNumber: 'BATCH-B',
        expiryDate: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 90 days from now
        quantityOnHand: 50,
        costPrice: 10,
        receivedDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
        isActive: true,
      },
    });
    batchBId = batchB.id;
  });

  it('should respect pre-allocated stock when creating a sale', async () => {
    // Step 1: Pre-allocate stock from Batch A (expires sooner)
    const allocationResult = await stockAllocationService.allocateStock({
      productId,
      requestedQuantity: 10,
      method: AllocationMethod.FEFO,
      reason: 'Pre-allocation for customer',
      referenceType: ReferenceType.CUSTOMER_RESERVATION,
      referenceId: 'test-reservation-id',
    }, userId);

    // Verify allocation was successful
    expect(allocationResult.success).toBe(true);
    expect(allocationResult.allocatedQuantity).toBe(10);
    
    // Verify allocation was made from Batch A (which expires sooner)
    expect(allocationResult.batches[0].inventoryItemId).toBe(batchAId);
    
    // Verify quantityAllocated was updated in the database
    const batchAAfterAllocation = await prisma.inventoryItem.findUnique({
      where: { id: batchAId },
    });
    expect(batchAAfterAllocation?.quantityAllocated).toBe(10);
    
    // Step 2: Create a sale for the same product
    const response = await request(app.getHttpServer())
      .post('/api/sales')
      .set('Authorization', `Bearer ${testContext.users.admin.accessToken}`)
      .send({
        cashierId: userId,
        customerName: 'Test Customer',
        paymentMethod: 'CASH',
        amountPaid: 100,
        items: [
          {
            productId,
            unitId,
            quantity: 10,
            unitPrice: 10,
          },
        ],
      });

    expect(response.status).toBe(201);
    const sale = response.body;
    
    // Step 3: Verify that the sale consumed the allocated stock from Batch A
    const batchAAfterSale = await prisma.inventoryItem.findUnique({
      where: { id: batchAId },
    });
    const batchBAfterSale = await prisma.inventoryItem.findUnique({
      where: { id: batchBId },
    });
    
    // Verify Batch A was consumed (quantityOnHand decreased)
    expect(batchAAfterSale?.quantityOnHand).toBe(40); // 50 - 10
    expect(batchAAfterSale?.quantityAllocated).toBe(0); // Allocation should be consumed
    
    // Verify Batch B was not affected
    expect(batchBAfterSale?.quantityOnHand).toBe(50); // Unchanged
    expect(batchBAfterSale?.quantityAllocated).toBe(0); // No allocation
    
    // Verify stock movements were created correctly
    const stockMovements = await prisma.stockMovement.findMany({
      orderBy: { createdAt: 'asc' },
    });
    
    // Should have ALLOCATION and OUT movements (no redundant allocation)
    expect(stockMovements.length).toBe(2);
    
    // First movement should be our pre-allocation
    expect(stockMovements[0].type).toBe(StockMovementType.ALLOCATION);
    expect(stockMovements[0].inventoryItemId).toBe(batchAId);
    expect(stockMovements[0].quantity).toBe(10);
    expect(stockMovements[0].referenceType).toBe(ReferenceType.CUSTOMER_RESERVATION);
    
    // Second movement should be OUT
    expect(stockMovements[1].type).toBe(StockMovementType.OUT);
    expect(stockMovements[1].inventoryItemId).toBe(batchAId);
    expect(stockMovements[1].quantity).toBe(-10);
    expect(stockMovements[1].referenceType).toBe(ReferenceType.SALE);
  });
}); 