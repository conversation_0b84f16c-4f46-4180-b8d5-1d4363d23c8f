import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { PrismaService } from '../../src/prisma/prisma.service';
import { InventoryService } from '../../src/inventory/inventory.service';
import { StockAllocationService } from '../../src/inventory/services/stock-allocation.service';
import { AllocationMethod } from '../../src/inventory/dto/stock-allocation.dto';
import { ProductCategory, MedicineClassification } from '@prisma/client';
import { IntegrationTestSetup, TestContext } from '../integration/test-setup';

describe('FEFO Stock Allocation Integration Test', () => {
  let testContext: TestContext;
  let app: INestApplication;
  let prisma: PrismaService;
  let inventoryService: InventoryService;
  let stockAllocationService: StockAllocationService;
  let userId: string;
  let productId: string;
  let unitId: string;
  let nearExpiryBatchId: string;
  let midExpiryBatchId: string;
  let farExpiryBatchId: string;
  let testSetup: IntegrationTestSetup;

  beforeAll(async () => {
    testSetup = new IntegrationTestSetup();
    testContext = await testSetup.setup();
    app = testContext.app;
    prisma = testContext.prisma;
    
    // Get the services from the app
    inventoryService = app.get<InventoryService>(InventoryService);
    stockAllocationService = app.get<StockAllocationService>(StockAllocationService);
    
    // Use the admin user for our tests
    userId = testContext.users.admin.id;
  });

  afterAll(async () => {
    await testSetup.teardown();
  });

  beforeEach(async () => {
    // Clean up any previous test data
    await prisma.stockMovement.deleteMany();
    await prisma.inventoryItem.deleteMany();
    await prisma.product.deleteMany();
    await prisma.productUnit.deleteMany();

    // Create test data: Product unit
    const unit = await prisma.productUnit.create({
      data: {
        name: 'Test Unit',
        abbreviation: 'TU',
        type: 'COUNT',
        description: 'Test unit',
        isActive: true,
      },
    });
    unitId = unit.id;

    // Create test data: Product
    const product = await prisma.product.create({
      data: {
        name: 'Test Product',
        code: 'TEST-001',
        type: 'MEDICINE',
        baseUnitId: unit.id,
        category: ProductCategory.ANALGESIC,
        medicineClassification: MedicineClassification.OBAT_BEBAS,
        isActive: true,
        unitHierarchies: {
          create: {
            unitId: unit.id,
            conversionFactor: 1,
            level: 0,
          },
        },
      },
    });
    productId = product.id;

    // Create three inventory batches with different expiry dates
    // Near expiry batch
    const nearExpiryBatch = await prisma.inventoryItem.create({
      data: {
        productId,
        unitId,
        batchNumber: 'NEAR-EXP',
        expiryDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        quantityOnHand: 50,
        costPrice: 10,
        receivedDate: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000), // 60 days ago
        isActive: true,
      },
    });
    nearExpiryBatchId = nearExpiryBatch.id;

    // Mid expiry batch
    const midExpiryBatch = await prisma.inventoryItem.create({
      data: {
        productId,
        unitId,
        batchNumber: 'MID-EXP',
        expiryDate: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 90 days from now
        quantityOnHand: 50,
        costPrice: 10,
        receivedDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
        isActive: true,
      },
    });
    midExpiryBatchId = midExpiryBatch.id;

    // Far expiry batch
    const farExpiryBatch = await prisma.inventoryItem.create({
      data: {
        productId,
        unitId,
        batchNumber: 'FAR-EXP',
        expiryDate: new Date(Date.now() + 180 * 24 * 60 * 60 * 1000), // 180 days from now
        quantityOnHand: 50,
        costPrice: 10,
        receivedDate: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000), // 10 days ago
        isActive: true,
      },
    });
    farExpiryBatchId = farExpiryBatch.id;
  });

  it('should allocate stock using FEFO (First Expired, First Out) method', async () => {
    // Allocate 30 units of stock
    const allocationResult = await stockAllocationService.allocateStock({
      productId,
      requestedQuantity: 30,
      method: AllocationMethod.FEFO,
      reason: 'Testing FEFO allocation',
    }, userId);

    // Verify allocation was successful
    expect(allocationResult.success).toBe(true);
    expect(allocationResult.allocatedQuantity).toBe(30);
    
    // Check that batches were allocated in the correct order (by expiry date)
    expect(allocationResult.batches.length).toBe(1);
    expect(allocationResult.batches[0].inventoryItemId).toBe(nearExpiryBatchId);
    expect(allocationResult.batches[0].allocatedQuantity).toBe(30);
    
    // Verify database was updated correctly
    const nearExpiryItem = await prisma.inventoryItem.findUnique({
      where: { id: nearExpiryBatchId },
    });
    const midExpiryItem = await prisma.inventoryItem.findUnique({
      where: { id: midExpiryBatchId },
    });
    const farExpiryItem = await prisma.inventoryItem.findUnique({
      where: { id: farExpiryBatchId },
    });
    
    expect(nearExpiryItem?.quantityAllocated).toBe(30);
    expect(midExpiryItem?.quantityAllocated).toBe(0);
    expect(farExpiryItem?.quantityAllocated).toBe(0);
  });

  it('should allocate stock from multiple batches in FEFO order when needed', async () => {
    // Allocate 80 units of stock (which will require multiple batches)
    const allocationResult = await stockAllocationService.allocateStock({
      productId,
      requestedQuantity: 80,
      method: AllocationMethod.FEFO,
      reason: 'Testing FEFO multi-batch allocation',
    }, userId);

    // Verify allocation was successful
    expect(allocationResult.success).toBe(true);
    expect(allocationResult.allocatedQuantity).toBe(80);
    
    // Check that batches were allocated in the correct order (by expiry date)
    expect(allocationResult.batches.length).toBe(2);
    expect(allocationResult.batches[0].inventoryItemId).toBe(nearExpiryBatchId);
    expect(allocationResult.batches[0].allocatedQuantity).toBe(50);
    expect(allocationResult.batches[1].inventoryItemId).toBe(midExpiryBatchId);
    expect(allocationResult.batches[1].allocatedQuantity).toBe(30);
    
    // Verify database was updated correctly
    const nearExpiryItem = await prisma.inventoryItem.findUnique({
      where: { id: nearExpiryBatchId },
    });
    const midExpiryItem = await prisma.inventoryItem.findUnique({
      where: { id: midExpiryBatchId },
    });
    const farExpiryItem = await prisma.inventoryItem.findUnique({
      where: { id: farExpiryBatchId },
    });
    
    expect(nearExpiryItem?.quantityAllocated).toBe(50);
    expect(midExpiryItem?.quantityAllocated).toBe(30);
    expect(farExpiryItem?.quantityAllocated).toBe(0);
  });

  it('should respect FEFO allocation when creating a sale', async () => {
    // Create a sale for the product
    const response = await request(app.getHttpServer())
      .post('/api/sales')
      .set('Authorization', `Bearer ${testContext.users.admin.accessToken}`)
      .send({
        cashierId: userId,
        customerName: 'Test Customer',
        paymentMethod: 'CASH',
        amountPaid: 1000,
        items: [
          {
            productId,
            unitId,
            quantity: 80,
            unitPrice: 10,
          },
        ],
      });

    expect(response.status).toBe(201);
    const sale = response.body;
    
    // Verify that the sale allocated and consumed stock in FEFO order
    const stockMovements = await prisma.stockMovement.findMany({
      where: {
        referenceId: sale.id,
        type: 'ALLOCATION',
      },
      include: {
        inventoryItem: true,
      },
      orderBy: {
        createdAt: 'asc',
      },
    });
    
    // Verify that the first allocation was from the nearest expiry batch
    expect(stockMovements.length).toBeGreaterThan(0);
    expect(stockMovements[0].inventoryItem.id).toBe(nearExpiryBatchId);
    
    // Check inventory items after sale
    const nearExpiryItem = await prisma.inventoryItem.findUnique({
      where: { id: nearExpiryBatchId },
    });
    const midExpiryItem = await prisma.inventoryItem.findUnique({
      where: { id: midExpiryBatchId },
    });
    const farExpiryItem = await prisma.inventoryItem.findUnique({
      where: { id: farExpiryBatchId },
    });
    
    // Near expiry batch should be fully consumed
    expect(nearExpiryItem?.quantityOnHand).toBe(0);
    expect(nearExpiryItem?.quantityAllocated).toBe(0);
    
    // Mid expiry batch should be partially consumed
    expect(midExpiryItem?.quantityOnHand).toBe(20);
    expect(midExpiryItem?.quantityAllocated).toBe(0);
    
    // Far expiry batch should not be affected
    expect(farExpiryItem?.quantityOnHand).toBe(50);
    expect(farExpiryItem?.quantityAllocated).toBe(0);
  });
}); 