import { IntegrationTestSetup, TestContext, expectSuccess, expectValidationError, expectNotFound } from './test-setup';
import { StockAllocationMethod } from '../../src/inventory/dto/stock-consumption.dto';
import { ProductType, ProductCategory, MedicineClassification, UnitType } from '@prisma/client';

describe('Stock Consumption Integration Tests', () => {
  let testSetup: IntegrationTestSetup;
  let ctx: TestContext;
  let testProductId: string;
  let testUnitId: string;
  let testSupplierId: string;
  let testInventoryItems: string[] = [];

  beforeAll(async () => {
    testSetup = new IntegrationTestSetup();
    ctx = await testSetup.setup();

    // Create test dependencies
    const testUnit = await ctx.prisma.productUnit.create({
      data: {
        name: 'Test Tablet',
        abbreviation: 'tab',
        type: UnitType.COUNT,
        isBaseUnit: true,
        description: 'Test tablet unit for consumption',
      },
    });
    testUnitId = testUnit.id;

    const testProduct = await ctx.prisma.product.create({
      data: {
        code: 'TEST-CONS-001',
        name: 'Test Consumption Medicine',
        type: ProductType.MEDICINE,
        category: ProductCategory.ANALGESIC,
        medicineClassification: MedicineClassification.OBAT_BEBAS,
        baseUnitId: testUnitId,
        isActive: true,
        createdBy: ctx.users.admin.id,
      },
    });
    testProductId = testProduct.id;

    const testSupplier = await ctx.prisma.supplier.create({
      data: {
        code: 'TEST-SUP-CONS-001',
        name: 'Test Consumption Supplier',
        type: 'PBF',
        status: 'ACTIVE',
        address: 'Test Address',
        city: 'Jakarta',
        province: 'DKI Jakarta',
        postalCode: '12345',
        phone: '+62 21 1234 5678',
        email: '<EMAIL>',
        npwp: '12.345.678.9-012.345',
        createdBy: ctx.users.admin.id,
      },
    });
    testSupplierId = testSupplier.id;

    // Create multiple inventory batches for testing
    const now = new Date();
    const futureDate1 = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000); // 30 days
    const futureDate2 = new Date(now.getTime() + 60 * 24 * 60 * 60 * 1000); // 60 days
    const futureDate3 = new Date(now.getTime() + 90 * 24 * 60 * 60 * 1000); // 90 days
    const pastDate1 = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000); // 30 days ago
    const pastDate2 = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000); // 60 days ago

    // Batch 1: Oldest received, expires in 30 days (near expiry)
    const batch1 = await ctx.prisma.inventoryItem.create({
      data: {
        productId: testProductId,
        unitId: testUnitId,
        supplierId: testSupplierId,
        batchNumber: 'CONS-BATCH001',
        quantityOnHand: 100,
        costPrice: 5000,
        sellingPrice: 7500,
        location: 'Rak A-1',
        receivedDate: pastDate2,
        expiryDate: futureDate1,
        isActive: true,
        createdBy: ctx.users.admin.id,
      },
    });
    testInventoryItems.push(batch1.id);

    // Batch 2: Middle received, expires in 60 days
    const batch2 = await ctx.prisma.inventoryItem.create({
      data: {
        productId: testProductId,
        unitId: testUnitId,
        supplierId: testSupplierId,
        batchNumber: 'CONS-BATCH002',
        quantityOnHand: 150,
        costPrice: 5500,
        sellingPrice: 8000,
        location: 'Rak A-2',
        receivedDate: pastDate1,
        expiryDate: futureDate2,
        isActive: true,
        createdBy: ctx.users.admin.id,
      },
    });
    testInventoryItems.push(batch2.id);

    // Batch 3: Newest received, expires in 90 days
    const batch3 = await ctx.prisma.inventoryItem.create({
      data: {
        productId: testProductId,
        unitId: testUnitId,
        supplierId: testSupplierId,
        batchNumber: 'CONS-BATCH003',
        quantityOnHand: 200,
        costPrice: 6000,
        sellingPrice: 8500,
        location: 'Rak A-3',
        receivedDate: now,
        expiryDate: futureDate3,
        isActive: true,
        createdBy: ctx.users.admin.id,
      },
    });
    testInventoryItems.push(batch3.id);

    console.log('Stock consumption test setup completed:', {
      testProductId,
      testUnitId,
      testSupplierId,
      testInventoryItems,
    });
  });

  afterAll(async () => {
    await testSetup.teardown();
  });

  describe('POST /api/inventory/consume-stock', () => {
    it('should consume stock using FEFO method', async () => {
      const consumptionData = {
        productId: testProductId,
        requestedQuantity: 80,
        method: StockAllocationMethod.FEFO,
        allowPartialAllocation: true,
        reason: 'Test FEFO consumption',
        notes: 'Testing first expired first out logic',
      };

      const response = await ctx.request
        .post('/api/inventory/consume-stock')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(consumptionData);

      expectSuccess(response, 201);
      expect(response.body.success).toBe(true);
      expect(response.body.allocatedQuantity).toBe(80);
      expect(response.body.method).toBe(StockAllocationMethod.FEFO);
      expect(response.body.batches).toHaveLength(1);
      
      // Should allocate from CONS-BATCH001 (earliest expiry, non-expired)
      expect(response.body.batches[0].batchNumber).toBe('CONS-BATCH001');
      expect(response.body.batches[0].allocatedQuantity).toBe(80);
      expect(response.body.warnings.some((warning: string) => warning.includes('kedaluwarsa dalam'))).toBe(true);
    });

    it('should consume stock using FIFO method', async () => {
      const consumptionData = {
        productId: testProductId,
        requestedQuantity: 50,
        method: StockAllocationMethod.FIFO,
        allowPartialAllocation: true,
        reason: 'Test FIFO consumption',
        notes: 'Testing first in first out logic',
      };

      const response = await ctx.request
        .post('/api/inventory/consume-stock')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(consumptionData);

      expectSuccess(response, 201);
      expect(response.body.success).toBe(true);
      expect(response.body.allocatedQuantity).toBe(50);
      expect(response.body.method).toBe(StockAllocationMethod.FIFO);
      expect(response.body.batches.length).toBeGreaterThanOrEqual(1);
      
      // Should allocate from remaining quantity in CONS-BATCH001 first (oldest received)
      expect(response.body.batches[0].batchNumber).toBe('CONS-BATCH001');
    });

    it('should require manager role for stock consumption', async () => {
      const consumptionData = {
        productId: testProductId,
        requestedQuantity: 10,
        method: StockAllocationMethod.FIFO,
        reason: 'Test authorization',
      };

      const response = await ctx.request
        .post('/api/inventory/consume-stock')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(consumptionData);

      expect(response.status).toBe(403);
    });

    it('should validate consumption data', async () => {
      const invalidData = {
        productId: 'invalid-id',
        requestedQuantity: -10,
        method: 'INVALID_METHOD',
      };

      const response = await ctx.request
        .post('/api/inventory/consume-stock')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(invalidData);

      expectValidationError(response);
    });
  });

  describe('POST /api/inventory/validate-stock-availability', () => {
    it('should validate stock availability for sufficient stock', async () => {
      const availabilityData = {
        productId: testProductId,
        requestedQuantity: 100,
      };

      const response = await ctx.request
        .post('/api/inventory/validate-stock-availability')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(availabilityData);

      expectSuccess(response);
      expect(response.body.isAvailable).toBe(true);
      expect(response.body.totalAvailable).toBeGreaterThanOrEqual(100);
      expect(response.body.requestedQuantity).toBe(100);
      expect(response.body.shortfall).toBe(0);
      expect(response.body.batchCount).toBeGreaterThan(0);
      expect(response.body.batches).toBeInstanceOf(Array);
    });

    it('should validate stock availability for insufficient stock', async () => {
      const availabilityData = {
        productId: testProductId,
        requestedQuantity: 1000, // More than available
      };

      const response = await ctx.request
        .post('/api/inventory/validate-stock-availability')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(availabilityData);

      expectSuccess(response);
      expect(response.body.isAvailable).toBe(false);
      expect(response.body.requestedQuantity).toBe(1000);
      expect(response.body.shortfall).toBeGreaterThan(0);
    });

    it('should handle non-existent product', async () => {
      const availabilityData = {
        productId: 'non-existent-id',
        requestedQuantity: 10,
      };

      const response = await ctx.request
        .post('/api/inventory/validate-stock-availability')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(availabilityData);

      expectNotFound(response);
    });
  });

  describe('GET /api/inventory/products/:productId/available-stock', () => {
    it('should get available stock excluding expired items', async () => {
      const response = await ctx.request
        .get(`/api/inventory/products/${testProductId}/available-stock`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.productId).toBe(testProductId);
      expect(response.body.totalAvailable).toBeGreaterThan(0);
      expect(response.body.includeExpired).toBe(false);
    });

    it('should get available stock including expired items', async () => {
      const response = await ctx.request
        .get(`/api/inventory/products/${testProductId}/available-stock?includeExpired=true`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.productId).toBe(testProductId);
      expect(response.body.totalAvailable).toBeGreaterThan(0);
      expect(response.body.includeExpired).toBe(true);
    });
  });

  describe('POST /api/inventory/preview-stock-consumption', () => {
    it('should preview stock consumption without affecting stock', async () => {
      // First get current stock levels
      const beforeResponse = await ctx.request
        .get(`/api/inventory/products/${testProductId}/available-stock`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      const beforeStock = beforeResponse.body.totalAvailable;

      const consumptionData = {
        productId: testProductId,
        requestedQuantity: 50,
        method: StockAllocationMethod.FEFO,
        reason: 'Test preview consumption',
      };

      const response = await ctx.request
        .post('/api/inventory/preview-stock-consumption')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(consumptionData);

      expectSuccess(response, 201);
      expect(response.body.previewOnly).toBe(true);
      expect(response.body.allocatedQuantity).toBeGreaterThan(0);

      // Verify stock levels haven't changed
      const afterResponse = await ctx.request
        .get(`/api/inventory/products/${testProductId}/available-stock`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expect(afterResponse.body.totalAvailable).toBe(beforeStock);
    });
  });
});
