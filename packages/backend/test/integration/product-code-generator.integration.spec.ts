import { IntegrationTestSetup, TestContext, expectValidationError, expectUnauthorized, expectSuccess } from './test-setup';

describe('Product Code Generator Integration Tests', () => {
  let testSetup: IntegrationTestSetup;
  let ctx: TestContext;
  let testUnitId: string;

  beforeAll(async () => {
    testSetup = new IntegrationTestSetup();
    ctx = await testSetup.setup();

    // Create a test unit for products
    const unit = await ctx.prisma.productUnit.create({
      data: {
        name: 'Test Tablet',
        abbreviation: 'tab',
        type: 'COUNT',
        isBaseUnit: true,
        description: 'Test tablet unit',
      },
    });
    testUnitId = unit.id;
  });

  afterAll(async () => {
    // Clean up test data
    await ctx.prisma.productUnitHierarchy.deleteMany({
      where: {
        product: {
          OR: [
            { createdBy: ctx.users.admin.id },
            { createdBy: ctx.users.pharmacist.id },
          ]
        }
      },
    });

    await ctx.prisma.product.deleteMany({
      where: {
        OR: [
          { createdBy: ctx.users.admin.id },
          { createdBy: ctx.users.pharmacist.id },
        ]
      },
    });

    await ctx.prisma.productUnit.deleteMany({
      where: { id: testUnitId },
    });

    await testSetup.teardown();
  });

  describe('GET /api/products/generate-code/:type', () => {
    it('should generate MEDICINE product code', async () => {
      const response = await ctx.request
        .get('/api/products/generate-code/MEDICINE')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.code).toMatch(/^MED-\d{3}$/);
      expect(response.body.code).toBe('MED-001'); // First MEDICINE code
    });

    it('should generate MEDICAL_DEVICE product code', async () => {
      const response = await ctx.request
        .get('/api/products/generate-code/MEDICAL_DEVICE')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.code).toMatch(/^DEV-\d{3}$/);
      expect(response.body.code).toBe('DEV-001'); // First MEDICAL_DEVICE code
    });

    it('should generate SUPPLEMENT product code', async () => {
      const response = await ctx.request
        .get('/api/products/generate-code/SUPPLEMENT')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.code).toMatch(/^SUP-\d{3}$/);
      expect(response.body.code).toBe('SUP-001'); // First SUPPLEMENT code
    });

    it('should generate COSMETIC product code', async () => {
      const response = await ctx.request
        .get('/api/products/generate-code/COSMETIC')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.code).toMatch(/^COS-\d{3}$/);
      expect(response.body.code).toBe('COS-001'); // First COSMETIC code
    });

    it('should generate GENERAL product code', async () => {
      const response = await ctx.request
        .get('/api/products/generate-code/GENERAL')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.code).toMatch(/^GEN-\d{3}$/);
      expect(response.body.code).toBe('GEN-001'); // First GENERAL code
    });

    it('should increment sequence numbers correctly', async () => {
      // Create a product with MED-001
      await ctx.request
        .post('/api/products')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({
          code: 'MED-001',
          name: 'Test Medicine Product',
          type: 'MEDICINE',
          category: 'ANALGESIC',
          medicineClassification: 'OBAT_BEBAS',
          baseUnitId: testUnitId,
        });

      // Generate next MEDICINE code
      const response = await ctx.request
        .get('/api/products/generate-code/MEDICINE')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.code).toBe('MED-002'); // Should increment to 002
    });

    it('should handle non-sequential existing codes correctly', async () => {
      // Create products with non-sequential codes
      await ctx.request
        .post('/api/products')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({
          code: 'DEV-005',
          name: 'Test Device Product 1',
          type: 'MEDICAL_DEVICE',
          category: 'MEDICAL_DEVICE',
          medicineClassification: 'NON_MEDICINE',
          baseUnitId: testUnitId,
        });

      await ctx.request
        .post('/api/products')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({
          code: 'DEV-003',
          name: 'Test Device Product 2',
          type: 'MEDICAL_DEVICE',
          category: 'MEDICAL_DEVICE',
          medicineClassification: 'NON_MEDICINE',
          baseUnitId: testUnitId,
        });

      // Generate next MEDICAL_DEVICE code
      const response = await ctx.request
        .get('/api/products/generate-code/MEDICAL_DEVICE')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.code).toBe('DEV-006'); // Should be highest + 1
    });

    it('should return 400 for invalid product type', async () => {
      const response = await ctx.request
        .get('/api/products/generate-code/INVALID_TYPE')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectValidationError(response);
      expect(response.body.message).toBe('Jenis produk tidak valid');
    });

    it('should require authentication', async () => {
      const response = await ctx.request
        .get('/api/products/generate-code/MEDICINE');

      expectUnauthorized(response);
    });

    it('should handle different product types independently', async () => {
      // Create products of different types
      await ctx.request
        .post('/api/products')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({
          code: 'MED-003',
          name: 'Medicine Product',
          type: 'MEDICINE',
          category: 'ANALGESIC',
          medicineClassification: 'OBAT_BEBAS',
          baseUnitId: testUnitId,
        });

      await ctx.request
        .post('/api/products')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({
          code: 'SUP-002',
          name: 'Supplement Product',
          type: 'SUPPLEMENT',
          category: 'SUPPLEMENT',
          medicineClassification: 'NON_MEDICINE',
          baseUnitId: testUnitId,
        });

      // Generate codes for different types
      const medicineResponse = await ctx.request
        .get('/api/products/generate-code/MEDICINE')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      const supplementResponse = await ctx.request
        .get('/api/products/generate-code/SUPPLEMENT')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      const cosmeticResponse = await ctx.request
        .get('/api/products/generate-code/COSMETIC')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(medicineResponse);
      expectSuccess(supplementResponse);
      expectSuccess(cosmeticResponse);

      expect(medicineResponse.body.code).toBe('MED-004'); // Next medicine code
      expect(supplementResponse.body.code).toBe('SUP-003'); // Next supplement code
      expect(cosmeticResponse.body.code).toBe('COS-001'); // First cosmetic code
    });
  });

  describe('GET /api/products/validate-code/:code', () => {
    it('should validate unique product code', async () => {
      const response = await ctx.request
        .get('/api/products/validate-code/MED-999')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.isUnique).toBe(true);
    });

    it('should validate duplicate product code', async () => {
      // Create a product first
      await ctx.request
        .post('/api/products')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({
          code: 'MED-100',
          name: 'Test Medicine Product',
          type: 'MEDICINE',
          category: 'ANALGESIC',
          medicineClassification: 'OBAT_BEBAS',
          baseUnitId: testUnitId,
        });

      // Validate the same code
      const response = await ctx.request
        .get('/api/products/validate-code/MED-100')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.isUnique).toBe(false);
    });

    it('should handle URL encoded codes', async () => {
      const response = await ctx.request
        .get('/api/products/validate-code/MED%2D200')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.isUnique).toBe(true);
    });

    it('should require authentication', async () => {
      const response = await ctx.request
        .get('/api/products/validate-code/MED-001');

      expectUnauthorized(response);
    });
  });

  describe('Integration with product creation', () => {
    it('should create product with auto-generated code', async () => {
      // Generate a code first
      const codeResponse = await ctx.request
        .get('/api/products/generate-code/SUPPLEMENT')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      const generatedCode = codeResponse.body.code;

      // Create product with the generated code
      const productResponse = await ctx.request
        .post('/api/products')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({
          code: generatedCode,
          name: 'Auto-Generated Code Product',
          type: 'SUPPLEMENT',
          category: 'SUPPLEMENT',
          medicineClassification: 'NON_MEDICINE',
          baseUnitId: testUnitId,
        });

      expectSuccess(productResponse, 201);
      expect(productResponse.body.code).toBe(generatedCode);
      expect(productResponse.body.name).toBe('Auto-Generated Code Product');
    });

    it('should prevent duplicate codes even with concurrent requests', async () => {
      // This test simulates the scenario where two users try to create products
      // with the same generated code simultaneously
      
      const codeResponse = await ctx.request
        .get('/api/products/generate-code/GENERAL')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      const generatedCode = codeResponse.body.code;

      // Create first product
      const firstResponse = await ctx.request
        .post('/api/products')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({
          code: generatedCode,
          name: 'First Product',
          type: 'GENERAL',
          category: 'OTHER',
          medicineClassification: 'NON_MEDICINE',
          baseUnitId: testUnitId,
        });

      expectSuccess(firstResponse, 201);

      // Try to create second product with same code
      const secondResponse = await ctx.request
        .post('/api/products')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({
          code: generatedCode,
          name: 'Second Product',
          type: 'GENERAL',
          category: 'OTHER',
          medicineClassification: 'NON_MEDICINE',
          baseUnitId: testUnitId,
        });

      expect(secondResponse.status).toBe(409); // Should get conflict error
    });

    it('should generate sequential codes for multiple products', async () => {
      const codes: string[] = [];

      // Generate and create 5 products
      for (let i = 0; i < 5; i++) {
        const codeResponse = await ctx.request
          .get('/api/products/generate-code/COSMETIC')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

        const generatedCode = codeResponse.body.code;
        codes.push(generatedCode);

        await ctx.request
          .post('/api/products')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send({
            code: generatedCode,
            name: `Cosmetic Product ${i + 1}`,
            type: 'COSMETIC',
            category: 'COSMETIC',
            medicineClassification: 'NON_MEDICINE',
            baseUnitId: testUnitId,
          });
      }

      // Verify sequential codes
      expect(codes).toEqual(['COS-001', 'COS-002', 'COS-003', 'COS-004', 'COS-005']);
    });
  });
});
