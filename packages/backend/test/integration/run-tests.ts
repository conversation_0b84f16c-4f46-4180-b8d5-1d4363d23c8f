#!/usr/bin/env ts-node

import { execSync } from 'child_process';
import { PrismaClient } from '@prisma/client';
import { config } from 'dotenv';
import { join } from 'path';
import { readdirSync, existsSync } from 'fs';

// CRITICAL: Load test environment variables with override to ensure precedence
config({ path: join(__dirname, '../../.env.test'), override: true });

// SAFETY CHECK: Ensure we're using the test database
if (!process.env.DATABASE_URL?.includes('_test')) {
  console.error(
    `🚨 CRITICAL ERROR: Test environment is not using a test database!\n` +
      `Current DATABASE_URL: ${process.env.DATABASE_URL}\n` +
      `Expected: A database URL containing '_test'\n` +
      `This prevents accidental data loss in development/production databases.`,
  );
  process.exit(1);
}

interface TestRunOptions {
  testFile?: string;
  coverage?: boolean;
  jestFlags?: string[];
}

class IntegrationTestRunner {
  private prisma: PrismaClient;
  private availableTestFiles: Map<string, string>;

  constructor() {
    this.prisma = new PrismaClient({
      datasources: {
        db: {
          url: process.env.DATABASE_URL,
        },
      },
    });
    this.availableTestFiles = this.discoverTestFiles();
  }

  private discoverTestFiles(): Map<string, string> {
    const testFiles = new Map<string, string>();
    const testDir = __dirname;

    try {
      const files = readdirSync(testDir);

      files
        .filter((file) => file.endsWith('.integration.spec.ts'))
        .forEach((file) => {
          // Extract the test name from filename
          // e.g., 'payment-proof.integration.spec.ts' -> 'payment-proof'
          const testName = file.replace('.integration.spec.ts', '');
          testFiles.set(testName, file);
        });

      return testFiles;
    } catch (error) {
      console.warn(
        'Warning: Could not read test directory, falling back to empty list',
      );
      return new Map();
    }
  }

  private validateTestFile(testFile: string): string {
    // Check if it's a known test name (e.g., 'payment-proof')
    if (this.availableTestFiles.has(testFile)) {
      return this.availableTestFiles.get(testFile)!;
    }

    // Check if it's a direct filename
    const directFileName = testFile.endsWith('.integration.spec.ts')
      ? testFile
      : `${testFile}.integration.spec.ts`;
    const testFilePath = join(__dirname, directFileName);

    if (existsSync(testFilePath)) {
      return directFileName;
    }

    // Show available options
    const availableOptions = Array.from(this.availableTestFiles.keys())
      .sort()
      .join(', ');
    const availableFiles = Array.from(this.availableTestFiles.values())
      .sort()
      .join(', ');

    throw new Error(
      `Test file "${testFile}" not found.\n\n` +
        `Available test names: ${availableOptions}\n` +
        `Available test files: ${availableFiles}\n\n` +
        `Examples:\n` +
        `  pnpm test:integration payment-proof\n` +
        `  pnpm test:integration suppliers\n` +
        `  pnpm test:integration payment-proof.integration.spec.ts`,
    );
  }

  private buildJestCommand(options: TestRunOptions = {}): string {
    const baseCommand =
      'npx jest --config test/integration/jest.integration.config.js';
    const parts = [baseCommand];

    if (options.coverage) {
      parts.push('--coverage');
    }

    if (options.testFile) {
      const fileName = this.validateTestFile(options.testFile);
      const testPattern = `test/integration/${fileName}`;
      parts.push(`--testPathPattern="${testPattern}"`);
    }

    // Add any additional Jest flags passed through CLI
    if (options.jestFlags && options.jestFlags.length > 0) {
      // Handle Jest flags that may contain spaces or special characters
      const processedFlags = options.jestFlags.map((flag) => {
        // If flag contains spaces and isn't already quoted, wrap in quotes
        if (
          flag.includes(' ') &&
          !flag.startsWith('"') &&
          !flag.startsWith("'")
        ) {
          return `"${flag}"`;
        }
        return flag;
      });
      parts.push(...processedFlags);
    }

    return parts.join(' ');
  }

  async setupTestDatabase(): Promise<void> {
    console.log('🗄️  Setting up test database...');

    // FINAL SAFETY CHECK: Verify we're connected to the test database
    const result = await this.prisma.$queryRaw<
      [{ current_database: string }]
    >`SELECT current_database()`;
    const currentDb = result[0]?.current_database;

    if (!currentDb?.includes('_test')) {
      throw new Error(
        `🚨 CRITICAL: Connected to wrong database: ${currentDb}\n` +
          `Expected a database with '_test' suffix.\n` +
          `Aborting to prevent data loss.`,
      );
    }

    console.log(`🔒 Confirmed connection to test database: ${currentDb}`);

    try {
      // Ensure test database exists and is clean
      await this.prisma.$executeRaw`DROP SCHEMA IF EXISTS public CASCADE;`;
      await this.prisma.$executeRaw`CREATE SCHEMA public;`;

      console.log('✅ Test database schema reset');

      // Run migrations with explicit test database URL
      console.log('🔄 Running database migrations...');
      execSync('npx prisma migrate deploy', {
        stdio: 'inherit',
        env: {
          ...process.env,
          DATABASE_URL: process.env.DATABASE_URL,
          NODE_ENV: 'test',
        },
        cwd: join(__dirname, '../../'),
      });

      console.log('✅ Database migrations completed');
    } catch (error) {
      console.error('❌ Failed to setup test database:', error);
      throw error;
    }
  }

  async cleanupTestDatabase(): Promise<void> {
    console.log('🧹 Cleaning up test database...');

    try {
      // SAFETY CHECK: Verify we're still connected to the test database
      const result = await this.prisma.$queryRaw<
        [{ current_database: string }]
      >`SELECT current_database()`;
      const currentDb = result[0]?.current_database;

      if (!currentDb?.includes('_test')) {
        console.error(
          `🚨 CRITICAL: Refusing to cleanup non-test database: ${currentDb}`,
        );
        return; // Don't cleanup if we're not on a test database
      }

      // Clean all tables in correct order
      await this.prisma.stockMovement.deleteMany();
      await this.prisma.inventoryItem.deleteMany();
      await this.prisma.supplierPayment.deleteMany();
      await this.prisma.supplierDocument.deleteMany();
      await this.prisma.supplierContact.deleteMany();
      await this.prisma.supplier.deleteMany();
      await this.prisma.productUnitHierarchy.deleteMany();
      await this.prisma.product.deleteMany();
      await this.prisma.productUnit.deleteMany();
      await this.prisma.appSettings.deleteMany();
      await this.prisma.user.deleteMany();

      console.log('✅ Test database cleaned');
    } catch (error) {
      console.error('❌ Failed to cleanup test database:', error);
      // Don't throw error during cleanup to avoid masking test failures
    } finally {
      await this.prisma.$disconnect();
    }
  }

  async runTests(options: TestRunOptions = {}): Promise<void> {
    if (options.testFile) {
      const fileName = this.validateTestFile(options.testFile);
      console.log(`🧪 Running specific integration test: ${fileName}`);
    } else {
      console.log('🧪 Running all integration tests...');
    }

    try {
      const command = this.buildJestCommand(options);
      execSync(command, {
        stdio: 'inherit',
        cwd: join(__dirname, '../../'),
        env: { ...process.env, NODE_ENV: 'test' },
      });

      if (options.testFile) {
        console.log(
          `✅ Integration test ${options.testFile} completed successfully`,
        );
      } else {
        console.log('✅ All integration tests completed successfully');
      }
    } catch (error) {
      console.error('❌ Integration tests failed');
      throw error;
    }
  }

  async generateCoverageReport(options: TestRunOptions = {}): Promise<void> {
    if (options.testFile) {
      const fileName = this.validateTestFile(options.testFile);
      console.log(`📊 Generating coverage report for: ${fileName}`);
    } else {
      console.log('📊 Generating coverage report...');
    }

    try {
      const command = this.buildJestCommand({ ...options, coverage: true });
      execSync(command, {
        stdio: 'inherit',
        cwd: join(__dirname, '../../'),
        env: { ...process.env, NODE_ENV: 'test' },
      });

      console.log('✅ Coverage report generated');
    } catch (error) {
      console.error('❌ Failed to generate coverage report');
      throw error;
    }
  }

  async validateEnvironment(): Promise<void> {
    console.log('🔍 Validating test environment...');

    // CRITICAL SAFETY CHECKS
    if (process.env.NODE_ENV !== 'test') {
      throw new Error(`NODE_ENV must be 'test', got: ${process.env.NODE_ENV}`);
    }

    if (!process.env.DATABASE_URL?.includes('_test')) {
      throw new Error(
        `CRITICAL: Database URL must contain '_test' for safety.\n` +
          `Current: ${process.env.DATABASE_URL}\n` +
          `This prevents accidental connection to development/production databases.`,
      );
    }

    // Additional safety: Check if we're accidentally connecting to common production/dev database names
    const dangerousDatabases = [
      'apotek',
      'pharmacy',
      'production',
      'prod',
      'main',
    ];
    const dbName = process.env.DATABASE_URL.split('/').pop()?.split('?')[0];
    if (dangerousDatabases.includes(dbName || '')) {
      throw new Error(
        `CRITICAL: Refusing to run tests against potentially dangerous database: ${dbName}\n` +
          `Use a dedicated test database with '_test' suffix.`,
      );
    }

    const requiredEnvVars = ['DATABASE_URL', 'JWT_SECRET', 'JWT_EXPIRES_IN'];

    const missing = requiredEnvVars.filter((envVar) => !process.env[envVar]);

    if (missing.length > 0) {
      throw new Error(
        `Missing required environment variables: ${missing.join(', ')}`,
      );
    }

    console.log(`📊 Test Database: ${process.env.DATABASE_URL}`);
    console.log(
      `🔑 JWT Secret: ${process.env.JWT_SECRET?.substring(0, 10)}...`,
    );

    // Test database connection
    try {
      await this.prisma.$connect();
      await this.prisma.$queryRaw`SELECT 1`;
      console.log('✅ Database connection successful');
    } catch (error) {
      throw new Error(`Database connection failed: ${error}`);
    }
  }

  async run(options: TestRunOptions = {}): Promise<void> {
    const startTime = Date.now();

    try {
      if (options.testFile) {
        console.log(`🚀 Starting Integration Test: ${options.testFile}`);
        console.log('================================');
      } else {
        console.log('🚀 Starting Integration Test Suite');
        console.log('================================');
      }

      await this.validateEnvironment();
      await this.setupTestDatabase();
      await this.runTests(options);

      const duration = (Date.now() - startTime) / 1000;
      console.log('================================');
      if (options.testFile) {
        console.log(
          `✅ Integration test "${options.testFile}" completed successfully in ${duration}s`,
        );
      } else {
        console.log(
          `✅ Integration test suite completed successfully in ${duration}s`,
        );
      }
    } catch (error) {
      console.error('================================');
      console.error('❌ Integration test suite failed:', error);
      process.exit(1);
    } finally {
      await this.cleanupTestDatabase();
    }
  }

  async runWithCoverage(options: TestRunOptions = {}): Promise<void> {
    const startTime = Date.now();

    try {
      if (options.testFile) {
        console.log(
          `🚀 Starting Integration Test with Coverage: ${options.testFile}`,
        );
        console.log('===============================================');
      } else {
        console.log('🚀 Starting Integration Test Suite with Coverage');
        console.log('===============================================');
      }

      await this.validateEnvironment();
      await this.setupTestDatabase();
      await this.generateCoverageReport(options);

      const duration = (Date.now() - startTime) / 1000;
      console.log('===============================================');
      if (options.testFile) {
        console.log(
          `✅ Integration test "${options.testFile}" with coverage completed successfully in ${duration}s`,
        );
      } else {
        console.log(
          `✅ Integration test suite with coverage completed successfully in ${duration}s`,
        );
      }
    } catch (error) {
      console.error('===============================================');
      console.error('❌ Integration test suite failed:', error);
      process.exit(1);
    } finally {
      await this.cleanupTestDatabase();
    }
  }
}

// CLI interface
async function main() {
  const runner = new IntegrationTestRunner();
  const args = process.argv.slice(2);

  // Parse arguments
  const options: TestRunOptions = {};

  // Separate test runner flags from Jest flags
  const testRunnerFlags = ['--coverage', '--help', '-h'];
  const jestFlags: string[] = [];
  let testFile: string | undefined;

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];

    if (testRunnerFlags.includes(arg)) {
      // Handle test runner specific flags
      if (arg === '--coverage') {
        options.coverage = true;
      }
    } else if (arg.startsWith('--')) {
      // Handle Jest flags
      if (arg.includes('=')) {
        // Handle --flag=value syntax
        jestFlags.push(arg);
      } else {
        // Handle --flag value syntax
        jestFlags.push(arg);

        // Check if this flag expects a value (next arg doesn't start with -- and isn't a test runner flag)
        if (
          i + 1 < args.length &&
          !args[i + 1].startsWith('--') &&
          !testRunnerFlags.includes(args[i + 1]) &&
          !testFile
        ) {
          // Check if the next argument looks like a flag value rather than a test file
          const nextArg = args[i + 1];

          // If it contains spaces, quotes, or special jest patterns, it's likely a flag value
          if (
            nextArg.includes(' ') ||
            nextArg.includes('"') ||
            nextArg.includes("'") ||
            nextArg.includes('*') ||
            nextArg.includes('should ') ||
            nextArg.includes('describe ') ||
            nextArg.includes('it ')
          ) {
            i++; // Skip the next argument as it's the value for this flag
            jestFlags.push(nextArg);
          }
        }
      }
    } else if (!testFile) {
      // First non-flag argument is the test file
      testFile = arg;
    } else {
      // Additional non-flag arguments are passed to Jest
      jestFlags.push(arg);
    }
  }

  if (testFile) {
    options.testFile = testFile;
  }

  if (jestFlags.length > 0) {
    options.jestFlags = jestFlags;
  }

  // Show help if requested
  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
Integration Test Runner

Usage:
  bun test:integration [test-name] [--coverage] [jest-flags...]

Examples:
  bun test:integration                                    # Run all tests
  bun test:integration payment-proof                     # Run payment-proof tests only
  bun test:integration suppliers                         # Run suppliers tests only
  bun test:integration --coverage                        # Run all tests with coverage
  bun test:integration auth --coverage                   # Run auth tests with coverage

  # Using Jest flags:
  bun test:integration sale-workflow --testNamePattern="should successfully create sale"
  bun test:integration bpom-compliance --testNamePattern="should perform BPOM compliance check"
  bun test:integration payment-proof --testNamePattern "should process payment successfully"
  bun test:integration --verbose                         # Run with verbose output
  bun test:integration payment-proof --bail              # Stop on first failure
  bun test:integration --detectOpenHandles               # Detect handles preventing Jest from exiting
  bun test:integration suppliers --maxWorkers=1          # Run tests serially

  # Advanced Jest flag combinations:
  bun test:integration bpom-compliance --testNamePattern="should perform BPOM compliance check for regular medicine" --verbose
  bun test:integration suppliers --testNamePattern="should validate" --bail --maxWorkers=1
  bun test:integration payment-proof --testNamePattern "should process" --detectOpenHandles --verbose
  bun test:integration --runInBand --verbose             # Run all tests serially with verbose output
  bun test:integration sale-workflow --testNamePattern="create.*sale" --silent  # Using regex patterns

Test Runner Flags:
  --coverage                                              # Generate coverage report
  --help, -h                                             # Show this help message

Jest Flags (passed through to Jest):
  --testNamePattern=<pattern>                            # Run tests matching pattern (supports regex)
  --testNamePattern "<pattern>"                          # Alternative syntax with spaces in pattern
  --verbose                                              # Display individual test results
  --bail                                                 # Stop after first test failure
  --detectOpenHandles                                    # Detect handles preventing exit
  --maxWorkers=<num>                                     # Number of worker processes (e.g., --maxWorkers=1)
  --runInBand                                            # Run tests serially (equivalent to --maxWorkers=1)
  --silent                                               # Prevent tests from printing messages
  --updateSnapshot                                       # Update snapshots
  --watchAll                                             # Watch all files for changes
  --coverage                                             # Generate coverage report (Note: use test runner --coverage instead)
  --testTimeout=<ms>                                     # Set test timeout in milliseconds
  --passWithNoTests                                      # Allow empty test suites to pass
  --forceExit                                            # Force Jest to exit after tests complete
  And many more Jest CLI options... (see https://jestjs.io/docs/cli)

Note: The enhanced script now supports both --flag=value and --flag value syntax.
      Quoted values with spaces are automatically handled correctly.

Available test files:`);

    try {
      const testFiles = Array.from(runner['availableTestFiles'].keys()).sort();
      testFiles.forEach((name) => console.log(`  ${name}`));
    } catch (error) {
      console.log('  (Could not discover test files)');
    }

    process.exit(0);
  }

  // Debug output for Jest flags if any are provided
  if (options.jestFlags && options.jestFlags.length > 0) {
    console.log('🔧 Jest flags detected:');
    options.jestFlags.forEach((flag, index) => {
      console.log(`  ${index + 1}. ${flag}`);
    });
    console.log('');
  }

  // Run tests
  if (options.coverage) {
    await runner.runWithCoverage(options);
  } else {
    await runner.run(options);
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

export { IntegrationTestRunner };
