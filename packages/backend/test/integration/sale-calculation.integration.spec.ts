import { IntegrationTestSetup, TestContext } from './test-setup';
import { PaymentMethod } from '@prisma/client';

describe('Sale Calculations', () => {
  let testContext: TestContext;
  let testSetup: IntegrationTestSetup;
  
  // Test data
  let productId: string;
  let unitId: string;
  let supplierId: string;
  let inventoryItemId: string;
  const productPrice = 10000; // Rp 10,000
  const taxRate = 0.11; // 11% tax rate

  beforeAll(async () => {
    // Set up test environment
    testSetup = new IntegrationTestSetup();
    testContext = await testSetup.setup();
  });

  afterAll(async () => {
    await testSetup.teardown();
  });

  beforeEach(async () => {
    // Create test data for each test
    const { prisma, users } = testContext;
    
    // Create product unit
    const unit = await testSetup.createTestProductUnit(users.admin.id);
    unitId = unit.id;
    
    // Create product
    const product = await testSetup.createTestProduct(unitId, users.admin.id);
    productId = product.id;
    
    // Create supplier with a unique code
    const timestamp = Date.now();
    const supplier = await prisma.supplier.create({
      data: {
        code: `TEST${timestamp}`,
        name: 'Test Supplier',
        type: 'PBF',
        status: 'ACTIVE',
        address: 'Test Address',
        city: 'Jakarta',
        province: 'DKI Jakarta',
        postalCode: '12345',
        phone: '+62 21 1234 5678',
        email: '<EMAIL>',
        npwp: '12.345.678.9-012.345',
        createdBy: users.admin.id,
      },
    });
    supplierId = supplier.id;
    
    // Create inventory item with stock
    const inventoryItem = await testSetup.createTestInventoryItem(
      productId, 
      unitId, 
      supplierId, 
      users.admin.id
    );
    inventoryItemId = inventoryItem.id;
    
    // Update inventory item price to our test price
    await prisma.inventoryItem.update({
      where: { id: inventoryItemId },
      data: {
        sellingPrice: productPrice,
        quantityOnHand: 100 // Ensure enough stock
      }
    });
  });

  describe('Test Case 1: Simple Sale', () => {
    it('should calculate subtotal, tax, and total correctly for multiple items', async () => {
      const { request, users } = testContext;
      const cashierId = users.cashier.id;
      
      // Create sale with multiple items
      const quantity1 = 2;
      const quantity2 = 3;
      const totalQuantity = quantity1 + quantity2;
      const expectedSubtotal = productPrice * totalQuantity;
      const expectedTax = Math.round(expectedSubtotal * taxRate);
      const expectedTotal = expectedSubtotal + expectedTax;
      
      const response = await request
        .post('/api/sales')
        .set('Authorization', `Bearer ${users.cashier.accessToken}`)
        .send({
          cashierId,
          paymentMethod: PaymentMethod.CASH,
          amountPaid: expectedTotal,
          taxAmount: expectedTax,
          items: [
            {
              productId,
              unitId,
              quantity: quantity1,
              unitPrice: productPrice
            },
            {
              productId,
              unitId,
              quantity: quantity2,
              unitPrice: productPrice
            }
          ]
        });
      
      // Assert response
      expect(response.status).toBe(201);
      expect(response.body).toBeDefined();
      
      // Assert calculations
      expect(response.body.subtotal).toBe(expectedSubtotal);
      expect(response.body.taxAmount).toBe(expectedTax);
      expect(response.body.totalAmount).toBe(expectedTotal);
      expect(response.body.changeAmount).toBe(0); // Exact payment
    });
  });

  describe('Test Case 2: Sale with Discount', () => {
    it('should apply percentage discount correctly', async () => {
      const { request, users } = testContext;
      const cashierId = users.cashier.id;
      
      const quantity = 5;
      const rawSubtotal = productPrice * quantity; // Raw subtotal before discount
      const discountPercentage = 10; // 10% discount
      const discountAmount = Math.round(rawSubtotal * (discountPercentage / 100));
      const subtotalAfterDiscount = rawSubtotal - discountAmount; // This is what the API returns as "subtotal"
      const taxAmount = Math.round(subtotalAfterDiscount * taxRate);
      const expectedTotal = subtotalAfterDiscount + taxAmount;
      
      const response = await request
        .post('/api/sales')
        .set('Authorization', `Bearer ${users.cashier.accessToken}`)
        .send({
          cashierId,
          paymentMethod: PaymentMethod.CASH,
          amountPaid: expectedTotal,
          taxAmount,
          discountType: 'PERCENTAGE',
          discountValue: discountPercentage,
          items: [
            {
              productId,
              unitId,
              quantity,
              unitPrice: productPrice
            }
          ]
        });
      
      // Assert response
      expect(response.status).toBe(201);
      expect(response.body).toBeDefined();
      
      // Assert calculations - note that the API returns the subtotal AFTER discount
      expect(response.body.subtotal).toBe(subtotalAfterDiscount);
      expect(response.body.discountAmount).toBe(discountAmount);
      expect(response.body.taxAmount).toBe(taxAmount);
      expect(response.body.totalAmount).toBe(expectedTotal);
    });

    it('should apply fixed amount discount correctly', async () => {
      const { request, users } = testContext;
      const cashierId = users.cashier.id;
      
      const quantity = 4;
      const rawSubtotal = productPrice * quantity; // Raw subtotal before discount
      const fixedDiscount = 15000; // Rp 15,000 discount
      const subtotalAfterDiscount = rawSubtotal - fixedDiscount; // This is what the API returns as "subtotal"
      const taxAmount = Math.round(subtotalAfterDiscount * taxRate);
      const expectedTotal = subtotalAfterDiscount + taxAmount;
      
      const response = await request
        .post('/api/sales')
        .set('Authorization', `Bearer ${users.cashier.accessToken}`)
        .send({
          cashierId,
          paymentMethod: PaymentMethod.CASH,
          amountPaid: expectedTotal,
          taxAmount,
          discountType: 'FIXED_AMOUNT',
          discountValue: fixedDiscount,
          items: [
            {
              productId,
              unitId,
              quantity,
              unitPrice: productPrice
            }
          ]
        });
      
      // Assert response
      expect(response.status).toBe(201);
      expect(response.body).toBeDefined();
      
      // Assert calculations - note that the API returns the subtotal AFTER discount
      expect(response.body.subtotal).toBe(subtotalAfterDiscount);
      expect(response.body.discountAmount).toBe(fixedDiscount);
      expect(response.body.taxAmount).toBe(taxAmount);
      expect(response.body.totalAmount).toBe(expectedTotal);
    });
  });

  describe('Test Case 3: Change Calculation', () => {
    it('should calculate change correctly for cash payment', async () => {
      const { request, users } = testContext;
      const cashierId = users.cashier.id;
      
      const quantity = 3;
      const subtotal = productPrice * quantity;
      const taxAmount = Math.round(subtotal * taxRate);
      const totalAmount = subtotal + taxAmount;
      const amountPaid = totalAmount + 20000; // Pay 20,000 more
      const expectedChange = amountPaid - totalAmount;
      
      const response = await request
        .post('/api/sales')
        .set('Authorization', `Bearer ${users.cashier.accessToken}`)
        .send({
          cashierId,
          paymentMethod: PaymentMethod.CASH,
          amountPaid,
          taxAmount,
          items: [
            {
              productId,
              unitId,
              quantity,
              unitPrice: productPrice
            }
          ]
        });
      
      // Assert response
      expect(response.status).toBe(201);
      expect(response.body).toBeDefined();
      
      // Assert calculations
      expect(response.body.subtotal).toBe(subtotal);
      expect(response.body.taxAmount).toBe(taxAmount);
      expect(response.body.totalAmount).toBe(totalAmount);
      expect(response.body.amountPaid).toBe(amountPaid);
      expect(response.body.changeAmount).toBe(expectedChange);
    });
  });

  describe('Test Case 4: Item-level Discounts', () => {
    it('should apply item-level percentage discounts correctly', async () => {
      const { request, users } = testContext;
      const cashierId = users.cashier.id;
      
      const quantity = 2;
      const itemDiscountPercentage = 5; // 5% discount on item
      
      const itemSubtotal = productPrice * quantity;
      const itemDiscountAmount = Math.round(itemSubtotal * (itemDiscountPercentage / 100));
      const itemTotalAfterDiscount = itemSubtotal - itemDiscountAmount;
      
      const taxAmount = Math.round(itemTotalAfterDiscount * taxRate);
      const expectedTotal = itemTotalAfterDiscount + taxAmount;
      
      const response = await request
        .post('/api/sales')
        .set('Authorization', `Bearer ${users.cashier.accessToken}`)
        .send({
          cashierId,
          paymentMethod: PaymentMethod.CASH,
          amountPaid: expectedTotal,
          taxAmount,
          items: [
            {
              productId,
              unitId,
              quantity,
              unitPrice: productPrice,
              discountType: 'PERCENTAGE',
              discountValue: itemDiscountPercentage
            }
          ]
        });
      
      // Assert response
      expect(response.status).toBe(201);
      expect(response.body).toBeDefined();
      
      // Assert calculations
      expect(response.body.subtotal).toBe(itemTotalAfterDiscount);
      expect(response.body.taxAmount).toBe(taxAmount);
      expect(response.body.totalAmount).toBe(expectedTotal);
      
      // Note: Sale items are not included in the response by default
    });

    it('should apply item-level fixed discounts correctly', async () => {
      const { request, users } = testContext;
      const cashierId = users.cashier.id;
      
      const quantity = 3;
      const itemFixedDiscount = 2000; // Rp 2,000 discount per item
      
      const itemSubtotal = productPrice * quantity;
      const itemDiscountAmount = itemFixedDiscount;
      const itemTotalAfterDiscount = itemSubtotal - itemDiscountAmount;
      
      const taxAmount = Math.round(itemTotalAfterDiscount * taxRate);
      const expectedTotal = itemTotalAfterDiscount + taxAmount;
      
      const response = await request
        .post('/api/sales')
        .set('Authorization', `Bearer ${users.cashier.accessToken}`)
        .send({
          cashierId,
          paymentMethod: PaymentMethod.CASH,
          amountPaid: expectedTotal,
          taxAmount,
          items: [
            {
              productId,
              unitId,
              quantity,
              unitPrice: productPrice,
              discountType: 'FIXED_AMOUNT',
              discountValue: itemFixedDiscount
            }
          ]
        });
      
      // Assert response
      expect(response.status).toBe(201);
      expect(response.body).toBeDefined();
      
      // Assert calculations
      expect(response.body.subtotal).toBe(itemTotalAfterDiscount);
      expect(response.body.taxAmount).toBe(taxAmount);
      expect(response.body.totalAmount).toBe(expectedTotal);
      
      // Note: Sale items are not included in the response by default
    });
  });

  describe('Test Case 5: Combined Discounts', () => {
    it('should apply both item-level and sale-level discounts correctly', async () => {
      const { request, users } = testContext;
      const cashierId = users.cashier.id;
      
      // Item 1 with discount
      const quantity1 = 2;
      const itemDiscountPercentage = 5; // 5% discount on item
      const item1Subtotal = productPrice * quantity1;
      const item1DiscountAmount = Math.round(item1Subtotal * (itemDiscountPercentage / 100));
      const item1TotalAfterDiscount = item1Subtotal - item1DiscountAmount;
      
      // Item 2 without discount
      const quantity2 = 1;
      const item2Subtotal = productPrice * quantity2;
      
      // Sale-level discount
      const rawSubtotal = item1TotalAfterDiscount + item2Subtotal;
      const saleDiscountPercentage = 10; // 10% discount on entire sale
      const saleDiscountAmount = Math.round(rawSubtotal * (saleDiscountPercentage / 100));
      const subtotalAfterAllDiscounts = rawSubtotal - saleDiscountAmount;
      
      const taxAmount = Math.round(subtotalAfterAllDiscounts * taxRate);
      const expectedTotal = subtotalAfterAllDiscounts + taxAmount;
      
      const response = await request
        .post('/api/sales')
        .set('Authorization', `Bearer ${users.cashier.accessToken}`)
        .send({
          cashierId,
          paymentMethod: PaymentMethod.CASH,
          amountPaid: expectedTotal,
          taxAmount,
          discountType: 'PERCENTAGE',
          discountValue: saleDiscountPercentage,
          items: [
            {
              productId,
              unitId,
              quantity: quantity1,
              unitPrice: productPrice,
              discountType: 'PERCENTAGE',
              discountValue: itemDiscountPercentage
            },
            {
              productId,
              unitId,
              quantity: quantity2,
              unitPrice: productPrice
            }
          ]
        });
      
      // Assert response
      expect(response.status).toBe(201);
      expect(response.body).toBeDefined();
      
      // Assert calculations
      expect(response.body.subtotal).toBe(subtotalAfterAllDiscounts);
      expect(response.body.discountAmount).toBe(saleDiscountAmount);
      expect(response.body.taxAmount).toBe(taxAmount);
      expect(response.body.totalAmount).toBe(expectedTotal);
      
      // Note: Sale items are not included in the response by default
    });
  });

  describe('Test Case 6: Rounding and Large Numbers', () => {
    it('should handle large numbers and rounding correctly', async () => {
      const { request, users, prisma } = testContext;
      const cashierId = users.cashier.id;
      
      // Create a product with a large price
      const largePrice = 9999999; // Almost 10 million Rupiah
      
      // Update inventory item price to large price
      await prisma.inventoryItem.update({
        where: { id: inventoryItemId },
        data: {
          sellingPrice: largePrice
        }
      });
      
      const quantity = 5;
      const subtotal = largePrice * quantity;
      const taxAmount = Math.round(subtotal * taxRate);
      const totalAmount = subtotal + taxAmount;
      
      const response = await request
        .post('/api/sales')
        .set('Authorization', `Bearer ${users.cashier.accessToken}`)
        .send({
          cashierId,
          paymentMethod: PaymentMethod.CASH,
          amountPaid: totalAmount,
          taxAmount,
          items: [
            {
              productId,
              unitId,
              quantity,
              unitPrice: largePrice
            }
          ]
        });
      
      // Assert response
      expect(response.status).toBe(201);
      expect(response.body).toBeDefined();
      
      // Assert calculations
      expect(response.body.subtotal).toBe(subtotal);
      expect(response.body.taxAmount).toBe(taxAmount);
      expect(response.body.totalAmount).toBe(totalAmount);
      
      // Verify no overflow or precision issues
      expect(Number.isInteger(response.body.subtotal)).toBe(true);
      expect(Number.isInteger(response.body.taxAmount)).toBe(true);
      expect(Number.isInteger(response.body.totalAmount)).toBe(true);
    });
    
    it('should handle fractional tax amounts correctly', async () => {
      const { request, users, prisma } = testContext;
      const cashierId = users.cashier.id;
      
      // Use a price that will cause fractional tax
      const oddPrice = 9091; // This will create a tax with decimals: 9091 * 0.11 = 999.01
      
      // Update inventory item price
      await prisma.inventoryItem.update({
        where: { id: inventoryItemId },
        data: {
          sellingPrice: oddPrice
        }
      });
      
      const quantity = 1;
      const subtotal = oddPrice * quantity;
      const taxAmount = Math.round(subtotal * taxRate); // Should be 1000 after rounding
      const totalAmount = subtotal + taxAmount;
      
      const response = await request
        .post('/api/sales')
        .set('Authorization', `Bearer ${users.cashier.accessToken}`)
        .send({
          cashierId,
          paymentMethod: PaymentMethod.CASH,
          amountPaid: totalAmount,
          taxAmount,
          items: [
            {
              productId,
              unitId,
              quantity,
              unitPrice: oddPrice
            }
          ]
        });
      
      // Assert response
      expect(response.status).toBe(201);
      expect(response.body).toBeDefined();
      
      // Assert calculations
      expect(response.body.subtotal).toBe(subtotal);
      expect(response.body.taxAmount).toBe(taxAmount);
      expect(response.body.totalAmount).toBe(totalAmount);
      
      // Verify tax was properly rounded
      expect(response.body.taxAmount).toBe(1000); // 999.01 rounded to 1000
    });
  });

  describe('Test Case 7: Edge Cases', () => {
    it('should handle 100% discount correctly', async () => {
      const { request, users } = testContext;
      const cashierId = users.cashier.id;
      
      const quantity = 2;
      const rawSubtotal = productPrice * quantity;
      const discountPercentage = 100; // 100% discount (free)
      const discountAmount = rawSubtotal; // Full amount discount
      const subtotalAfterDiscount = 0; // Should be zero
      const taxAmount = 0; // No tax on zero amount
      const expectedTotal = 0; // Total should be zero
      
      const response = await request
        .post('/api/sales')
        .set('Authorization', `Bearer ${users.cashier.accessToken}`)
        .send({
          cashierId,
          paymentMethod: PaymentMethod.CASH,
          amountPaid: 0,
          taxAmount,
          discountType: 'PERCENTAGE',
          discountValue: discountPercentage,
          items: [
            {
              productId,
              unitId,
              quantity,
              unitPrice: productPrice
            }
          ]
        });
      
      // Assert response
      expect(response.status).toBe(201);
      expect(response.body).toBeDefined();
      
      // Assert calculations
      expect(response.body.subtotal).toBe(subtotalAfterDiscount);
      expect(response.body.discountAmount).toBe(discountAmount);
      expect(response.body.taxAmount).toBe(taxAmount);
      expect(response.body.totalAmount).toBe(expectedTotal);
      expect(response.body.changeAmount).toBe(0);
    });

    it('should reject negative quantities', async () => {
      const { request, users } = testContext;
      const cashierId = users.cashier.id;
      
      const response = await request
        .post('/api/sales')
        .set('Authorization', `Bearer ${users.cashier.accessToken}`)
        .send({
          cashierId,
          paymentMethod: PaymentMethod.CASH,
          amountPaid: 10000,
          items: [
            {
              productId,
              unitId,
              quantity: -1, // Negative quantity
              unitPrice: productPrice
            }
          ]
        });
      
      // Should be rejected with validation error
      expect(response.status).toBe(400);
    });
    
    it('should handle discount greater than subtotal for fixed discount (discount should be capped at subtotal)', async () => {
      const { request, users } = testContext;
      const cashierId = users.cashier.id;
      
      const quantity = 1;
      const subtotal = productPrice * quantity;
      const fixedDiscount = subtotal + 5000; // Discount more than the subtotal
      const expectedDiscountAmount = subtotal; // Discount should be capped at subtotal
      const subtotalAfterDiscount = 0; // Should be zero, not negative
      const taxAmount = 0; // No tax on zero amount
      
      const response = await request
        .post('/api/sales')
        .set('Authorization', `Bearer ${users.cashier.accessToken}`)
        .send({
          cashierId,
          paymentMethod: PaymentMethod.CASH,
          amountPaid: 0,
          taxAmount,
          discountType: 'FIXED_AMOUNT',
          discountValue: fixedDiscount,
          items: [
            {
              productId,
              unitId,
              quantity,
              unitPrice: productPrice
            }
          ]
        });
      
      // System should accept the request but cap the discount at the subtotal
      expect(response.status).toBe(201);
      
      // Verify that the subtotal is zero (not negative)
      expect(response.body.subtotal).toBe(subtotalAfterDiscount);
      expect(response.body.subtotal).toBe(0);
      expect(response.body.discountAmount).toBe(expectedDiscountAmount);
      
      // Discount has been properly capped at the subtotal value
      console.log('✅ Fixed: The system now caps discounts at the subtotal value, preventing negative prices');
    });
    
    // Bug-finder test: This test ensures the discount validation remains in place
    it('should prevent negative prices by capping discounts (regression test)', async () => {
      const { request, users } = testContext;
      const cashierId = users.cashier.id;
      
      // Test with various discount scenarios that could potentially cause negative prices
      const testCases = [
        {
          name: 'Sale-level fixed discount greater than subtotal',
          data: {
            cashierId,
            paymentMethod: PaymentMethod.CASH,
            amountPaid: 0,
            taxAmount: 0,
            discountType: 'FIXED_AMOUNT',
            discountValue: productPrice * 2, // Double the subtotal
            items: [{ productId, unitId, quantity: 1, unitPrice: productPrice }]
          },
          expectedDiscountAmount: productPrice // Should be capped at subtotal
        },
        {
          name: 'Sale-level percentage discount of 200%',
          data: {
            cashierId,
            paymentMethod: PaymentMethod.CASH,
            amountPaid: 0,
            taxAmount: 0,
            discountType: 'PERCENTAGE',
            discountValue: 200, // 200% discount
            items: [{ productId, unitId, quantity: 1, unitPrice: productPrice }]
          },
          expectedDiscountAmount: productPrice // Should be capped at 100%
        },
        {
          name: 'Item-level fixed discount greater than item total',
          data: {
            cashierId,
            paymentMethod: PaymentMethod.CASH,
            amountPaid: 0,
            taxAmount: 0,
            items: [{ 
              productId, 
              unitId, 
              quantity: 1, 
              unitPrice: productPrice,
              discountType: 'FIXED_AMOUNT',
              discountValue: productPrice + 1000 // More than item price
            }]
          },
          expectedItemDiscountAmount: productPrice // Should be capped at item total
        },
        {
          name: 'Item-level percentage discount of 150%',
          data: {
            cashierId,
            paymentMethod: PaymentMethod.CASH,
            amountPaid: 0,
            taxAmount: 0,
            items: [{ 
              productId, 
              unitId, 
              quantity: 1, 
              unitPrice: productPrice,
              discountType: 'PERCENTAGE',
              discountValue: 150 // 150% discount
            }]
          },
          expectedItemDiscountAmount: productPrice // Should be capped at 100%
        }
      ];
      
      // Run all test cases
      for (const testCase of testCases) {
        console.log(`Testing: ${testCase.name}`);
        
        const response = await request
          .post('/api/sales')
          .set('Authorization', `Bearer ${users.cashier.accessToken}`)
          .send(testCase.data);
        
        // Request should be accepted
        expect(response.status).toBe(201);
        
        // Subtotal should never be negative
        expect(response.body.subtotal).toBeGreaterThanOrEqual(0);
        
        // If we're testing a sale-level discount
        if (testCase.expectedDiscountAmount !== undefined) {
          expect(response.body.discountAmount).toBe(testCase.expectedDiscountAmount);
        }
        
        // For item-level discount tests, we need to fetch the sale with items
        if (testCase.expectedItemDiscountAmount !== undefined) {
          const saleResponse = await request
            .get(`/api/sales/${response.body.id}`)
            .set('Authorization', `Bearer ${users.cashier.accessToken}`);
          
          expect(saleResponse.status).toBe(200);
          expect(saleResponse.body.saleItems).toBeDefined();
          expect(saleResponse.body.saleItems.length).toBeGreaterThan(0);
          
          // Check item discount amount
          const item = saleResponse.body.saleItems[0];
          expect(item.discountAmount).toBe(testCase.expectedItemDiscountAmount);
        }
      }
      
      console.log('✅ Regression test passed: The system properly prevents negative prices by capping discounts');
    });
  });
}); 