import { IntegrationTestSetup, TestContext, expectUnauthorized, expectForbidden, expectSuccess } from './test-setup';

describe('Settings Integration Tests', () => {
  let testSetup: IntegrationTestSetup;
  let ctx: TestContext;

  beforeAll(async () => {
    testSetup = new IntegrationTestSetup();
    ctx = await testSetup.setup();
    await testSetup.seedPharmacySettings();
  });

  afterAll(async () => {
    await testSetup.teardown();
  });

  describe('GET /api/settings/pharmacy', () => {
    it('should return pharmacy settings for authenticated admin', async () => {
      const response = await ctx.request
        .get('/api/settings/pharmacy')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body).toHaveProperty('pharmacyName');
      expect(response.body).toHaveProperty('pharmacyDescription');
      expect(response.body).toHaveProperty('pharmacyAddress');
      expect(response.body).toHaveProperty('pharmacyPhone');
      expect(response.body).toHaveProperty('pharmacyLicense');
      expect(response.body).toHaveProperty('operatingHours');
      expect(response.body).toHaveProperty('emergencyContact');
      
      expect(response.body.pharmacyName).toBe('Test Pharmacy');
      expect(response.body.pharmacyDescription).toBe('Test pharmacy description');
    });

    it('should return pharmacy settings for authenticated pharmacist', async () => {
      const response = await ctx.request
        .get('/api/settings/pharmacy')
        .set('Authorization', `Bearer ${ctx.users.pharmacist.accessToken}`);

      expectSuccess(response);
      expect(response.body.pharmacyName).toBe('Test Pharmacy');
    });

    it('should return pharmacy settings for authenticated cashier', async () => {
      const response = await ctx.request
        .get('/api/settings/pharmacy')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      expect(response.body.pharmacyName).toBe('Test Pharmacy');
    });

    it('should fail without authentication', async () => {
      const response = await ctx.request
        .get('/api/settings/pharmacy');

      expectUnauthorized(response);
    });

    it('should fail with invalid token', async () => {
      const response = await ctx.request
        .get('/api/settings/pharmacy')
        .set('Authorization', 'Bearer invalid-token');

      expectUnauthorized(response);
    });

    it('should return empty object when no settings exist', async () => {
      // Clear all settings
      await ctx.prisma.appSettings.deleteMany();

      const response = await ctx.request
        .get('/api/settings/pharmacy')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body).toEqual({});

      // Restore settings for other tests
      await testSetup.seedPharmacySettings();
    });
  });

  describe('PUT /api/settings/pharmacy', () => {
    it('should update pharmacy settings for admin', async () => {
      const updateData = {
        pharmacyName: 'Updated Pharmacy Name',
        pharmacyDescription: 'Updated description',
        pharmacyAddress: 'Updated Address 789',
        pharmacyPhone: '+62 21 9999 8888',
        pharmacyLicense: 'UPDATED.LICENSE.456',
        operatingHours: 'Mon-Sun: 24/7',
        emergencyContact: '+62 812 9999 7777',
      };

      const response = await ctx.request
        .put('/api/settings/pharmacy')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(updateData);

      expectSuccess(response);
      expect(response.body.pharmacyName).toBe(updateData.pharmacyName);
      expect(response.body.pharmacyDescription).toBe(updateData.pharmacyDescription);
      expect(response.body.pharmacyAddress).toBe(updateData.pharmacyAddress);
      expect(response.body.pharmacyPhone).toBe(updateData.pharmacyPhone);
      expect(response.body.pharmacyLicense).toBe(updateData.pharmacyLicense);
      expect(response.body.operatingHours).toBe(updateData.operatingHours);
      expect(response.body.emergencyContact).toBe(updateData.emergencyContact);
    });

    it('should update partial pharmacy settings for admin', async () => {
      const updateData = {
        pharmacyName: 'Partially Updated Name',
        pharmacyPhone: '+62 21 5555 4444',
      };

      const response = await ctx.request
        .put('/api/settings/pharmacy')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(updateData);

      expectSuccess(response);
      expect(response.body.pharmacyName).toBe(updateData.pharmacyName);
      expect(response.body.pharmacyPhone).toBe(updateData.pharmacyPhone);
      // Other fields should remain unchanged
      expect(response.body.pharmacyDescription).toBe('Updated description');
    });

    it('should create new settings if they do not exist', async () => {
      // Clear all settings
      await ctx.prisma.appSettings.deleteMany();

      const newSettings = {
        pharmacyName: 'New Pharmacy',
        pharmacyAddress: 'New Address 123',
      };

      const response = await ctx.request
        .put('/api/settings/pharmacy')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(newSettings);

      expectSuccess(response);
      expect(response.body.pharmacyName).toBe(newSettings.pharmacyName);
      expect(response.body.pharmacyAddress).toBe(newSettings.pharmacyAddress);
    });

    it('should fail for pharmacist (not admin)', async () => {
      const updateData = {
        pharmacyName: 'Unauthorized Update',
      };

      const response = await ctx.request
        .put('/api/settings/pharmacy')
        .set('Authorization', `Bearer ${ctx.users.pharmacist.accessToken}`)
        .send(updateData);

      expectForbidden(response);
      expect(response.body.message).toContain('Admin access required');
    });

    it('should fail for cashier (not admin)', async () => {
      const updateData = {
        pharmacyName: 'Unauthorized Update',
      };

      const response = await ctx.request
        .put('/api/settings/pharmacy')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(updateData);

      expectForbidden(response);
      expect(response.body.message).toContain('Admin access required');
    });

    it('should fail without authentication', async () => {
      const response = await ctx.request
        .put('/api/settings/pharmacy')
        .send({ pharmacyName: 'Test' });

      expectUnauthorized(response);
    });

    it('should fail with invalid token', async () => {
      const response = await ctx.request
        .put('/api/settings/pharmacy')
        .set('Authorization', 'Bearer invalid-token')
        .send({ pharmacyName: 'Test' });

      expectUnauthorized(response);
    });

    it('should handle empty update data', async () => {
      const response = await ctx.request
        .put('/api/settings/pharmacy')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({});

      expectSuccess(response);
      // Should return current settings unchanged
      expect(response.body).toHaveProperty('pharmacyName');
    });

    it('should validate string fields', async () => {
      const invalidData = {
        pharmacyName: 123, // Should be string
        pharmacyPhone: true, // Should be string
      };

      const response = await ctx.request
        .put('/api/settings/pharmacy')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(invalidData);

      expect(response.status).toBe(400);
    });
  });

  describe('Settings persistence', () => {
    it('should persist settings across requests', async () => {
      // Update settings
      const updateData = {
        pharmacyName: 'Persistent Pharmacy',
        pharmacyDescription: 'This should persist',
      };

      await ctx.request
        .put('/api/settings/pharmacy')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(updateData);

      // Retrieve settings in a new request
      const response = await ctx.request
        .get('/api/settings/pharmacy')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.pharmacyName).toBe(updateData.pharmacyName);
      expect(response.body.pharmacyDescription).toBe(updateData.pharmacyDescription);
    });
  });
});
