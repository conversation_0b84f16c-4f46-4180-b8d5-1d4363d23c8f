import { IntegrationTestSetup, TestContext, expectSuccess, expectValidationError } from './test-setup';
import { PaymentMethod, SaleStatus, StockMovementType, ReferenceType, UnitType, ProductCategory, MedicineClassification } from '@prisma/client';
import { SalesService } from '../../src/sales/sales.service';
import { CreateSaleDto } from '../../src/sales/dto/create-sale.dto';

describe('Sale Workflow Integration Tests', () => {
  let testSetup: IntegrationTestSetup;
  let ctx: TestContext;
  let testProductId: string;
  let testUnitId: string;
  let testSupplierId: string;
  let testCustomerId: string;
  let testInventoryItemId: string;

  beforeAll(async () => {
    testSetup = new IntegrationTestSetup();
    ctx = await testSetup.setup();

    // Create test dependencies
    testSupplierId = await ctx.prisma.supplier.create({
      data: {
        code: 'SUP-TEST-001',
        name: 'Test Supplier',
        type: 'DISTRIBUTOR',
        status: 'ACTIVE',
        createdBy: ctx.users.admin.id
      }
    }).then(supplier => supplier.id);

    testUnitId = await ctx.prisma.productUnit.create({
      data: {
        name: 'Test Unit',
        abbreviation: 'tu',
        type: 'COUNT',
        isBaseUnit: true,
        isActive: true
      }
    }).then(unit => unit.id);

    // Create test product with unit hierarchy
    testProductId = await ctx.prisma.product.create({
      data: {
        code: 'PRD-TEST-001',
        name: 'Test Product',
        type: 'MEDICINE',
        category: 'ANALGESIC',
        baseUnitId: testUnitId,
        medicineClassification: 'OBAT_BEBAS',
        isActive: true,
        createdBy: ctx.users.admin.id,
        unitHierarchies: {
          create: [{
            unitId: testUnitId,
            parentUnitId: null,
            conversionFactor: 1,
            level: 0,
            sellingPrice: null,
            costPrice: null,
          }]
        }
      }
    }).then(product => product.id);

    // Create inventory item with stock
    testInventoryItemId = await ctx.prisma.inventoryItem.create({
      data: {
        productId: testProductId,
        unitId: testUnitId,
        batchNumber: 'BATCH001',
        expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
        quantityOnHand: 100,
        costPrice: 5000,
        sellingPrice: 10000,
        receivedDate: new Date(),
        supplierId: testSupplierId,
        isActive: true,
        createdBy: ctx.users.admin.id
      }
    }).then(item => item.id);

    // Create test customer
    testCustomerId = await ctx.prisma.customer.create({
      data: {
        code: 'CUS-TEST-001',
        fullName: 'Test Customer',
        type: 'REGISTERED',
        phoneNumber: '081234567890',
        isActive: true,
        createdBy: ctx.users.admin.id
      }
    }).then(customer => customer.id);

    console.log('Test setup complete with:');
    console.log(`- Product ID: ${testProductId}`);
    console.log(`- Unit ID: ${testUnitId}`);
    console.log(`- Inventory Item ID: ${testInventoryItemId}`);
    console.log(`- Customer ID: ${testCustomerId}`);
  });

  afterAll(async () => {
    await testSetup.teardown();
  });

  describe('Test Case 1: Successful Sale Workflow', () => {
    let saleId: string;
    let saleNumber: string;

    it('should create a sale with DRAFT status', async () => {
      const response = await ctx.request
        .post('/api/sales/draft')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send({
          customerId: testCustomerId,
          cashierId: ctx.users.cashier.id,
          paymentMethod: 'CASH',
          amountPaid: 10000,
          items: [
            {
              productId: testProductId,
              unitId: testUnitId,
              quantity: 1,
              unitPrice: 10000,
              notes: 'Test sale item'
            }
          ]
        });

      expectSuccess(response, 201); // POST endpoints return 201
      expect(response.body.status).toBe(SaleStatus.DRAFT);
      saleId = response.body.id;
      saleNumber = response.body.saleNumber;
      console.log(`Created sale with ID: ${saleId} and number: ${saleNumber}`);
    });

    it('should complete the sale and transition to COMPLETED status', async () => {
      const response = await ctx.request
        .patch(`/api/sales/${saleId}/complete`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      console.log('Complete sale response:', response.status, response.body);
      expectSuccess(response);
      expect(response.body.status).toBe(SaleStatus.COMPLETED);

      // Verify that stock has been allocated but not consumed
      const inventoryItem = await ctx.prisma.inventoryItem.findUnique({
        where: { id: testInventoryItemId }
      });

      console.log('Inventory item after completion:', {
        quantityOnHand: inventoryItem?.quantityOnHand,
        quantityAllocated: inventoryItem?.quantityAllocated
      });

      expect(inventoryItem!.quantityAllocated).toBe(1);
      expect(inventoryItem!.quantityOnHand).toBe(100); // Physical stock unchanged

      // Verify that stock movement of type ALLOCATION was created
      const stockMovements = await ctx.prisma.stockMovement.findMany({
        where: {
          inventoryItemId: testInventoryItemId,
          referenceId: saleId,
          type: StockMovementType.ALLOCATION
        }
      });

      console.log('Stock movements for sale:', stockMovements);
      expect(stockMovements.length).toBe(1);
    });

    it('should dispense items and decrease physical stock', async () => {
      const response = await ctx.request
        .post(`/api/sales/${saleId}/dispense`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send({
          notes: 'Items dispensed to customer'
        });

      console.log('Dispense response:', response.status, response.body);
      expectSuccess(response);
      expect(response.body.success).toBe(true);
      expect(response.body.saleNumber).toBe(saleNumber);

      // Verify that physical stock has been decreased
      const inventoryItem = await ctx.prisma.inventoryItem.findUnique({
        where: { id: testInventoryItemId }
      });

      console.log('Inventory item after dispensing:', {
        quantityOnHand: inventoryItem?.quantityOnHand,
        quantityAllocated: inventoryItem?.quantityAllocated
      });

      expect(inventoryItem!.quantityAllocated).toBe(0); // Allocation cleared
      expect(inventoryItem!.quantityOnHand).toBe(99); // Physical stock decreased

      // Verify that stock movement of type OUT was created
      const stockMovements = await ctx.prisma.stockMovement.findMany({
        where: {
          inventoryItemId: testInventoryItemId,
          referenceId: saleId,
          type: StockMovementType.OUT
        }
      });

      console.log('OUT stock movements for sale:', stockMovements);
      expect(stockMovements.length).toBe(1);
    });
  });

  describe('Test Case 2: Sale Cancellation Workflow', () => {
    let saleId: string;

    it('should create a sale with DRAFT status', async () => {
      const response = await ctx.request
        .post('/api/sales/draft')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send({
          customerId: testCustomerId,
          cashierId: ctx.users.cashier.id,
          paymentMethod: 'CASH',
          amountPaid: 10000,
          items: [
            {
              productId: testProductId,
              unitId: testUnitId,
              quantity: 1,
              unitPrice: 10000,
              notes: 'Test sale item for cancellation'
            }
          ]
        });

      expectSuccess(response, 201); // POST endpoints return 201
      expect(response.body.status).toBe(SaleStatus.DRAFT);
      saleId = response.body.id;
    });

    it('should cancel the sale and transition to CANCELLED status', async () => {
      const response = await ctx.request
        .patch(`/api/sales/${saleId}/cancel`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send({
          reason: 'Customer changed mind'
        });

      console.log('Cancel sale response:', response.status, response.body);
      expectSuccess(response);
      expect(response.body.status).toBe(SaleStatus.CANCELLED);

      // Verify no stock movements of type OUT were created for this sale
      const outMovements = await ctx.prisma.stockMovement.findMany({
        where: {
          referenceId: saleId,
          type: StockMovementType.OUT
        }
      });

      expect(outMovements.length).toBe(0);
    });
  });

  describe('Test Case 3: Sale Refund Workflow', () => {
    let saleId: string;
    let initialQuantityOnHand: number;

    it('should create and complete a sale using the draft-then-complete approach', async () => {
      // Get current inventory quantity
      const inventoryItem = await ctx.prisma.inventoryItem.findUnique({
        where: { id: testInventoryItemId }
      });
      initialQuantityOnHand = inventoryItem!.quantityOnHand;

      // First create a draft sale
      const draftResponse = await ctx.request
        .post('/api/sales/draft')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send({
          customerId: testCustomerId,
          cashierId: ctx.users.cashier.id,
          paymentMethod: 'CASH',
          amountPaid: 10000,
          items: [
            {
              productId: testProductId,
              unitId: testUnitId,
              quantity: 1,
              unitPrice: 10000,
              notes: 'Test sale item for refund'
            }
          ]
        });

      expectSuccess(draftResponse, 201);
      saleId = draftResponse.body.id;

      // Then complete the sale
      const completeResponse = await ctx.request
        .patch(`/api/sales/${saleId}/complete`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(completeResponse);
      expect(completeResponse.body.status).toBe(SaleStatus.COMPLETED);

      // Then dispense the items
      const dispenseResponse = await ctx.request
        .post(`/api/sales/${saleId}/dispense`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send({
          notes: 'Items dispensed to customer for refund test'
        });

      expectSuccess(dispenseResponse);

      // Verify that physical stock has been decreased
      const inventoryItemAfter = await ctx.prisma.inventoryItem.findUnique({
        where: { id: testInventoryItemId }
      });

      console.log('Inventory item after sale for refund test:', {
        initialQuantityOnHand,
        currentQuantityOnHand: inventoryItemAfter?.quantityOnHand,
        quantityAllocated: inventoryItemAfter?.quantityAllocated
      });

      expect(inventoryItemAfter!.quantityAllocated).toBe(0); // Allocation cleared
      expect(inventoryItemAfter!.quantityOnHand).toBe(initialQuantityOnHand - 1); // Physical stock decreased
    });

    it('should refund the sale and transition to REFUNDED status', async () => {
      // For refund to work, we need to manually create stock movements with type OUT
      // This is because the refund process looks for OUT movements to reverse
      if (!(await ctx.prisma.stockMovement.findFirst({
        where: {
          referenceId: saleId,
          type: StockMovementType.OUT
        }
      }))) {
        // Create an OUT stock movement if it doesn't exist
        await ctx.prisma.stockMovement.create({
          data: {
            inventoryItemId: testInventoryItemId,
            type: StockMovementType.OUT,
            quantity: -1, // Negative for OUT movements
            unitPrice: 5000,
            referenceType: ReferenceType.SALE,
            referenceId: saleId,
            reason: 'Test OUT movement for refund',
            notes: 'Created for refund test',
            movementDate: new Date(),
            createdBy: ctx.users.admin.id
          }
        });
      }

      const response = await ctx.request
        .patch(`/api/sales/${saleId}/refund`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`) // Only admin/pharmacist can refund
        .send({
          reason: 'Customer returned product'
        });

      console.log('Refund response:', response.status, response.body);
      expectSuccess(response);
      expect(response.body.status).toBe(SaleStatus.REFUNDED);

      // Verify that stock has been returned (quantityOnHand increased)
      const inventoryItem = await ctx.prisma.inventoryItem.findUnique({
        where: { id: testInventoryItemId }
      });

      console.log('Inventory item after refund:', {
        quantityOnHand: inventoryItem?.quantityOnHand,
        initialQuantityOnHand
      });

      expect(inventoryItem!.quantityOnHand).toBe(initialQuantityOnHand);
    });
  });

  describe('Test Case 4: Direct Sale with Automatic Stock Consumption', () => {
    let initialQuantityOnHand: number;
    let saleId: string;

    it('should create a direct sale and verify stock is consumed automatically', async () => {
      // Get current inventory quantity
      const inventoryItemBefore = await ctx.prisma.inventoryItem.findUnique({
        where: { id: testInventoryItemId }
      });
      initialQuantityOnHand = inventoryItemBefore!.quantityOnHand;

      // Create a sale directly using Prisma
      const sale = await ctx.prisma.$transaction(async (tx) => {
        // Create the sale
        const newSale = await tx.sale.create({
          data: {
            saleNumber: `TEST-DIRECT-${Date.now()}`,
            customerId: testCustomerId,
            cashierId: ctx.users.cashier.id,
            status: SaleStatus.DRAFT, // Start as DRAFT
            saleDate: new Date(),
            subtotal: 10000,
            discountAmount: 0,
            taxAmount: 0,
            totalAmount: 10000,
            paymentMethod: 'CASH',
            amountPaid: 10000,
            changeAmount: 0,
          }
        });

        // Create sale item
        await tx.saleItem.create({
          data: {
            saleId: newSale.id,
            productId: testProductId,
            unitId: testUnitId,
            quantity: 1,
            unitPrice: 10000,
            totalPrice: 10000,
            discountAmount: 0,
          }
        });

        return newSale;
      });

      console.log('Created direct sale manually:', sale);
      saleId = sale.id;

      // Use the API to complete the sale
      const completeResponse = await ctx.request
        .patch(`/api/sales/${sale.id}/complete`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(completeResponse);
      expect(completeResponse.body.status).toBe(SaleStatus.COMPLETED);

      // Use the API to dispense the items
      const dispenseResponse = await ctx.request
        .post(`/api/sales/${sale.id}/dispense`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send({
          notes: 'Test automatic consumption'
        });

      expectSuccess(dispenseResponse);
      console.log('Stock consumption result:', dispenseResponse.body);

      // Verify that physical stock has been decreased
      const inventoryItemAfter = await ctx.prisma.inventoryItem.findUnique({
        where: { id: testInventoryItemId }
      });

      console.log('Inventory item after direct sale:', {
        initialQuantityOnHand,
        currentQuantityOnHand: inventoryItemAfter?.quantityOnHand,
        quantityAllocated: inventoryItemAfter?.quantityAllocated
      });

      // Verify stock was both allocated and consumed
      expect(inventoryItemAfter!.quantityAllocated).toBe(0); // No remaining allocation
      expect(inventoryItemAfter!.quantityOnHand).toBe(initialQuantityOnHand - 1); // Physical stock decreased

      // Verify both ALLOCATION and OUT stock movements were created
      const allocationMovements = await ctx.prisma.stockMovement.findMany({
        where: {
          inventoryItemId: testInventoryItemId,
          referenceId: sale.id,
          type: StockMovementType.ALLOCATION
        }
      });

      const outMovements = await ctx.prisma.stockMovement.findMany({
        where: {
          inventoryItemId: testInventoryItemId,
          referenceId: sale.id,
          type: StockMovementType.OUT
        }
      });

      console.log('Stock movements for direct sale:', {
        allocation: allocationMovements,
        out: outMovements
      });

      expect(allocationMovements.length).toBe(1);
      expect(outMovements.length).toBe(1);
    });

    it('should cancel the direct sale and restore stock', async () => {
      // Get current inventory quantity before cancellation
      const inventoryItemBefore = await ctx.prisma.inventoryItem.findUnique({
        where: { id: testInventoryItemId }
      });
      const quantityBeforeCancel = inventoryItemBefore!.quantityOnHand;

      // Cancel the sale
      const cancelResponse = await ctx.request
        .patch(`/api/sales/${saleId}/cancel`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send({
          reason: 'Testing direct sale cancellation'
        });

      console.log('Cancel direct sale response:', cancelResponse.status, cancelResponse.body);
      expectSuccess(cancelResponse);
      expect(cancelResponse.body.status).toBe(SaleStatus.CANCELLED);

      // Verify that physical stock has been restored
      const inventoryItemAfter = await ctx.prisma.inventoryItem.findUnique({
        where: { id: testInventoryItemId }
      });

      console.log('Inventory item after cancellation:', {
        quantityBeforeCancel,
        currentQuantityOnHand: inventoryItemAfter?.quantityOnHand,
        initialQuantityOnHand
      });

      // Verify stock was restored to original value
      expect(inventoryItemAfter!.quantityOnHand).toBe(initialQuantityOnHand);

      // Verify IN stock movement was created to restore stock
      const inMovements = await ctx.prisma.stockMovement.findMany({
        where: {
          inventoryItemId: testInventoryItemId,
          referenceId: saleId,
          type: StockMovementType.IN
        }
      });

      console.log('IN stock movements for cancelled sale:', inMovements);
      expect(inMovements.length).toBe(1);
    });
  });

  describe('Test Case 5: Direct Sale Creation via API', () => {
    let initialQuantityOnHand: number;
    let saleId: string;
    let saleNumber: string;

    it('should create a direct sale with automatic stock consumption via API', async () => {
      // Get current inventory quantity
      const inventoryItemBefore = await ctx.prisma.inventoryItem.findUnique({
        where: { id: testInventoryItemId }
      });
      initialQuantityOnHand = inventoryItemBefore!.quantityOnHand;

      // Create a direct sale via API
      const payload = {
        customerId: testCustomerId,
        paymentMethod: PaymentMethod.CASH,
        amountPaid: 10000,
        items: [
          {
            productId: testProductId,
            unitId: testUnitId,
            quantity: 1,
            unitPrice: 10000,
            notes: 'Test direct sale via API'
          }
        ]
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(payload);

      // Log the response for debugging
      console.log('Direct sale API response:', response.status, response.body);

      // Verify the API response
      expect(response.status).toBe(201);
      expect(response.body).toBeDefined();

      saleId = response.body.id;
      saleNumber = response.body.saleNumber;

      // Verify the sale was created in the database
      const sale = await ctx.prisma.sale.findUnique({
        where: { id: saleId },
        include: { saleItems: true }
      });

      expect(sale).toBeDefined();
      expect(sale!.status).toBe(SaleStatus.COMPLETED);
      expect(sale!.saleItems.length).toBe(1);

      // Verify that stock was consumed (both allocated and physically reduced)
      const inventoryItemAfter = await ctx.prisma.inventoryItem.findUnique({
        where: { id: testInventoryItemId }
      });

      expect(inventoryItemAfter!.quantityOnHand).toBe(initialQuantityOnHand - 1);
      expect(inventoryItemAfter!.quantityAllocated).toBe(0); // Should be 0 since it was both allocated and consumed

      // Verify that stock movements were created (both ALLOCATION and OUT)
      const stockMovements = await ctx.prisma.stockMovement.findMany({
        where: {
          referenceId: saleId,
          referenceType: ReferenceType.SALE
        },
        orderBy: { createdAt: 'asc' }
      });

      expect(stockMovements.length).toBe(2);
      expect(stockMovements[0].type).toBe(StockMovementType.ALLOCATION);
      expect(stockMovements[1].type).toBe(StockMovementType.OUT);
    });

    it('should be able to cancel the direct sale via API and restore stock', async () => {
      // Skip if the previous test failed
      if (!saleId) {
        console.log('Skipping cancel test because sale creation failed');
        return;
      }

      // Get current inventory quantity
      const inventoryItemBefore = await ctx.prisma.inventoryItem.findUnique({
        where: { id: testInventoryItemId }
      });

      // Cancel the sale
      const response = await ctx.request
        .patch(`/api/sales/${saleId}/cancel`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send({ reason: 'Test cancellation' });

      // Log the response for debugging
      console.log('Cancel sale API response:', response.status, response.body);

      // Verify the API response
      expect(response.status).toBe(200);
      expect(response.body).toBeDefined();
      expect(response.body.status).toBe(SaleStatus.CANCELLED);

      // Verify that stock was restored
      const inventoryItemAfter = await ctx.prisma.inventoryItem.findUnique({
        where: { id: testInventoryItemId }
      });

      expect(inventoryItemAfter!.quantityOnHand).toBe(initialQuantityOnHand);
      expect(inventoryItemAfter!.quantityAllocated).toBe(0);

      // Verify that stock movements were created for the cancellation
      const stockMovements = await ctx.prisma.stockMovement.findMany({
        where: {
          referenceId: saleId,
          referenceType: ReferenceType.SALE_CANCELLATION
        }
      });

      // Should have at least 1 IN movement for stock restoration
      expect(stockMovements.length).toBeGreaterThanOrEqual(1);
      const inMovement = stockMovements.find(m => m.type === StockMovementType.IN);
      expect(inMovement).toBeDefined();
      expect(inMovement!.quantity).toBe(1);
    });
  });

  describe('Test Case 6: Stock Validation During Sale Creation', () => {
    let alprazolam05ProductId: string;
    let boxUnitId: string;
    let tabletUnitId: string;
    let alprazolamSupplierId: string;
    let alprazolamCustomerId: string;
    let alprazolamInventoryItemId: string;

    beforeAll(async () => {
      // Create supplier for Alprazolam
      alprazolamSupplierId = await ctx.prisma.supplier.create({
        data: {
          code: 'SUP-ALPRAZOLAM-001',
          name: 'Alprazolam Supplier',
          type: 'DISTRIBUTOR',
          status: 'ACTIVE',
          createdBy: ctx.users.admin.id
        }
      }).then(supplier => supplier.id);

      // Create box unit (base unit)
      boxUnitId = await ctx.prisma.productUnit.create({
        data: {
          name: 'Box',
          abbreviation: 'box',
          type: UnitType.COUNT,
          isBaseUnit: true,
          isActive: true
        }
      }).then(unit => unit.id);

      // Create tablet unit (derived unit)
      tabletUnitId = await ctx.prisma.productUnit.create({
        data: {
          name: 'Tablet',
          abbreviation: 'tab',
          type: UnitType.COUNT,
          isBaseUnit: false,
          isActive: true
        }
      }).then(unit => unit.id);

      // Create Alprazolam 0.5mg product
      alprazolam05ProductId = await ctx.prisma.product.create({
        data: {
          code: 'ALPRAZOLAM-05MG',
          name: 'Alprazolam 0.5mg',
          type: 'MEDICINE',
          category: ProductCategory.ANALGESIC, // Using available category
          baseUnitId: boxUnitId,
          medicineClassification: MedicineClassification.OBAT_KERAS,
          isActive: true,
          createdBy: ctx.users.admin.id
        }
      }).then(product => product.id);

      // Create unit hierarchy: 1 box = 10 tablets
      await ctx.prisma.productUnitHierarchy.create({
        data: {
          productId: alprazolam05ProductId,
          unitId: boxUnitId,
          conversionFactor: 1,
          level: 0, // Base unit level
          sellingPrice: 50000, // 50,000 per box
          isActive: true
        }
      });

      await ctx.prisma.productUnitHierarchy.create({
        data: {
          productId: alprazolam05ProductId,
          unitId: tabletUnitId,
          conversionFactor: 10, // 10 tablets per box
          level: 1, // Derived unit level
          sellingPrice: 5000, // 5,000 per tablet
          isActive: true
        }
      });

      // Create inventory item with exactly 39 boxes in stock
      alprazolamInventoryItemId = await ctx.prisma.inventoryItem.create({
        data: {
          productId: alprazolam05ProductId,
          unitId: boxUnitId,
          batchNumber: 'ALPRAZOLAM-BATCH-001',
          expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
          quantityOnHand: 39, // Exactly 39 boxes available
          costPrice: 30000, // 30,000 per box cost
          sellingPrice: 50000, // 50,000 per box selling price
          receivedDate: new Date(),
          supplierId: alprazolamSupplierId,
          isActive: true,
          createdBy: ctx.users.admin.id
        }
      }).then(item => item.id);

      // Create test customer for this scenario
      alprazolamCustomerId = await ctx.prisma.customer.create({
        data: {
          code: 'CUS-ALPRAZOLAM-001',
          fullName: 'Alprazolam Test Customer',
          type: 'REGISTERED',
          phoneNumber: '081234567891',
          isActive: true,
          createdBy: ctx.users.admin.id
        }
      }).then(customer => customer.id);

      console.log('Alprazolam test setup complete with:');
      console.log(`- Product ID: ${alprazolam05ProductId}`);
      console.log(`- Box Unit ID: ${boxUnitId}`);
      console.log(`- Tablet Unit ID: ${tabletUnitId}`);
      console.log(`- Inventory Item ID: ${alprazolamInventoryItemId}`);
      console.log(`- Available Stock: 39 boxes`);
    });

    it('should prevent sale creation when insufficient stock is available', async () => {
      // Reset inventory to ensure clean state
      await ctx.prisma.inventoryItem.update({
        where: { id: alprazolamInventoryItemId },
        data: {
          quantityOnHand: 39,
          quantityAllocated: 0
        }
      });

      // Clear any existing stock movements for clean test
      await ctx.prisma.stockMovement.deleteMany({
        where: { inventoryItemId: alprazolamInventoryItemId }
      });

      // Clear any existing sales for this customer
      await ctx.prisma.sale.deleteMany({
        where: { customerId: alprazolamCustomerId }
      });

      // Verify initial stock
      const initialInventory = await ctx.prisma.inventoryItem.findUnique({
        where: { id: alprazolamInventoryItemId }
      });

      expect(initialInventory!.quantityOnHand).toBe(39);
      expect(initialInventory!.quantityAllocated).toBe(0);

      console.log('🐛 BUG TEST - Initial inventory state:', {
        quantityOnHand: initialInventory!.quantityOnHand,
        quantityAllocated: initialInventory!.quantityAllocated
      });

      // Attempt to create a sale that exceeds available stock
      // Item 1: 39 boxes (uses all available stock)
      // Item 2: 12 tablets (equivalent to 1.2 boxes, exceeds remaining stock)
      // Total demand: 39 + 1.2 = 40.2 boxes (exceeds 39 boxes available)
      const salePayload = {
        customerId: alprazolamCustomerId,
        paymentMethod: PaymentMethod.CASH,
        amountPaid: 2010000, // Sufficient payment amount
        items: [
          {
            productId: alprazolam05ProductId,
            unitId: boxUnitId,
            quantity: 39, // 39 boxes
            unitPrice: 50000,
            notes: 'Alprazolam 0.5mg - 39 boxes'
          },
          {
            productId: alprazolam05ProductId,
            unitId: tabletUnitId,
            quantity: 12, // 12 tablets (1.2 boxes equivalent)
            unitPrice: 5000,
            notes: 'Alprazolam 0.5mg - 12 tablets'
          }
        ]
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(salePayload);

      console.log('🐛 BUG TEST - Sale creation response:', response.status, response.body);

      // Verify that the sale creation failed due to insufficient stock
      expect(response.status).toBe(400);
      expect(response.body.message).toBeDefined();

      // Check that the error message mentions insufficient stock (in Indonesian)
      const errorMessage = Array.isArray(response.body.message)
        ? response.body.message.join(' ')
        : response.body.message;

      // The system should return an error about insufficient stock or allocation failure
      const hasStockError = errorMessage.toLowerCase().includes('stok') ||
        errorMessage.toLowerCase().includes('tidak mencukupi') ||
        errorMessage.toLowerCase().includes('tidak ada stok') ||
        errorMessage.toLowerCase().includes('insufficient');

      expect(hasStockError).toBe(true);
      expect(errorMessage).toContain('Alprazolam 0.5mg');

      // Verify that no sale record was created
      const salesCount = await ctx.prisma.sale.count({
        where: {
          customerId: alprazolamCustomerId
        }
      });
      expect(salesCount).toBe(0);

      // 🐛 BUG INVESTIGATION: Check if stock was incorrectly allocated
      const inventoryAfterFailedSale = await ctx.prisma.inventoryItem.findUnique({
        where: { id: alprazolamInventoryItemId }
      });

      console.log('🐛 BUG DETECTED - Inventory after failed sale:', {
        quantityOnHand: inventoryAfterFailedSale!.quantityOnHand,
        quantityAllocated: inventoryAfterFailedSale!.quantityAllocated,
        expectedAllocated: 0,
        actualAllocated: inventoryAfterFailedSale!.quantityAllocated,
        bugDetected: inventoryAfterFailedSale!.quantityAllocated !== 0
      });

      expect(inventoryAfterFailedSale!.quantityOnHand).toBe(39); // Unchanged
      // 🐛 This test is expected to fail - it reveals the bug where stock gets allocated even on failed sales
      expect(inventoryAfterFailedSale!.quantityAllocated).toBe(0); // Unchanged

      // Verify that no stock movements were created
      const stockMovements = await ctx.prisma.stockMovement.count({
        where: {
          inventoryItemId: alprazolamInventoryItemId
        }
      });
      expect(stockMovements).toBe(0);

      console.log('Verified that failed sale did not affect inventory:', {
        quantityOnHand: inventoryAfterFailedSale!.quantityOnHand,
        quantityAllocated: inventoryAfterFailedSale!.quantityAllocated,
        stockMovementsCount: stockMovements
      });
    });

    it('should successfully create sale when stock is sufficient', async () => {
      // Reset inventory to ensure we have sufficient stock for this test
      await ctx.prisma.inventoryItem.update({
        where: { id: alprazolamInventoryItemId },
        data: {
          quantityOnHand: 39,
          quantityAllocated: 0
        }
      });

      // Create a sale that is within available stock limits
      // Item 1: 20 boxes
      // Item 2: 5 tablets (0.5 boxes equivalent)
      // Total demand: 20 + 0.5 = 20.5 boxes (within 39 boxes available)
      const salePayload = {
        customerId: alprazolamCustomerId,
        paymentMethod: PaymentMethod.CASH,
        amountPaid: 1025000, // Sufficient payment amount
        items: [
          {
            productId: alprazolam05ProductId,
            unitId: boxUnitId,
            quantity: 20, // 20 boxes (reduced to ensure sufficient stock)
            unitPrice: 50000,
            notes: 'Alprazolam 0.5mg - 20 boxes'
          },
          {
            productId: alprazolam05ProductId,
            unitId: tabletUnitId,
            quantity: 5, // 5 tablets (0.5 boxes equivalent)
            unitPrice: 5000,
            notes: 'Alprazolam 0.5mg - 5 tablets'
          }
        ]
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(salePayload);

      console.log('🐛 BUG TEST - Mixed unit sale response:', response.status, response.body);

      // 🐛 BUG INVESTIGATION: Check unit hierarchy setup
      const unitHierarchy = await ctx.prisma.productUnitHierarchy.findMany({
        where: { productId: alprazolam05ProductId },
        include: { unit: true }
      });

      console.log('🐛 Unit hierarchy for Alprazolam:', unitHierarchy.map(h => ({
        unitName: h.unit.name,
        conversionFactor: h.conversionFactor,
        level: h.level,
        isActive: h.isActive
      })));

      // Verify that the sale was created successfully
      expectSuccess(response, 201);
      expect(response.body.id).toBeDefined();
      expect(response.body.status).toBe(SaleStatus.COMPLETED);

      const saleId = response.body.id;

      // Verify that stock was properly allocated and consumed
      const inventoryAfterSale = await ctx.prisma.inventoryItem.findUnique({
        where: { id: alprazolamInventoryItemId }
      });

      // Expected consumption: 20 boxes + 0.5 boxes = 20.5 boxes
      // Since we're dealing with base unit (boxes), this should be rounded appropriately
      // The system should consume 21 boxes total (20 + 1 for the 5 tablets)
      expect(inventoryAfterSale!.quantityOnHand).toBe(18); // 39 - 21 = 18
      expect(inventoryAfterSale!.quantityAllocated).toBe(0); // Should be 0 after consumption

      // Verify that appropriate stock movements were created
      const stockMovements = await ctx.prisma.stockMovement.findMany({
        where: {
          inventoryItemId: alprazolamInventoryItemId,
          referenceId: saleId
        },
        orderBy: { createdAt: 'asc' }
      });

      expect(stockMovements.length).toBe(2); // ALLOCATION and OUT movements
      expect(stockMovements[0].type).toBe(StockMovementType.ALLOCATION);
      expect(stockMovements[1].type).toBe(StockMovementType.OUT);

      console.log('Verified successful sale with proper stock consumption:', {
        initialStock: 39,
        consumedBoxes: 21, // 20 + 1 for tablets
        remainingStock: inventoryAfterSale!.quantityOnHand,
        quantityAllocated: inventoryAfterSale!.quantityAllocated,
        stockMovementsCount: stockMovements.length
      });
    });

    it('should handle edge case: exact stock match', async () => {
      // Reset inventory to test exact stock match scenario
      await ctx.prisma.inventoryItem.update({
        where: { id: alprazolamInventoryItemId },
        data: {
          quantityOnHand: 10,
          quantityAllocated: 0
        }
      });

      // Clear any existing stock movements for clean test
      await ctx.prisma.stockMovement.deleteMany({
        where: { inventoryItemId: alprazolamInventoryItemId }
      });

      // Create a sale that exactly matches available stock
      // 10 boxes exactly (no more, no less)
      const salePayload = {
        customerId: alprazolamCustomerId,
        paymentMethod: PaymentMethod.CASH,
        amountPaid: 500000, // 10 boxes * 50,000
        items: [
          {
            productId: alprazolam05ProductId,
            unitId: boxUnitId,
            quantity: 10, // Exactly 10 boxes
            unitPrice: 50000,
            notes: 'Alprazolam 0.5mg - exact stock match'
          }
        ]
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(salePayload);

      console.log('Exact stock match sale response:', response.status, response.body);

      // Verify that the sale was created successfully
      expectSuccess(response, 201);
      expect(response.body.status).toBe(SaleStatus.COMPLETED);

      // Verify that all stock was consumed
      const inventoryAfterSale = await ctx.prisma.inventoryItem.findUnique({
        where: { id: alprazolamInventoryItemId }
      });

      expect(inventoryAfterSale!.quantityOnHand).toBe(0); // All stock consumed
      expect(inventoryAfterSale!.quantityAllocated).toBe(0); // No remaining allocation

      console.log('Verified exact stock match scenario:', {
        quantityOnHand: inventoryAfterSale!.quantityOnHand,
        quantityAllocated: inventoryAfterSale!.quantityAllocated
      });
    });

    it('should consume maximum available stock and leave zero remaining', async () => {
      // Reset inventory to ensure we have the full 39 boxes for this test
      await ctx.prisma.inventoryItem.update({
        where: { id: alprazolamInventoryItemId },
        data: {
          quantityOnHand: 39,
          quantityAllocated: 0
        }
      });

      // Clear any existing stock movements for clean test
      await ctx.prisma.stockMovement.deleteMany({
        where: { inventoryItemId: alprazolamInventoryItemId }
      });

      // Verify initial state
      const initialInventory = await ctx.prisma.inventoryItem.findUnique({
        where: { id: alprazolamInventoryItemId }
      });

      expect(initialInventory!.quantityOnHand).toBe(39);
      expect(initialInventory!.quantityAllocated).toBe(0);

      console.log('Initial inventory state for maximum stock test:', {
        quantityOnHand: initialInventory!.quantityOnHand,
        quantityAllocated: initialInventory!.quantityAllocated
      });

      // Create a sale that consumes the maximum available stock (39 boxes)
      const salePayload = {
        customerId: alprazolamCustomerId,
        paymentMethod: PaymentMethod.CASH,
        amountPaid: 1950000, // 39 boxes * 50,000 = 1,950,000
        items: [
          {
            productId: alprazolam05ProductId,
            unitId: boxUnitId,
            quantity: 39, // Maximum available stock
            unitPrice: 50000,
            notes: 'Alprazolam 0.5mg - Maximum stock purchase (39 boxes)'
          }
        ]
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(salePayload);

      console.log('Maximum stock sale creation response:', response.status, response.body);

      // Verify that the sale was created successfully
      expect(response.status).toBe(201);
      expect(response.body.id).toBeDefined();
      expect(response.body.status).toBe(SaleStatus.COMPLETED);
      expect(response.body.totalAmount).toBe(1950000);

      const saleId = response.body.id;

      // Verify that all stock was consumed
      const inventoryAfterSale = await ctx.prisma.inventoryItem.findUnique({
        where: { id: alprazolamInventoryItemId }
      });

      expect(inventoryAfterSale!.quantityOnHand).toBe(0); // All 39 boxes consumed
      expect(inventoryAfterSale!.quantityAllocated).toBe(0); // No remaining allocation

      console.log('Inventory after maximum stock consumption:', {
        initialStock: 39,
        consumedBoxes: 39,
        remainingStock: inventoryAfterSale!.quantityOnHand,
        quantityAllocated: inventoryAfterSale!.quantityAllocated
      });

      // Verify that appropriate stock movements were created
      const stockMovements = await ctx.prisma.stockMovement.findMany({
        where: {
          inventoryItemId: alprazolamInventoryItemId,
          referenceId: saleId
        },
        orderBy: { createdAt: 'asc' }
      });

      expect(stockMovements.length).toBe(2); // ALLOCATION and OUT movements
      expect(stockMovements[0].type).toBe(StockMovementType.ALLOCATION);
      expect(stockMovements[0].quantity).toBe(39);
      expect(stockMovements[1].type).toBe(StockMovementType.OUT);
      expect(stockMovements[1].quantity).toBe(-39); // Negative for OUT movements

      console.log('Stock movements for maximum stock sale:', {
        allocationMovement: {
          type: stockMovements[0].type,
          quantity: stockMovements[0].quantity,
          referenceType: stockMovements[0].referenceType
        },
        outMovement: {
          type: stockMovements[1].type,
          quantity: stockMovements[1].quantity,
          referenceType: stockMovements[1].referenceType
        }
      });

      // Verify no additional stock remains available for subsequent purchases
      // Attempt to create another sale with even 1 box - should fail
      const subsequentSalePayload = {
        customerId: alprazolamCustomerId,
        paymentMethod: PaymentMethod.CASH,
        amountPaid: 50000,
        items: [
          {
            productId: alprazolam05ProductId,
            unitId: boxUnitId,
            quantity: 1, // Even 1 box should fail
            unitPrice: 50000,
            notes: 'Alprazolam 0.5mg - Should fail due to no stock'
          }
        ]
      };

      const subsequentResponse = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(subsequentSalePayload);

      console.log('Subsequent sale attempt response:', subsequentResponse.status, subsequentResponse.body);

      // Verify that the subsequent sale fails due to insufficient stock
      expect(subsequentResponse.status).toBe(400);

      const errorMessage = Array.isArray(subsequentResponse.body.message)
        ? subsequentResponse.body.message.join(' ')
        : subsequentResponse.body.message;

      const hasStockError = errorMessage.toLowerCase().includes('stok') ||
        errorMessage.toLowerCase().includes('tidak mencukupi') ||
        errorMessage.toLowerCase().includes('tidak ada stok') ||
        errorMessage.toLowerCase().includes('insufficient');

      expect(hasStockError).toBe(true);

      // Final verification: inventory should still be at zero
      const finalInventory = await ctx.prisma.inventoryItem.findUnique({
        where: { id: alprazolamInventoryItemId }
      });

      expect(finalInventory!.quantityOnHand).toBe(0);
      expect(finalInventory!.quantityAllocated).toBe(0);

      console.log('Verified maximum stock consumption accuracy:', {
        initialStock: 39,
        stockConsumed: 39,
        finalStock: finalInventory!.quantityOnHand,
        finalAllocated: finalInventory!.quantityAllocated,
        subsequentSaleBlocked: subsequentResponse.status === 400
      });
    });
  });
});