import { IntegrationTestSetup, TestContext, expectValidationError, expectUnauthorized, expectForbidden, expectNotFound, expectConflict, expectSuccess } from './test-setup';

describe('Suppliers Integration Tests', () => {
  let testSetup: IntegrationTestSetup;
  let ctx: TestContext;
  let testSupplierId: string;

  beforeAll(async () => {
    testSetup = new IntegrationTestSetup();
    ctx = await testSetup.setup();
  });

  afterAll(async () => {
    await testSetup.teardown();
  });

  describe('POST /api/suppliers', () => {
    it('should create supplier as admin', async () => {
      const supplierData = {
        code: 'SUP001',
        name: 'Test Supplier 1',
        type: 'PBF',
        address: 'Jl. Test No. 123',
        city: 'Jakarta',
        province: 'DKI Jakarta',
        postalCode: '12345',
        phone: '+62 21 1234 5678',
        email: '<EMAIL>',
        website: 'https://supplier1.com',
        npwp: '12.345.678.9-012.345',
        licenseNumber: 'LIC123456',
        pharmacyLicense: 'PHA789012',
        creditLimit: 1000000,
        paymentTerms: 30,
        preferredPayment: 'TRANSFER',
        bankName: 'Bank Test',
        bankAccount: '**********',
        bankAccountName: 'Test Supplier 1',
        notes: 'Test supplier notes',
        contacts: [
          {
            name: 'John Doe',
            position: 'Sales Manager',
            phone: '+62 812 3456 7890',
            email: '<EMAIL>',
            isPrimary: true,
            notes: 'Primary contact',
          },
        ],
      };

      const response = await ctx.request
        .post('/api/suppliers')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(supplierData);

      expectSuccess(response, 201);
      expect(response.body.code).toBe(supplierData.code);
      expect(response.body.name).toBe(supplierData.name);
      expect(response.body.type).toBe(supplierData.type);
      expect(response.body.npwp).toBe(supplierData.npwp);
      expect(response.body.createdBy).toBe(ctx.users.admin.id);
      
      testSupplierId = response.body.id;
    });

    it('should create supplier as pharmacist', async () => {
      const supplierData = {
        code: 'SUP002',
        name: 'Test Supplier 2',
        type: 'DISTRIBUTOR',
        address: 'Jl. Distributor No. 456',
        city: 'Bandung',
        province: 'Jawa Barat',
      };

      const response = await ctx.request
        .post('/api/suppliers')
        .set('Authorization', `Bearer ${ctx.users.pharmacist.accessToken}`)
        .send(supplierData);

      expectSuccess(response, 201);
      expect(response.body.createdBy).toBe(ctx.users.pharmacist.id);
    });

    it('should fail for cashier (insufficient permissions)', async () => {
      const supplierData = {
        code: 'SUP003',
        name: 'Test Supplier 3',
        type: 'PBF',
      };

      const response = await ctx.request
        .post('/api/suppliers')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(supplierData);

      expectForbidden(response);
      expect(response.body.message).toContain('Hanya admin dan apoteker');
    });

    it('should fail without authentication', async () => {
      const response = await ctx.request
        .post('/api/suppliers')
        .send({ code: 'SUP004', name: 'Test', type: 'PBF' });

      expectUnauthorized(response);
    });

    it('should fail with duplicate code', async () => {
      const supplierData = {
        code: 'SUP001', // Already exists
        name: 'Duplicate Supplier',
        type: 'PBF',
      };

      const response = await ctx.request
        .post('/api/suppliers')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(supplierData);

      expect(response.status).toBe(409);
    });

    it('should fail with invalid supplier type', async () => {
      const supplierData = {
        code: 'SUP005',
        name: 'Invalid Type Supplier',
        type: 'INVALID_TYPE',
      };

      const response = await ctx.request
        .post('/api/suppliers')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(supplierData);

      expectValidationError(response);
    });

    it('should fail with missing required fields', async () => {
      const response = await ctx.request
        .post('/api/suppliers')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({});

      expectValidationError(response);
    });

    it('should validate email format', async () => {
      const supplierData = {
        code: 'SUP006',
        name: 'Email Test Supplier',
        type: 'PBF',
        email: 'invalid-email',
      };

      const response = await ctx.request
        .post('/api/suppliers')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(supplierData);

      expectValidationError(response, 'email');
    });

    it('should validate website URL format', async () => {
      const supplierData = {
        code: 'SUP007',
        name: 'URL Test Supplier',
        type: 'PBF',
        website: 'invalid-url',
      };

      const response = await ctx.request
        .post('/api/suppliers')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(supplierData);

      expectValidationError(response, 'website');
    });
  });

  describe('GET /api/suppliers', () => {
    beforeAll(async () => {
      // Create additional test suppliers for filtering tests
      const suppliers = [
        { code: 'PBF001', name: 'PBF Supplier', type: 'PBF', city: 'Jakarta', status: 'ACTIVE' },
        { code: 'MAN001', name: 'Manufacturer', type: 'MANUFACTURER', city: 'Surabaya', status: 'ACTIVE' },
        { code: 'DIS001', name: 'Distributor', type: 'DISTRIBUTOR', city: 'Jakarta', status: 'INACTIVE' },
      ];

      for (const supplier of suppliers) {
        await ctx.request
          .post('/api/suppliers')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(supplier);
      }
    });

    it('should list all suppliers for authenticated user', async () => {
      const response = await ctx.request
        .get('/api/suppliers')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      expect(response.body.data).toBeInstanceOf(Array);
      expect(response.body.meta).toBeDefined();
      expect(response.body.meta.total).toBeGreaterThan(0);
      expect(response.body.meta.page).toBe(1);
      expect(response.body.meta.limit).toBe(10);
    });

    it('should filter suppliers by type', async () => {
      const response = await ctx.request
        .get('/api/suppliers?type=PBF')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.data.every((s: any) => s.type === 'PBF')).toBe(true);
    });

    it('should filter suppliers by status', async () => {
      const response = await ctx.request
        .get('/api/suppliers?status=ACTIVE')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.data.every((s: any) => s.status === 'ACTIVE')).toBe(true);
    });

    it('should filter suppliers by city', async () => {
      const response = await ctx.request
        .get('/api/suppliers?city=Jakarta')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.data.every((s: any) => s.city === 'Jakarta')).toBe(true);
    });

    it('should search suppliers by name', async () => {
      const response = await ctx.request
        .get('/api/suppliers?search=Supplier')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.data.some((s: any) => s.name.includes('Supplier'))).toBe(true);
    });

    it('should paginate results', async () => {
      const response = await ctx.request
        .get('/api/suppliers?page=1&limit=2')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.data.length).toBeLessThanOrEqual(2);
      expect(response.body.meta.page).toBe(1);
      expect(response.body.meta.limit).toBe(2);
    });

    it('should sort suppliers', async () => {
      const response = await ctx.request
        .get('/api/suppliers?sortBy=name&sortOrder=asc')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      const names = response.body.data.map((s: any) => s.name);
      const sortedNames = [...names].sort();
      expect(names).toEqual(sortedNames);
    });

    it('should fail without authentication', async () => {
      const response = await ctx.request
        .get('/api/suppliers');

      expectUnauthorized(response);
    });

    it('should validate pagination parameters', async () => {
      const response = await ctx.request
        .get('/api/suppliers?page=0&limit=101')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      // Page 0 should be invalid (minimum 1), limit 101 should be invalid (maximum 100)
      expectValidationError(response);
    });
  });

  describe('GET /api/suppliers/stats', () => {
    it('should return supplier statistics for authenticated user', async () => {
      const response = await ctx.request
        .get('/api/suppliers/stats')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body).toHaveProperty('total');
      expect(response.body).toHaveProperty('byType');
      expect(response.body).toHaveProperty('active');
      expect(response.body).toHaveProperty('inactive');
      expect(typeof response.body.total).toBe('number');
      expect(typeof response.body.active).toBe('number');
      expect(typeof response.body.inactive).toBe('number');
    });

    it('should fail without authentication', async () => {
      const response = await ctx.request
        .get('/api/suppliers/stats');

      expectUnauthorized(response);
    });
  });

  describe('GET /api/suppliers/:id', () => {
    it('should return supplier details for authenticated user', async () => {
      const response = await ctx.request
        .get(`/api/suppliers/${testSupplierId}`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      expect(response.body.id).toBe(testSupplierId);
      expect(response.body.code).toBe('SUP001');
      expect(response.body.name).toBe('Test Supplier 1');
    });

    it('should fail with non-existent supplier ID', async () => {
      const response = await ctx.request
        .get('/api/suppliers/non-existent-id')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectNotFound(response);
    });

    it('should fail without authentication', async () => {
      const response = await ctx.request
        .get(`/api/suppliers/${testSupplierId}`);

      expectUnauthorized(response);
    });
  });

  describe('PATCH /api/suppliers/:id', () => {
    it('should update supplier as admin', async () => {
      const updateData = {
        name: 'Updated Supplier Name',
        address: 'Updated Address 789',
        phone: '+62 21 9999 8888',
        status: 'INACTIVE',
      };

      const response = await ctx.request
        .patch(`/api/suppliers/${testSupplierId}`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(updateData);

      expectSuccess(response);
      expect(response.body.name).toBe(updateData.name);
      expect(response.body.address).toBe(updateData.address);
      expect(response.body.phone).toBe(updateData.phone);
      expect(response.body.status).toBe(updateData.status);
      expect(response.body.updatedBy).toBe(ctx.users.admin.id);
    });

    it('should update supplier as pharmacist', async () => {
      const updateData = {
        notes: 'Updated by pharmacist',
      };

      const response = await ctx.request
        .patch(`/api/suppliers/${testSupplierId}`)
        .set('Authorization', `Bearer ${ctx.users.pharmacist.accessToken}`)
        .send(updateData);

      expectSuccess(response);
      expect(response.body.notes).toBe(updateData.notes);
      expect(response.body.updatedBy).toBe(ctx.users.pharmacist.id);
    });

    it('should fail for cashier (insufficient permissions)', async () => {
      const response = await ctx.request
        .patch(`/api/suppliers/${testSupplierId}`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send({ name: 'Unauthorized Update' });

      expectForbidden(response);
    });

    it('should fail with non-existent supplier ID', async () => {
      const response = await ctx.request
        .patch('/api/suppliers/non-existent-id')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({ name: 'Test' });

      expectNotFound(response);
    });

    it('should fail without authentication', async () => {
      const response = await ctx.request
        .patch(`/api/suppliers/${testSupplierId}`)
        .send({ name: 'Test' });

      expectUnauthorized(response);
    });

    it('should validate email format in update', async () => {
      const response = await ctx.request
        .patch(`/api/suppliers/${testSupplierId}`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({ email: 'invalid-email' });

      expectValidationError(response, 'email');
    });
  });

  describe('DELETE /api/suppliers/:id', () => {
    let supplierToDelete: string;

    beforeAll(async () => {
      // Create a supplier specifically for deletion test
      const response = await ctx.request
        .post('/api/suppliers')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({
          code: 'DEL001',
          name: 'Supplier to Delete',
          type: 'LOCAL',
        });
      supplierToDelete = response.body.id;
    });

    it('should delete supplier as admin', async () => {
      const response = await ctx.request
        .delete(`/api/suppliers/${supplierToDelete}`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expect(response.status).toBe(204);

      // Verify supplier is hard deleted (completely removed from database)
      const getResponse = await ctx.request
        .get(`/api/suppliers/${supplierToDelete}`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectNotFound(getResponse);
    });

    it('should fail for pharmacist (insufficient permissions)', async () => {
      const response = await ctx.request
        .delete(`/api/suppliers/${testSupplierId}`)
        .set('Authorization', `Bearer ${ctx.users.pharmacist.accessToken}`);

      expectForbidden(response);
    });

    it('should fail for cashier (insufficient permissions)', async () => {
      const response = await ctx.request
        .delete(`/api/suppliers/${testSupplierId}`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectForbidden(response);
    });

    it('should fail with non-existent supplier ID', async () => {
      const response = await ctx.request
        .delete('/api/suppliers/non-existent-id')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectNotFound(response);
    });

    it('should fail without authentication', async () => {
      const response = await ctx.request
        .delete(`/api/suppliers/${testSupplierId}`);

      expectUnauthorized(response);
    });
  });

  describe('PATCH /api/suppliers/:id/deactivate', () => {
    let supplierToDeactivate: string;

    beforeEach(async () => {
      const uniqueCode = `DEACT${Date.now()}${Math.floor(Math.random() * 1000)}`;
      const response = await ctx.request
        .post('/api/suppliers')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({
          code: uniqueCode,
          name: 'Supplier to Deactivate',
          type: 'LOCAL',
        });

      if (response.status !== 201) {
        throw new Error(`Failed to create supplier for test. Status: ${response.status}, Body: ${JSON.stringify(response.body)}`);
      }

      supplierToDeactivate = response.body.id;
      // Supplier is created as ACTIVE by default, which is what we want for deactivate tests
    });

    it('should deactivate supplier as admin', async () => {
      const response = await ctx.request
        .patch(`/api/suppliers/${supplierToDeactivate}/deactivate`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.status).toBe('INACTIVE');
    });

    it('should deactivate supplier as pharmacist', async () => {
      const response = await ctx.request
        .patch(`/api/suppliers/${supplierToDeactivate}/deactivate`)
        .set('Authorization', `Bearer ${ctx.users.pharmacist.accessToken}`);

      expectSuccess(response);
      expect(response.body.status).toBe('INACTIVE');
    });

    it('should fail for cashier (insufficient permissions)', async () => {
      const response = await ctx.request
        .patch(`/api/suppliers/${supplierToDeactivate}/deactivate`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectForbidden(response);
    });

    it('should fail if supplier is already inactive', async () => {
      // First deactivate
      await ctx.request
        .patch(`/api/suppliers/${supplierToDeactivate}/deactivate`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      // Try to deactivate again
      const response = await ctx.request
        .patch(`/api/suppliers/${supplierToDeactivate}/deactivate`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectConflict(response);
    });

    it('should fail with non-existent supplier ID', async () => {
      const response = await ctx.request
        .patch('/api/suppliers/non-existent-id/deactivate')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectNotFound(response);
    });

    it('should fail without authentication', async () => {
      const response = await ctx.request
        .patch(`/api/suppliers/${supplierToDeactivate}/deactivate`);

      expectUnauthorized(response);
    });
  });

  describe('PATCH /api/suppliers/:id/activate', () => {
    let supplierToActivate: string;

    beforeEach(async () => {
      // Create supplier (will be ACTIVE by default)
      const uniqueCode = `ACT${Date.now()}${Math.floor(Math.random() * 1000)}`;
      const createResponse = await ctx.request
        .post('/api/suppliers')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({
          code: uniqueCode,
          name: 'Supplier to Activate',
          type: 'LOCAL',
        });

      if (createResponse.status !== 201) {
        throw new Error(`Failed to create supplier for test. Status: ${createResponse.status}, Body: ${JSON.stringify(createResponse.body)}`);
      }

      supplierToActivate = createResponse.body.id;

      // Deactivate the supplier so we can test activation
      const deactivateResponse = await ctx.request
        .patch(`/api/suppliers/${supplierToActivate}/deactivate`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      if (deactivateResponse.status !== 200) {
        throw new Error(`Failed to deactivate supplier for test. Status: ${deactivateResponse.status}, Body: ${JSON.stringify(deactivateResponse.body)}`);
      }
    });

    it('should activate supplier as admin', async () => {
      const response = await ctx.request
        .patch(`/api/suppliers/${supplierToActivate}/activate`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.status).toBe('ACTIVE');
    });

    it('should activate supplier as pharmacist', async () => {
      const response = await ctx.request
        .patch(`/api/suppliers/${supplierToActivate}/activate`)
        .set('Authorization', `Bearer ${ctx.users.pharmacist.accessToken}`);

      expectSuccess(response);
      expect(response.body.status).toBe('ACTIVE');
    });

    it('should fail for cashier (insufficient permissions)', async () => {
      const response = await ctx.request
        .patch(`/api/suppliers/${supplierToActivate}/activate`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectForbidden(response);
    });

    it('should fail if supplier is already active', async () => {
      // First activate
      await ctx.request
        .patch(`/api/suppliers/${supplierToActivate}/activate`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      // Try to activate again
      const response = await ctx.request
        .patch(`/api/suppliers/${supplierToActivate}/activate`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectConflict(response);
    });

    it('should fail with non-existent supplier ID', async () => {
      const response = await ctx.request
        .patch('/api/suppliers/non-existent-id/activate')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectNotFound(response);
    });

    it('should fail without authentication', async () => {
      const response = await ctx.request
        .patch(`/api/suppliers/${supplierToActivate}/activate`);

      expectUnauthorized(response);
    });
  });
});
