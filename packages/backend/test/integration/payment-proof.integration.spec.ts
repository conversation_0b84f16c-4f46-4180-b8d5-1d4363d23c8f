import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../../src/app.module';
import { PrismaService } from '../../src/prisma/prisma.service';
import { AuthService } from '../../src/auth/auth.service';
import { RegisterDto } from '../../src/auth/dto/register.dto';
import { UserRole } from '@prisma/client';
import * as path from 'path';
import * as fs from 'fs';

describe('Payment Proof Management (e2e)', () => {
  let app: INestApplication;
  let prisma: PrismaService;
  let authService: AuthService;
  let adminToken: string;
  let supplierId: string;
  let paymentId: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    prisma = moduleFixture.get<PrismaService>(PrismaService);
    authService = moduleFixture.get<AuthService>(AuthService);

    await app.init();

    // Clean up database
    await prisma.supplierDocument.deleteMany();
    await prisma.supplierPayment.deleteMany();
    await prisma.supplier.deleteMany();
    await prisma.user.deleteMany();

    // Create admin user
    const adminData: RegisterDto = {
      email: '<EMAIL>',
      password: 'password123',
      firstName: 'Admin',
      lastName: 'User',
      role: UserRole.ADMIN,
    };

    const admin = await authService.register(adminData);
    const adminLoginResponse = await authService.login({
      email: adminData.email,
      password: adminData.password,
    });
    adminToken = adminLoginResponse.accessToken;

    // Create test supplier
    const supplierResponse = await request(app.getHttpServer())
      .post('/suppliers')
      .set('Authorization', `Bearer ${adminToken}`)
      .send({
        code: 'SUP-PROOF-001',
        name: 'Test Supplier for Payment Proof',
        email: '<EMAIL>',
        phone: '081234567890',
        address: 'Test Address',
        city: 'Test City',
        province: 'Test Province',
        postalCode: '12345',
        type: 'PBF',
      });

    supplierId = supplierResponse.body.id;

    // Create test payment
    const paymentResponse = await request(app.getHttpServer())
      .post(`/suppliers/${supplierId}/payments`)
      .set('Authorization', `Bearer ${adminToken}`)
      .send({
        amount: 1000000,
        paymentDate: '2024-01-15',
        paymentMethod: 'TRANSFER',
        status: 'PENDING',
        invoiceNumber: 'INV-001',
        reference: 'REF-001',
        notes: 'Test payment for proof upload',
      });

    paymentId = paymentResponse.body.id;
  });

  afterAll(async () => {
    // Clean up test files
    const uploadsDir = path.join(process.cwd(), 'uploads', 'payment-proofs');
    if (fs.existsSync(uploadsDir)) {
      const files = fs.readdirSync(uploadsDir);
      files.forEach(file => {
        if (file.startsWith('payment-proof-')) {
          fs.unlinkSync(path.join(uploadsDir, file));
        }
      });
    }

    await prisma.supplierDocument.deleteMany();
    await prisma.supplierPayment.deleteMany();
    await prisma.supplier.deleteMany();
    await prisma.user.deleteMany();
    await app.close();
  });

  describe('GET /suppliers/:id/payments/:paymentId/proof', () => {
    it('should return null when no payment proof exists', async () => {
      const response = await request(app.getHttpServer())
        .get(`/suppliers/${supplierId}/payments/${paymentId}/proof`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      // NestJS serializes null to empty object in some cases
      expect(response.body === null || Object.keys(response.body).length === 0).toBe(true);
    });

    it('should return 404 for non-existent supplier', async () => {
      await request(app.getHttpServer())
        .get(`/suppliers/non-existent/payments/${paymentId}/proof`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(404);
    });

    it('should return 404 for non-existent payment', async () => {
      await request(app.getHttpServer())
        .get(`/suppliers/${supplierId}/payments/non-existent/proof`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(404);
    });
  });

  describe('POST /suppliers/:id/payments/:paymentId/proof', () => {
    it('should upload payment proof successfully', async () => {
      // Create a test file
      const testFilePath = path.join(__dirname, 'test-payment-proof.pdf');
      const testFileContent = Buffer.from('Test PDF content for payment proof');
      fs.writeFileSync(testFilePath, testFileContent);

      const response = await request(app.getHttpServer())
        .post(`/suppliers/${supplierId}/payments/${paymentId}/proof`)
        .set('Authorization', `Bearer ${adminToken}`)
        .attach('file', testFilePath)
        .field('name', 'Bukti Pembayaran INV-001')
        .field('description', 'Bukti transfer untuk invoice INV-001')
        .expect(201);

      expect(response.body).toMatchObject({
        supplierId,
        paymentId,
        type: 'PAYMENT_PROOF',
        name: 'Bukti Pembayaran INV-001',
        description: 'Bukti transfer untuk invoice INV-001',
        fileName: 'test-payment-proof.pdf',
        mimeType: 'application/pdf',
        isActive: true,
      });

      expect(response.body.id).toBeDefined();
      expect(response.body.filePath).toBeDefined();
      expect(response.body.fileSize).toBeGreaterThan(0);
      expect(response.body.uploadedAt).toBeDefined();

      // Clean up test file
      fs.unlinkSync(testFilePath);
    });

    it('should reject duplicate payment proof upload', async () => {
      // Try to upload another proof for the same payment
      const testFilePath = path.join(__dirname, 'test-payment-proof-2.pdf');
      const testFileContent = Buffer.from('Another test PDF content');
      fs.writeFileSync(testFilePath, testFileContent);

      await request(app.getHttpServer())
        .post(`/suppliers/${supplierId}/payments/${paymentId}/proof`)
        .set('Authorization', `Bearer ${adminToken}`)
        .attach('file', testFilePath)
        .field('name', 'Duplicate Bukti Pembayaran')
        .expect(400);

      // Clean up test file
      fs.unlinkSync(testFilePath);
    });

    it('should reject invalid file types', async () => {
      const testFilePath = path.join(__dirname, 'test-invalid.txt');
      fs.writeFileSync(testFilePath, 'Invalid file type');

      await request(app.getHttpServer())
        .post(`/suppliers/${supplierId}/payments/${paymentId}/proof`)
        .set('Authorization', `Bearer ${adminToken}`)
        .attach('file', testFilePath)
        .field('name', 'Invalid File')
        .expect(400);

      // Clean up test file
      fs.unlinkSync(testFilePath);
    });

    it('should require authentication', async () => {
      await request(app.getHttpServer())
        .post(`/suppliers/${supplierId}/payments/${paymentId}/proof`)
        .expect(401);
    });
  });

  describe('GET /suppliers/:id/payments/:paymentId/proof (after upload)', () => {
    it('should return the uploaded payment proof', async () => {
      const response = await request(app.getHttpServer())
        .get(`/suppliers/${supplierId}/payments/${paymentId}/proof`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body).toMatchObject({
        supplierId,
        paymentId,
        type: 'PAYMENT_PROOF',
        name: 'Bukti Pembayaran INV-001',
        description: 'Bukti transfer untuk invoice INV-001',
        isActive: true,
      });
    });
  });

  describe('DELETE /suppliers/:id/payments/:paymentId/proof', () => {
    it('should delete payment proof successfully', async () => {
      await request(app.getHttpServer())
        .delete(`/suppliers/${supplierId}/payments/${paymentId}/proof`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(204);

      // Verify proof is deleted (soft delete)
      const response = await request(app.getHttpServer())
        .get(`/suppliers/${supplierId}/payments/${paymentId}/proof`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      // NestJS serializes null to empty object in some cases
      expect(response.body === null || Object.keys(response.body).length === 0).toBe(true);
    });

    it('should return 404 when trying to delete non-existent proof', async () => {
      await request(app.getHttpServer())
        .delete(`/suppliers/${supplierId}/payments/${paymentId}/proof`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(404);
    });

    it('should require authentication', async () => {
      await request(app.getHttpServer())
        .delete(`/suppliers/${supplierId}/payments/${paymentId}/proof`)
        .expect(401);
    });
  });

  describe('Document isolation', () => {
    it('should exclude payment proofs from regular document listings', async () => {
      // First, upload a regular document
      const testFilePath = path.join(__dirname, 'test-regular-doc.pdf');
      fs.writeFileSync(testFilePath, Buffer.from('Regular document content'));

      await request(app.getHttpServer())
        .post(`/suppliers/${supplierId}/documents/upload`)
        .set('Authorization', `Bearer ${adminToken}`)
        .attach('file', testFilePath)
        .field('type', 'CONTRACT')
        .field('name', 'Regular Contract Document')
        .expect(201);

      // Upload a new payment proof for testing
      const proofFilePath = path.join(__dirname, 'test-proof-isolation.pdf');
      fs.writeFileSync(proofFilePath, Buffer.from('Payment proof content'));

      // Create another payment for this test
      const newPaymentResponse = await request(app.getHttpServer())
        .post(`/suppliers/${supplierId}/payments`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          amount: 500000,
          paymentDate: '2024-01-20',
          paymentMethod: 'CASH',
          status: 'PAID',
          invoiceNumber: 'INV-002',
        });

      await request(app.getHttpServer())
        .post(`/suppliers/${supplierId}/payments/${newPaymentResponse.body.id}/proof`)
        .set('Authorization', `Bearer ${adminToken}`)
        .attach('file', proofFilePath)
        .field('name', 'Isolated Payment Proof')
        .expect(201);

      // Get regular documents - should not include payment proofs
      const documentsResponse = await request(app.getHttpServer())
        .get(`/suppliers/${supplierId}/documents`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(documentsResponse.body.data).toHaveLength(1);
      expect(documentsResponse.body.data[0].type).toBe('CONTRACT');
      expect(documentsResponse.body.data[0].name).toBe('Regular Contract Document');

      // Clean up test files
      fs.unlinkSync(testFilePath);
      fs.unlinkSync(proofFilePath);
    });
  });
});
