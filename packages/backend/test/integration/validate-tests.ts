#!/usr/bin/env ts-node

import { execSync } from 'child_process';
import { existsSync } from 'fs';
import { join } from 'path';

class TestValidator {
  private readonly testFiles = [
    'test-setup.ts',
    'auth.integration.spec.ts',
    'settings.integration.spec.ts',
    'suppliers.integration.spec.ts',
    'supplier-documents.integration.spec.ts',
    'supplier-payments.integration.spec.ts',
    'supplier-file-upload.integration.spec.ts',
    'app.integration.spec.ts',
  ];

  private readonly configFiles = [
    'jest.integration.config.js',
    'jest.setup.ts',
    'run-tests.ts',
    '.env.test',
  ];

  validateFileStructure(): void {
    console.log('🔍 Validating test file structure...');
    
    const testDir = join(__dirname);
    const backendDir = join(__dirname, '../../');
    
    // Check test files
    for (const file of this.testFiles) {
      const filePath = join(testDir, file);
      if (!existsSync(filePath)) {
        throw new Error(`Missing test file: ${file}`);
      }
    }
    
    // Check config files
    for (const file of this.configFiles) {
      const filePath = file === '.env.test' 
        ? join(backendDir, file)
        : join(testDir, file);
      
      if (!existsSync(filePath)) {
        throw new Error(`Missing config file: ${file}`);
      }
    }
    
    console.log('✅ All test files present');
  }

  validateTypeScript(): void {
    console.log('🔍 Validating TypeScript compilation...');
    
    try {
      execSync('npx tsc --noEmit --project tsconfig.json', {
        cwd: join(__dirname, '../../'),
        stdio: 'pipe',
      });
      console.log('✅ TypeScript compilation successful');
    } catch (error) {
      console.error('❌ TypeScript compilation failed');
      throw error;
    }
  }

  validateDependencies(): void {
    console.log('🔍 Validating dependencies...');
    
    const requiredDeps = [
      '@nestjs/testing',
      'supertest',
      '@types/supertest',
      'jest',
      'ts-jest',
      '@prisma/client',
      'bcrypt',
    ];
    
    try {
      const packageJson = require('../../package.json');
      const allDeps = {
        ...packageJson.dependencies,
        ...packageJson.devDependencies,
      };
      
      for (const dep of requiredDeps) {
        if (!allDeps[dep]) {
          throw new Error(`Missing dependency: ${dep}`);
        }
      }
      
      console.log('✅ All required dependencies present');
    } catch (error) {
      console.error('❌ Dependency validation failed');
      throw error;
    }
  }

  validateEnvironment(): void {
    console.log('🔍 Validating environment configuration...');
    
    const envFile = join(__dirname, '../../.env.test');
    if (!existsSync(envFile)) {
      throw new Error('Missing .env.test file');
    }
    
    // Load and validate environment variables
    require('dotenv').config({ path: envFile });
    
    const requiredEnvVars = [
      'DATABASE_URL',
      'JWT_SECRET',
      'JWT_EXPIRES_IN',
    ];
    
    for (const envVar of requiredEnvVars) {
      if (!process.env[envVar]) {
        throw new Error(`Missing environment variable: ${envVar}`);
      }
    }
    
    console.log('✅ Environment configuration valid');
  }

  validateTestStructure(): void {
    console.log('🔍 Validating test structure...');
    
    const testContent = {
      'auth.integration.spec.ts': [
        'POST /api/auth/register',
        'POST /api/auth/login',
        'GET /api/auth/me',
        'PUT /api/auth/profile',
      ],
      'settings.integration.spec.ts': [
        'GET /api/settings/pharmacy',
        'PUT /api/settings/pharmacy',
      ],
      'suppliers.integration.spec.ts': [
        'POST /api/suppliers',
        'GET /api/suppliers',
        'GET /api/suppliers/:id',
        'PATCH /api/suppliers/:id',
        'DELETE /api/suppliers/:id',
      ],
      'supplier-documents.integration.spec.ts': [
        'POST /api/suppliers/:id/documents',
        'GET /api/suppliers/:id/documents',
      ],
      'supplier-payments.integration.spec.ts': [
        'POST /api/suppliers/:id/payments',
        'GET /api/suppliers/:id/payments',
        'PATCH /api/suppliers/:id/payments/:paymentId',
      ],
    };
    
    for (const [file, expectedTests] of Object.entries(testContent)) {
      const filePath = join(__dirname, file);
      const content = require('fs').readFileSync(filePath, 'utf8');
      
      for (const test of expectedTests) {
        if (!content.includes(test)) {
          throw new Error(`Missing test case "${test}" in ${file}`);
        }
      }
    }
    
    console.log('✅ Test structure validation passed');
  }

  countTestCases(): void {
    console.log('📊 Counting test cases...');
    
    let totalTests = 0;
    
    for (const file of this.testFiles.filter(f => f.endsWith('.spec.ts'))) {
      const filePath = join(__dirname, file);
      const content = require('fs').readFileSync(filePath, 'utf8');
      
      // Count 'it(' and 'test(' occurrences
      const itMatches = content.match(/\s+it\(/g) || [];
      const testMatches = content.match(/\s+test\(/g) || [];
      const fileTests = itMatches.length + testMatches.length;
      
      console.log(`  ${file}: ${fileTests} test cases`);
      totalTests += fileTests;
    }
    
    console.log(`📈 Total test cases: ${totalTests}`);
    
    if (totalTests < 100) {
      console.warn('⚠️  Warning: Less than 100 test cases. Consider adding more comprehensive tests.');
    }
  }

  validateEndpointCoverage(): void {
    console.log('🔍 Validating endpoint coverage...');
    
    const expectedEndpoints = [
      // Auth endpoints
      'POST /api/auth/register',
      'POST /api/auth/login',
      'POST /api/auth/logout',
      'GET /api/auth/me',
      'GET /api/auth/profile',
      'PUT /api/auth/profile',
      
      // Settings endpoints
      'GET /api/settings/pharmacy',
      'PUT /api/settings/pharmacy',
      
      // Supplier endpoints
      'POST /api/suppliers',
      'GET /api/suppliers',
      'GET /api/suppliers/stats',
      'GET /api/suppliers/:id',
      'PATCH /api/suppliers/:id',
      'DELETE /api/suppliers/:id',
      
      // Document endpoints
      'GET /api/suppliers/:id/documents',
      'POST /api/suppliers/:id/documents',
      'GET /api/suppliers/:id/documents/:documentId',
      'GET /api/suppliers/:id/documents/:documentId/download',
      'PATCH /api/suppliers/:id/documents/:documentId',
      'DELETE /api/suppliers/:id/documents/:documentId',
      'POST /api/suppliers/:id/documents/upload',
      
      // Payment endpoints
      'GET /api/suppliers/:id/payments',
      'POST /api/suppliers/:id/payments',
      'GET /api/suppliers/:id/payments/summary',
      'GET /api/suppliers/:id/payments/:paymentId',
      'PATCH /api/suppliers/:id/payments/:paymentId',
      'DELETE /api/suppliers/:id/payments/:paymentId',
      
      // App root
      'GET /api',
    ];
    
    const allTestContent = this.testFiles
      .filter(f => f.endsWith('.spec.ts'))
      .map(f => require('fs').readFileSync(join(__dirname, f), 'utf8'))
      .join('\n');
    
    const missingEndpoints = expectedEndpoints.filter(endpoint => 
      !allTestContent.includes(endpoint)
    );
    
    if (missingEndpoints.length > 0) {
      console.warn('⚠️  Missing endpoint tests:');
      missingEndpoints.forEach(endpoint => console.warn(`    ${endpoint}`));
    } else {
      console.log('✅ All expected endpoints covered');
    }
    
    console.log(`📊 Endpoint coverage: ${expectedEndpoints.length - missingEndpoints.length}/${expectedEndpoints.length}`);
  }

  async run(): Promise<void> {
    console.log('🧪 Integration Test Suite Validation');
    console.log('=====================================');
    
    try {
      this.validateFileStructure();
      this.validateDependencies();
      this.validateEnvironment();
      this.validateTypeScript();
      this.validateTestStructure();
      this.validateEndpointCoverage();
      this.countTestCases();
      
      console.log('=====================================');
      console.log('✅ All validations passed! Test suite is ready to run.');
      console.log('');
      console.log('Next steps:');
      console.log('  1. Ensure PostgreSQL is running');
      console.log('  2. Create test database: createdb apotek_test');
      console.log('  3. Run tests: pnpm test:integration');
      
    } catch (error) {
      console.error('=====================================');
      console.error('❌ Validation failed:', error.message);
      process.exit(1);
    }
  }
}

// Run validation if called directly
if (require.main === module) {
  new TestValidator().run().catch(console.error);
}

export { TestValidator };
