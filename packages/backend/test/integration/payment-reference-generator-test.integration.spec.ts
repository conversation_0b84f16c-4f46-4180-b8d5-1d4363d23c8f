import { Test, TestingModule } from '@nestjs/testing';
import { ReferenceGeneratorService } from '../../src/common/services/reference-generator.service';
import { PrismaService } from '../../src/prisma/prisma.service';
import { SettingsService } from '../../src/settings/settings.service';

describe('Reference Generator Service (Unit)', () => {
  let service: ReferenceGeneratorService;
  let prisma: PrismaService;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ReferenceGeneratorService,
        PrismaService,
        SettingsService,
      ],
    }).compile();

    service = module.get<ReferenceGeneratorService>(ReferenceGeneratorService);
    prisma = module.get<PrismaService>(PrismaService);
  });

  afterAll(async () => {
    await prisma.appSettings.deleteMany();
    await prisma.$disconnect();
  });

  beforeEach(async () => {
    // Ensure pharmacy settings exist
    await prisma.appSettings.upsert({
      where: { settingKey: 'pharmacyName' },
      update: { settingValue: 'Apotek Sehat Bersama' },
      create: { settingKey: 'pharmacyName', settingValue: 'Apotek Sehat Bersama' },
    });
  });

  describe('generatePaymentReference', () => {
    it('should generate a payment reference number', async () => {
      const reference = await service.generatePaymentReference();
      
      expect(typeof reference).toBe('string');
      expect(reference).toMatch(/^PAY-[A-Z]{2,4}SUP-\d{6}-\d{3}$/);
      expect(reference).toContain('PAY-ASBSUP-');
      
      console.log('Generated reference:', reference);
    });

    it('should generate unique reference numbers', async () => {
      const reference1 = await service.generatePaymentReference();

      // Create a payment with this reference
      const user = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          firstName: 'Test',
          lastName: 'User',
          role: 'ADMIN',
        },
      });

      const supplier = await prisma.supplier.create({
        data: {
          code: 'TEST001',
          name: 'Test Supplier',
          type: 'DISTRIBUTOR',
          createdBy: user.id,
          updatedBy: user.id,
        },
      });

      const payment = await prisma.supplierPayment.create({
        data: {
          supplierId: supplier.id,
          amount: 100000,
          paymentMethod: 'TRANSFER',
          paymentDate: new Date(),
          status: 'PENDING',
          reference: reference1,
          createdBy: user.id,
        },
      });

      const reference2 = await service.generatePaymentReference();
      console.log('Generated reference 1:', reference1);
      console.log('Generated reference 2:', reference2);
      expect(reference1).not.toBe(reference2);
      
      // Cleanup
      await prisma.supplierPayment.delete({
        where: { id: payment.id },
      });
      await prisma.supplier.delete(
        { where: { id: supplier.id } },
      );
      await prisma.user.delete(
        { where: { email: '<EMAIL>' } },
      );
    });

    it('should include current date in DDMMYY format', async () => {
      const reference = await service.generatePaymentReference();
      
      const today = new Date();
      const expectedDate = [
        today.getDate().toString().padStart(2, '0'),
        (today.getMonth() + 1).toString().padStart(2, '0'),
        today.getFullYear().toString().slice(-2)
      ].join('');

      expect(reference).toContain(expectedDate);
      console.log(`Expected date: ${expectedDate}, Reference: ${reference}`);
    });
  });

  describe('validateReferenceUniqueness', () => {
    it('should validate a unique reference number', async () => {
      const testReference = 'PAY-ASBSUP-110624-999';
      
      const isUnique = await service.validateReferenceUniqueness(testReference);
      expect(isUnique).toBe(true);
    });

    it('should detect duplicate reference numbers', async () => {
      // First create a test user
      const testUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: 'hashedpassword',
          firstName: 'Test',
          lastName: 'User',
          role: 'ADMIN',
        },
      });

      // Create a supplier
      const supplier = await prisma.supplier.create({
        data: {
          code: 'TEST001',
          name: 'Test Supplier',
          type: 'DISTRIBUTOR',
          createdBy: testUser.id,
          updatedBy: testUser.id,
        },
      });

      const testReference = 'PAY-ASBSUP-110624-001';
      
      // Create a payment with this reference
      await prisma.supplierPayment.create({
        data: {
          supplierId: supplier.id,
          amount: 100000,
          paymentMethod: 'TRANSFER',
          paymentDate: new Date(),
          status: 'PENDING',
          reference: testReference,
          createdBy: testUser.id,
        },
      });

      // Now validate the same reference - should be false (not unique)
      const isUnique = await service.validateReferenceUniqueness(testReference);
      expect(isUnique).toBe(false);
      
      // Cleanup
      await prisma.supplierPayment.deleteMany();
      await prisma.supplier.deleteMany();
      await prisma.user.deleteMany();
    });
  });

  describe('Pharmacy Name Initials', () => {
    it('should generate correct initials from different pharmacy names', async () => {
      const testCases = [
        { name: 'Apotek Sehat Bersama', expected: 'ASB' },
        { name: 'Kimia Farma', expected: 'KF' },
        { name: 'Apotek Mandiri Sejahtera Utama', expected: 'AMSU' },
        { name: 'Apotek', expected: 'APT' }, // fallback for single word
      ];

      for (const testCase of testCases) {
        await prisma.appSettings.upsert({
          where: { settingKey: 'pharmacyName' },
          update: { settingValue: testCase.name },
          create: { settingKey: 'pharmacyName', settingValue: testCase.name },
        });

        const reference = await service.generatePaymentReference();
        expect(reference).toContain(`PAY-${testCase.expected}SUP-`);
        console.log(`${testCase.name} -> ${testCase.expected}: ${reference}`);
      }
    });
  });
});
