/**
 * Comprehensive Unit Conversion Service Tests
 * 
 * This test suite validates the unit conversion service across all unit types
 * and hierarchies used in the pharmacy POS system, ensuring mathematical
 * accuracy and proper handling of edge cases.
 */

import { Test, TestingModule } from '@nestjs/testing';
import { PrismaService } from '../src/prisma/prisma.service';
import { UnitConversionService } from '../src/common/services/unit-conversion.service';
import { UnitType } from '@prisma/client';

describe('UnitConversionService - Comprehensive Tests', () => {
  let service: UnitConversionService;
  let prisma: PrismaService;
  let module: TestingModule;

  // Test data storage
  let testUnits: any = {};
  let testProducts: any = {};

  beforeAll(async () => {
    module = await Test.createTestingModule({
      providers: [
        UnitConversionService,
        {
          provide: PrismaService,
          useValue: {
            product: {
              findUnique: jest.fn(),
            },
            productUnitHierarchy: {
              findMany: jest.fn(),
            },
          },
        },
      ],
    }).compile();

    service = module.get<UnitConversionService>(UnitConversionService);
    prisma = module.get<PrismaService>(PrismaService);

    // Setup comprehensive test data
    await setupTestData();
  });

  afterAll(async () => {
    await module.close();
  });

  async function setupTestData() {
    // Create test units for all types
    testUnits = {
      // COUNT type units
      tablet: { id: 'unit-tablet', name: 'Tablet', type: UnitType.COUNT, isBaseUnit: true },
      kapsul: { id: 'unit-kapsul', name: 'Kapsul', type: UnitType.COUNT, isBaseUnit: true },
      pcs: { id: 'unit-pcs', name: 'Pcs', type: UnitType.COUNT, isBaseUnit: true },

      // VOLUME type units
      ml: { id: 'unit-ml', name: 'Mililiter', type: UnitType.VOLUME, isBaseUnit: true },
      liter: { id: 'unit-liter', name: 'Liter', type: UnitType.VOLUME, isBaseUnit: false },

      // WEIGHT type units
      gram: { id: 'unit-gram', name: 'Gram', type: UnitType.WEIGHT, isBaseUnit: true },
      mg: { id: 'unit-mg', name: 'Miligram', type: UnitType.WEIGHT, isBaseUnit: true },
      kg: { id: 'unit-kg', name: 'Kilogram', type: UnitType.WEIGHT, isBaseUnit: false },

      // PACKAGE type units
      strip: { id: 'unit-strip', name: 'Strip', type: UnitType.PACKAGE, isBaseUnit: false },
      box: { id: 'unit-box', name: 'Box', type: UnitType.PACKAGE, isBaseUnit: false },
      botol: { id: 'unit-botol', name: 'Botol', type: UnitType.PACKAGE, isBaseUnit: false },
      tube: { id: 'unit-tube', name: 'Tube', type: UnitType.PACKAGE, isBaseUnit: false },
      sachet: { id: 'unit-sachet', name: 'Sachet', type: UnitType.PACKAGE, isBaseUnit: false },

      // LENGTH type units
      meter: { id: 'unit-meter', name: 'Meter', type: UnitType.LENGTH, isBaseUnit: true },
      cm: { id: 'unit-cm', name: 'Sentimeter', type: UnitType.LENGTH, isBaseUnit: true },
      mm: { id: 'unit-mm', name: 'Milimeter', type: UnitType.LENGTH, isBaseUnit: true },
    };

    // Create test products with various unit hierarchies
    testProducts = {
      // Tablet-based medicine (Tablet → Strip → Box)
      paracetamol: {
        id: 'prod-paracetamol',
        name: 'Paracetamol 500mg',
        baseUnitId: testUnits.tablet.id,
        baseUnit: testUnits.tablet,
        hierarchies: [
          { unitId: testUnits.tablet.id, conversionFactor: 1, level: 0 },
          { unitId: testUnits.strip.id, conversionFactor: 10, level: 1 }, // 1 strip = 10 tablets
          { unitId: testUnits.box.id, conversionFactor: 100, level: 2 }, // 1 box = 100 tablets
        ]
      },

      // Liquid medicine (ml → Botol)
      obhCombi: {
        id: 'prod-obh',
        name: 'OBH Combi',
        baseUnitId: testUnits.ml.id,
        baseUnit: testUnits.ml,
        hierarchies: [
          { unitId: testUnits.ml.id, conversionFactor: 1, level: 0 },
          { unitId: testUnits.botol.id, conversionFactor: 60, level: 1 }, // 1 bottle = 60ml
        ]
      },

      // Weight-based medicine (Gram → Tube)
      salep: {
        id: 'prod-salep',
        name: 'Salep Gentamicin',
        baseUnitId: testUnits.gram.id,
        baseUnit: testUnits.gram,
        hierarchies: [
          { unitId: testUnits.gram.id, conversionFactor: 1, level: 0 },
          { unitId: testUnits.tube.id, conversionFactor: 5, level: 1 }, // 1 tube = 5 grams
        ]
      },

      // Complex hierarchy (Tablet → Tube → Box)
      vitaminC: {
        id: 'prod-vitamin-c',
        name: 'Vitamin C Effervescent',
        baseUnitId: testUnits.tablet.id,
        baseUnit: testUnits.tablet,
        hierarchies: [
          { unitId: testUnits.tablet.id, conversionFactor: 1, level: 0 },
          { unitId: testUnits.tube.id, conversionFactor: 10, level: 1 }, // 1 tube = 10 tablets
          { unitId: testUnits.box.id, conversionFactor: 60, level: 2 }, // 1 box = 60 tablets
        ]
      },

      // Liquid with sachet (ml → Sachet → Box)
      jamuTolakAngin: {
        id: 'prod-jamu',
        name: 'Jamu Tolak Angin',
        baseUnitId: testUnits.ml.id,
        baseUnit: testUnits.ml,
        hierarchies: [
          { unitId: testUnits.ml.id, conversionFactor: 1, level: 0 },
          { unitId: testUnits.sachet.id, conversionFactor: 15, level: 1 }, // 1 sachet = 15ml
          { unitId: testUnits.box.id, conversionFactor: 120, level: 2 }, // 1 box = 120ml
        ]
      },

      // Controlled substance with smaller box (Tablet → Strip → Box)
      alprazolam: {
        id: 'prod-alprazolam',
        name: 'Alprazolam 0.5mg',
        baseUnitId: testUnits.tablet.id,
        baseUnit: testUnits.tablet,
        hierarchies: [
          { unitId: testUnits.tablet.id, conversionFactor: 1, level: 0 },
          { unitId: testUnits.strip.id, conversionFactor: 10, level: 1 }, // 1 strip = 10 tablets
          { unitId: testUnits.box.id, conversionFactor: 30, level: 2 }, // 1 box = 30 tablets (smaller)
        ]
      },
    };
  }

  function mockProductData(productKey: string) {
    const product = testProducts[productKey];

    // Mock product lookup
    (prisma.product.findUnique as jest.Mock).mockResolvedValue(product);

    // Mock hierarchy lookup
    const hierarchyData = product.hierarchies.map((h: any) => ({
      ...h,
      unit: testUnits[Object.keys(testUnits).find(key => testUnits[key].id === h.unitId) || ''],
      productId: product.id,
      parentUnitId: null,
      isActive: true,
    }));

    (prisma.productUnitHierarchy.findMany as jest.Mock).mockResolvedValue(hierarchyData);
  }

  describe('Basic Unit Conversion Tests', () => {
    describe('Same Unit Conversions', () => {
      it('should return same quantity for identical units', async () => {
        mockProductData('paracetamol');

        const result = await service.convertUnits(
          testProducts.paracetamol.id,
          testUnits.tablet.id,
          testUnits.tablet.id,
          50
        );

        expect(result.success).toBe(true);
        expect(result.convertedQuantity).toBe(50);
      });
    });

    describe('Tablet-based Medicine Conversions (Paracetamol)', () => {
      beforeEach(() => {
        mockProductData('paracetamol');
      });

      it('should convert tablets to strips correctly', async () => {
        // 25 tablets = 2.5 strips (25 ÷ 10)
        const result = await service.convertUnits(
          testProducts.paracetamol.id,
          testUnits.tablet.id,
          testUnits.strip.id,
          25
        );

        expect(result.success).toBe(true);
        expect(result.convertedQuantity).toBe(2.5);
      });

      it('should convert strips to tablets correctly', async () => {
        // 3 strips = 30 tablets (3 × 10)
        const result = await service.convertUnits(
          testProducts.paracetamol.id,
          testUnits.strip.id,
          testUnits.tablet.id,
          3
        );

        expect(result.success).toBe(true);
        expect(result.convertedQuantity).toBe(30);
      });

      it('should convert tablets to boxes correctly', async () => {
        // 250 tablets = 2.5 boxes (250 ÷ 100)
        const result = await service.convertUnits(
          testProducts.paracetamol.id,
          testUnits.tablet.id,
          testUnits.box.id,
          250
        );

        expect(result.success).toBe(true);
        expect(result.convertedQuantity).toBe(2.5);
      });

      it('should convert boxes to tablets correctly', async () => {
        // 2 boxes = 200 tablets (2 × 100)
        const result = await service.convertUnits(
          testProducts.paracetamol.id,
          testUnits.box.id,
          testUnits.tablet.id,
          2
        );

        expect(result.success).toBe(true);
        expect(result.convertedQuantity).toBe(200);
      });

      it('should convert strips to boxes correctly', async () => {
        // 15 strips = 1.5 boxes (15 strips × 10 tablets/strip ÷ 100 tablets/box)
        const result = await service.convertUnits(
          testProducts.paracetamol.id,
          testUnits.strip.id,
          testUnits.box.id,
          15
        );

        expect(result.success).toBe(true);
        expect(result.convertedQuantity).toBe(1.5);
      });

      it('should convert boxes to strips correctly', async () => {
        // 2.5 boxes = 25 strips (2.5 boxes × 100 tablets/box ÷ 10 tablets/strip)
        const result = await service.convertUnits(
          testProducts.paracetamol.id,
          testUnits.box.id,
          testUnits.strip.id,
          2.5
        );

        expect(result.success).toBe(true);
        expect(result.convertedQuantity).toBe(25);
      });
    });

    describe('Liquid Medicine Conversions (OBH Combi)', () => {
      beforeEach(() => {
        mockProductData('obhCombi');
      });

      it('should convert ml to bottles correctly', async () => {
        // 180 ml = 3 bottles (180 ÷ 60)
        const result = await service.convertUnits(
          testProducts.obhCombi.id,
          testUnits.ml.id,
          testUnits.botol.id,
          180
        );

        expect(result.success).toBe(true);
        expect(result.convertedQuantity).toBe(3);
      });

      it('should convert bottles to ml correctly', async () => {
        // 2.5 bottles = 150 ml (2.5 × 60)
        const result = await service.convertUnits(
          testProducts.obhCombi.id,
          testUnits.botol.id,
          testUnits.ml.id,
          2.5
        );

        expect(result.success).toBe(true);
        expect(result.convertedQuantity).toBe(150);
      });
    });

    describe('Weight-based Medicine Conversions (Salep)', () => {
      beforeEach(() => {
        mockProductData('salep');
      });

      it('should convert grams to tubes correctly', async () => {
        // 15 grams = 3 tubes (15 ÷ 5)
        const result = await service.convertUnits(
          testProducts.salep.id,
          testUnits.gram.id,
          testUnits.tube.id,
          15
        );

        expect(result.success).toBe(true);
        expect(result.convertedQuantity).toBe(3);
      });

      it('should convert tubes to grams correctly', async () => {
        // 2.5 tubes = 12.5 grams (2.5 × 5)
        const result = await service.convertUnits(
          testProducts.salep.id,
          testUnits.tube.id,
          testUnits.gram.id,
          2.5
        );

        expect(result.success).toBe(true);
        expect(result.convertedQuantity).toBe(12.5);
      });
    });
  });

  describe('Complex Multi-level Hierarchy Tests', () => {
    describe('Vitamin C (Tablet → Tube → Box)', () => {
      beforeEach(() => {
        mockProductData('vitaminC');
      });

      it('should convert tablets to tubes correctly', async () => {
        // 25 tablets = 2.5 tubes (25 ÷ 10)
        const result = await service.convertUnits(
          testProducts.vitaminC.id,
          testUnits.tablet.id,
          testUnits.tube.id,
          25
        );

        expect(result.success).toBe(true);
        expect(result.convertedQuantity).toBe(2.5);
      });

      it('should convert tablets to boxes correctly', async () => {
        // 120 tablets = 2 boxes (120 ÷ 60)
        const result = await service.convertUnits(
          testProducts.vitaminC.id,
          testUnits.tablet.id,
          testUnits.box.id,
          120
        );

        expect(result.success).toBe(true);
        expect(result.convertedQuantity).toBe(2);
      });

      it('should convert tubes to boxes correctly', async () => {
        // 9 tubes = 1.5 boxes (9 tubes × 10 tablets/tube ÷ 60 tablets/box)
        const result = await service.convertUnits(
          testProducts.vitaminC.id,
          testUnits.tube.id,
          testUnits.box.id,
          9
        );

        expect(result.success).toBe(true);
        expect(result.convertedQuantity).toBe(1.5);
      });

      it('should convert boxes to tubes correctly', async () => {
        // 1.5 boxes = 9 tubes (1.5 boxes × 60 tablets/box ÷ 10 tablets/tube)
        const result = await service.convertUnits(
          testProducts.vitaminC.id,
          testUnits.box.id,
          testUnits.tube.id,
          1.5
        );

        expect(result.success).toBe(true);
        expect(result.convertedQuantity).toBe(9);
      });
    });

    describe('Jamu Tolak Angin (ml → Sachet → Box)', () => {
      beforeEach(() => {
        mockProductData('jamuTolakAngin');
      });

      it('should convert ml to sachets correctly', async () => {
        // 45 ml = 3 sachets (45 ÷ 15)
        const result = await service.convertUnits(
          testProducts.jamuTolakAngin.id,
          testUnits.ml.id,
          testUnits.sachet.id,
          45
        );

        expect(result.success).toBe(true);
        expect(result.convertedQuantity).toBe(3);
      });

      it('should convert ml to boxes correctly', async () => {
        // 240 ml = 2 boxes (240 ÷ 120)
        const result = await service.convertUnits(
          testProducts.jamuTolakAngin.id,
          testUnits.ml.id,
          testUnits.box.id,
          240
        );

        expect(result.success).toBe(true);
        expect(result.convertedQuantity).toBe(2);
      });

      it('should convert sachets to boxes correctly', async () => {
        // 12 sachets = 1.5 boxes (12 sachets × 15 ml/sachet ÷ 120 ml/box)
        const result = await service.convertUnits(
          testProducts.jamuTolakAngin.id,
          testUnits.sachet.id,
          testUnits.box.id,
          12
        );

        expect(result.success).toBe(true);
        expect(result.convertedQuantity).toBe(1.5);
      });
    });
  });

  describe('Real-world Pharmacy Scenarios', () => {
    describe('Alprazolam (Controlled Substance)', () => {
      beforeEach(() => {
        mockProductData('alprazolam');
      });

      it('should handle the original bug scenario correctly', async () => {
        // Original issue: 28 boxes + 22 strips should = 1060 tablets

        // 28 boxes = 840 tablets (28 × 30)
        const boxResult = await service.convertToBaseUnit(
          testProducts.alprazolam.id,
          testUnits.box.id,
          28
        );

        expect(boxResult.success).toBe(true);
        expect(boxResult.convertedQuantity).toBe(840);

        // 22 strips = 220 tablets (22 × 10)
        const stripResult = await service.convertToBaseUnit(
          testProducts.alprazolam.id,
          testUnits.strip.id,
          22
        );

        expect(stripResult.success).toBe(true);
        expect(stripResult.convertedQuantity).toBe(220);

        // Total should be 1060 tablets
        const totalDemand = boxResult.convertedQuantity! + stripResult.convertedQuantity!;
        expect(totalDemand).toBe(1060);
      });

      it('should convert fractional quantities correctly', async () => {
        // 0.5 boxes = 15 tablets (0.5 × 30)
        const result = await service.convertToBaseUnit(
          testProducts.alprazolam.id,
          testUnits.box.id,
          0.5
        );

        expect(result.success).toBe(true);
        expect(result.convertedQuantity).toBe(15);
      });

      it('should handle mixed unit calculations', async () => {
        // Scenario: 5 boxes + 3 strips = ? tablets
        // 5 boxes = 150 tablets, 3 strips = 30 tablets, total = 180 tablets

        const boxResult = await service.convertToBaseUnit(
          testProducts.alprazolam.id,
          testUnits.box.id,
          5
        );

        const stripResult = await service.convertToBaseUnit(
          testProducts.alprazolam.id,
          testUnits.strip.id,
          3
        );

        expect(boxResult.success).toBe(true);
        expect(stripResult.success).toBe(true);
        expect(boxResult.convertedQuantity).toBe(150);
        expect(stripResult.convertedQuantity).toBe(30);

        const total = boxResult.convertedQuantity! + stripResult.convertedQuantity!;
        expect(total).toBe(180);
      });
    });
  });

  describe('Base Unit Conversion Methods', () => {
    describe('convertToBaseUnit', () => {
      beforeEach(() => {
        mockProductData('paracetamol');
      });

      it('should convert to base unit correctly', async () => {
        // 5 strips = 50 tablets (base unit)
        const result = await service.convertToBaseUnit(
          testProducts.paracetamol.id,
          testUnits.strip.id,
          5
        );

        expect(result.success).toBe(true);
        expect(result.convertedQuantity).toBe(50);
      });

      it('should handle base unit to base unit conversion', async () => {
        // 25 tablets = 25 tablets (no conversion)
        const result = await service.convertToBaseUnit(
          testProducts.paracetamol.id,
          testUnits.tablet.id,
          25
        );

        expect(result.success).toBe(true);
        expect(result.convertedQuantity).toBe(25);
      });
    });

    describe('convertFromBaseUnit', () => {
      beforeEach(() => {
        mockProductData('paracetamol');
      });

      it('should convert from base unit correctly', async () => {
        // 50 tablets (base) = 5 strips
        const result = await service.convertFromBaseUnit(
          testProducts.paracetamol.id,
          testUnits.strip.id,
          50
        );

        expect(result.success).toBe(true);
        expect(result.convertedQuantity).toBe(5);
      });

      it('should handle base unit to base unit conversion', async () => {
        // 25 tablets = 25 tablets (no conversion)
        const result = await service.convertFromBaseUnit(
          testProducts.paracetamol.id,
          testUnits.tablet.id,
          25
        );

        expect(result.success).toBe(true);
        expect(result.convertedQuantity).toBe(25);
      });
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle non-existent product', async () => {
      (prisma.product.findUnique as jest.Mock).mockResolvedValue(null);

      const result = await service.convertToBaseUnit(
        'non-existent-product',
        testUnits.tablet.id,
        10
      );

      expect(result.success).toBe(false);
      expect(result.error).toContain('Product not found');
    });

    it('should handle missing unit hierarchy', async () => {
      (prisma.product.findUnique as jest.Mock).mockResolvedValue(testProducts.paracetamol);
      (prisma.productUnitHierarchy.findMany as jest.Mock).mockResolvedValue([]);

      const result = await service.convertUnits(
        testProducts.paracetamol.id,
        testUnits.tablet.id,
        testUnits.strip.id,
        10
      );

      expect(result.success).toBe(false);
      expect(result.error).toContain('No conversion path found');
    });

    it('should handle invalid unit conversion path', async () => {
      mockProductData('paracetamol');

      const result = await service.convertUnits(
        testProducts.paracetamol.id,
        'invalid-unit-id',
        testUnits.strip.id,
        10
      );

      expect(result.success).toBe(false);
      expect(result.error).toContain('No conversion path found');
    });

    it('should handle zero quantity', async () => {
      mockProductData('paracetamol');

      const result = await service.convertUnits(
        testProducts.paracetamol.id,
        testUnits.tablet.id,
        testUnits.strip.id,
        0
      );

      expect(result.success).toBe(true);
      expect(result.convertedQuantity).toBe(0);
    });

    it('should handle very small fractional quantities', async () => {
      mockProductData('paracetamol');

      const result = await service.convertUnits(
        testProducts.paracetamol.id,
        testUnits.tablet.id,
        testUnits.strip.id,
        0.1
      );

      expect(result.success).toBe(true);
      expect(result.convertedQuantity).toBe(0.01);
    });

    it('should round to 4 decimal places', async () => {
      mockProductData('paracetamol');

      // 1/3 tablets = 0.0333... strips
      const result = await service.convertUnits(
        testProducts.paracetamol.id,
        testUnits.tablet.id,
        testUnits.strip.id,
        1 / 3
      );

      expect(result.success).toBe(true);
      expect(result.convertedQuantity).toBe(0.0333); // Rounded to 4 decimal places
    });
  });

  describe('Mathematical Accuracy Validation', () => {
    beforeEach(() => {
      mockProductData('paracetamol');
    });

    it('should maintain mathematical consistency in round-trip conversions', async () => {
      // Convert 100 tablets to strips, then back to tablets
      const toStrips = await service.convertUnits(
        testProducts.paracetamol.id,
        testUnits.tablet.id,
        testUnits.strip.id,
        100
      );

      const backToTablets = await service.convertUnits(
        testProducts.paracetamol.id,
        testUnits.strip.id,
        testUnits.tablet.id,
        toStrips.convertedQuantity!
      );

      expect(toStrips.success).toBe(true);
      expect(backToTablets.success).toBe(true);
      expect(backToTablets.convertedQuantity).toBe(100);
    });

    it('should handle large quantities accurately', async () => {
      // 10,000 tablets = 1,000 strips
      const result = await service.convertUnits(
        testProducts.paracetamol.id,
        testUnits.tablet.id,
        testUnits.strip.id,
        10000
      );

      expect(result.success).toBe(true);
      expect(result.convertedQuantity).toBe(1000);
    });

    it('should validate conversion factor relationships', async () => {
      // Test the relationship: 1 box = 10 strips = 100 tablets

      // 1 box = 100 tablets
      const boxToTablets = await service.convertUnits(
        testProducts.paracetamol.id,
        testUnits.box.id,
        testUnits.tablet.id,
        1
      );

      // 1 box = 10 strips
      const boxToStrips = await service.convertUnits(
        testProducts.paracetamol.id,
        testUnits.box.id,
        testUnits.strip.id,
        1
      );

      // 10 strips = 100 tablets
      const stripsToTablets = await service.convertUnits(
        testProducts.paracetamol.id,
        testUnits.strip.id,
        testUnits.tablet.id,
        10
      );

      expect(boxToTablets.convertedQuantity).toBe(100);
      expect(boxToStrips.convertedQuantity).toBe(10);
      expect(stripsToTablets.convertedQuantity).toBe(100);
    });
  });
});
