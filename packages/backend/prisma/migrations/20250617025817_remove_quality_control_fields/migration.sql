/*
  Warnings:

  - You are about to drop the column `inspectionBy` on the `goods_receipt_items` table. All the data in the column will be lost.
  - You are about to drop the column `inspectionDate` on the `goods_receipt_items` table. All the data in the column will be lost.
  - You are about to drop the column `qualityNotes` on the `goods_receipt_items` table. All the data in the column will be lost.
  - You are about to drop the column `qualityStatus` on the `goods_receipt_items` table. All the data in the column will be lost.
  - You are about to drop the column `inspectionBy` on the `goods_receipts` table. All the data in the column will be lost.
  - You are about to drop the column `inspectionDate` on the `goods_receipts` table. All the data in the column will be lost.
  - You are about to drop the column `qualityNotes` on the `goods_receipts` table. All the data in the column will be lost.
  - You are about to drop the column `qualityStatus` on the `goods_receipts` table. All the data in the column will be lost.

*/
-- DropForeignKey
ALTER TABLE "goods_receipt_items" DROP CONSTRAINT "goods_receipt_items_inspectionBy_fkey";

-- DropForeignKey
ALTER TABLE "goods_receipts" DROP CONSTRAINT "goods_receipts_inspectionBy_fkey";

-- AlterTable
ALTER TABLE "goods_receipt_items" DROP COLUMN "inspectionBy",
DROP COLUMN "inspectionDate",
DROP COLUMN "qualityNotes",
DROP COLUMN "qualityStatus";

-- AlterTable
ALTER TABLE "goods_receipts" DROP COLUMN "inspectionBy",
DROP COLUMN "inspectionDate",
DROP COLUMN "qualityNotes",
DROP COLUMN "qualityStatus";

-- DropEnum
DROP TYPE "QualityControlStatus";
