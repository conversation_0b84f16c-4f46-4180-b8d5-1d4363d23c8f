-- AlterTable
ALTER TABLE "goods_receipt_items" ADD COLUMN     "isSubstitution" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "originalProductId" TEXT,
ADD COLUMN     "substitutionApprovedAt" TIMESTAMP(3),
ADD COLUMN     "substitutionApprovedBy" TEXT,
ADD COLUMN     "substitutionNotes" TEXT,
ADD COLUMN     "substitutionReason" TEXT,
ADD COLUMN     "userId" TEXT;

-- AddForeignKey
ALTER TABLE "goods_receipt_items" ADD CONSTRAINT "goods_receipt_items_originalProductId_fkey" FOREIGN KEY ("originalProductId") REFERENCES "products"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "goods_receipt_items" ADD CONSTRAINT "goods_receipt_items_substitutionApprovedBy_fkey" FOREIGN KEY ("substitutionApprovedBy") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeign<PERSON><PERSON>
ALTER TABLE "goods_receipt_items" ADD CONSTRAINT "goods_receipt_items_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;
