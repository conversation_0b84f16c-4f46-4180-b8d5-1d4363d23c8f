/*
  Warnings:

  - The `referenceType` column on the `stock_movements` table would be dropped and recreated. This will lead to data loss if there is data in the column.

*/
-- CreateEnum
CREATE TYPE "ReferenceType" AS ENUM ('SALE', 'SALE_CANCELLATION', 'SALE_REFUND', '<PERSON>LE_DELETION', 'PURCHAS<PERSON>', 'PURCHASE_RETURN', 'INITIAL_STOCK', 'ADJUSTMENT', 'TRANSFER', 'ALLOCATION', 'DEALLOCATION', 'ACTIVATION', 'DEACTIVATION', 'CUSTOMER_RESERVATION', 'CUSTOMER_ORDER', 'EXPIRY_WRITE_OFF', 'OTHER');

-- AlterTable
ALTER TABLE "stock_movements" DROP COLUMN "referenceType",
ADD COLUMN     "referenceType" "ReferenceType";
