/*
  Warnings:

  - The values [IN_INSPECTION,APPROVED,PARTIALLY_APPROVED] on the enum `GoodsReceiptStatus` will be removed. If these variants are still used in the database, this will fail.
  - The values [APPROVED] on the enum `PurchaseOrderStatus` will be removed. If these variants are still used in the database, this will fail.
  - You are about to drop the column `qualitySpecs` on the `purchase_order_items` table. All the data in the column will be lost.
  - You are about to drop the column `approvedAt` on the `purchase_orders` table. All the data in the column will be lost.
  - You are about to drop the column `approvedBy` on the `purchase_orders` table. All the data in the column will be lost.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "GoodsReceiptStatus_new" AS ENUM ('PENDING', 'REJECTED', 'COMPLETED');
ALTER TABLE "goods_receipts" ALTER COLUMN "status" DROP DEFAULT;
ALTER TABLE "goods_receipts" ALTER COLUMN "status" TYPE "GoodsReceiptStatus_new" USING ("status"::text::"GoodsReceiptStatus_new");
ALTER TYPE "GoodsReceiptStatus" RENAME TO "GoodsReceiptStatus_old";
ALTER TYPE "GoodsReceiptStatus_new" RENAME TO "GoodsReceiptStatus";
DROP TYPE "GoodsReceiptStatus_old";
ALTER TABLE "goods_receipts" ALTER COLUMN "status" SET DEFAULT 'PENDING';
COMMIT;

-- AlterEnum
BEGIN;
CREATE TYPE "PurchaseOrderStatus_new" AS ENUM ('DRAFT', 'SUBMITTED', 'ORDERED', 'PARTIALLY_RECEIVED', 'COMPLETED', 'CANCELLED');
ALTER TABLE "purchase_orders" ALTER COLUMN "status" DROP DEFAULT;
ALTER TABLE "purchase_orders" ALTER COLUMN "status" TYPE "PurchaseOrderStatus_new" USING ("status"::text::"PurchaseOrderStatus_new");
ALTER TYPE "PurchaseOrderStatus" RENAME TO "PurchaseOrderStatus_old";
ALTER TYPE "PurchaseOrderStatus_new" RENAME TO "PurchaseOrderStatus";
DROP TYPE "PurchaseOrderStatus_old";
ALTER TABLE "purchase_orders" ALTER COLUMN "status" SET DEFAULT 'DRAFT';
COMMIT;

-- DropForeignKey
ALTER TABLE "purchase_orders" DROP CONSTRAINT "purchase_orders_approvedBy_fkey";

-- AlterTable
ALTER TABLE "purchase_order_items" DROP COLUMN "qualitySpecs";

-- AlterTable
ALTER TABLE "purchase_orders" DROP COLUMN "approvedAt",
DROP COLUMN "approvedBy";
