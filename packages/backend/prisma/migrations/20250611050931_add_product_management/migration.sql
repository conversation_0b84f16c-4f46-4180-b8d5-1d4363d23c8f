-- Create<PERSON><PERSON>
CREATE TYPE "UnitType" AS ENUM ('WEIGHT', 'VOLUME', 'COUNT', 'LENGTH', 'AREA', 'PACKAGE');

-- CreateEnum
CREATE TYPE "ProductType" AS ENUM ('MEDICINE', 'MEDICAL_DEVICE', 'SUPPLEMENT', 'COSMETIC', 'GENERAL');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "ProductCategory" AS ENUM ('ANALGESIC', 'ANTIBIOTIC', 'ANTACID', 'VITAMIN', 'SUPPLEMENT', 'COUGH_COLD', 'DIGESTIVE', 'TOPICAL', 'CARDIOVASCULAR', 'DIABETES', 'HYPERTENSION', 'MEDICAL_DEVICE', 'COSMETIC', 'BABY_CARE', 'PERSONAL_CARE', 'FIRST_AID', 'OTHER');

-- C<PERSON><PERSON><PERSON>
CREATE TYPE "MedicineClassification" AS ENUM ('OBAT_BEBAS', 'OBAT_BEBAS_TERBATAS', 'OBAT_KERAS', 'NARKOTIKA', 'JAMU', 'OBAT_HERBAL_TERSTANDAR', 'FITOFARMAKA', 'NON_MEDICINE');

-- CreateTable
CREATE TABLE "product_units" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "abbreviation" TEXT NOT NULL,
    "type" "UnitType" NOT NULL,
    "isBaseUnit" BOOLEAN NOT NULL DEFAULT false,
    "description" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "product_units_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "products" (
    "id" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "genericName" TEXT,
    "type" "ProductType" NOT NULL DEFAULT 'MEDICINE',
    "category" "ProductCategory" NOT NULL DEFAULT 'OTHER',
    "manufacturer" TEXT,
    "bpomNumber" TEXT,
    "medicineClassification" "MedicineClassification" NOT NULL DEFAULT 'NON_MEDICINE',
    "regulatorySymbol" TEXT,
    "baseUnitId" TEXT NOT NULL,
    "minimumStock" INTEGER,
    "maximumStock" INTEGER,
    "reorderPoint" INTEGER,
    "description" TEXT,
    "activeIngredient" TEXT,
    "strength" TEXT,
    "dosageForm" TEXT,
    "indication" TEXT,
    "contraindication" TEXT,
    "sideEffects" TEXT,
    "dosage" TEXT,
    "storage" TEXT,
    "notes" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "createdBy" TEXT,
    "updatedBy" TEXT,

    CONSTRAINT "products_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "product_unit_hierarchies" (
    "id" TEXT NOT NULL,
    "productId" TEXT NOT NULL,
    "unitId" TEXT NOT NULL,
    "parentUnitId" TEXT,
    "conversionFactor" DECIMAL(10,4) NOT NULL,
    "level" INTEGER NOT NULL,
    "sellingPrice" DECIMAL(15,2),
    "costPrice" DECIMAL(15,2),
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "product_unit_hierarchies_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "product_units_name_type_key" ON "product_units"("name", "type");

-- CreateIndex
CREATE UNIQUE INDEX "products_code_key" ON "products"("code");

-- CreateIndex
CREATE UNIQUE INDEX "product_unit_hierarchies_productId_unitId_key" ON "product_unit_hierarchies"("productId", "unitId");

-- AddForeignKey
ALTER TABLE "products" ADD CONSTRAINT "products_baseUnitId_fkey" FOREIGN KEY ("baseUnitId") REFERENCES "product_units"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "products" ADD CONSTRAINT "products_createdBy_fkey" FOREIGN KEY ("createdBy") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "products" ADD CONSTRAINT "products_updatedBy_fkey" FOREIGN KEY ("updatedBy") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "product_unit_hierarchies" ADD CONSTRAINT "product_unit_hierarchies_productId_fkey" FOREIGN KEY ("productId") REFERENCES "products"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "product_unit_hierarchies" ADD CONSTRAINT "product_unit_hierarchies_unitId_fkey" FOREIGN KEY ("unitId") REFERENCES "product_units"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "product_unit_hierarchies" ADD CONSTRAINT "product_unit_hierarchies_parentUnitId_fkey" FOREIGN KEY ("parentUnitId") REFERENCES "product_unit_hierarchies"("id") ON DELETE SET NULL ON UPDATE CASCADE;
