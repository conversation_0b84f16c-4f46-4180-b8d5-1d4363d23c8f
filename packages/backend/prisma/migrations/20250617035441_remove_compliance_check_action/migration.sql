/*
  Warnings:

  - The values [COMPLIANCE_CHECK] on the enum `BatchAuditAction` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "BatchAuditAction_new" AS ENUM ('CREATED', 'UPDATED', 'VALIDATED', 'REJECTED', 'SUBSTITUTED', 'EXPIRED', 'RECALLED', 'FORMAT_VALIDATION', 'UNIQUENESS_CHECK', 'GOODS_RECEIPT', 'INVENTORY_CREATED', 'STOCK_MOVEMENT');
ALTER TABLE "batch_audit_logs" ALTER COLUMN "action" TYPE "BatchAuditAction_new" USING ("action"::text::"BatchAuditAction_new");
ALTER TYPE "BatchAuditAction" RENAME TO "BatchAuditAction_old";
ALTER TYPE "BatchAuditAction_new" RENAME TO "BatchAuditAction";
DROP TYPE "BatchAuditAction_old";
COMMIT;
