-- CreateEnum
CREATE TYPE "BatchAuditAction" AS ENUM ('CREATED', 'UPDATED', 'VALIDATED', 'REJECTED', 'SUBSTITUTED', 'EXPIRED', 'RECALLED', 'COMPLIANCE_CHECK', 'FORMAT_VALIDATION', 'UNIQUENESS_CHECK', 'GOODS_RECEIPT', 'INVENTORY_CREATED', 'STOCK_MOVEMENT');

-- CreateTable
CREATE TABLE "batch_audit_logs" (
    "id" TEXT NOT NULL,
    "batchNumber" TEXT NOT NULL,
    "action" "BatchAuditAction" NOT NULL,
    "status" TEXT NOT NULL,
    "productId" TEXT,
    "supplierId" TEXT,
    "userId" TEXT,
    "referenceType" TEXT,
    "referenceId" TEXT,
    "referenceNumber" TEXT,
    "validationRules" TEXT[],
    "validationResults" JSONB,
    "bpomCompliant" BOOLEAN,
    "complianceNotes" TEXT,
    "complianceLevel" TEXT,
    "oldValues" JSONB,
    "newValues" JSONB,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "sessionId" TEXT,
    "message" TEXT,
    "details" TEXT,
    "errorMessage" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "batch_audit_logs_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "batch_audit_logs_batchNumber_idx" ON "batch_audit_logs"("batchNumber");

-- CreateIndex
CREATE INDEX "batch_audit_logs_action_idx" ON "batch_audit_logs"("action");

-- CreateIndex
CREATE INDEX "batch_audit_logs_productId_idx" ON "batch_audit_logs"("productId");

-- CreateIndex
CREATE INDEX "batch_audit_logs_createdAt_idx" ON "batch_audit_logs"("createdAt");

-- AddForeignKey
ALTER TABLE "batch_audit_logs" ADD CONSTRAINT "batch_audit_logs_productId_fkey" FOREIGN KEY ("productId") REFERENCES "products"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "batch_audit_logs" ADD CONSTRAINT "batch_audit_logs_supplierId_fkey" FOREIGN KEY ("supplierId") REFERENCES "suppliers"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "batch_audit_logs" ADD CONSTRAINT "batch_audit_logs_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;
