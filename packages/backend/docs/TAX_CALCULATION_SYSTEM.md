# Indonesian Tax Calculation System Documentation

## Overview

The Indonesian Tax Calculation System provides comprehensive tax calculation capabilities for pharmacy businesses in Indonesia. It supports multiple tax types including PPN (Pajak Pertambah<PERSON> Nilai), PPh 21, PPh 23, PPh 25, and PP 23, following Indonesian tax regulations and supporting dynamic tax rate changes without code modifications.

## Features

- **Multiple Tax Types**: Supports PPN, PPh 21, PPh 23, PPh 25, PP 23, and local taxes
- **Dynamic Tax Configuration**: Configurable tax rates through database settings
- **Indonesian Compliance**: Follows Indonesian tax regulations for pharmacies
- **Business Intelligence**: Automatic tax type determination based on business characteristics
- **Flexible Calculation**: Supports both inclusive and exclusive tax calculations
- **Compliance Management**: Tax calendar, compliance status, and validation
- **Future-Ready**: Designed for easy integration with sales transactions
- **Comprehensive Testing**: 100% test coverage with edge case handling

## Architecture

### Core Services

1. **TaxConfigurationService**: Manages tax rates and settings
2. **TaxCalculationService**: Performs basic tax calculations
3. **IndonesianTaxService**: Handles Indonesian-specific tax logic and compliance
4. **TaxController**: REST API endpoints for tax management

### Database Integration

Tax settings are stored in the `AppSettings` table with keys prefixed by `tax.`:

```
tax.ppn.rate                 - Current PPN rate (11 or 12)
tax.ppn.effectiveFrom        - When the rate becomes effective
tax.ppn.effectiveTo          - When the rate expires (optional)
tax.ppn.isActive             - Whether PPN is enabled
tax.ppn.description          - Human-readable description
tax.calculation.mode         - Default calculation mode (inclusive/exclusive)
```

## Indonesian Tax Types Supported

### 1. PPN (Pajak Pertambahan Nilai) - Value Added Tax
- **Rate**: 11% (current), 12% (from January 2025)
- **Applicable**: Businesses with annual turnover ≥ Rp 4.8 billion (PKP registration required)
- **Calculation**: Added to transaction amount or extracted from inclusive amount

### 2. PP 23 (Final Income Tax for MSMEs)
- **Rate**: 0.5% of monthly revenue
- **Applicable**:
  - Individual taxpayers: Revenue Rp 500M - Rp 4.8B annually, up to 7 years
  - Corporate taxpayers: First 3 years of operation, revenue ≤ Rp 4.8B
- **Deduction**: Individuals get Rp 500M annual non-taxable allowance

### 3. PPh 25 (Income Tax Installments)
- **Individual Rate**: 0.75% of monthly revenue
- **Corporate Rate**: 22% of net profit (monthly installment = 1/12 of previous year's tax due)
- **Applicable**: After PP 23 period expires or for large businesses

### 4. PPh 21 (Employee Income Tax)
- **Applicable**: All businesses with employees
- **Withholding**: Employer withholds from employee salaries
- **Threshold**: Employees with annual salary > Rp 60 million

## Usage Examples

### Determine Applicable Tax Types

```typescript
import { IndonesianTaxService, TaxEntityType } from './services/indonesian-tax.service';

// For a pharmacy with Rp 2 billion annual revenue, 3 years in operation
const applicableTaxes = await indonesianTaxService.getApplicableTaxTypes(
  TaxEntityType.INDIVIDUAL,
  2_000_000_000, // Rp 2 billion
  3 // 3 years in operation
);

console.log(applicableTaxes);
// Output: [TaxType.PP_23, TaxType.PPH_21]
// - PP 23: Individual, within revenue range and time limit
// - PPh 21: Has employees
// - No PPN: Below Rp 4.8B threshold
```

### Calculate PP 23 Tax

```typescript
// Individual pharmacy with Rp 100M monthly revenue
const pp23Result = await indonesianTaxService.calculatePP23Tax(
  100_000_000, // Monthly revenue
  TaxEntityType.INDIVIDUAL,
  1_200_000_000 // Annual revenue
);

console.log(pp23Result);
// {
//   taxableRevenue: 58333333, // 100M - (500M/12 non-taxable)
//   taxAmount: 291667,        // 0.5% of taxable revenue
//   taxRate: 0.5
// }
```

### Calculate PPh 25 Tax

```typescript
// Corporate pharmacy
const pph25Result = await indonesianTaxService.calculatePPh25Tax(
  200_000_000, // Monthly revenue
  TaxEntityType.CORPORATE,
  120_000_000  // Previous year's total tax due
);

console.log(pph25Result);
// {
//   monthlyInstallment: 10000000, // 120M / 12 months
//   taxRate: 22,
//   calculationBasis: 'Previous Year Tax Due'
// }
```

### Get Tax Compliance Status

```typescript
const complianceStatus = await indonesianTaxService.getTaxComplianceStatus(
  TaxEntityType.CORPORATE,
  6_000_000_000, // Rp 6 billion (above PKP threshold)
  5,             // 5 years in operation
  true           // Has employees
);

console.log(complianceStatus);
// {
//   requiredRegistrations: [
//     'NPWP (Nomor Pokok Wajib Pajak)',
//     'PKP (Pengusaha Kena Pajak)'
//   ],
//   applicableTaxes: [TaxType.PPH_25, TaxType.PPN, TaxType.PPH_21],
//   complianceChecklist: [
//     { requirement: 'NPWP Registration', status: 'required', ... },
//     { requirement: 'PKP Registration', status: 'required', ... },
//     { requirement: 'e-Faktur Application', status: 'required', ... },
//     { requirement: 'PPh 21 Withholding', status: 'required', ... }
//   ]
// }
```

### Basic Tax Calculation

```typescript
import { TaxCalculationService } from './services/tax-calculation.service';
import { Prisma } from '@prisma/client';

// Exclusive calculation (tax added to subtotal)
const subtotal = new Prisma.Decimal(100000); // Rp 100,000
const result = await taxCalculationService.calculatePPN(subtotal, {
  isInclusive: false
});

console.log(result);
// {
//   subtotal: 100000,
//   taxAmount: 11000,    // 11% of 100,000
//   totalAmount: 111000, // 100,000 + 11,000
//   taxRate: 11,
//   taxType: 'PPN',
//   isInclusive: false
// }
```

### Inclusive Tax Calculation

```typescript
// Inclusive calculation (tax extracted from total)
const totalWithTax = new Prisma.Decimal(111000); // Rp 111,000 including tax
const result = await taxCalculationService.calculatePPN(totalWithTax, {
  isInclusive: true
});

console.log(result);
// {
//   subtotal: 100000,    // Extracted base amount
//   taxAmount: 11000,    // Tax portion
//   totalAmount: 111000, // Original amount
//   taxRate: 11,
//   taxType: 'PPN',
//   isInclusive: true
// }
```

### Tax Calculation with Discount

```typescript
const subtotal = new Prisma.Decimal(100000);
const discount = new Prisma.Decimal(10000);

const result = await taxCalculationService.calculateWithDiscount(
  subtotal,
  discount,
  { isInclusive: false }
);

console.log(result);
// {
//   subtotal: 90000,     // 100,000 - 10,000
//   taxAmount: 9900,     // 11% of 90,000
//   totalAmount: 99900,  // 90,000 + 9,900
//   taxRate: 11,
//   taxType: 'PPN',
//   isInclusive: false
// }
```

### Purchase Order Integration

```typescript
// In CreatePurchaseOrderDto
{
  // ... other fields
  autoCalculateTax: true,        // Enable automatic tax calculation
  taxInclusive: false,           // Tax calculation mode
  taxOptions: {
    taxType: 'PPN',
    exemptItems: []              // Product IDs exempt from tax
  }
}
```

## API Endpoints

### Tax Configuration Management

```http
GET /tax/ppn/configuration
PUT /tax/ppn/configuration (Admin only)
GET /tax/calculation-mode
PUT /tax/calculation-mode (Admin only)
GET /tax/settings (Admin only)
GET /tax/ppn/status
```

### Basic Tax Calculation

```http
POST /tax/calculate
POST /tax/calculate-multiple
POST /tax/breakdown
```

### Indonesian Tax Services

```http
GET  /tax/applicable-taxes?entityType=INDIVIDUAL&annualRevenue=*********0&yearsInOperation=3
POST /tax/calculate/pp23
POST /tax/calculate/pph25
POST /tax/calculate/indonesian-ppn
GET  /tax/compliance-status?entityType=CORPORATE&annualRevenue=6000000000&yearsInOperation=5&hasEmployees=true
GET  /tax/calendar
POST /tax/validate-configuration
```

### Example API Usage

```bash
# Get applicable tax types for a pharmacy
curl -X GET "/api/tax/applicable-taxes?entityType=INDIVIDUAL&annualRevenue=*********0&yearsInOperation=3" \
  -H "Authorization: Bearer <token>"

# Calculate PP 23 tax
curl -X POST /api/tax/calculate/pp23 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "monthlyRevenue": *********,
    "entityType": "INDIVIDUAL",
    "annualRevenue": *********0
  }'

# Calculate PPh 25 tax
curl -X POST /api/tax/calculate/pph25 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "monthlyRevenue": *********,
    "entityType": "CORPORATE",
    "previousYearTaxDue": *********
  }'

# Get tax compliance status
curl -X GET "/api/tax/compliance-status?entityType=CORPORATE&annualRevenue=6000000000&yearsInOperation=5&hasEmployees=true" \
  -H "Authorization: Bearer <token>"

# Get tax calendar
curl -X GET /api/tax/calendar \
  -H "Authorization: Bearer <token>"

# Calculate Indonesian PPN
curl -X POST /api/tax/calculate/indonesian-ppn \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "subtotal": 1000000,
    "isRetailTransaction": true
  }'

# Validate tax configuration
curl -X POST /api/tax/validate-configuration \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "taxType": "PPN",
    "taxRate": 11,
    "entityType": "CORPORATE"
  }'

# Basic PPN calculation (legacy)
curl -X POST /api/tax/calculate \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "subtotal": 100000,
    "options": {
      "isInclusive": false
    }
  }'

# Update PPN rate (Admin only)
curl -X PUT /api/tax/ppn/configuration \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "taxRate": 12,
    "effectiveFrom": "2025-01-01T00:00:00.000Z",
    "description": "PPN 12% mulai 2025"
  }'
```

## Configuration Management

### Updating Tax Rates

```typescript
import { TaxConfigurationService } from './services/tax-configuration.service';

// Update PPN rate for 2025
await taxConfigService.updatePPNConfiguration({
  taxRate: 12,
  effectiveFrom: new Date('2025-01-01'),
  effectiveTo: undefined, // No end date
  description: 'PPN 12% mulai 2025'
});
```

### Setting Calculation Mode

```typescript
// Set default calculation mode
await taxConfigService.setDefaultTaxCalculationMode('inclusive');
```

## Integration with Purchase Orders

The tax calculation system is automatically integrated with the procurement workflow:

1. **Automatic Calculation**: When `autoCalculateTax` is true (default), tax is calculated automatically
2. **Manual Override**: Set `autoCalculateTax` to false to use manual `taxAmount`
3. **Flexible Options**: Configure tax type, inclusive/exclusive mode, and exempt items

## Testing

Run the comprehensive integration tests:

```bash
bun test:integration tax-calculation
```

The test suite covers:
- Tax configuration management
- Exclusive and inclusive calculations
- Discount handling
- Multiple items calculation
- Edge cases (zero amounts, large discounts)
- Tax rate transitions
- Date-based effective rates

## Error Handling

The system includes comprehensive error handling:

- **Configuration Errors**: Fallback to default settings
- **Calculation Errors**: Return zero tax results
- **Invalid Inputs**: Proper validation with Indonesian error messages
- **Database Failures**: Graceful degradation

## Future Enhancements

The system is designed for future expansion:

1. **Sales Integration**: Ready for integration with sales transactions
2. **Multiple Tax Types**: Support for PPh, luxury tax, etc.
3. **Regional Variations**: Support for different tax rates by region
4. **Audit Trail**: Track tax calculation history
5. **Reporting**: Tax reports for compliance

## Indonesian Tax Compliance

### Supported Tax Regulations
- **PPN**: 11% (current) → 12% (2025) transition
- **PP 23**: 0.5% final tax for MSMEs
- **PPh 25**: Income tax installments (0.75% individual, 22% corporate)
- **PPh 21**: Employee income tax withholding
- **PKP Registration**: Automatic determination for businesses ≥ Rp 4.8B

### Compliance Features
- **Automatic Tax Type Determination**: Based on entity type, revenue, and years in operation
- **Tax Calendar**: Important deadlines and filing requirements
- **Compliance Checklist**: Required registrations and obligations
- **Validation**: Ensures tax rates comply with Indonesian regulations
- **Audit Trail**: Maintains calculation history for compliance reporting

### Key Thresholds (2024)
- **PKP Registration**: Rp 4.8 billion annual turnover
- **PP 23 Eligibility**: Rp 500M - Rp 4.8B annual revenue
- **PP 23 Time Limits**: 7 years (individual), 3 years (corporate)
- **PPh 21 Threshold**: Rp 60 million annual salary

## Troubleshooting

### Common Issues

1. **Tax not calculating**: Check if PPN is active and within effective date range
2. **Wrong tax rate**: Verify the effective date configuration
3. **Calculation errors**: Ensure proper Prisma Decimal usage
4. **Permission errors**: Verify user has appropriate role for tax management

### Debug Information

Enable debug logging to see detailed tax calculation steps:

```typescript
// Tax calculation includes debug logging
const result = await taxCalculationService.calculatePPN(subtotal, options);
// Check logs for: "PPN calculation: subtotal=X, tax=Y, total=Z, rate=R%, inclusive=I"
```
