# Enhanced Pino Logging System

## Overview

The pharmacy backend uses an enhanced Pino logging system with auto-rotating log files, comprehensive error handling, and Betterstack Logtail integration for production monitoring.

## Features

### 🔄 Auto-Rotating Log Files
- **Dual rotation criteria**: Daily rotation + file size limits
- **Configurable file sizes**: Default 10MB in production, 5MB in development
- **Automatic directory creation**: Creates log directories if they don't exist
- **Timestamped filenames**: `pharmacy-backend-YYYY-MM-DD.log` format

### 📊 Environment-Specific Behavior

#### Development Environment
- **Log Level**: `trace` (ALL log levels saved to files)
- **Console Output**: Pretty-printed, colorized logs for easy reading
- **File Logging**: Enabled with comprehensive debugging information
- **Retention**: 7 days default

#### Production Environment
- **Log Level**: `info` (optimized for performance)
- **Console Output**: Structured JSON logs
- **File Logging**: Enabled with rotation and cleanup
- **Betterstack Integration**: Automatic log shipping to Logtail
- **Retention**: 30 days default

#### Test Environment
- **Log Level**: `error` (minimal logging)
- **File Logging**: Disabled to avoid test pollution
- **Console Output**: Suppressed for clean test output

### 🧹 Automatic Cleanup
- **Scheduled cleanup**: Daily at 2 AM
- **Retention policies**: Configurable retention periods
- **Manual cleanup**: Available via LogFileManager service
- **Statistics tracking**: File count, sizes, and age monitoring

## Configuration

### Environment Variables

```bash
# Basic Logging
LOG_LEVEL="info"                    # trace, debug, info, warn, error, fatal
NODE_ENV="production"               # development, production, test

# File Rotation
LOG_DIRECTORY="./logs"              # Directory for log files
LOG_MAX_SIZE="10m"                  # Max file size (10m, 100k, 1g)
LOG_FREQUENCY="daily"               # Rotation frequency (daily, hourly)
LOG_RETENTION_DAYS=30               # Days to keep log files

# Betterstack Logtail (Production)
LOGTAIL_TOKEN="your-token-here"     # Betterstack source token
LOGTAIL_INGESTING_HOST="https://in.logtail.com"  # Optional custom endpoint
```

### Example Configurations

#### Development (.env.development)
```bash
NODE_ENV=development
LOG_LEVEL=trace
LOG_DIRECTORY=./logs
LOG_MAX_SIZE=5m
LOG_FREQUENCY=daily
LOG_RETENTION_DAYS=7
```

#### Production (.env.production)
```bash
NODE_ENV=production
LOG_LEVEL=info
LOG_DIRECTORY=./logs
LOG_MAX_SIZE=10m
LOG_FREQUENCY=daily
LOG_RETENTION_DAYS=30
LOGTAIL_TOKEN=your-production-token
```

## Usage Examples

### Basic Logging in Services

```typescript
import { Injectable, Logger } from '@nestjs/common';

@Injectable()
export class ProductService {
  private readonly logger = new Logger(ProductService.name);

  async createProduct(data: CreateProductDto) {
    this.logger.log('Creating new product', { productName: data.name });
    
    try {
      const product = await this.productRepository.create(data);
      this.logger.log('Product created successfully', { 
        productId: product.id,
        productName: product.name 
      });
      return product;
    } catch (error) {
      this.logger.error('Failed to create product', error, { 
        productData: data 
      });
      throw error;
    }
  }
}
```

### Structured Logging with Context

```typescript
// Log with structured data
this.logger.info('User action performed', {
  userId: user.id,
  action: 'product_purchase',
  productId: product.id,
  amount: transaction.amount,
  timestamp: new Date().toISOString()
});

// Log errors with context
this.logger.error('Database connection failed', error, {
  database: 'pharmacy_db',
  operation: 'product_query',
  retryAttempt: 3
});
```

### Using Standalone Logger

```typescript
import { createStandaloneLogger } from '@/common/utils';
import { ConfigService } from '@nestjs/config';

// In a utility function or script
const configService = new ConfigService();
const logger = createStandaloneLogger(configService, 'DataMigration');

logger.info('Starting data migration');
logger.warn('Deprecated API usage detected');
logger.error('Migration failed', error);
```

## Log File Management

### Manual Cleanup

```typescript
import { LogFileManager } from '@/common/utils';

@Injectable()
export class MaintenanceService {
  constructor(private readonly logFileManager: LogFileManager) {}

  async performLogCleanup() {
    const result = await this.logFileManager.performManualCleanup();
    
    console.log(`Deleted ${result.deletedFiles} files`);
    console.log('Before:', result.beforeStats);
    console.log('After:', result.afterStats);
  }

  async getLogStatistics() {
    const stats = await this.logFileManager.getLogDirectoryStats();
    
    return {
      totalFiles: stats.totalFiles,
      totalSize: this.logFileManager.formatFileSize(stats.totalSize),
      expiredFiles: stats.expiredFiles,
      oldestFile: stats.oldestFile?.filename,
      newestFile: stats.newestFile?.filename
    };
  }
}
```

### Scheduled Cleanup

The system automatically runs cleanup daily at 2 AM via the `@Cron` decorator:

```typescript
@Cron(CronExpression.EVERY_DAY_AT_2AM)
async scheduledCleanup(): Promise<void> {
  // Automatically removes files older than retention period
}
```

## File Organization

```
logs/
├── pharmacy-backend-2024-01-15.log     # Current day's log
├── pharmacy-backend-2024-01-14.log     # Previous day's log
├── pharmacy-backend-2024-01-13.log     # Older logs
└── ...                                 # Automatically cleaned up after retention period
```

## Security Features

### Data Redaction

Sensitive information is automatically redacted from logs:

```typescript
// These fields are automatically redacted
const redactedFields = [
  'password',
  'token',
  'authorization',
  'cookie',
  'secret',
  'key',
  'req.headers.authorization',
  'req.headers.cookie',
  'req.body.password',
  'req.body.confirmPassword'
];
```

### Safe Logging Practices

```typescript
// ✅ Good - structured logging without sensitive data
this.logger.info('User login attempt', {
  userId: user.id,
  email: user.email,
  timestamp: new Date().toISOString()
});

// ❌ Bad - logging sensitive information
this.logger.info('User login', { 
  password: user.password,  // This will be redacted
  token: authToken         // This will be redacted
});
```

## Monitoring and Alerts

### Betterstack Logtail Integration

In production, logs are automatically shipped to Betterstack Logtail for:
- Real-time log monitoring
- Error alerting
- Log aggregation and search
- Performance monitoring

### Log Levels for Monitoring

- **ERROR**: Critical issues requiring immediate attention
- **WARN**: Potential issues that should be monitored
- **INFO**: General application flow and business events
- **DEBUG**: Detailed debugging information (development only)
- **TRACE**: Very detailed debugging (development only)

## Troubleshooting

### Common Issues

1. **Log directory not created**
   - Check file permissions
   - Verify LOG_DIRECTORY path is writable

2. **Logs not rotating**
   - Verify LOG_MAX_SIZE and LOG_FREQUENCY settings
   - Check disk space availability

3. **Betterstack not receiving logs**
   - Verify LOGTAIL_TOKEN is correct
   - Check network connectivity
   - Ensure NODE_ENV=production

### Debug Commands

```bash
# Check log directory
ls -la ./logs/

# Monitor log file sizes
du -h ./logs/*

# View recent logs
tail -f ./logs/pharmacy-backend-$(date +%Y-%m-%d).log

# Check log rotation
find ./logs -name "*.log" -type f -exec ls -lh {} \;
```

## Performance Considerations

- **Async logging**: All file operations are asynchronous
- **Structured logging**: Use objects for better performance than string concatenation
- **Log level filtering**: Higher log levels in production reduce I/O
- **File rotation**: Prevents individual log files from becoming too large
- **Automatic cleanup**: Prevents disk space issues from old logs

## Best Practices

1. **Use appropriate log levels** for different types of information
2. **Include context** in log messages for better debugging
3. **Avoid logging sensitive information** (passwords, tokens, etc.)
4. **Use structured logging** with objects rather than string interpolation
5. **Monitor log file sizes** and adjust rotation settings as needed
6. **Set up alerts** in Betterstack for critical errors
7. **Regularly review logs** for patterns and potential issues
