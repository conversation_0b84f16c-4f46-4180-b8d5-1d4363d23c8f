/**
 * Indonesian NPWP (Nomor Pokok <PERSON>) validation utility
 * Format: XX.XXX.XXX.X-XXX.XXX
 */
export class ValidationUtils {
  /**
   * Validates Indonesian NPWP format
   * @param npwp NPWP string to validate
   * @returns boolean indicating if NPWP is valid
   */
  static validateNPWP(npwp: string): boolean {
    if (!npwp || typeof npwp !== 'string') {
      return false;
    }

    // Remove all non-digit characters for validation
    const digitsOnly = npwp.replace(/\D/g, '');
    
    // NPWP must have exactly 15 digits
    if (digitsOnly.length !== 15) {
      return false;
    }

    // Check if it matches the expected format: XX.XXX.XXX.X-XXX.XXX
    const npwpRegex = /^\d{2}\.\d{3}\.\d{3}\.\d{1}-\d{3}\.\d{3}$/;
    return npwpRegex.test(npwp);
  }

  /**
   * Formats NPWP string to standard Indonesian format
   * @param npwp NPWP string (can be with or without formatting)
   * @returns formatted NPWP string or null if invalid
   */
  static formatNPWP(npwp: string): string | null {
    if (!npwp || typeof npwp !== 'string') {
      return null;
    }

    // Remove all non-digit characters
    const digitsOnly = npwp.replace(/\D/g, '');
    
    // Must have exactly 15 digits
    if (digitsOnly.length !== 15) {
      return null;
    }

    // Format as XX.XXX.XXX.X-XXX.XXX
    return `${digitsOnly.slice(0, 2)}.${digitsOnly.slice(2, 5)}.${digitsOnly.slice(5, 8)}.${digitsOnly.slice(8, 9)}-${digitsOnly.slice(9, 12)}.${digitsOnly.slice(12, 15)}`;
  }

  /**
   * Validates Indonesian phone number format
   * @param phone Phone number string to validate
   * @returns boolean indicating if phone number is valid
   */
  static validateIndonesianPhone(phone: string): boolean {
    if (!phone || typeof phone !== 'string') {
      return false;
    }

    // Remove all non-digit characters except +
    const cleanPhone = phone.replace(/[^\d+]/g, '');

    // Indonesian phone patterns:
    // Mobile: +628XXXXXXXXX, 08XXXXXXXXX, 628XXXXXXXXX
    // Landline: +62XXXXXXXXX, 0XXXXXXXXX, 62XXXXXXXXX (area codes: 21, 22, 24, 31, etc.)
    const mobileRegex = /^(\+62|62|0)8\d{8,11}$/;
    const landlineRegex = /^(\+62|62|0)(2[1-9]|3[1-9]|4[1-9]|5[1-9]|6[1-9]|7[1-9])\d{6,9}$/;

    return mobileRegex.test(cleanPhone) || landlineRegex.test(cleanPhone);
  }

  /**
   * Validates Indonesian postal code format
   * @param postalCode Postal code string to validate
   * @returns boolean indicating if postal code is valid
   */
  static validateIndonesianPostalCode(postalCode: string): boolean {
    if (!postalCode || typeof postalCode !== 'string') {
      return false;
    }

    // Indonesian postal codes are 5 digits
    const postalCodeRegex = /^\d{5}$/;
    return postalCodeRegex.test(postalCode);
  }

  /**
   * Validates email format
   * @param email Email string to validate
   * @returns boolean indicating if email is valid
   */
  static validateEmail(email: string): boolean {
    if (!email || typeof email !== 'string') {
      return false;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validates URL format
   * @param url URL string to validate
   * @returns boolean indicating if URL is valid
   */
  static validateURL(url: string): boolean {
    if (!url || typeof url !== 'string') {
      return false;
    }

    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Validates that a number is positive
   * @param value Number to validate
   * @returns boolean indicating if number is positive
   */
  static validatePositiveNumber(value: any): boolean {
    if (value === null || value === undefined || value === '') {
      return false;
    }
    const num = Number(value);
    return !isNaN(num) && isFinite(num) && num >= 0;
  }

  /**
   * Parses currency string to number (handles Indonesian Rupiah formatting)
   * @param currencyString Currency string to parse
   * @returns number or null if invalid
   */
  static parseCurrency(currencyString: string): number | null {
    if (!currencyString || typeof currencyString !== 'string') {
      return null;
    }

    // Remove currency symbols, spaces, and dots (thousand separators)
    const cleanString = currencyString
      .replace(/[Rp\s]/g, '')
      .replace(/\./g, '')
      .replace(/,/g, '.');

    const num = Number(cleanString);
    return isNaN(num) ? null : num;
  }
}
