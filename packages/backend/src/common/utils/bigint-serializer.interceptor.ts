import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Decimal } from '@prisma/client/runtime/library';

/**
 * Interceptor to handle serialization of all complex data types in API responses
 * - Converts BigInt to string
 * - Converts Date to ISO string
 * - Converts Decimal to string/number
 * - Handles objects with toJSON methods
 * - Handles circular references
 * - Handles Map and Set objects
 */
@Injectable()
export class BigIntSerializerInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    return next.handle().pipe(
      map(data => {
        return this.transformData(data);
      }),
    );
  }

  private transformData(data: any, seen = new WeakMap()): any {
    // Handle null/undefined
    if (data === null || data === undefined) {
      return data;
    }

    // Handle primitive types
    if (typeof data !== 'object' && typeof data !== 'bigint') {
      return data;
    }

    // Handle BigInt
    if (typeof data === 'bigint') {
      return data.toString();
    }

    // Handle circular references
    if (seen.has(data)) {
      return '[Circular Reference]';
    }

    // Add object to seen map
    if (typeof data === 'object' && data !== null) {
      seen.set(data, true);
    }

    // Handle Date objects
    if (data instanceof Date) {
      return data.toISOString();
    }

    // Handle Decimal objects from Prisma
    if (data instanceof Decimal || (data && typeof data.toFixed === 'function' && typeof data.toString === 'function')) {
      return Number.parseFloat(data.toString());
    }

    // Handle objects with custom toJSON methods
    if (data && typeof data.toJSON === 'function') {
      return this.transformData(data.toJSON(), seen);
    }

    // Handle arrays
    if (Array.isArray(data)) {
      return data.map(item => this.transformData(item, seen));
    }

    // Handle Map objects
    if (data instanceof Map) {
      return Object.fromEntries(
        Array.from(data.entries()).map(([key, value]) => [
          key, 
          this.transformData(value, seen)
        ])
      );
    }

    // Handle Set objects
    if (data instanceof Set) {
      return Array.from(data).map(item => this.transformData(item, seen));
    }

    // Handle Buffer objects
    if (Buffer.isBuffer(data)) {
      return data.toString('base64');
    }

    // Handle regular objects
    if (typeof data === 'object') {
      const result = {};
      for (const key in data) {
        if (Object.prototype.hasOwnProperty.call(data, key)) {
          result[key] = this.transformData(data[key], seen);
        }
      }
      return result;
    }

    return data;
  }
} 