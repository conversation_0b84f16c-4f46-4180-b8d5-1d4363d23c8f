import { ValidationUtils } from '../validation.utils';

describe('ValidationUtils', () => {
  describe('validateNPWP', () => {
    it('should validate correct NPWP format', () => {
      const validNPWPs = [
        '01.234.567.8-901.234',
        '12.345.678.9-012.345',
        '99.999.999.9-999.999',
      ];

      validNPWPs.forEach(npwp => {
        expect(ValidationUtils.validateNPWP(npwp)).toBe(true);
      });
    });

    it('should reject invalid NPWP formats', () => {
      const invalidNPWPs = [
        '1.234.567.8-901.234', // Missing leading zero
        '01.234.567.89-01.234', // Wrong digit grouping
        '01.234.567.8-901.23', // Missing digit
        '01.234.567.8-901.2345', // Extra digit
        '01-234-567-8-901-234', // Wrong separators
        '01234567890123456', // No separators
        '01.234.567.8901.234', // Missing dash
        '', // Empty string
        null, // Null
        undefined, // Undefined
        '01.234.567.8-901.ABC', // Non-numeric characters
      ];

      invalidNPWPs.forEach(npwp => {
        expect(ValidationUtils.validateNPWP(npwp as any)).toBe(false);
      });
    });
  });

  describe('formatNPWP', () => {
    it('should format NPWP correctly from digits only', () => {
      const testCases = [
        { input: '012345678901234', expected: '01.234.567.8-901.234' },
        { input: '123456789012345', expected: '12.345.678.9-012.345' },
        { input: '999999999999999', expected: '99.999.999.9-999.999' },
      ];

      testCases.forEach(({ input, expected }) => {
        expect(ValidationUtils.formatNPWP(input)).toBe(expected);
      });
    });

    it('should format NPWP correctly from partially formatted input', () => {
      const testCases = [
        { input: '01.234.567.8901.234', expected: '01.234.567.8-901.234' },
        { input: '01-234-567-8-901-234', expected: '01.234.567.8-901.234' },
        { input: '01 234 567 8 901 234', expected: '01.234.567.8-901.234' },
      ];

      testCases.forEach(({ input, expected }) => {
        expect(ValidationUtils.formatNPWP(input)).toBe(expected);
      });
    });

    it('should return null for invalid input', () => {
      const invalidInputs = [
        '12345678901234', // 14 digits
        '1234567890123456', // 16 digits
        '', // Empty string
        null, // Null
        undefined, // Undefined
        'abcdefghijklmno', // Non-numeric
      ];

      invalidInputs.forEach(input => {
        expect(ValidationUtils.formatNPWP(input as any)).toBe(null);
      });
    });
  });

  describe('validateIndonesianPhone', () => {
    it('should validate correct Indonesian phone formats', () => {
      const validPhones = [
        '+62812345678',
        '+628123456789',
        '+6281234567890',
        '+62812345678901',
        '62812345678',
        '628123456789',
        '6281234567890',
        '62812345678901',
        '0812345678',
        '08123456789',
        '081234567890',
        '0812345678901',
      ];

      validPhones.forEach(phone => {
        expect(ValidationUtils.validateIndonesianPhone(phone)).toBe(true);
      });
    });

    it('should reject invalid Indonesian phone formats', () => {
      const invalidPhones = [
        '+1234567890', // Not Indonesian country code
        '+62712345678', // Not mobile number (07 is not valid)
        '0712345678', // Not mobile number
        '812345678', // Missing country code or leading zero
        '+628123456', // Too short
        '+628123456789012', // Too long
        '', // Empty string
        null, // Null
        undefined, // Undefined
      ];

      invalidPhones.forEach(phone => {
        expect(ValidationUtils.validateIndonesianPhone(phone as any)).toBe(false);
      });
    });
  });

  describe('validateIndonesianPostalCode', () => {
    it('should validate correct Indonesian postal codes', () => {
      const validPostalCodes = [
        '12345',
        '10110',
        '40123',
        '60123',
        '80123',
      ];

      validPostalCodes.forEach(postalCode => {
        expect(ValidationUtils.validateIndonesianPostalCode(postalCode)).toBe(true);
      });
    });

    it('should reject invalid Indonesian postal codes', () => {
      const invalidPostalCodes = [
        '1234', // Too short
        '123456', // Too long
        '1234a', // Contains letter
        '12-34', // Contains dash
        '', // Empty string
        null, // Null
        undefined, // Undefined
        ' 12345', // Leading space
        '12345 ', // Trailing space
      ];

      invalidPostalCodes.forEach(postalCode => {
        expect(ValidationUtils.validateIndonesianPostalCode(postalCode as any)).toBe(false);
      });
    });
  });

  describe('validateEmail', () => {
    it('should validate correct email formats', () => {
      const validEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ];

      validEmails.forEach(email => {
        expect(ValidationUtils.validateEmail(email)).toBe(true);
      });
    });

    it('should reject invalid email formats', () => {
      const invalidEmails = [
        'invalid-email', // No @ symbol
        '@domain.com', // No local part
        'user@', // No domain
        'user@domain', // No TLD
        'user <EMAIL>', // Space in local part
        'user@domain .com', // Space in domain
        '', // Empty string
        null, // Null
        undefined, // Undefined
      ];

      invalidEmails.forEach(email => {
        expect(ValidationUtils.validateEmail(email as any)).toBe(false);
      });
    });
  });

  describe('validateURL', () => {
    it('should validate correct URL formats', () => {
      const validURLs = [
        'https://example.com',
        'http://test.co.id',
        'https://www.company.org',
        'http://subdomain.domain.com/path',
        'https://domain.com/path?query=value',
        'https://domain.com:8080/path',
      ];

      validURLs.forEach(url => {
        expect(ValidationUtils.validateURL(url)).toBe(true);
      });
    });

    it('should reject invalid URL formats', () => {
      const invalidURLs = [
        'not-a-url', // No protocol
        'https://', // No domain
        '', // Empty string
        null, // Null
        undefined, // Undefined
        'just text',
      ];

      invalidURLs.forEach(url => {
        expect(ValidationUtils.validateURL(url as any)).toBe(false);
      });
    });
  });

  describe('validatePositiveNumber', () => {
    it('should validate positive numbers', () => {
      const validNumbers = [0, 1, 100, 1000.5, '0', '1', '100', '1000.5'];

      validNumbers.forEach(num => {
        expect(ValidationUtils.validatePositiveNumber(num)).toBe(true);
      });
    });

    it('should reject negative numbers and invalid values', () => {
      const invalidNumbers = [-1, -100, 'abc', '', null, undefined, NaN, Infinity];

      invalidNumbers.forEach(num => {
        expect(ValidationUtils.validatePositiveNumber(num)).toBe(false);
      });
    });
  });

  describe('parseCurrency', () => {
    it('should parse Indonesian Rupiah currency strings', () => {
      const testCases = [
        { input: 'Rp 1.000.000', expected: 1000000 },
        { input: 'Rp1.000.000', expected: 1000000 },
        { input: '1.000.000', expected: 1000000 },
        { input: 'Rp 500.000,50', expected: 500000.50 },
        { input: '1000000', expected: 1000000 },
        { input: 'Rp 0', expected: 0 },
        { input: '0', expected: 0 },
      ];

      testCases.forEach(({ input, expected }) => {
        expect(ValidationUtils.parseCurrency(input)).toBe(expected);
      });
    });

    it('should return null for invalid currency strings', () => {
      const invalidInputs = [
        'abc',
        '',
        null,
        undefined,
        'Rp abc',
        'not a number',
      ];

      invalidInputs.forEach(input => {
        expect(ValidationUtils.parseCurrency(input as any)).toBe(null);
      });
    });
  });
});
