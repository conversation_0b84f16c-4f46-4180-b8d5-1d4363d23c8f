import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { LoggerConfigService, createLoggerOptions, createStandaloneLogger } from '../logger.config';
import * as fs from 'fs';

// Mock fs module for directory creation tests
jest.mock('fs');

describe('LoggerConfigService', () => {
  let service: LoggerConfigService;
  let configService: ConfigService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LoggerConfigService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string, defaultValue?: any) => {
              const config: Record<string, any> = {
                NODE_ENV: 'test',
                LOG_LEVEL: 'debug',
                LOGTAIL_TOKEN: 'test-token',
                LOGTAIL_INGESTING_HOST: 'https://test.logtail.com',
              };
              return config[key] ?? defaultValue;
            }),
          },
        },
      ],
    }).compile();

    service = module.get<LoggerConfigService>(LoggerConfigService);
    configService = module.get<ConfigService>(ConfigService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getLoggerConfig', () => {
    it('should return correct config for test environment', () => {
      const config = service.getLoggerConfig();

      expect(config.level).toBe('debug');
      expect(config.environment).toBe('test');
      expect(config.enableLogtail).toBe(false); // Should be false in test
      expect(config.prettyPrint).toBe(false); // Should be false in test
      expect(config.redact).toContain('password');
      expect(config.redact).toContain('token');
    });

    it('should return correct config for development environment', () => {
      jest.spyOn(configService, 'get').mockImplementation((key: string, defaultValue?: any) => {
        const config: Record<string, any> = {
          NODE_ENV: 'development',
          LOG_LEVEL: 'trace', // Development should default to trace
          LOG_DIRECTORY: './test-logs',
          LOG_MAX_SIZE: '5m',
          LOG_FREQUENCY: 'daily',
          LOG_RETENTION_DAYS: 7,
        };
        return config[key] ?? defaultValue;
      });

      const config = service.getLoggerConfig();

      expect(config.level).toBe('trace'); // Development should log ALL levels
      expect(config.environment).toBe('development');
      expect(config.enableLogtail).toBe(false); // No token in dev
      expect(config.prettyPrint).toBe(true); // Should be true in dev
      expect(config.developmentFileLogging).toBe(true); // Should enable file logging in dev
      expect(config.fileRotation.enabled).toBe(true); // Should enable file rotation
      expect(config.fileRotation.directory).toBe('./test-logs');
      expect(config.fileRotation.maxSize).toBe('5m');
      expect(config.fileRotation.frequency).toBe('daily');
      expect(config.fileRotation.retentionDays).toBe(7);
    });

    it('should return correct config for production environment', () => {
      const mockConfigService = {
        get: jest.fn((key: string, defaultValue?: any) => {
          const config: Record<string, any> = {
            NODE_ENV: 'production',
            LOG_LEVEL: 'info',
            LOGTAIL_TOKEN: 'prod-token',
            LOGTAIL_INGESTING_HOST: 'https://in.logtail.com',
            LOG_DIRECTORY: './logs',
            LOG_MAX_SIZE: '10m',
            LOG_FREQUENCY: 'daily',
            LOG_RETENTION_DAYS: 30,
          };
          return config[key] ?? defaultValue;
        }),
      } as any;

      const testService = new LoggerConfigService(mockConfigService);
      const config = testService.getLoggerConfig();

      expect(config.level).toBe('info');
      expect(config.environment).toBe('production');
      expect(config.enableLogtail).toBe(true); // Should be true with token
      expect(config.logtailToken).toBe('prod-token');
      expect(config.logtailIngestingHost).toBe('https://in.logtail.com');
      expect(config.prettyPrint).toBe(false); // Should be false in prod
      expect(config.developmentFileLogging).toBe(false); // Should be false in prod
      expect(config.fileRotation.enabled).toBe(true); // Should enable file rotation
      expect(config.fileRotation.directory).toBe('./logs');
      expect(config.fileRotation.maxSize).toBe('10m');
      expect(config.fileRotation.frequency).toBe('daily');
      expect(config.fileRotation.retentionDays).toBe(30);
    });

    it('should return correct config for test environment', () => {
      jest.spyOn(configService, 'get').mockImplementation((key: string, defaultValue?: any) => {
        const config: Record<string, any> = {
          NODE_ENV: 'test',
          LOG_LEVEL: 'error',
        };
        return config[key] ?? defaultValue;
      });

      const config = service.getLoggerConfig();

      expect(config.level).toBe('error');
      expect(config.environment).toBe('test');
      expect(config.enableLogtail).toBe(false); // Should be false in test
      expect(config.prettyPrint).toBe(false); // Should be false in test
      expect(config.developmentFileLogging).toBe(false); // Should be false in test
      expect(config.fileRotation.enabled).toBe(false); // Should disable file rotation in test
    });
  });

  describe('createTransportTargets', () => {
    it('should create single pino-pretty target for development', () => {
      jest.spyOn(configService, 'get').mockImplementation((key: string, defaultValue?: any) => {
        const config: Record<string, any> = {
          NODE_ENV: 'development',
          LOG_LEVEL: 'trace',
          LOG_DIRECTORY: './test-logs',
        };
        return config[key] ?? defaultValue;
      });

      const config = service.getLoggerConfig();
      const targets = (service as any).createTransportTargets(config);

      expect(targets).toHaveLength(2); // pino-pretty + file rotation
      expect(targets[0].target).toBe('pino-pretty');
      expect(targets[1].target).toBe('pino-roll');
      expect(targets[1].level).toBe('trace'); // Should log ALL levels to file in development
    });

    it('should create multiple targets for production with Logtail', () => {
      const mockConfigService = {
        get: jest.fn((key: string, defaultValue?: any) => {
          const config: Record<string, any> = {
            NODE_ENV: 'production',
            LOG_LEVEL: 'info',
            LOGTAIL_TOKEN: 'prod-token',
            LOG_DIRECTORY: './logs',
            LOG_MAX_SIZE: '10m',
            LOG_FREQUENCY: 'daily',
          };
          return config[key] ?? defaultValue;
        }),
      } as any;

      const testService = new LoggerConfigService(mockConfigService);
      const config = testService.getLoggerConfig();
      const targets = (testService as any).createTransportTargets(config);

      expect(targets).toHaveLength(3); // stdout + file rotation + logtail

      const stdoutTarget = targets.find((t: any) => t.target === 'pino/file');
      const fileTarget = targets.find((t: any) => t.target === 'pino-roll');
      const logtailTarget = targets.find((t: any) => t.target === '@logtail/pino');

      expect(stdoutTarget).toBeDefined();
      expect(fileTarget).toBeDefined();
      expect(logtailTarget).toBeDefined();
      expect(logtailTarget.options.sourceToken).toBe('prod-token');
    });

    it('should create targets without Logtail for production without token', () => {
      jest.spyOn(configService, 'get').mockImplementation((key: string, defaultValue?: any) => {
        const config: Record<string, any> = {
          NODE_ENV: 'production',
          LOG_LEVEL: 'info',
          LOG_DIRECTORY: './logs',
        };
        return config[key] ?? defaultValue;
      });

      const config = service.getLoggerConfig();
      const targets = (service as any).createTransportTargets(config);

      expect(targets).toHaveLength(2); // stdout + file rotation

      const stdoutTarget = targets.find((t: any) => t.target === 'pino/file');
      const fileTarget = targets.find((t: any) => t.target === 'pino-roll');
      const logtailTarget = targets.find((t: any) => t.target === '@logtail/pino');

      expect(stdoutTarget).toBeDefined();
      expect(fileTarget).toBeDefined();
      expect(logtailTarget).toBeUndefined();
    });
  });

  describe('createPinoOptions', () => {
    it('should create correct options for development', () => {
      jest.spyOn(configService, 'get').mockImplementation((key: string, defaultValue?: any) => {
        const config: Record<string, any> = {
          NODE_ENV: 'development',
          LOG_LEVEL: 'trace', // Development should default to trace
          LOG_DIRECTORY: './test-logs',
        };
        return config[key] ?? defaultValue;
      });

      const options = service.createPinoOptions();

      expect(options.pinoHttp).toBeDefined();
      expect((options.pinoHttp as any).level).toBe('trace');
      expect((options.pinoHttp as any).transport).toBeDefined();
      expect((options.pinoHttp as any).transport.targets).toBeDefined();
      expect((options.pinoHttp as any).transport.targets).toHaveLength(2); // pino-pretty + file rotation

      const prettyTarget = (options.pinoHttp as any).transport.targets.find(
        (target: any) => target.target === 'pino-pretty'
      );
      const fileTarget = (options.pinoHttp as any).transport.targets.find(
        (target: any) => target.target === 'pino-roll'
      );

      expect(prettyTarget).toBeDefined();
      expect(fileTarget).toBeDefined();
      expect(fileTarget.level).toBe('trace'); // Should log ALL levels to file in development
    });

    it('should create correct options for production with Logtail', () => {
      const mockConfigService = {
        get: jest.fn((key: string, defaultValue?: any) => {
          const config: Record<string, any> = {
            NODE_ENV: 'production',
            LOG_LEVEL: 'info',
            LOGTAIL_TOKEN: 'prod-token',
            LOGTAIL_INGESTING_HOST: 'https://in.logtail.com',
            LOG_DIRECTORY: './logs',
          };
          return config[key] ?? defaultValue;
        }),
      } as any;

      const testService = new LoggerConfigService(mockConfigService);
      const options = testService.createPinoOptions();

      expect(options.pinoHttp).toBeDefined();
      expect((options.pinoHttp as any).level).toBe('info');
      expect((options.pinoHttp as any).transport).toBeDefined();
      expect((options.pinoHttp as any).transport.targets).toBeDefined();
      expect((options.pinoHttp as any).transport.targets).toHaveLength(3); // stdout + file + logtail

      const stdoutTarget = (options.pinoHttp as any).transport.targets.find(
        (target: any) => target.target === 'pino/file'
      );
      const fileTarget = (options.pinoHttp as any).transport.targets.find(
        (target: any) => target.target === 'pino-roll'
      );
      const logtailTarget = (options.pinoHttp as any).transport.targets.find(
        (target: any) => target.target === '@logtail/pino'
      );

      expect(stdoutTarget).toBeDefined();
      expect(fileTarget).toBeDefined();
      expect(logtailTarget).toBeDefined();
      expect(logtailTarget.options.sourceToken).toBe('prod-token');
      expect(logtailTarget.options.endpoint).toBe('https://in.logtail.com');
    });

    it('should create correct options for production without Logtail', () => {
      jest.spyOn(configService, 'get').mockImplementation((key: string, defaultValue?: any) => {
        const config: Record<string, any> = {
          NODE_ENV: 'production',
          LOG_LEVEL: 'info',
          LOG_DIRECTORY: './logs',
        };
        return config[key] ?? defaultValue;
      });

      const options = service.createPinoOptions();

      expect(options.pinoHttp).toBeDefined();
      expect((options.pinoHttp as any).transport).toBeDefined();
      expect((options.pinoHttp as any).transport.targets).toBeDefined();
      expect((options.pinoHttp as any).transport.targets).toHaveLength(2); // stdout + file rotation

      const stdoutTarget = (options.pinoHttp as any).transport.targets.find(
        (target: any) => target.target === 'pino/file'
      );
      const fileTarget = (options.pinoHttp as any).transport.targets.find(
        (target: any) => target.target === 'pino-roll'
      );

      expect(stdoutTarget).toBeDefined();
      expect(stdoutTarget.options.destination).toBe(1); // stdout
      expect(fileTarget).toBeDefined();
    });
  });

  describe('createStandaloneLogger', () => {
    it('should create logger with context', () => {
      const logger = service.createStandaloneLogger('TestContext');
      
      expect(logger).toBeDefined();
      expect(typeof logger.info).toBe('function');
      expect(typeof logger.error).toBe('function');
      expect(typeof logger.debug).toBe('function');
    });

    it('should create logger without context', () => {
      const logger = service.createStandaloneLogger();
      
      expect(logger).toBeDefined();
      expect(typeof logger.info).toBe('function');
    });
  });
});

describe('Factory Functions', () => {
  let configService: ConfigService;

  beforeEach(() => {
    configService = {
      get: jest.fn((key: string, defaultValue?: any) => {
        const config: Record<string, any> = {
          NODE_ENV: 'test',
          LOG_LEVEL: 'debug',
        };
        return config[key] ?? defaultValue;
      }),
    } as any;
  });

  describe('createLoggerOptions', () => {
    it('should create logger options using factory function', () => {
      const options = createLoggerOptions(configService);
      
      expect(options).toBeDefined();
      expect(options.pinoHttp).toBeDefined();
    });
  });

  describe('createStandaloneLogger', () => {
    it('should create standalone logger using factory function', () => {
      const logger = createStandaloneLogger(configService, 'FactoryTest');
      
      expect(logger).toBeDefined();
      expect(typeof logger.info).toBe('function');
    });
  });
});
