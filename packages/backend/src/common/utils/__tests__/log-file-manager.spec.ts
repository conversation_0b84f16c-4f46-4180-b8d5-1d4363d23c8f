import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { LogFileManager } from '../log-file-manager';
import * as fs from 'fs';
import * as path from 'path';

// Mock fs module
jest.mock('fs');
const mockFs = fs as jest.Mocked<typeof fs>;

describe('LogFileManager', () => {
  let service: LogFileManager;
  let configService: ConfigService;
  let module: TestingModule;

  const mockLogDir = './test-logs';
  const mockRetentionDays = 7;

  beforeEach(async () => {
    // Reset all mocks
    jest.clearAllMocks();

    module = await Test.createTestingModule({
      providers: [
        LogFileManager,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string, defaultValue?: any) => {
              switch (key) {
                case 'LOG_DIRECTORY':
                  return mockLogDir;
                case 'LOG_RETENTION_DAYS':
                  return mockRetentionDays;
                default:
                  return defaultValue;
              }
            }),
          },
        },
      ],
    }).compile();

    service = module.get<LogFileManager>(LogFileManager);
    configService = module.get<ConfigService>(ConfigService);
  });

  afterEach(async () => {
    await module.close();
  });

  describe('ensureLogDirectoryExists', () => {
    it('should create log directory if it does not exist', async () => {
      mockFs.existsSync.mockReturnValue(false);
      mockFs.mkdirSync.mockImplementation(() => undefined);

      await service.ensureLogDirectoryExists();

      expect(mockFs.existsSync).toHaveBeenCalledWith(mockLogDir);
      expect(mockFs.mkdirSync).toHaveBeenCalledWith(mockLogDir, { recursive: true });
    });

    it('should not create directory if it already exists', async () => {
      mockFs.existsSync.mockReturnValue(true);

      await service.ensureLogDirectoryExists();

      expect(mockFs.existsSync).toHaveBeenCalledWith(mockLogDir);
      expect(mockFs.mkdirSync).not.toHaveBeenCalled();
    });

    it('should handle errors when creating directory', async () => {
      mockFs.existsSync.mockReturnValue(false);
      mockFs.mkdirSync.mockImplementation(() => {
        throw new Error('Permission denied');
      });

      await expect(service.ensureLogDirectoryExists()).rejects.toThrow('Permission denied');
    });
  });

  describe('getLogFiles', () => {
    it('should return empty array if log directory does not exist', async () => {
      mockFs.existsSync.mockReturnValue(false);

      const result = await service.getLogFiles();

      expect(result).toEqual([]);
      expect(mockFs.existsSync).toHaveBeenCalledWith(mockLogDir);
    });

    it('should return log files with correct information', async () => {
      const mockFiles = ['app-2024-01-01.log', 'app-2024-01-02.log', 'not-a-log.txt'];
      const mockStats = {
        size: 1024,
        birthtime: new Date('2024-01-01'),
        mtime: new Date('2024-01-02'),
      };

      mockFs.existsSync.mockReturnValue(true);
      mockFs.readdirSync.mockReturnValue(mockFiles as any);
      mockFs.statSync.mockReturnValue(mockStats as any);

      const result = await service.getLogFiles();

      expect(result).toHaveLength(2); // Only .log files
      expect(result[0]).toMatchObject({
        filename: expect.stringMatching(/\.log$/),
        fullPath: expect.stringMatching(/.*\.log$/),
        size: 1024,
        createdAt: mockStats.birthtime,
        modifiedAt: mockStats.mtime,
        age: expect.any(Number),
      });
    });

    it('should handle errors gracefully', async () => {
      mockFs.existsSync.mockReturnValue(true);
      mockFs.readdirSync.mockImplementation(() => {
        throw new Error('Read error');
      });

      const result = await service.getLogFiles();

      expect(result).toEqual([]);
    });
  });

  describe('getExpiredLogFiles', () => {
    it('should return files older than retention period', async () => {
      const oldDate = new Date();
      oldDate.setDate(oldDate.getDate() - 10); // 10 days old

      const recentDate = new Date();
      recentDate.setDate(recentDate.getDate() - 3); // 3 days old

      jest.spyOn(service, 'getLogFiles').mockResolvedValue([
        {
          filename: 'old.log',
          fullPath: '/path/old.log',
          size: 1024,
          createdAt: oldDate,
          modifiedAt: oldDate,
          age: 10,
        },
        {
          filename: 'recent.log',
          fullPath: '/path/recent.log',
          size: 1024,
          createdAt: recentDate,
          modifiedAt: recentDate,
          age: 3,
        },
      ]);

      const result = await service.getExpiredLogFiles();

      expect(result).toHaveLength(1);
      expect(result[0].filename).toBe('old.log');
    });
  });

  describe('deleteLogFile', () => {
    it('should delete existing file successfully', async () => {
      const filePath = '/path/to/test.log';
      mockFs.existsSync.mockReturnValue(true);
      mockFs.unlinkSync.mockImplementation(() => undefined);

      const result = await service.deleteLogFile(filePath);

      expect(result).toBe(true);
      expect(mockFs.existsSync).toHaveBeenCalledWith(filePath);
      expect(mockFs.unlinkSync).toHaveBeenCalledWith(filePath);
    });

    it('should return false if file does not exist', async () => {
      const filePath = '/path/to/nonexistent.log';
      mockFs.existsSync.mockReturnValue(false);

      const result = await service.deleteLogFile(filePath);

      expect(result).toBe(false);
      expect(mockFs.unlinkSync).not.toHaveBeenCalled();
    });

    it('should handle deletion errors', async () => {
      const filePath = '/path/to/test.log';
      mockFs.existsSync.mockReturnValue(true);
      mockFs.unlinkSync.mockImplementation(() => {
        throw new Error('Permission denied');
      });

      const result = await service.deleteLogFile(filePath);

      expect(result).toBe(false);
    });
  });

  describe('cleanupExpiredLogFiles', () => {
    it('should delete all expired files', async () => {
      const expiredFiles = [
        { fullPath: '/path/old1.log' },
        { fullPath: '/path/old2.log' },
      ];

      jest.spyOn(service, 'getExpiredLogFiles').mockResolvedValue(expiredFiles as any);
      jest.spyOn(service, 'deleteLogFile').mockResolvedValue(true);

      const result = await service.cleanupExpiredLogFiles();

      expect(result).toBe(2);
      expect(service.deleteLogFile).toHaveBeenCalledTimes(2);
    });
  });

  describe('formatFileSize', () => {
    it('should format bytes correctly', () => {
      expect(service.formatFileSize(1024)).toBe('1.00 KB');
      expect(service.formatFileSize(1048576)).toBe('1.00 MB');
      expect(service.formatFileSize(1073741824)).toBe('1.00 GB');
      expect(service.formatFileSize(500)).toBe('500.00 B');
    });
  });

  describe('getLogDirectoryStats', () => {
    it('should return correct statistics', async () => {
      const mockLogFiles = [
        {
          filename: 'file1.log',
          fullPath: '/path/file1.log',
          size: 1024,
          createdAt: new Date('2024-01-01'),
          modifiedAt: new Date('2024-01-01'),
          age: 5,
        },
        {
          filename: 'file2.log',
          fullPath: '/path/file2.log',
          size: 2048,
          createdAt: new Date('2024-01-02'),
          modifiedAt: new Date('2024-01-02'),
          age: 3,
        },
      ];

      jest.spyOn(service, 'getLogFiles').mockResolvedValue(mockLogFiles);
      jest.spyOn(service, 'getExpiredLogFiles').mockResolvedValue([]);

      const result = await service.getLogDirectoryStats();

      expect(result).toEqual({
        totalFiles: 2,
        totalSize: 3072,
        oldestFile: mockLogFiles[1],
        newestFile: mockLogFiles[0],
        expiredFiles: 0,
      });
    });
  });
});
