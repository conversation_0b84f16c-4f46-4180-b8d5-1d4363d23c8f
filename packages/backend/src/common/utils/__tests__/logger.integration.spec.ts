import { Test, TestingModule } from '@nestjs/testing';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { LoggerModule } from '../../logger.module';
import { PinoLogger } from 'nestjs-pino';
import { LoggerConfigService } from '../logger.config';

describe('Logger Integration', () => {
  let module: TestingModule;
  let logger: PinoLogger;
  let loggerConfigService: LoggerConfigService;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          ignoreEnvFile: true,
          load: [
            () => ({
              NODE_ENV: 'test',
              LOG_LEVEL: 'debug',
              LOGTAIL_TOKEN: 'test-token',
              LOGTAIL_INGESTING_HOST: 'https://test.logtail.com',
            }),
          ],
        }),
        LoggerModule,
      ],
    }).compile();

    // Use resolve() for scoped providers like PinoLogger
    logger = await module.resolve<PinoLogger>(PinoLogger);
    loggerConfigService = module.get<LoggerConfigService>(LoggerConfigService);
  });

  afterAll(async () => {
    await module.close();
  });

  it('should inject PinoLogger successfully', () => {
    expect(logger).toBeDefined();
    expect(typeof logger.info).toBe('function');
    expect(typeof logger.error).toBe('function');
    expect(typeof logger.debug).toBe('function');
    expect(typeof logger.warn).toBe('function');
  });

  it('should inject LoggerConfigService successfully', () => {
    expect(loggerConfigService).toBeDefined();
    expect(typeof loggerConfigService.getLoggerConfig).toBe('function');
    expect(typeof loggerConfigService.createPinoOptions).toBe('function');
    expect(typeof loggerConfigService.createStandaloneLogger).toBe('function');
  });

  it('should have correct logger configuration for test environment', () => {
    const config = loggerConfigService.getLoggerConfig();
    
    expect(config.environment).toBe('test');
    expect(config.level).toBe('debug');
    expect(config.enableLogtail).toBe(false); // Should be false in test
    expect(config.prettyPrint).toBe(false); // Should be false in test
  });

  it('should be able to log messages without errors', () => {
    expect(() => {
      logger.info('Test info message');
      logger.debug('Test debug message');
      logger.warn('Test warning message');
      logger.error('Test error message');
    }).not.toThrow();
  });

  it('should be able to log structured data', () => {
    expect(() => {
      logger.info({ userId: 123, action: 'login' }, 'User logged in');
      logger.error({ error: 'Database connection failed', code: 'DB_ERROR' }, 'Database error occurred');
    }).not.toThrow();
  });

  it('should create standalone logger with context', () => {
    const standaloneLogger = loggerConfigService.createStandaloneLogger('TestService');
    
    expect(standaloneLogger).toBeDefined();
    expect(typeof standaloneLogger.info).toBe('function');
    
    expect(() => {
      standaloneLogger.info('Standalone logger test message');
    }).not.toThrow();
  });
});

describe('Logger with Different Environments', () => {
  it('should configure correctly for development environment', async () => {
    const module = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          ignoreEnvFile: true,
          load: [
            () => ({
              NODE_ENV: 'development',
              LOG_LEVEL: 'debug',
            }),
          ],
        }),
        LoggerModule,
      ],
    }).compile();

    const loggerConfigService = module.get<LoggerConfigService>(LoggerConfigService);
    const config = loggerConfigService.getLoggerConfig();

    expect(config.environment).toBe('development');
    expect(config.prettyPrint).toBe(true); // Should be true in development
    expect(config.enableLogtail).toBe(false); // No token provided

    await module.close();
  });

  it('should configure correctly for production environment with Logtail', async () => {
    const module = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          ignoreEnvFile: true,
          load: [
            () => ({
              NODE_ENV: 'production',
              LOG_LEVEL: 'info',
              LOGTAIL_TOKEN: 'prod-token',
              LOGTAIL_INGESTING_HOST: 'https://in.logtail.com',
            }),
          ],
        }),
        LoggerModule,
      ],
    }).compile();

    const loggerConfigService = module.get<LoggerConfigService>(LoggerConfigService);
    const config = loggerConfigService.getLoggerConfig();

    expect(config.environment).toBe('production');
    expect(config.level).toBe('info');
    expect(config.prettyPrint).toBe(false); // Should be false in production
    expect(config.enableLogtail).toBe(true); // Should be true with token
    expect(config.logtailToken).toBe('prod-token');
    expect(config.logtailIngestingHost).toBe('https://in.logtail.com');

    await module.close();
  });
});
