import * as fs from 'fs';
import * as path from 'path';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Cron, CronExpression } from '@nestjs/schedule';

/**
 * Log file information interface
 */
export interface LogFileInfo {
  filename: string;
  fullPath: string;
  size: number;
  createdAt: Date;
  modifiedAt: Date;
  age: number; // in days
}

/**
 * Log file management service for handling file rotation, cleanup, and retention
 */
@Injectable()
export class LogFileManager {
  private readonly logger = new Logger(LogFileManager.name);

  constructor(private readonly configService: ConfigService) {}

  /**
   * Get log directory path
   */
  private getLogDirectory(): string {
    return this.configService.get<string>('LOG_DIRECTORY', './logs');
  }

  /**
   * Get retention period in days
   */
  private getRetentionDays(): number {
    return this.configService.get<number>('LOG_RETENTION_DAYS', 30);
  }

  /**
   * Ensure log directory exists
   */
  async ensureLogDirectoryExists(): Promise<void> {
    const logDir = this.getLogDirectory();
    
    try {
      if (!fs.existsSync(logDir)) {
        fs.mkdirSync(logDir, { recursive: true });
        this.logger.log(`Created log directory: ${logDir}`);
      }
    } catch (error) {
      this.logger.error(`Failed to create log directory: ${logDir}`, error);
      throw error;
    }
  }

  /**
   * Get all log files in the log directory
   */
  async getLogFiles(): Promise<LogFileInfo[]> {
    const logDir = this.getLogDirectory();
    
    try {
      if (!fs.existsSync(logDir)) {
        return [];
      }

      const files = fs.readdirSync(logDir);
      const logFiles: LogFileInfo[] = [];

      for (const filename of files) {
        // Only process .log files
        if (!filename.endsWith('.log')) {
          continue;
        }

        const fullPath = path.join(logDir, filename);
        const stats = fs.statSync(fullPath);
        
        const now = new Date();
        const age = Math.floor((now.getTime() - stats.mtime.getTime()) / (1000 * 60 * 60 * 24));

        logFiles.push({
          filename,
          fullPath,
          size: stats.size,
          createdAt: stats.birthtime,
          modifiedAt: stats.mtime,
          age,
        });
      }

      return logFiles.sort((a, b) => b.modifiedAt.getTime() - a.modifiedAt.getTime());
    } catch (error) {
      this.logger.error(`Failed to get log files from directory: ${logDir}`, error);
      return [];
    }
  }

  /**
   * Get log files that exceed retention period
   */
  async getExpiredLogFiles(): Promise<LogFileInfo[]> {
    const retentionDays = this.getRetentionDays();
    const allLogFiles = await this.getLogFiles();
    
    return allLogFiles.filter(file => file.age > retentionDays);
  }

  /**
   * Delete a specific log file
   */
  async deleteLogFile(filePath: string): Promise<boolean> {
    try {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
        this.logger.log(`Deleted log file: ${filePath}`);
        return true;
      }
      return false;
    } catch (error) {
      this.logger.error(`Failed to delete log file: ${filePath}`, error);
      return false;
    }
  }

  /**
   * Clean up expired log files
   */
  async cleanupExpiredLogFiles(): Promise<number> {
    const expiredFiles = await this.getExpiredLogFiles();
    let deletedCount = 0;

    this.logger.log(`Found ${expiredFiles.length} expired log files to clean up`);

    for (const file of expiredFiles) {
      const deleted = await this.deleteLogFile(file.fullPath);
      if (deleted) {
        deletedCount++;
      }
    }

    this.logger.log(`Cleaned up ${deletedCount} expired log files`);
    return deletedCount;
  }

  /**
   * Get log directory statistics
   */
  async getLogDirectoryStats(): Promise<{
    totalFiles: number;
    totalSize: number;
    oldestFile?: LogFileInfo;
    newestFile?: LogFileInfo;
    expiredFiles: number;
  }> {
    const logFiles = await this.getLogFiles();
    const expiredFiles = await this.getExpiredLogFiles();

    const totalSize = logFiles.reduce((sum, file) => sum + file.size, 0);

    return {
      totalFiles: logFiles.length,
      totalSize,
      oldestFile: logFiles[logFiles.length - 1],
      newestFile: logFiles[0],
      expiredFiles: expiredFiles.length,
    };
  }

  /**
   * Format file size in human readable format
   */
  formatFileSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(2)} ${units[unitIndex]}`;
  }

  /**
   * Scheduled cleanup job - runs daily at 2 AM
   */
  @Cron(CronExpression.EVERY_DAY_AT_2AM)
  async scheduledCleanup(): Promise<void> {
    try {
      this.logger.log('Starting scheduled log file cleanup');
      
      const stats = await this.getLogDirectoryStats();
      this.logger.log(`Log directory stats before cleanup: ${stats.totalFiles} files, ${this.formatFileSize(stats.totalSize)}, ${stats.expiredFiles} expired`);
      
      const deletedCount = await this.cleanupExpiredLogFiles();
      
      const statsAfter = await this.getLogDirectoryStats();
      this.logger.log(`Log directory stats after cleanup: ${statsAfter.totalFiles} files, ${this.formatFileSize(statsAfter.totalSize)}`);
      
      this.logger.log(`Scheduled log cleanup completed. Deleted ${deletedCount} files`);
    } catch (error) {
      this.logger.error('Failed to perform scheduled log cleanup', error);
    }
  }

  /**
   * Manual cleanup with detailed reporting
   */
  async performManualCleanup(): Promise<{
    deletedFiles: number;
    beforeStats: any;
    afterStats: any;
  }> {
    const beforeStats = await this.getLogDirectoryStats();
    const deletedFiles = await this.cleanupExpiredLogFiles();
    const afterStats = await this.getLogDirectoryStats();

    return {
      deletedFiles,
      beforeStats,
      afterStats,
    };
  }

  /**
   * Archive old log files (compress them instead of deleting)
   */
  async archiveOldLogFiles(): Promise<number> {
    // This could be implemented to compress old files instead of deleting them
    // For now, we'll just log that this feature could be added
    this.logger.log('Archive functionality not yet implemented. Consider adding compression for old log files.');
    return 0;
  }
}
