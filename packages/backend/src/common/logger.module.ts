import { Module, Global } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { LoggerModule as PinoLoggerModule } from 'nestjs-pino';
import { LoggerConfigService, createLoggerOptions } from './utils/logger.config';

/**
 * Global logger module that provides Pino logger configuration
 * with Betterstack Logtail integration
 */
@Global()
@Module({
  imports: [
    PinoLoggerModule.forRootAsync({
      inject: [ConfigService],
      useFactory: createLoggerOptions,
    }),
  ],
  providers: [LoggerConfigService],
  exports: [LoggerConfigService, PinoLoggerModule],
})
export class LoggerModule {}
