import { Modu<PERSON> } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { UnitConversionService } from './services/unit-conversion.service';
import { FileUploadService } from './services/file-upload.service';
import { ReferenceGeneratorService } from './services/reference-generator.service';
import { LoggerConfigService } from './utils/logger.config';
import { LogFileManager } from './utils/log-file-manager';
import { GlobalExceptionFilter } from './utils/global-exception.filter';
import { PrismaModule } from '../prisma/prisma.module';
import { SettingsModule } from '../settings/settings.module';

@Module({
  imports: [
    PrismaModule,
    SettingsModule,
    ScheduleModule.forRoot(), // Enable scheduled tasks for log cleanup
  ],
  providers: [
    UnitConversionService,
    FileUploadService,
    ReferenceGeneratorService,
    LoggerConfigService,
    LogFileManager,
    GlobalExceptionFilter,
  ],
  exports: [
    UnitConversionService,
    FileUploadService,
    ReferenceGeneratorService,
    LoggerConfigService,
    LogFileManager,
    GlobalExceptionFilter,
  ],
})
export class CommonModule { }
