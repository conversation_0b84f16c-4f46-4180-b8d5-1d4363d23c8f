# Panduan Implementasi Sistem Validasi Batch Number

## Prasyarat Implementasi

### 1. Database Schema
Pastikan tabel berikut sudah ada dan ter-migrate:
```sql
-- Tabel utama sudah ada di Prisma schema
-- Tambahan index untuk performa
CREATE INDEX IF NOT EXISTS idx_inventory_batch_product ON inventory_items(batch_number, product_id);
CREATE INDEX IF NOT EXISTS idx_batch_audit_batch_date ON batch_audit_logs(batch_number, created_at);
CREATE INDEX IF NOT EXISTS idx_products_bpom ON products(bpom_number) WHERE bpom_number IS NOT NULL;
```

### 2. Environment Variables
```env
# Batch Validation Configuration
BATCH_VALIDATION_ENABLED=true
BATCH_VALIDATION_STRICT_MODE=true
BATCH_VALIDATION_CACHE_TTL=3600

# BPOM Compliance
BPOM_COMPLIANCE_ENABLED=true
BPOM_STRICT_VALIDATION=true
CONTROLLED_SUBSTANCE_VALIDATION=true

# Performance Settings
VALIDATION_RATE_LIMIT_PER_MINUTE=100
REAL_TIME_VALIDATION_TIMEOUT_MS=5000
BATCH_VALIDATION_MAX_CONCURRENT=10
```

### 3. Dependencies
```json
{
  "dependencies": {
    "@nestjs/common": "^10.0.0",
    "@nestjs/core": "^10.0.0",
    "@prisma/client": "^5.0.0",
    "class-validator": "^0.14.0",
    "class-transformer": "^0.5.0"
  }
}
```

## Langkah Implementasi

### Step 1: Setup Services

#### 1.1 Register Services di Module
```typescript
// procurement.module.ts
import { BatchNumberValidationService } from './services/batch-number-validation.service';
import { BPOMComplianceService } from './services/bpom-compliance.service';
import { BatchAuditService } from './services/batch-audit.service';

@Module({
  providers: [
    BatchNumberValidationService,
    BPOMComplianceService,
    BatchAuditService,
    // ... other services
  ],
  exports: [
    BatchNumberValidationService,
    BPOMComplianceService,
    BatchAuditService,
  ],
})
export class ProcurementModule {}
```

#### 1.2 Configure Controllers
```typescript
// procurement.module.ts
import { BatchValidationController } from './batch-validation.controller';
import { BPOMComplianceController } from './bpom-compliance.controller';

@Module({
  controllers: [
    BatchValidationController,
    BPOMComplianceController,
    // ... other controllers
  ],
})
export class ProcurementModule {}
```

### Step 2: Frontend Integration

#### 2.1 Create Validation Hook
```typescript
// hooks/useBatchValidation.ts
import { useState, useEffect } from 'react';
import { useMutation } from '@tanstack/react-query';

interface UseBatchValidationProps {
  batchNumber: string;
  productId: string;
  debounceMs?: number;
}

export function useBatchValidation({ 
  batchNumber, 
  productId, 
  debounceMs = 300 
}: UseBatchValidationProps) {
  const [debouncedBatchNumber, setDebouncedBatchNumber] = useState(batchNumber);

  // Debounce batch number input
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedBatchNumber(batchNumber);
    }, debounceMs);

    return () => clearTimeout(timer);
  }, [batchNumber, debounceMs]);

  const validationMutation = useMutation({
    mutationFn: async ({ batchNumber, productId }: { batchNumber: string; productId: string }) => {
      const response = await fetch('/api/procurement/batch-validation/real-time', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ batchNumber, productId }),
      });
      return response.json();
    },
  });

  // Trigger validation when debounced value changes
  useEffect(() => {
    if (debouncedBatchNumber && productId) {
      validationMutation.mutate({ 
        batchNumber: debouncedBatchNumber, 
        productId 
      });
    }
  }, [debouncedBatchNumber, productId]);

  return {
    validation: validationMutation.data?.data,
    isValidating: validationMutation.isPending,
    error: validationMutation.error,
  };
}
```

#### 2.2 Create Batch Input Component
```typescript
// components/BatchNumberInput.tsx
import React from 'react';
import { Input } from '@/components/ui/input';
import { useBatchValidation } from '@/hooks/useBatchValidation';

interface BatchNumberInputProps {
  value: string;
  onChange: (value: string) => void;
  productId: string;
  className?: string;
}

export function BatchNumberInput({ 
  value, 
  onChange, 
  productId, 
  className 
}: BatchNumberInputProps) {
  const { validation, isValidating } = useBatchValidation({
    batchNumber: value,
    productId,
  });

  const getValidationStatus = () => {
    if (isValidating) return 'validating';
    if (!validation) return 'idle';
    return validation.isValid ? 'valid' : 'invalid';
  };

  const getValidationMessage = () => {
    if (isValidating) return 'Memvalidasi...';
    if (!validation) return '';
    return validation.message;
  };

  return (
    <div className="space-y-2">
      <Input
        value={value}
        onChange={(e) => onChange(e.target.value.toUpperCase())}
        placeholder="Masukkan nomor batch"
        className={`${className} ${
          getValidationStatus() === 'valid' ? 'border-green-500' :
          getValidationStatus() === 'invalid' ? 'border-red-500' :
          getValidationStatus() === 'validating' ? 'border-yellow-500' :
          ''
        }`}
      />
      
      {getValidationMessage() && (
        <p className={`text-sm ${
          getValidationStatus() === 'valid' ? 'text-green-600' :
          getValidationStatus() === 'invalid' ? 'text-red-600' :
          'text-yellow-600'
        }`}>
          {getValidationMessage()}
        </p>
      )}
    </div>
  );
}
```

### Step 3: Integration dengan Goods Receipt

#### 3.1 Update Goods Receipt Form
```typescript
// components/GoodsReceiptForm.tsx
import { BatchNumberInput } from './BatchNumberInput';

export function GoodsReceiptForm() {
  const [items, setItems] = useState([]);

  const updateItemBatch = (index: number, batchNumber: string) => {
    setItems(prev => prev.map((item, i) => 
      i === index ? { ...item, batchNumber } : item
    ));
  };

  return (
    <form>
      {items.map((item, index) => (
        <div key={index} className="grid grid-cols-4 gap-4">
          <div>
            <label>Produk</label>
            <ProductSelect 
              value={item.productId}
              onChange={(productId) => updateItem(index, 'productId', productId)}
            />
          </div>
          
          <div>
            <label>Nomor Batch</label>
            <BatchNumberInput
              value={item.batchNumber}
              onChange={(batchNumber) => updateItemBatch(index, batchNumber)}
              productId={item.productId}
            />
          </div>
          
          <div>
            <label>Tanggal Kedaluwarsa</label>
            <DatePicker
              value={item.expiryDate}
              onChange={(date) => updateItem(index, 'expiryDate', date)}
            />
          </div>
          
          <div>
            <label>Jumlah</label>
            <Input
              type="number"
              value={item.quantity}
              onChange={(e) => updateItem(index, 'quantity', e.target.value)}
            />
          </div>
        </div>
      ))}
    </form>
  );
}
```

### Step 4: Testing Implementation

#### 4.1 Run Unit Tests
```bash
# Test individual services
bun test src/procurement/services/batch-number-validation.service.spec.ts
bun test src/procurement/services/bpom-compliance.service.spec.ts
```

#### 4.2 Run Integration Tests
```bash
# Test API endpoints
bun test:integration batch-validation
bun test:integration bpom-compliance
```

#### 4.3 Manual Testing Checklist
- [ ] Valid batch number validation
- [ ] Invalid format rejection
- [ ] Duplicate detection
- [ ] Controlled substance validation
- [ ] BPOM compliance check
- [ ] Real-time validation in UI
- [ ] Error handling
- [ ] Performance under load

### Step 5: Deployment

#### 5.1 Database Migration
```bash
# Run Prisma migrations
bun prisma migrate deploy

# Create performance indexes
bun prisma db execute --file=./sql/batch_validation_indexes.sql
```

#### 5.2 Environment Setup
```bash
# Production environment
export BATCH_VALIDATION_ENABLED=true
export BPOM_COMPLIANCE_ENABLED=true
export BATCH_VALIDATION_STRICT_MODE=true

# Staging environment
export BATCH_VALIDATION_ENABLED=true
export BPOM_COMPLIANCE_ENABLED=false
export BATCH_VALIDATION_STRICT_MODE=false
```

#### 5.3 Health Checks
```typescript
// health/batch-validation.health.ts
import { Injectable } from '@nestjs/common';
import { HealthIndicator, HealthIndicatorResult, HealthCheckError } from '@nestjs/terminus';
import { BatchNumberValidationService } from '../procurement/services/batch-number-validation.service';

@Injectable()
export class BatchValidationHealthIndicator extends HealthIndicator {
  constructor(
    private readonly batchValidationService: BatchNumberValidationService,
  ) {
    super();
  }

  async isHealthy(key: string): Promise<HealthIndicatorResult> {
    try {
      // Test basic validation functionality
      const testResult = await this.batchValidationService.validateRealTime(
        'TEST123',
        'test-product-id'
      );
      
      const isHealthy = testResult !== null;
      const result = this.getStatus(key, isHealthy);

      if (isHealthy) {
        return result;
      }
      throw new HealthCheckError('Batch validation failed', result);
    } catch (error) {
      throw new HealthCheckError('Batch validation failed', {
        [key]: { status: 'down', error: error.message },
      });
    }
  }
}
```

## Monitoring & Maintenance

### 1. Performance Monitoring
```typescript
// monitoring/batch-validation.metrics.ts
import { Injectable } from '@nestjs/common';
import { Counter, Histogram, register } from 'prom-client';

@Injectable()
export class BatchValidationMetrics {
  private readonly validationCounter = new Counter({
    name: 'batch_validation_total',
    help: 'Total number of batch validations',
    labelNames: ['status', 'type'],
  });

  private readonly validationDuration = new Histogram({
    name: 'batch_validation_duration_seconds',
    help: 'Duration of batch validation in seconds',
    labelNames: ['type'],
  });

  constructor() {
    register.registerMetric(this.validationCounter);
    register.registerMetric(this.validationDuration);
  }

  recordValidation(status: 'success' | 'error', type: 'real-time' | 'full', duration: number) {
    this.validationCounter.inc({ status, type });
    this.validationDuration.observe({ type }, duration);
  }
}
```

### 2. Error Tracking
```typescript
// monitoring/error-tracker.ts
import { Injectable, Logger } from '@nestjs/common';

@Injectable()
export class BatchValidationErrorTracker {
  private readonly logger = new Logger(BatchValidationErrorTracker.name);

  trackValidationError(error: Error, context: any) {
    this.logger.error('Batch validation error', {
      error: error.message,
      stack: error.stack,
      context,
      timestamp: new Date().toISOString(),
    });

    // Send to external monitoring service
    // e.g., Sentry, DataDog, etc.
  }

  trackPerformanceIssue(operation: string, duration: number, threshold: number) {
    if (duration > threshold) {
      this.logger.warn('Performance issue detected', {
        operation,
        duration,
        threshold,
        timestamp: new Date().toISOString(),
      });
    }
  }
}
```

### 3. Maintenance Tasks
```typescript
// tasks/batch-validation.maintenance.ts
import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';

@Injectable()
export class BatchValidationMaintenanceService {
  
  @Cron(CronExpression.EVERY_DAY_AT_2AM)
  async cleanupOldAuditLogs() {
    // Archive audit logs older than 1 year
    const oneYearAgo = new Date();
    oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
    
    // Implementation for archiving old logs
  }

  @Cron(CronExpression.EVERY_HOUR)
  async refreshValidationCache() {
    // Refresh frequently used validation data
    // Implementation for cache refresh
  }

  @Cron(CronExpression.EVERY_6_HOURS)
  async validateSystemHealth() {
    // Run system health checks
    // Implementation for health validation
  }
}
```

## Troubleshooting Common Issues

### Issue 1: Slow Validation Performance
**Symptoms**: Validation takes > 5 seconds
**Solutions**:
1. Check database indexes
2. Review cache hit rates
3. Optimize database queries
4. Consider read replicas

### Issue 2: False Positive Duplicates
**Symptoms**: Valid batches rejected as duplicates
**Solutions**:
1. Check case sensitivity handling
2. Review whitespace normalization
3. Verify product ID matching
4. Check soft-deleted records

### Issue 3: BPOM Compliance Errors
**Symptoms**: Valid products fail BPOM checks
**Solutions**:
1. Verify BPOM number format
2. Check product type mapping
3. Update BPOM registration data
4. Review compliance rules

### Issue 4: Memory Leaks
**Symptoms**: Increasing memory usage over time
**Solutions**:
1. Check cache cleanup
2. Review event listeners
3. Monitor database connections
4. Profile memory usage
