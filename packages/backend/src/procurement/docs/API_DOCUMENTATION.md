# API Documentation - Batch Validation & BPOM Compliance

## Base URL
```
https://api.apotek.com/api/procurement
```

## Authentication
Semua endpoint memer<PERSON>an Bearer token authentication:
```
Authorization: Bearer <access_token>
```

## Batch Validation Endpoints

### 1. Comprehensive Batch Validation

**Endpoint**: `POST /batch-validation/validate`

**Description**: Melakukan validasi komprehensif terhadap nomor batch termasuk format, keunikan, dan kepatuhan <PERSON>.

**Request Body**:
```json
{
  "batchNumber": "ABC240615XY",
  "productId": "product-uuid",
  "supplierId": "supplier-uuid",
  "expiryDate": "2025-06-15T00:00:00.000Z",
  "manufacturingDate": "2024-06-15T00:00:00.000Z",
  "userId": "user-uuid",
  "isSubstitution": false,
  "originalBatchNumber": "ABC240615XZ"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "isValid": true,
    "bpomCompliant": true,
    "validationLevel": "BPOM_COMPLIANT",
    "formatValidation": {
      "isValid": true,
      "pattern": "BPOM_STANDARD",
      "extractedDate": "2024-06-15"
    },
    "uniquenessCheck": {
      "isUnique": true,
      "conflicts": []
    },
    "expiryAlignment": {
      "isValid": true,
      "shelfLifeMonths": 12
    },
    "errors": [],
    "warnings": [],
    "recommendations": [
      "Format batch number sesuai dengan standar BPOM"
    ],
    "auditTrail": {
      "validationId": "validation-uuid",
      "timestamp": "2024-06-15T10:30:00.000Z",
      "userId": "user-uuid"
    }
  },
  "message": "Validasi batch berhasil dilakukan"
}
```

**Error Responses**:
```json
{
  "success": false,
  "message": "Validasi batch gagal",
  "error": "Format batch number tidak valid",
  "details": {
    "field": "batchNumber",
    "code": "INVALID_FORMAT"
  }
}
```

### 2. Real-time Validation

**Endpoint**: `POST /batch-validation/real-time`

**Description**: Validasi cepat untuk feedback real-time di UI.

**Request Body**:
```json
{
  "batchNumber": "ABC240615XY",
  "productId": "product-uuid"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "isValid": true,
    "level": "success",
    "message": "Format batch number sesuai standar BPOM",
    "suggestions": [
      "Pastikan tanggal kedaluwarsa sesuai"
    ]
  }
}
```

### 3. Uniqueness Check

**Endpoint**: `POST /batch-validation/check-uniqueness`

**Description**: Memeriksa keunikan nomor batch di seluruh sistem.

**Request Body**:
```json
{
  "batchNumber": "ABC240615XY",
  "productId": "product-uuid",
  "excludeId": "inventory-item-uuid"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "isUnique": false,
    "conflicts": [
      {
        "source": "inventory",
        "id": "inventory-item-uuid",
        "productName": "Paracetamol 500mg",
        "supplierName": "PT Pharma Indonesia",
        "createdAt": "2024-06-01T00:00:00.000Z"
      }
    ],
    "totalConflicts": 1
  }
}
```

### 4. Batch History

**Endpoint**: `GET /batch-validation/history/{batchNumber}`

**Description**: Mengambil riwayat penggunaan nomor batch.

**Parameters**:
- `batchNumber` (path): Nomor batch yang dicari
- `includeAudit` (query): Include audit trail (default: false)
- `limit` (query): Limit hasil (default: 50)

**Response**:
```json
{
  "success": true,
  "data": {
    "batchNumber": "ABC240615XY",
    "usageHistory": [
      {
        "id": "usage-uuid",
        "type": "GOODS_RECEIPT",
        "productName": "Paracetamol 500mg",
        "quantity": 100,
        "date": "2024-06-15T00:00:00.000Z",
        "status": "ACTIVE"
      }
    ],
    "validationHistory": [
      {
        "id": "validation-uuid",
        "result": "VALID",
        "timestamp": "2024-06-15T10:30:00.000Z",
        "userId": "user-uuid"
      }
    ],
    "totalUsages": 1
  }
}
```

## BPOM Compliance Endpoints

### 1. Compliance Check

**Endpoint**: `POST /bpom-compliance/check`

**Description**: Melakukan pemeriksaan kepatuhan BPOM komprehensif.

**Request Body**:
```json
{
  "productId": "product-uuid",
  "batchNumber": "ABC240615XY",
  "userId": "user-uuid"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "isCompliant": true,
    "complianceLevel": "BPOM_REGISTERED",
    "registrationValid": true,
    "registrationType": "DKL",
    "registrationNumber": "DKL1234567890A1",
    "controlledSubstance": false,
    "importProduct": false,
    "warnings": [],
    "errors": [],
    "recommendations": [
      "Pastikan penyimpanan sesuai dengan persyaratan BPOM"
    ],
    "auditTrail": [
      {
        "checkDate": "2024-06-15T10:30:00.000Z",
        "checkType": "COMPREHENSIVE",
        "result": "COMPLIANT"
      }
    ]
  }
}
```

### 2. Batch Status Overview

**Endpoint**: `GET /bpom-compliance/batch-status`

**Description**: Mengambil status compliance untuk multiple batch.

**Query Parameters**:
- `productIds` (string): Comma-separated product IDs
- `batchNumbers` (string): Comma-separated batch numbers
- `limit` (number): Limit hasil (default: 50)
- `offset` (number): Offset untuk pagination (default: 0)

**Response**:
```json
{
  "success": true,
  "data": {
    "results": [
      {
        "productId": "product-uuid",
        "productName": "Paracetamol 500mg",
        "batchNumber": "ABC240615XY",
        "compliance": {
          "isCompliant": true,
          "complianceLevel": "BPOM_REGISTERED",
          "registrationValid": true
        }
      }
    ],
    "total": 1,
    "summary": {
      "compliant": 1,
      "nonCompliant": 0,
      "controlled": 0,
      "imported": 0
    }
  }
}
```

### 3. Controlled Substances

**Endpoint**: `GET /bpom-compliance/controlled-substances`

**Description**: Mengambil data tracking obat terkontrol.

**Query Parameters**:
- `startDate` (string): Tanggal mulai (ISO format)
- `endDate` (string): Tanggal akhir (ISO format)
- `classification` (string): NARKOTIKA atau PSIKOTROPIKA

**Response**:
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "item-uuid",
        "productId": "product-uuid",
        "productName": "Morphine 10mg",
        "batchNumber": "NAR12345678AB",
        "classification": "NARKOTIKA",
        "quantity": 50,
        "unit": "units",
        "supplierName": "PT Pharma Controlled",
        "receivedDate": "2024-06-15T00:00:00.000Z",
        "expiryDate": "2025-06-15T00:00:00.000Z",
        "bpomNumber": "DKL9876543210B2"
      }
    ],
    "summary": {
      "totalItems": 1,
      "narkotika": 1,
      "psikotropika": 0,
      "totalQuantity": 50
    }
  }
}
```

### 4. Inspection Preparation

**Endpoint**: `POST /bpom-compliance/inspection-prep`

**Description**: Generate laporan persiapan inspeksi BPOM.

**Request Body**:
```json
{
  "startDate": "2024-01-01T00:00:00.000Z",
  "endDate": "2024-12-31T23:59:59.999Z",
  "productIds": ["product-uuid-1", "product-uuid-2"],
  "includeControlledSubstances": true
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "period": {
      "startDate": "2024-01-01T00:00:00.000Z",
      "endDate": "2024-12-31T23:59:59.999Z"
    },
    "summary": {
      "totalBatches": 150,
      "compliantBatches": 145,
      "nonCompliantBatches": 5,
      "controlledSubstances": 10,
      "importedProducts": 20
    },
    "complianceIssues": [
      {
        "productName": "Product A",
        "batchNumber": "ISSUE123",
        "issues": ["Format batch number tidak sesuai standar"],
        "complianceLevel": "BASIC"
      }
    ],
    "controlledSubstanceReport": [
      {
        "productName": "Morphine 10mg",
        "batchNumber": "NAR12345678AB",
        "classification": "NARKOTIKA",
        "quantity": 50,
        "unit": "units",
        "supplierName": "PT Pharma Controlled",
        "complianceStatus": "COMPLIANT"
      }
    ],
    "preparationChecklist": [
      {
        "item": "Dokumen registrasi BPOM lengkap",
        "status": "COMPLETE"
      },
      {
        "item": "Batch records dan dokumentasi produksi",
        "status": "NEEDS_ATTENTION",
        "notes": "Perlu verifikasi manual"
      }
    ]
  }
}
```

### 5. Compliance Statistics

**Endpoint**: `GET /bpom-compliance/statistics`

**Description**: Mengambil statistik compliance BPOM.

**Query Parameters**:
- `period` (string): 7d, 30d, 90d, 1y (default: 30d)

**Response**:
```json
{
  "success": true,
  "data": {
    "totalBatches": 500,
    "bpomRegistered": 480,
    "controlledSubstances": 25,
    "medicines": 400,
    "medicalDevices": 50,
    "supplements": 50,
    "complianceRate": 96.5,
    "period": {
      "days": 30,
      "startDate": "2024-05-15T00:00:00.000Z",
      "endDate": "2024-06-15T00:00:00.000Z"
    }
  }
}
```

## Error Codes

### Validation Errors (400)
- `BATCH_REQUIRED`: Batch number wajib diisi
- `BATCH_INVALID_FORMAT`: Format batch number tidak valid
- `BATCH_TOO_SHORT`: Batch number terlalu pendek
- `BATCH_TOO_LONG`: Batch number terlalu panjang
- `BATCH_INVALID_CHARACTERS`: Karakter tidak diizinkan
- `BATCH_DUPLICATE`: Batch number sudah digunakan
- `BATCH_EXPIRED`: Batch sudah kedaluwarsa
- `PRODUCT_NOT_FOUND`: Produk tidak ditemukan
- `SUPPLIER_NOT_FOUND`: Supplier tidak ditemukan

### BPOM Compliance Errors (400)
- `BPOM_INVALID_REGISTRATION`: Nomor registrasi BPOM tidak valid
- `BPOM_REGISTRATION_EXPIRED`: Registrasi BPOM sudah kedaluwarsa
- `BPOM_TYPE_MISMATCH`: Tipe registrasi tidak sesuai produk
- `CONTROLLED_INVALID_FORMAT`: Format batch obat terkontrol tidak valid
- `CONTROLLED_MISSING_REQUIREMENTS`: Persyaratan obat terkontrol tidak lengkap

### Authentication Errors (401)
- `UNAUTHORIZED`: Token tidak valid atau expired
- `TOKEN_MISSING`: Authorization header tidak ada

### Authorization Errors (403)
- `FORBIDDEN`: Tidak memiliki permission untuk akses endpoint
- `ROLE_INSUFFICIENT`: Role user tidak mencukupi

### Server Errors (500)
- `INTERNAL_ERROR`: Error internal server
- `DATABASE_ERROR`: Error koneksi database
- `VALIDATION_SERVICE_ERROR`: Error pada service validasi

## Rate Limiting

### Limits per User
- Real-time validation: 100 requests/minute
- Comprehensive validation: 50 requests/minute
- BPOM compliance check: 20 requests/minute
- Batch history: 30 requests/minute

### Headers
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1624567890
```

## Webhooks (Future Feature)

### Batch Validation Events
```json
{
  "event": "batch.validation.completed",
  "data": {
    "batchNumber": "ABC240615XY",
    "productId": "product-uuid",
    "isValid": true,
    "timestamp": "2024-06-15T10:30:00.000Z"
  }
}
```

### BPOM Compliance Events
```json
{
  "event": "bpom.compliance.failed",
  "data": {
    "productId": "product-uuid",
    "batchNumber": "ABC240615XY",
    "issues": ["Registration expired"],
    "timestamp": "2024-06-15T10:30:00.000Z"
  }
}
```
