import { BadRequestException } from '@nestjs/common';
import { PurchaseOrderStatus, GoodsReceiptStatus } from '@prisma/client';

/**
 * Procurement-specific validation utilities for business logic
 */
export class ProcurementValidationUtils {

  /**
   * Validates if a purchase order status transition is allowed
   * @param currentStatus Current status of the purchase order
   * @param newStatus New status to transition to
   * @returns boolean indicating if transition is valid
   */
  static validatePurchaseOrderStatusTransition(
    currentStatus: PurchaseOrderStatus,
    newStatus: PurchaseOrderStatus
  ): boolean {
    const validTransitions: Partial<Record<PurchaseOrderStatus, PurchaseOrderStatus[]>> = {
      [PurchaseOrderStatus.DRAFT]: [
        PurchaseOrderStatus.SUBMITTED,
        PurchaseOrderStatus.CANCELLED
      ],
      [PurchaseOrderStatus.SUBMITTED]: [
        PurchaseOrderStatus.ORDERED,
        PurchaseOrderStatus.CANCELLED,
        PurchaseOrderStatus.DRAFT // Allow back to draft for corrections
      ],
      [PurchaseOrderStatus.ORDERED]: [
        PurchaseOrderStatus.PARTIALLY_RECEIVED,
        PurchaseOrderStatus.COMPLETED,
        PurchaseOrderStatus.CANCELLED
      ],
      [PurchaseOrderStatus.PARTIALLY_RECEIVED]: [
        PurchaseOrderStatus.COMPLETED,
        PurchaseOrderStatus.CANCELLED
      ],
      [PurchaseOrderStatus.COMPLETED]: [], // Final state
      [PurchaseOrderStatus.CANCELLED]: [] // Final state
    };

    return validTransitions[currentStatus]?.includes(newStatus) || false;
  }

  /**
   * Validates if a goods receipt status transition is allowed
   * @param currentStatus Current status of the goods receipt
   * @param newStatus New status to transition to
   * @returns boolean indicating if transition is valid
   */
  static validateGoodsReceiptStatusTransition(
    currentStatus: GoodsReceiptStatus,
    newStatus: GoodsReceiptStatus
  ): boolean {
    const validTransitions: Partial<Record<GoodsReceiptStatus, GoodsReceiptStatus[]>> = {
      [GoodsReceiptStatus.PENDING]: [
        GoodsReceiptStatus.COMPLETED, // Direct completion without approval workflow
        GoodsReceiptStatus.REJECTED
      ],
      [GoodsReceiptStatus.REJECTED]: [], // Final state
      [GoodsReceiptStatus.COMPLETED]: [] // Final state
    };

    return validTransitions[currentStatus]?.includes(newStatus) || false;
  }



  /**
   * Validates if expiry date is reasonable for pharmaceutical products
   * @param expiryDate Expiry date to validate
   * @param manufacturingDate Manufacturing date (optional)
   * @throws BadRequestException if validation fails
   */
  static validateExpiryDate(expiryDate: Date, manufacturingDate?: Date): void {
    const now = new Date();

    // Expiry date should not be in the past
    if (expiryDate <= now) {
      throw new BadRequestException('Tanggal kedaluwarsa tidak boleh di masa lalu');
    }

    // If manufacturing date is provided, expiry should be after manufacturing
    if (manufacturingDate && expiryDate <= manufacturingDate) {
      throw new BadRequestException('Tanggal kedaluwarsa harus setelah tanggal produksi');
    }

    // Manufacturing date should not be in the future
    if (manufacturingDate && manufacturingDate > now) {
      throw new BadRequestException('Tanggal produksi tidak boleh di masa depan');
    }

    // Reasonable shelf life check (not more than 10 years for most pharmaceuticals)
    const maxShelfLifeYears = 10;
    const maxExpiryDate = new Date();
    maxExpiryDate.setFullYear(maxExpiryDate.getFullYear() + maxShelfLifeYears);

    if (expiryDate > maxExpiryDate) {
      throw new BadRequestException(
        `Tanggal kedaluwarsa tidak boleh lebih dari ${maxShelfLifeYears} tahun dari sekarang`
      );
    }
  }

  /**
   * Validates batch number format for Indonesian pharmaceutical standards
   * @param batchNumber Batch number to validate
   * @returns boolean indicating if batch number is valid
   */
  static validateBatchNumber(batchNumber: string): boolean {
    if (!batchNumber || typeof batchNumber !== 'string') {
      return false;
    }

    // Basic validation: should be alphanumeric, 3-20 characters
    const batchRegex = /^[A-Za-z0-9\-_]{3,20}$/;
    return batchRegex.test(batchNumber);
  }

  /**
   * Validates if purchase order can be modified based on its current status
   * @param status Current purchase order status
   * @returns boolean indicating if order can be modified
   */
  static canModifyPurchaseOrder(status: PurchaseOrderStatus): boolean {
    const modifiableStatuses: PurchaseOrderStatus[] = [
      PurchaseOrderStatus.DRAFT,
      PurchaseOrderStatus.SUBMITTED
    ];
    return modifiableStatuses.includes(status);
  }

  /**
   * Validates if goods receipt can be modified based on its current status
   * @param status Current goods receipt status
   * @returns boolean indicating if receipt can be modified
   */
  static canModifyGoodsReceipt(status: GoodsReceiptStatus): boolean {
    const modifiableStatuses: GoodsReceiptStatus[] = [
      GoodsReceiptStatus.PENDING
    ];
    return modifiableStatuses.includes(status);
  }

  /**
   * Validates discount values
   * @param discountType Type of discount (PERCENTAGE or FIXED_AMOUNT)
   * @param discountValue Discount value
   * @param totalAmount Total amount for percentage validation
   * @throws BadRequestException if validation fails
   */
  static validateDiscount(
    discountType: string,
    discountValue: number,
    totalAmount?: number
  ): void {
    if (discountValue < 0) {
      throw new BadRequestException('Nilai diskon tidak boleh negatif');
    }

    if (discountType === 'PERCENTAGE') {
      if (discountValue > 100) {
        throw new BadRequestException('Diskon persentase tidak boleh lebih dari 100%');
      }
    } else if (discountType === 'FIXED_AMOUNT') {
      if (totalAmount && discountValue > totalAmount) {
        throw new BadRequestException('Diskon nominal tidak boleh lebih dari total amount');
      }
    }
  }
}
