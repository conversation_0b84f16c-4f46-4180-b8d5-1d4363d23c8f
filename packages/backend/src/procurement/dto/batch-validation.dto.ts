import { IsString, IsOptional, IsDateString, IsBoolean } from 'class-validator';
import { Transform } from 'class-transformer';

/**
 * DTO for comprehensive batch number validation request
 */
export class ValidateBatchNumberDto {
  @IsString()
  batchNumber: string;

  @IsString()
  productId: string;

  @IsOptional()
  @IsString()
  supplierId?: string;

  @IsOptional()
  @IsDateString()
  expiryDate?: string;

  @IsOptional()
  @IsDateString()
  manufacturingDate?: string;

  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  @IsBoolean()
  isSubstitution?: boolean;

  @IsOptional()
  @IsString()
  originalBatchNumber?: string;
}

/**
 * DTO for real-time batch validation request
 */
export class RealTimeValidationDto {
  @IsString()
  batchNumber: string;

  @IsString()
  productId: string;
}

/**
 * DTO for batch uniqueness check request
 */
export class BatchUniquenessDto {
  @IsString()
  batchNumber: string;

  @IsString()
  productId: string;

  @IsOptional()
  @IsString()
  supplierId?: string;
}

/**
 * Response DTO for batch validation result
 */
export interface BatchValidationResponseDto {
  success: boolean;
  data: {
    isValid: boolean;
    errors: string[];
    warnings: string[];
    validationLevel: 'BASIC' | 'STANDARD' | 'BPOM_COMPLIANT' | 'CONTROLLED';
    bpomCompliant: boolean; // Whether batch number format complies with BPOM standards
    uniquenessCheck: {
      isUnique: boolean;
      conflictingBatches?: Array<{
        id: string;
        batchNumber: string;
        productName: string;
        supplierName: string;
        createdAt: Date;
      }>;
    };
    formatValidation: {
      passedRules: string[];
      failedRules: string[];
      recommendedFormat?: string;
    };
  };
  message: string;
}

/**
 * Response DTO for real-time validation
 */
export interface RealTimeValidationResponseDto {
  success: boolean;
  data: {
    isValid: boolean;
    message?: string;
    level: 'error' | 'warning' | 'success';
  };
  message: string;
}

/**
 * Response DTO for uniqueness check
 */
export interface UniquenessCheckResponseDto {
  success: boolean;
  data: {
    isUnique: boolean;
    conflicts: Array<{
      source: 'inventory' | 'goods_receipt';
      productName: string;
      supplierName: string;
      createdAt: Date;
    }>;
  };
  message: string;
}

/**
 * Response DTO for batch history
 */
export interface BatchHistoryResponseDto {
  success: boolean;
  data: {
    batchNumber: string;
    usageHistory: Array<{
      id: string;
      type: 'goods_receipt' | 'inventory_creation' | 'sale' | 'adjustment';
      productName: string;
      quantity: number;
      unitName: string;
      date: Date;
      reference?: string;
      notes?: string;
    }>;
    totalReceived: number;
    totalUsed: number;
    currentStock: number;
    lastActivity: Date | null;
    createdAt: Date | null;
    status: 'active' | 'depleted' | 'expired' | 'recalled';
    expiryDate?: Date;
    storageLocations: string[];
  };
  message: string;
}

/**
 * Response DTO for validation rules
 */
export interface ValidationRulesResponseDto {
  success: boolean;
  data: {
    formats: {
      [key: string]: {
        pattern: string;
        description: string;
        example: string;
      };
    };
    requirements: {
      length: { min: number; max: number };
      allowedCharacters: string;
      uniqueness: string;
      bpomBatchValidation: string;
    };
    medicineTypes: {
      [key: string]: string;
    };
  };
  message: string;
}
