import { PartialType } from '@nestjs/mapped-types';
import { IsOptional, IsEnum, IsDateString, IsString, IsInt, Min } from 'class-validator';
import { Transform } from 'class-transformer';
import { GoodsReceiptStatus } from '@prisma/client';
import { CreateGoodsReceiptDto } from './create-goods-receipt.dto';
// Note: Batch numbers are immutable after creation for audit trail purposes

export class UpdateGoodsReceiptItemDto {
  @IsOptional()
  @IsString()
  id?: string; // For updating existing items

  @IsOptional()
  @Transform(({ value }) => value ? parseInt(value) : undefined)
  @IsInt()
  @Min(0)
  quantityAccepted?: number;

  @IsOptional()
  @Transform(({ value }) => value ? parseInt(value) : undefined)
  @IsInt()
  @Min(0)
  quantityRejected?: number;



  @IsOptional()
  @IsString()
  storageLocation?: string;

  @IsOptional()
  @IsString()
  conditionOnReceipt?: string;

  @IsOptional()
  @IsString()
  damageNotes?: string;

  // Product Substitution Information
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  isSubstitution?: boolean;

  @IsOptional()
  @IsString()
  originalProductId?: string;

  @IsOptional()
  @IsString()
  substitutionReason?: string;



  @IsOptional()
  @IsString()
  substitutionNotes?: string;
}

export class UpdateGoodsReceiptDto extends PartialType(CreateGoodsReceiptDto) {
  @IsOptional()
  @IsEnum(GoodsReceiptStatus)
  status?: GoodsReceiptStatus;



  @IsOptional()
  @IsString()
  receivedBy?: string;

  @IsOptional()
  @IsString()
  deliveryCondition?: string;
}
