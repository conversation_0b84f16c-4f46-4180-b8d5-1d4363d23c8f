import { IsOptional, IsEnum } from 'class-validator';

/**
 * Query DTO for goods receipt statistics endpoint
 */
export class GoodsReceiptStatsQueryDto {
  @IsOptional()
  @IsEnum(['7d', '30d', '90d', 'quarter', 'year', 'all'])
  period?: '7d' | '30d' | '90d' | 'quarter' | 'year' | 'all';
}

/**
 * Response DTO for goods receipt statistics
 * This ensures proper validation and documentation of the API response
 */
export class GoodsReceiptStatsResponseDto {
  totalReceipts: number;
  pending: number;
  rejected: number;
  completed: number;

  totalValue: number;
  statusStats: Record<string, number>;

  recentReceipts: any[];

  processingTimeAnalytics?: any;
  volumeTrends?: any[];
  period: string;
}
