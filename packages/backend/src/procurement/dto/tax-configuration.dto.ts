import {
  IsS<PERSON>,
  <PERSON><PERSON><PERSON>ber,
  IsBoolean,
  IsOptional,
  IsDateString,
  IsEnum,
  Min,
  Max,
  IsArray,
  ArrayMinSize,
  ValidateNested
} from 'class-validator';
import { Transform, Type } from 'class-transformer';

export enum TaxCalculationMode {
  INCLUSIVE = 'inclusive',
  EXCLUSIVE = 'exclusive'
}

export enum TaxType {
  PPN = 'PPN',                    // Pajak <PERSON> (Value Added Tax)
  PPH_21 = 'PPH_21',             // PPh Pasal 21 (Employee Income Tax)
  PPH_23 = 'PPH_23',             // PPh Pasal 23 (Final Income Tax for MSMEs)
  PPH_25 = 'PPH_25',             // PPh Pasal 25 (Income Tax Installments)
  PPH_29 = 'PPH_29',             // PPh Pasal 29 (Annual Tax Due)
  PP_23 = 'PP_23',               // PP 23 (Final Income Tax for MSMEs)
  LUXURY_TAX = 'LUXURY_TAX',     // Pajak Penjualan atas Barang Mewah
  LOCAL_TAX = 'LOCAL_TAX'        // <PERSON>jak <PERSON>h (Regional/Local Taxes)
}

export enum TaxEntityType {
  INDIVIDUAL = 'INDIVIDUAL',     // Wajib Pajak Pribadi
  CORPORATE = 'CORPORATE'        // Wajib Pajak Badan (PT)
}

export enum TaxCalculationBasis {
  REVENUE = 'REVENUE',           // Based on revenue/omset (PP 23)
  NET_PROFIT = 'NET_PROFIT',     // Based on net profit (PPh 25)
  GROSS_AMOUNT = 'GROSS_AMOUNT', // Based on gross transaction amount (PPN)
  SALARY = 'SALARY'              // Based on salary (PPh 21)
}

/**
 * DTO for updating PPN tax configuration
 */
export class UpdatePPNConfigurationDto {
  @IsOptional()
  @Transform(({ value }) => typeof value === 'string' ? parseFloat(value) : value)
  @IsNumber({ maxDecimalPlaces: 2 }, { message: 'Tarif pajak harus berupa angka dengan maksimal 2 desimal' })
  @Min(0, { message: 'Tarif pajak tidak boleh negatif' })
  @Max(100, { message: 'Tarif pajak tidak boleh lebih dari 100%' })
  taxRate?: number;

  @IsOptional()
  @IsBoolean({ message: 'Status aktif harus berupa boolean (true/false)' })
  isActive?: boolean;

  @IsOptional()
  @IsDateString({}, { message: 'Tanggal berlaku harus dalam format yang valid' })
  effectiveFrom?: string;

  @IsOptional()
  @IsDateString({}, { message: 'Tanggal berakhir harus dalam format yang valid' })
  effectiveTo?: string;

  @IsOptional()
  @IsString({ message: 'Deskripsi harus berupa teks' })
  description?: string;
}

/**
 * DTO for comprehensive tax configuration
 */
export class UpdateTaxConfigurationDto {
  @IsEnum(TaxType)
  taxType: TaxType;

  @IsOptional()
  @Transform(({ value }) => typeof value === 'string' ? parseFloat(value) : value)
  @IsNumber({ maxDecimalPlaces: 4 })
  @Min(0)
  @Max(100)
  taxRate?: number;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @IsOptional()
  @IsDateString()
  effectiveFrom?: string;

  @IsOptional()
  @IsDateString()
  effectiveTo?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsEnum(TaxCalculationBasis)
  calculationBasis?: TaxCalculationBasis;

  @IsOptional()
  @IsEnum(TaxEntityType)
  applicableEntityType?: TaxEntityType;

  @IsOptional()
  @Transform(({ value }) => typeof value === 'string' ? parseFloat(value) : value)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  minimumThreshold?: number; // e.g., Rp 500 million for PP 23

  @IsOptional()
  @Transform(({ value }) => typeof value === 'string' ? parseFloat(value) : value)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  maximumThreshold?: number; // e.g., Rp 4.8 billion for PKP registration
}

/**
 * DTO for tax calculation options in purchase orders
 */
export class TaxCalculationOptionsDto {
  @IsOptional()
  @IsEnum(TaxType)
  taxType?: TaxType;

  @IsOptional()
  @IsBoolean()
  isInclusive?: boolean;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  exemptItems?: string[];

  @IsOptional()
  @IsEnum(TaxEntityType)
  entityType?: TaxEntityType;

  @IsOptional()
  @Transform(({ value }) => typeof value === 'string' ? parseFloat(value) : value)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  annualRevenue?: number; // For determining applicable tax type
}

/**
 * DTO for setting default tax calculation mode
 */
export class SetTaxCalculationModeDto {
  @IsEnum(TaxCalculationMode, { message: 'Mode kalkulasi pajak harus "inclusive" atau "exclusive"' })
  mode: TaxCalculationMode;
}

/**
 * DTO for tax calculation request
 */
export class CalculateTaxDto {
  @Transform(({ value }) => typeof value === 'string' ? parseFloat(value) : value)
  @IsNumber({ maxDecimalPlaces: 2 }, { message: 'Subtotal harus berupa angka dengan maksimal 2 desimal' })
  @Min(0, { message: 'Subtotal tidak boleh negatif' })
  subtotal: number;

  @IsOptional()
  @Transform(({ value }) => typeof value === 'string' ? parseFloat(value) : value)
  @IsNumber({ maxDecimalPlaces: 2 }, { message: 'Jumlah diskon harus berupa angka dengan maksimal 2 desimal' })
  @Min(0, { message: 'Jumlah diskon tidak boleh negatif' })
  discountAmount?: number;

  @IsOptional()
  @ValidateNested()
  @Type(() => TaxCalculationOptionsDto)
  options?: TaxCalculationOptionsDto;
}

/**
 * DTO for multiple items tax calculation
 */
export class CalculateMultipleItemsTaxDto {
  @IsArray({ message: 'Items harus berupa array' })
  @ArrayMinSize(1, { message: 'Minimal harus ada 1 item untuk kalkulasi pajak' })
  @ValidateNested({ each: true })
  @Type(() => TaxCalculationItemDto)
  items: TaxCalculationItemDto[];
}

export class TaxCalculationItemDto {
  @Transform(({ value }) => typeof value === 'string' ? parseFloat(value) : value)
  @IsNumber({ maxDecimalPlaces: 2 }, { message: 'Subtotal item harus berupa angka dengan maksimal 2 desimal' })
  @Min(0, { message: 'Subtotal item tidak boleh negatif' })
  subtotal: number;

  @IsOptional()
  @ValidateNested()
  @Type(() => TaxCalculationOptionsDto)
  options?: TaxCalculationOptionsDto;
}

/**
 * Response DTO for tax configuration
 */
export class TaxConfigurationResponseDto {
  taxType: string;
  taxRate: number;
  isActive: boolean;
  effectiveFrom: Date;
  effectiveTo?: Date;
  description?: string;
}

/**
 * Response DTO for tax calculation result
 */
export class TaxCalculationResultDto {
  subtotal: number;
  taxAmount: number;
  totalAmount: number;
  taxRate: number;
  taxType: string;
  isInclusive: boolean;
}

/**
 * Response DTO for detailed tax breakdown
 */
export class TaxBreakdownResponseDto {
  subtotal: number;
  taxRate: number;
  taxAmount: number;
  totalAmount: number;
  taxDescription: string;
  isInclusive: boolean;
}

/**
 * Response DTO for all tax settings
 */
export class AllTaxSettingsResponseDto {
  ppnConfiguration: TaxConfigurationResponseDto;
  defaultCalculationMode: TaxCalculationMode;
  allSettings: Record<string, string | null>;
}
