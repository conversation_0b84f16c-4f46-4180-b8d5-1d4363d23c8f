import { IsOptional, IsEnum } from 'class-validator';

/**
 * Query DTO for purchase order statistics endpoint
 */
export class PurchaseOrderStatsQueryDto {
  @IsOptional()
  @IsEnum(['7d', '30d', '90d', 'quarter', 'year', 'all'])
  period?: '7d' | '30d' | '90d' | 'quarter' | 'year' | 'all';
}

/**
 * Response DTO for purchase order statistics
 * This ensures proper validation and documentation of the API response
 */
export class PurchaseOrderStatsResponseDto {
  totalOrders: number;
  statusStats: Record<string, number>;
  totalValue: number;
  pendingValue: number;
  activeValue: number;
  completedValue: number;
  valueStats: Record<string, number>;
  recentOrders: any[];
  supplierStats: any[];
  processingTimeAnalytics: any;
  volumeTrends: any[];
  period: string;
}
