import { PartialType } from '@nestjs/mapped-types';
import { IsOptional, IsEnum, IsDateString, IsString } from 'class-validator';
import { PurchaseOrderStatus } from '@prisma/client';
import { CreatePurchaseOrderDto } from './create-purchase-order.dto';

export class UpdatePurchaseOrderDto extends PartialType(CreatePurchaseOrderDto) {
  @IsOptional()
  @IsEnum(PurchaseOrderStatus)
  status?: PurchaseOrderStatus;

  @IsOptional()
  @IsDateString()
  actualDelivery?: string;

  @IsOptional()
  @IsString()
  paymentStatus?: string; // PENDING, PARTIAL, PAID, OVERDUE


}
