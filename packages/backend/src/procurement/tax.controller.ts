import {
  Controller,
  Get,
  Put,
  Post,
  Body,
  UseGuards,
  HttpCode,
  HttpStatus,
  BadRequestException,
  Query,
  ParseFloatPipe,
} from '@nestjs/common';
import { TaxConfigurationService } from './services/tax-configuration.service';
import { TaxCalculationService } from './services/tax-calculation.service';
import { IndonesianTaxService } from './services/indonesian-tax.service';
import {
  UpdatePPNConfigurationDto,
  SetTaxCalculationModeDto,
  CalculateTaxDto,
  CalculateMultipleItemsTaxDto,
  TaxConfigurationResponseDto,
  TaxCalculationResultDto,
  TaxBreakdownResponseDto,
  AllTaxSettingsResponseDto,
  TaxCalculationMode,
  TaxType,
  TaxEntityType,

} from './dto/tax-configuration.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AdminGuard } from '../settings/guards/admin.guard';
import { Prisma } from '@prisma/client';
import { parseISO, isValid } from 'date-fns';

/**
 * Controller for managing tax configurations and calculations
 * Handles PPN (Pajak Pertam<PERSON>lai) and other Indonesian tax types
 */
@Controller('tax')
@UseGuards(JwtAuthGuard)
export class TaxController {
  constructor(
    private readonly taxConfigService: TaxConfigurationService,
    private readonly taxCalculationService: TaxCalculationService,
    private readonly indonesianTaxService: IndonesianTaxService,
  ) { }

  /**
   * Get current PPN configuration
   */
  @Get('ppn/configuration')
  async getPPNConfiguration(): Promise<TaxConfigurationResponseDto> {
    const config = await this.taxConfigService.getCurrentPPNConfiguration();
    return {
      taxType: config.taxType,
      taxRate: config.taxRate,
      isActive: config.isActive,
      effectiveFrom: config.effectiveFrom,
      effectiveTo: config.effectiveTo,
      description: config.description,
    };
  }

  /**
   * Update PPN configuration (Admin only)
   */
  @Put('ppn/configuration')
  @UseGuards(AdminGuard)
  @HttpCode(HttpStatus.OK)
  async updatePPNConfiguration(
    @Body() updateDto: UpdatePPNConfigurationDto
  ): Promise<TaxConfigurationResponseDto> {
    try {
      // Parse dates safely using parseISO
      let effectiveFrom: Date | undefined;
      let effectiveTo: Date | undefined;

      if (updateDto.effectiveFrom) {
        effectiveFrom = parseISO(updateDto.effectiveFrom);
        if (!isValid(effectiveFrom)) {
          throw new BadRequestException('Format tanggal effectiveFrom tidak valid. Gunakan format ISO 8601.');
        }
      }

      if (updateDto.effectiveTo) {
        effectiveTo = parseISO(updateDto.effectiveTo);
        if (!isValid(effectiveTo)) {
          throw new BadRequestException('Format tanggal effectiveTo tidak valid. Gunakan format ISO 8601.');
        }
      }

      const config = await this.taxConfigService.updatePPNConfiguration({
        taxRate: updateDto.taxRate,
        isActive: updateDto.isActive,
        effectiveFrom,
        effectiveTo,
        description: updateDto.description,
      });

      return {
        taxType: config.taxType,
        taxRate: config.taxRate,
        isActive: config.isActive,
        effectiveFrom: config.effectiveFrom,
        effectiveTo: config.effectiveTo,
        description: config.description,
      };
    } catch (error) {
      throw new BadRequestException('Gagal memperbarui konfigurasi PPN. ' + error.message);
    }
  }

  /**
   * Get default tax calculation mode
   */
  @Get('calculation-mode')
  async getTaxCalculationMode(): Promise<{ mode: TaxCalculationMode }> {
    const mode = await this.taxConfigService.getDefaultTaxCalculationMode();
    return { mode: mode as TaxCalculationMode };
  }

  /**
   * Set default tax calculation mode (Admin only)
   */
  @Put('calculation-mode')
  @UseGuards(AdminGuard)
  @HttpCode(HttpStatus.OK)
  async setTaxCalculationMode(
    @Body() setModeDto: SetTaxCalculationModeDto
  ): Promise<{ mode: TaxCalculationMode }> {
    try {
      await this.taxConfigService.setDefaultTaxCalculationMode(setModeDto.mode);
      return { mode: setModeDto.mode };
    } catch (error) {
      throw new BadRequestException('Gagal mengatur mode kalkulasi pajak. ' + error.message);
    }
  }

  /**
   * Calculate tax for a given amount
   */
  @Post('calculate')
  @HttpCode(HttpStatus.OK)
  async calculateTax(@Body() calculateDto: CalculateTaxDto): Promise<TaxCalculationResultDto> {
    try {
      const subtotal = new Prisma.Decimal(calculateDto.subtotal.toString());

      let result: TaxCalculationResultDto;
      if (calculateDto.discountAmount && calculateDto.discountAmount > 0) {
        const discountAmount = new Prisma.Decimal(calculateDto.discountAmount.toString());
        result = await this.taxCalculationService.calculateWithDiscount(
          subtotal,
          discountAmount,
          calculateDto.options
        );
      } else {
        result = await this.taxCalculationService.calculatePPN(
          subtotal,
          calculateDto.options
        );
      }

      return {
        subtotal: result.subtotal,
        taxAmount: result.taxAmount,
        totalAmount: result.totalAmount,
        taxRate: result.taxRate,
        taxType: result.taxType,
        isInclusive: result.isInclusive,
      };
    } catch (error) {
      throw new BadRequestException('Gagal menghitung pajak. Periksa kembali data yang dimasukkan. ' + error.message);
    }
  }

  /**
   * Calculate tax for multiple items
   */
  @Post('calculate-multiple')
  @HttpCode(HttpStatus.OK)
  async calculateMultipleItemsTax(
    @Body() calculateDto: CalculateMultipleItemsTaxDto
  ): Promise<TaxCalculationResultDto> {
    try {
      const items = calculateDto.items.map(item => ({
        subtotal: new Prisma.Decimal(item.subtotal.toString()),
        options: item.options,
      }));

      const result = await this.taxCalculationService.calculateMultipleItems(items);

      return {
        subtotal: result.subtotal,
        taxAmount: result.taxAmount,
        totalAmount: result.totalAmount,
        taxRate: result.taxRate,
        taxType: result.taxType,
        isInclusive: result.isInclusive,
      };
    } catch (error) {
      throw new BadRequestException('Gagal menghitung pajak untuk beberapa item. Periksa kembali data yang dimasukkan. ' + error.message);
    }
  }

  /**
   * Get detailed tax breakdown for display
   */
  @Post('breakdown')
  @HttpCode(HttpStatus.OK)
  async getTaxBreakdown(@Body() calculateDto: CalculateTaxDto): Promise<TaxBreakdownResponseDto> {
    try {
      const subtotal = new Prisma.Decimal(calculateDto.subtotal.toString());
      const breakdown = await this.taxCalculationService.getTaxBreakdown(
        subtotal,
        calculateDto.options
      );

      return breakdown;
    } catch (error) {
      throw new BadRequestException('Gagal mendapatkan rincian pajak. ' + error.message);
    }
  }

  /**
   * Get all tax settings (Admin only)
   */
  @Get('settings')
  @UseGuards(AdminGuard)
  async getAllTaxSettings(): Promise<AllTaxSettingsResponseDto> {
    try {
      const [ppnConfig, calculationMode, allSettings] = await Promise.all([
        this.taxConfigService.getCurrentPPNConfiguration(),
        this.taxConfigService.getDefaultTaxCalculationMode(),
        this.taxConfigService.getAllTaxSettings(),
      ]);

      return {
        ppnConfiguration: {
          taxType: ppnConfig.taxType,
          taxRate: ppnConfig.taxRate,
          isActive: ppnConfig.isActive,
          effectiveFrom: ppnConfig.effectiveFrom,
          effectiveTo: ppnConfig.effectiveTo,
          description: ppnConfig.description,
        },
        defaultCalculationMode: calculationMode as TaxCalculationMode,
        allSettings,
      };
    } catch (error) {
      throw new BadRequestException('Gagal mendapatkan pengaturan pajak. ' + error.message);
    }
  }

  /**
   * Check if PPN is currently active
   */
  @Get('ppn/status')
  async getPPNStatus(): Promise<{ isActive: boolean; currentRate: number }> {
    try {
      const [isActive, currentRate] = await Promise.all([
        this.taxConfigService.isPPNActive(),
        this.taxConfigService.getEffectivePPNRate(),
      ]);

      return { isActive, currentRate };
    } catch (error) {
      throw new BadRequestException('Gagal mendapatkan status PPN. ' + error.message);
    }
  }

  /**
   * Get applicable tax types for a pharmacy based on business characteristics
   */
  @Get('applicable-taxes')
  async getApplicableTaxTypes(
    @Query('entityType') entityType: TaxEntityType,
    @Query('annualRevenue', new ParseFloatPipe({ errorHttpStatusCode: HttpStatus.BAD_REQUEST })) annualRevenue: number,
    @Query('yearsInOperation', new ParseFloatPipe({ errorHttpStatusCode: HttpStatus.BAD_REQUEST, optional: true })) yearsInOperation: number = 1
  ): Promise<{ applicableTaxes: TaxType[] }> {
    try {
      const applicableTaxes = await this.indonesianTaxService.getApplicableTaxTypes(
        entityType,
        annualRevenue,
        yearsInOperation
      );

      return { applicableTaxes };
    } catch (error) {
      throw new BadRequestException('Gagal menentukan jenis pajak yang berlaku. Periksa parameter yang dimasukkan. ' + error.message);
    }
  }

  /**
   * Calculate PP 23 tax (Final Income Tax for MSMEs)
   */
  @Post('calculate/pp23')
  @HttpCode(HttpStatus.OK)
  async calculatePP23Tax(
    @Body() body: {
      monthlyRevenue: number;
      entityType: TaxEntityType;
      annualRevenue: number;
    }
  ) {
    try {
      const result = await this.indonesianTaxService.calculatePP23Tax(
        body.monthlyRevenue,
        body.entityType,
        body.annualRevenue
      );

      return result;
    } catch (error) {
      throw new BadRequestException('Gagal menghitung pajak PP 23. Periksa data omzet dan jenis wajib pajak. ' + error.message);
    }
  }

  /**
   * Calculate PPh 25 tax (Income Tax Installments)
   */
  @Post('calculate/pph25')
  @HttpCode(HttpStatus.OK)
  async calculatePPh25Tax(
    @Body() body: {
      monthlyRevenue: number;
      entityType: TaxEntityType;
      previousYearTaxDue?: number;
    }
  ) {
    try {
      const result = await this.indonesianTaxService.calculatePPh25Tax(
        body.monthlyRevenue,
        body.entityType,
        body.previousYearTaxDue
      );

      return result;
    } catch (error) {
      throw new BadRequestException('Gagal menghitung pajak PPh 25. Periksa data omzet dan jenis wajib pajak. ' + error.message);
    }
  }

  /**
   * Calculate Indonesian PPN with specific rules
   */
  @Post('calculate/indonesian-ppn')
  @HttpCode(HttpStatus.OK)
  async calculateIndonesianPPN(
    @Body() body: {
      subtotal: number;
      isRetailTransaction?: boolean;
    }
  ) {
    try {
      const result = await this.indonesianTaxService.calculateIndonesianPPN(
        body.subtotal,
        body.isRetailTransaction
      );

      return result;
    } catch (error) {
      throw new BadRequestException('Gagal menghitung PPN Indonesia. Periksa jumlah subtotal. ' + error.message);
    }
  }

  /**
   * Get tax compliance status for a pharmacy
   */
  @Get('compliance-status')
  async getTaxComplianceStatus(
    @Query('entityType') entityType: TaxEntityType,
    @Query('annualRevenue', new ParseFloatPipe({ errorHttpStatusCode: HttpStatus.BAD_REQUEST })) annualRevenue: number,
    @Query('yearsInOperation', new ParseFloatPipe({ errorHttpStatusCode: HttpStatus.BAD_REQUEST })) yearsInOperation: number,
    @Query('hasEmployees') hasEmployees: boolean
  ) {
    try {
      const result = await this.indonesianTaxService.getTaxComplianceStatus(
        entityType,
        annualRevenue,
        yearsInOperation,
        String(hasEmployees) === 'true'
      );

      return result;
    } catch (error) {
      throw new BadRequestException('Gagal mendapatkan status kepatuhan pajak. Periksa parameter usaha yang dimasukkan. ' + error.message);
    }
  }

  /**
   * Get tax calendar for pharmacy compliance
   */
  @Get('calendar')
  async getTaxCalendar() {
    try {
      const calendar = this.indonesianTaxService.getTaxCalendar();
      return { calendar };
    } catch (error) {
      throw new BadRequestException('Gagal mendapatkan kalender pajak. ' + error.message);
    }
  }

  /**
   * Validate tax configuration against Indonesian regulations
   */
  @Post('validate-configuration')
  @HttpCode(HttpStatus.OK)
  async validateTaxConfiguration(
    @Body() body: {
      taxType: TaxType;
      taxRate: number;
      entityType?: TaxEntityType;
    }
  ) {
    try {
      const result = await this.indonesianTaxService.validateTaxConfiguration(
        body.taxType,
        body.taxRate,
        body.entityType
      );

      return result;
    } catch (error) {
      throw new BadRequestException('Gagal memvalidasi konfigurasi pajak. Periksa jenis dan tarif pajak yang dimasukkan. ' + error.message);
    }
  }
}
