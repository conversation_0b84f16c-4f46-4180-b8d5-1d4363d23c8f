/**
 * Shared TypeScript interfaces for Goods Receipt Statistics
 * These interfaces ensure type consistency between backend and frontend
 */

export interface GoodsReceiptStatsResponse {
  // Basic statistics
  totalReceipts: number;
  pending: number;
  completed: number;
  rejected: number;

  totalValue: number;

  // Grouped statistics
  statusStats: Record<string, number>;

  // Recent data
  recentReceipts: GoodsReceiptSummary[];

  // Enhanced analytics (Phase 2)
  processingTimeAnalytics: ProcessingTimeAnalytics;
  volumeTrends: VolumeTrend[];

  // Metadata
  period: string;
}

export interface GoodsReceiptSummary {
  id: string;
  receiptNumber: string;
  status: string;

  totalAmount: number;
  createdAt: Date;
  supplier?: {
    name: string;
  };
  purchaseOrder?: {
    orderNumber: string;
  };
}



export interface ProcessingTimeAnalytics {
  averageProcessingTime: number;
  totalReceipts: number;
  efficiencyMetrics: {
    fastProcessing: number;
    slowProcessing: number;
    averageProcessing: number;
    fastPercentage: number;
    slowPercentage: number;
  };
}

export interface VolumeTrend {
  date: string;
  count: number;
  value: number;
}

/**
 * Query parameters for statistics endpoint
 */
export interface GoodsReceiptStatsQuery {
  period?: '7d' | '30d' | '90d' | 'quarter' | 'year' | 'all';
}

/**
 * Error response interface
 */
export interface GoodsReceiptStatsError {
  message: string;
  code: string;
  details?: any;
}
