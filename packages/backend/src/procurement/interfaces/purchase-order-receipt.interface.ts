/**
 * Purchase Order Receipt Data Interface
 * Used for generating PDF receipts for purchase orders
 */

export interface PurchaseOrderReceiptData {
  // Pharmacy/Company Information
  pharmacy: {
    name: string;
    address: string;
    phone: string;
    email?: string;
    license?: string; // SIPA number
  };

  // Purchase Order Information
  purchaseOrder: {
    orderNumber: string;
    orderDate: string;
    expectedDelivery?: string;
    status: string;
    notes?: string;
    internalNotes?: string;
  };

  // Supplier Information
  supplier: {
    name: string;
    code: string;
    address?: string;
    phone?: string;
    email?: string;
    contactPerson?: string;
  };

  // Items in the purchase order
  items: PurchaseOrderReceiptItem[];

  // Financial totals
  totals: {
    subtotal: number;
    discountAmount: number;
    taxAmount: number;
    totalAmount: number;
  };

  // Payment and delivery information
  payment: {
    method?: string;
    terms?: number; // Payment terms in days
  };

  delivery: {
    address?: string;
    contact?: string;
    phone?: string;
    notes?: string;
  };

  // Created by information
  createdBy: {
    name: string;
    email: string;
  };

  // Footer information
  footer: {
    notes?: string;
    terms?: string;
    signature?: {
      required: boolean;
      title: string;
      name?: string;
    };
  };
}

export interface PurchaseOrderReceiptItem {
  productCode: string;
  productName: string;
  manufacturer?: string;
  category?: string;
  quantityOrdered: number;
  unit: string;
  unitPrice: number;
  discountAmount: number;
  totalPrice: number;
  notes?: string;
  expectedDelivery?: string;
}

export interface PurchaseOrderPdfOptions {
  format?: 'A4' | 'Letter';
  orientation?: 'portrait' | 'landscape';
  includeSignature?: boolean;
  includeTerms?: boolean;
}
