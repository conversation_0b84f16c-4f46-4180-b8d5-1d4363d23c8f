import {
  Controller,
  Get,
  Post,
  Body,
  Query,
  Param,
  HttpCode,
  HttpStatus,
  BadRequestException,
  UseGuards,
  ValidationPipe,
} from '@nestjs/common';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ManagerGuard } from '../suppliers/guards/manager.guard';
import { BatchNumberValidationService, BatchValidationContext } from './services/batch-number-validation.service';
import { BatchAuditService, BatchAuditQuery } from './services/batch-audit.service';
import { PrismaService } from '../prisma/prisma.service';
import {
  ValidateBatchNumberDto,
  RealTimeValidationDto,
  BatchUniquenessDto,
  BatchValidationResponseDto,
  RealTimeValidationResponseDto,
  UniquenessCheckResponseDto,
  BatchHistoryResponseDto,
  ValidationRulesResponseDto,
} from './dto/batch-validation.dto';

/**
 * Controller for batch number validation endpoints
 * Provides real-time validation, uniqueness checking, and batch history
 */
@Controller('procurement/batch-validation')
@UseGuards(JwtAuthGuard)
export class BatchValidationController {
  constructor(
    private readonly batchValidationService: BatchNumberValidationService,
    private readonly batchAuditService: BatchAuditService,
    private readonly prisma: PrismaService
  ) { }

  /**
   * Comprehensive batch number validation
   * POST /api/procurement/batch-validation/validate
   */
  @Post('validate')
  @UseGuards(ManagerGuard) // Admin or Pharmacist can perform validation
  @HttpCode(HttpStatus.OK)
  async validateBatchNumber(
    @Body(ValidationPipe) dto: ValidateBatchNumberDto
  ): Promise<BatchValidationResponseDto> {
    try {
      const context: BatchValidationContext = {
        batchNumber: dto.batchNumber,
        productId: dto.productId,
        supplierId: dto.supplierId,
        expiryDate: dto.expiryDate ? new Date(dto.expiryDate) : undefined,
        manufacturingDate: dto.manufacturingDate ? new Date(dto.manufacturingDate) : undefined,
        isSubstitution: dto.isSubstitution,
        originalBatchNumber: dto.originalBatchNumber,
      };

      const result = await this.batchValidationService.validateBatchNumber(context);

      return {
        success: true,
        data: result,
        message: result.isValid
          ? 'Validasi batch berhasil'
          : 'Validasi batch gagal, periksa error dan warning'
      };
    } catch (error) {
      throw new BadRequestException({
        success: false,
        message: 'Gagal melakukan validasi batch number',
        error: error.message
      });
    }
  }

  /**
   * Real-time batch number validation for frontend
   * POST /api/procurement/batch-validation/real-time
   */
  @Post('real-time')
  @HttpCode(HttpStatus.OK)
  @UseGuards(ManagerGuard) // Admin or Pharmacist can perform validation
  async validateRealTime(
    @Body(ValidationPipe) dto: RealTimeValidationDto
  ): Promise<RealTimeValidationResponseDto> {
    try {
      // Let the service handle empty validation for better UX
      const result = await this.batchValidationService.validateRealTime(
        dto.batchNumber || '',
        dto.productId || ''
      );

      return {
        success: true,
        data: result,
        message: result.isValid ? 'Validasi berhasil' : (result.message || 'Validasi gagal')
      };
    } catch (error) {
      throw new BadRequestException({
        success: false,
        message: 'Gagal melakukan validasi real-time',
        error: error.message
      });
    }
  }

  /**
   * Check batch number uniqueness
   * POST /api/procurement/batch-validation/check-uniqueness
   */
  @Post('check-uniqueness')
  @HttpCode(HttpStatus.OK)
  @UseGuards(ManagerGuard) // Admin or Pharmacist can check uniqueness
  async checkUniqueness(
    @Body(ValidationPipe) dto: BatchUniquenessDto
  ): Promise<UniquenessCheckResponseDto> {
    try {
      if (!dto.batchNumber || !dto.productId) {
        throw new BadRequestException('Batch number dan product ID wajib diisi');
      }

      const result = await this.batchValidationService.checkUniqueness(
        dto.batchNumber,
        dto.productId,
        dto.supplierId
      );

      return {
        success: true,
        data: result,
        message: result.isUnique
          ? 'Nomor batch tersedia'
          : `Nomor batch sudah digunakan (${result.conflicts.length} konflik ditemukan)`
      };
    } catch (error) {
      throw new BadRequestException({
        success: false,
        message: 'Gagal memeriksa keunikan batch number',
        error: error.message
      });
    }
  }

  /**
   * Get batch history list with pagination and filters
   * GET /api/procurement/batch-validation/history
   */
  @Get('history')
  async getBatchHistoryList(
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('search') search?: string,
    @Query('status') status?: string,
    @Query('complianceStatus') complianceStatus?: string,
    @Query('productId') productId?: string,
    @Query('supplierId') supplierId?: string,
    @Query('batchNumber') batchNumber?: string,
    @Query('expiryDateFrom') expiryDateFrom?: string,
    @Query('expiryDateTo') expiryDateTo?: string,
    @Query('sortBy') sortBy?: string,
    @Query('sortOrder') sortOrder?: 'asc' | 'desc'
  ) {
    try {
      const pageNum = page ? parseInt(page) : 1;
      const limitNum = limit ? parseInt(limit) : 10;
      const offset = (pageNum - 1) * limitNum;

      // Build where clause
      const where: any = {
        isActive: true,
        batchNumber: { not: null }
      };

      if (search) {
        where.OR = [
          { batchNumber: { contains: search, mode: 'insensitive' } },
          { product: { name: { contains: search, mode: 'insensitive' } } }
        ];
      }

      if (batchNumber) {
        where.batchNumber = { contains: batchNumber, mode: 'insensitive' };
      }

      if (productId) {
        where.productId = productId;
      }

      if (supplierId) {
        where.supplierId = supplierId;
      }

      if (expiryDateFrom || expiryDateTo) {
        where.expiryDate = {};
        if (expiryDateFrom) where.expiryDate.gte = new Date(expiryDateFrom);
        if (expiryDateTo) where.expiryDate.lte = new Date(expiryDateTo);
      }

      // Add status filter
      if (status) {
        const now = new Date();
        switch (status) {
          case 'ACTIVE':
            where.expiryDate = { gt: now };
            break;
          case 'EXPIRED':
            where.expiryDate = { lte: now };
            break;
          case 'EXPIRING_SOON':
            const thirtyDaysFromNow = new Date();
            thirtyDaysFromNow.setDate(now.getDate() + 30);
            where.expiryDate = { gt: now, lte: thirtyDaysFromNow };
            break;
        }
      }

      // Add compliance status filter
      if (complianceStatus) {
        switch (complianceStatus) {
          case 'CONTROLLED':
            where.product = {
              ...where.product,
              medicineClassification: { in: ['NARKOTIKA', 'PSIKOTROPIKA'] }
            };
            break;
          case 'COMPLIANT':
            where.product = {
              ...where.product,
              medicineClassification: { not: null, notIn: ['NARKOTIKA', 'PSIKOTROPIKA'] }
            };
            break;
          case 'PENDING_REVIEW':
            where.product = {
              ...where.product,
              medicineClassification: null
            };
            break;
        }
      }

      // Build order by
      const orderBy: any = {};
      if (sortBy) {
        orderBy[sortBy] = sortOrder || 'desc';
      } else {
        orderBy.createdAt = 'desc';
      }

      const [items, total] = await Promise.all([
        this.prisma.inventoryItem.findMany({
          where,
          include: {
            product: {
              select: {
                name: true,
                type: true,
                medicineClassification: true,
              }
            },
            supplier: {
              select: {
                name: true,
              }
            }
          },
          orderBy,
          skip: offset,
          take: limitNum,
        }),
        this.prisma.inventoryItem.count({ where })
      ]);

      // Transform data to match frontend expectations
      const data = items.map(item => {
        const now = new Date();
        const expiryDate = item.expiryDate ? new Date(item.expiryDate) : null;
        let itemStatus = 'ACTIVE';
        let complianceStatus = 'COMPLIANT';

        if (expiryDate) {
          if (expiryDate <= now) {
            itemStatus = 'EXPIRED';
          } else {
            const daysUntilExpiry = Math.ceil((expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
            if (daysUntilExpiry <= 30) {
              itemStatus = 'EXPIRING_SOON';
            }
          }
        }

        // Determine compliance status based on product classification
        if (item.product.medicineClassification === 'NARKOTIKA' || item.product.medicineClassification === 'PSIKOTROPIKA') {
          complianceStatus = 'CONTROLLED';
        } else if (!item.product.medicineClassification) {
          complianceStatus = 'PENDING_REVIEW';
        }

        return {
          id: item.id,
          batchNumber: item.batchNumber,
          productId: item.productId,
          productName: item.product.name,
          supplierId: item.supplierId,
          supplierName: item.supplier?.name || null,
          status: itemStatus,
          complianceStatus,
          quantityOnHand: item.quantityOnHand,
          expiryDate: item.expiryDate,
          receivedDate: item.createdAt,
          lastUpdated: item.updatedAt,
        };
      });

      return {
        success: true,
        data: {
          data,
          pagination: {
            page: pageNum,
            limit: limitNum,
            total,
            totalPages: Math.ceil(total / limitNum),
          }
        },
        message: 'Riwayat batch berhasil diambil'
      };
    } catch (error) {
      throw new BadRequestException({
        success: false,
        message: 'Gagal mengambil riwayat batch',
        error: error.message
      });
    }
  }

  /**
   * Get batch number history and audit trail
   * GET /api/procurement/batch-validation/history/:batchNumber
   */
  @Get('history/:batchNumber')
  async getBatchHistory(
    @Param('batchNumber') batchNumber: string
  ): Promise<BatchHistoryResponseDto> {
    try {
      if (!batchNumber) {
        throw new BadRequestException('Batch number wajib diisi');
      }

      // Get batch usage history from inventory and goods receipts
      const history = await this.getBatchUsageHistory(batchNumber);

      return {
        success: true,
        data: history,
        message: `Riwayat batch ${batchNumber} berhasil diambil`
      };
    } catch (error) {
      throw new BadRequestException({
        success: false,
        message: 'Gagal mengambil riwayat batch number',
        error: error.message
      });
    }
  }

  /**
   * Get batch validation statistics
   * GET /api/procurement/batch-validation/statistics
   */
  @Get('statistics')
  async getBatchValidationStatistics(
    @Query('period') period?: string // '7d', '30d', '90d', '1y'
  ) {
    try {
      const periodDays = this.getPeriodDays(period || '30d');
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - periodDays);

      // Get all inventory items with batch numbers in the period
      const inventoryItems = await this.prisma.inventoryItem.findMany({
        where: {
          createdAt: { gte: startDate },
          isActive: true,
          batchNumber: { not: null }
        },
        include: {
          product: {
            select: {
              name: true,
              type: true,
              medicineClassification: true,
            }
          }
        }
      });

      // Get validation audit logs for the period
      const validationLogs = await this.prisma.batchAuditLog.findMany({
        where: {
          createdAt: { gte: startDate },
          action: { in: ['VALIDATED', 'FORMAT_VALIDATION', 'UNIQUENESS_CHECK'] }
        }
      });

      const totalBatches = inventoryItems.length;
      const activeBatches = inventoryItems.filter(item => {
        if (!item.expiryDate) return false;
        const expiryDate = new Date(item.expiryDate);
        return expiryDate > new Date();
      }).length;

      const expiredBatches = inventoryItems.filter(item => {
        if (!item.expiryDate) return false;
        const expiryDate = new Date(item.expiryDate);
        return expiryDate <= new Date();
      }).length;

      const expiringSoon = inventoryItems.filter(item => {
        if (!item.expiryDate) return false;
        const expiryDate = new Date(item.expiryDate);
        const now = new Date();
        const daysUntilExpiry = Math.ceil((expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
        return daysUntilExpiry > 0 && daysUntilExpiry <= 30;
      }).length;

      const validationErrors = validationLogs.filter(log => log.status === 'FAILED').length;
      const validationSuccessRate = validationLogs.length > 0
        ? ((validationLogs.filter(log => log.status === 'SUCCESS').length / validationLogs.length) * 100)
        : 100;

      const complianceRate = inventoryItems.length > 0
        ? ((inventoryItems.filter(item => item.product.medicineClassification !== null).length / inventoryItems.length) * 100)
        : 100;

      return {
        success: true,
        data: {
          totalBatches,
          activeBatches,
          expiredBatches,
          expiringSoon,
          validationErrors,
          validationSuccessRate: Math.round(validationSuccessRate * 100) / 100,
          complianceRate: Math.round(complianceRate * 100) / 100,
          period: {
            days: periodDays,
            startDate: startDate.toISOString(),
            endDate: new Date().toISOString()
          }
        },
        message: 'Statistik validasi batch berhasil diambil'
      };
    } catch (error) {
      throw new BadRequestException({
        success: false,
        message: 'Gagal mengambil statistik validasi batch',
        error: error.message
      });
    }
  }

  /**
   * Get expiry tracking data
   * GET /api/procurement/expiry-tracking
   */
  @Get('expiry-tracking')
  async getExpiryTracking(
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('expiryDateFrom') expiryDateFrom?: string,
    @Query('expiryDateTo') expiryDateTo?: string,
    @Query('status') status?: string,
    @Query('sortBy') sortBy?: string,
    @Query('sortOrder') sortOrder?: 'asc' | 'desc'
  ) {
    try {
      const pageNum = page ? parseInt(page) : 1;
      const limitNum = limit ? parseInt(limit) : 10;
      const offset = (pageNum - 1) * limitNum;

      const where: any = {
        isActive: true,
        batchNumber: { not: null },
        expiryDate: { not: null }
      };

      if (expiryDateFrom || expiryDateTo) {
        where.expiryDate = {};
        if (expiryDateFrom) where.expiryDate.gte = new Date(expiryDateFrom);
        if (expiryDateTo) where.expiryDate.lte = new Date(expiryDateTo);
      }

      // Filter by expiry status
      if (status) {
        const now = new Date();
        switch (status) {
          case 'ACTIVE':
            const futureDate = new Date();
            futureDate.setDate(now.getDate() + 30);
            where.expiryDate = { gt: futureDate };
            break;
          case 'EXPIRING_SOON':
            const thirtyDaysFromNow = new Date();
            thirtyDaysFromNow.setDate(now.getDate() + 30);
            where.expiryDate = { gt: now, lte: thirtyDaysFromNow };
            break;
          case 'EXPIRED':
            where.expiryDate = { lte: now };
            break;
        }
      }

      const orderBy: any = {};
      if (sortBy) {
        orderBy[sortBy] = sortOrder || 'asc';
      } else {
        orderBy.expiryDate = 'asc'; // Default to earliest expiry first
      }

      const [items, total] = await Promise.all([
        this.prisma.inventoryItem.findMany({
          where,
          include: {
            product: {
              select: {
                name: true,
                type: true,
              }
            },
            supplier: {
              select: {
                name: true,
              }
            }
          },
          orderBy,
          skip: offset,
          take: limitNum,
        }),
        this.prisma.inventoryItem.count({ where })
      ]);

      const data = items.map(item => {
        const now = new Date();
        const expiryDate = new Date(item.expiryDate!);
        const daysUntilExpiry = Math.ceil((expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

        let itemStatus = 'ACTIVE';
        let priority = 'LOW';

        if (daysUntilExpiry <= 0) {
          itemStatus = 'EXPIRED';
          priority = 'HIGH';
        } else if (daysUntilExpiry <= 7) {
          itemStatus = 'EXPIRING_SOON';
          priority = 'HIGH';
        } else if (daysUntilExpiry <= 30) {
          itemStatus = 'EXPIRING_SOON';
          priority = 'MEDIUM';
        }

        return {
          id: item.id,
          batchNumber: item.batchNumber,
          productId: item.productId,
          productName: item.product.name,
          supplierId: item.supplierId,
          supplierName: item.supplier?.name || null,
          expiryDate: item.expiryDate,
          daysUntilExpiry,
          quantityOnHand: item.quantityOnHand,
          location: item.location || 'Gudang Utama',
          status: itemStatus,
          priority,
          lastUpdated: item.updatedAt,
        };
      });

      return {
        success: true,
        data: {
          data,
          pagination: {
            page: pageNum,
            limit: limitNum,
            total,
            totalPages: Math.ceil(total / limitNum),
          }
        },
        message: 'Data tracking kedaluwarsa berhasil diambil'
      };
    } catch (error) {
      throw new BadRequestException({
        success: false,
        message: 'Gagal mengambil data tracking kedaluwarsa',
        error: error.message
      });
    }
  }

  /**
   * Export batch data
   * GET /api/procurement/batch-validation/export
   */
  @Get('export')
  async exportBatchData(
    @Query('format') format?: string,
    @Query('includeAuditTrail') includeAuditTrail?: string,
    @Query('includeComplianceData') includeComplianceData?: string,
    @Query('search') search?: string,
    @Query('status') status?: string,
    @Query('complianceStatus') complianceStatus?: string,
    @Query('productId') productId?: string,
    @Query('supplierId') supplierId?: string,
    @Query('batchNumber') batchNumber?: string,
    @Query('expiryDateFrom') expiryDateFrom?: string,
    @Query('expiryDateTo') expiryDateTo?: string
  ) {
    try {
      // Build where clause (reuse logic from history list)
      const where: any = {
        isActive: true,
        batchNumber: { not: null }
      };

      if (search) {
        where.OR = [
          { batchNumber: { contains: search, mode: 'insensitive' } },
          { product: { name: { contains: search, mode: 'insensitive' } } }
        ];
      }

      if (batchNumber) {
        where.batchNumber = { contains: batchNumber, mode: 'insensitive' };
      }

      if (productId) {
        where.productId = productId;
      }

      if (supplierId) {
        where.supplierId = supplierId;
      }

      if (expiryDateFrom || expiryDateTo) {
        where.expiryDate = {};
        if (expiryDateFrom) where.expiryDate.gte = new Date(expiryDateFrom);
        if (expiryDateTo) where.expiryDate.lte = new Date(expiryDateTo);
      }

      // Add status filter
      if (status) {
        const now = new Date();
        switch (status) {
          case 'ACTIVE':
            where.expiryDate = { gt: now };
            break;
          case 'EXPIRED':
            where.expiryDate = { lte: now };
            break;
          case 'EXPIRING_SOON':
            const thirtyDaysFromNow = new Date();
            thirtyDaysFromNow.setDate(now.getDate() + 30);
            where.expiryDate = { gt: now, lte: thirtyDaysFromNow };
            break;
        }
      }

      // Add compliance status filter
      if (complianceStatus) {
        switch (complianceStatus) {
          case 'CONTROLLED':
            where.product = {
              ...where.product,
              medicineClassification: { in: ['NARKOTIKA', 'PSIKOTROPIKA'] }
            };
            break;
          case 'COMPLIANT':
            where.product = {
              ...where.product,
              medicineClassification: { not: null, notIn: ['NARKOTIKA', 'PSIKOTROPIKA'] }
            };
            break;
          case 'PENDING_REVIEW':
            where.product = {
              ...where.product,
              medicineClassification: null
            };
            break;
        }
      }

      const items = await this.prisma.inventoryItem.findMany({
        where,
        include: {
          product: {
            select: {
              name: true,
              type: true,
              medicineClassification: true,
              bpomNumber: true,
            }
          },
          supplier: {
            select: {
              name: true,
            }
          }
        },
        orderBy: { createdAt: 'desc' },
      });

      // Transform data for export
      const exportData = items.map(item => {
        const now = new Date();
        const expiryDate = item.expiryDate ? new Date(item.expiryDate) : null;
        let itemStatus = 'ACTIVE';
        let complianceStatus = 'COMPLIANT';

        if (expiryDate) {
          if (expiryDate <= now) {
            itemStatus = 'EXPIRED';
          } else {
            const daysUntilExpiry = Math.ceil((expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
            if (daysUntilExpiry <= 30) {
              itemStatus = 'EXPIRING_SOON';
            }
          }
        }

        if (item.product.medicineClassification === 'NARKOTIKA' || item.product.medicineClassification === 'PSIKOTROPIKA') {
          complianceStatus = 'CONTROLLED';
        } else if (!item.product.medicineClassification) {
          complianceStatus = 'PENDING_REVIEW';
        }

        return {
          'Batch Number': item.batchNumber,
          'Product Name': item.product.name,
          'Product Type': item.product.type,
          'Medicine Classification': item.product.medicineClassification || 'N/A',
          'BPOM Number': item.product.bpomNumber || 'N/A',
          'Supplier': item.supplier?.name || 'N/A',
          'Quantity': item.quantityOnHand,
          'Unit Price': item.costPrice || 0,
          'Total Value': (item.quantityOnHand * Number(item.costPrice || 0)),
          'Expiry Date': expiryDate ? expiryDate.toISOString().split('T')[0] : 'N/A',
          'Status': itemStatus,
          'Compliance Status': complianceStatus,
          'Location': item.location || 'Gudang Utama',
          'Received Date': item.createdAt.toISOString().split('T')[0],
          'Last Updated': item.updatedAt.toISOString().split('T')[0],
        };
      });

      // For now, return JSON data. In a real implementation, you'd generate Excel/CSV files
      return {
        success: true,
        data: {
          format: format || 'JSON',
          totalRecords: exportData.length,
          exportData,
          generatedAt: new Date().toISOString(),
          filters: {
            search,
            status,
            complianceStatus,
            productId,
            supplierId,
            batchNumber,
            expiryDateFrom,
            expiryDateTo,
          }
        },
        message: 'Data batch berhasil diekspor'
      };
    } catch (error) {
      throw new BadRequestException({
        success: false,
        message: 'Gagal mengekspor data batch',
        error: error.message
      });
    }
  }

  /**
   * Helper method to convert period string to days
   */
  private getPeriodDays(period: string): number {
    switch (period) {
      case '7d': return 7;
      case '30d': return 30;
      case '90d': return 90;
      case '1y': return 365;
      default: return 30;
    }
  }

  /**
   * Get batch validation rules and format examples
   * GET /api/procurement/batch-validation/rules
   */
  @Get('rules')
  async getValidationRules(): Promise<ValidationRulesResponseDto> {
    try {
      const rules = {
        formats: {
          STANDARD: {
            pattern: '^[A-Z]{2,4}[0-9]{4,8}[A-Z0-9]{0,8}$',
            description: 'Format standar farmasi (2-4 huruf + 4-8 angka + opsional alphanumeric)',
            example: 'ABC12345678'
          },
          BPOM_COMPLIANT: {
            pattern: '^[A-Z]{2,4}[0-9]{6}[A-Z0-9]{2,6}$',
            description: 'Format sesuai BPOM dengan kode tanggal (2-4 huruf + 6 angka tanggal + 2-6 alphanumeric)',
            example: 'KMF240615AB'
          },
          CONTROLLED: {
            pattern: '^[A-Z]{3}[0-9]{8}[A-Z]{2}$',
            description: 'Format khusus obat terkontrol (3 huruf + 8 angka + 2 huruf)',
            example: 'NAR12345678AB'
          },
          GENERIC: {
            pattern: '^GEN[0-9]{6}[A-Z0-9]{2,4}$',
            description: 'Format obat generik (GEN + 6 angka + 2-4 alphanumeric)',
            example: 'GEN240615AB'
          },
          IMPORT: {
            pattern: '^IMP[A-Z]{2,3}[A-Z0-9]{4,12}$',
            description: 'Format obat impor (IMP + kode supplier + batch)',
            example: 'IMPUSA123456'
          },
          BASIC: {
            pattern: '^[A-Za-z0-9\\-_]{3,20}$',
            description: 'Format dasar (3-20 karakter: huruf, angka, -, _)',
            example: 'BATCH-123'
          }
        },
        requirements: {
          length: { min: 3, max: 20 },
          allowedCharacters: 'Huruf (A-Z, a-z), angka (0-9), tanda hubung (-), underscore (_)',
          uniqueness: 'Harus unik per produk',
          bpomBatchValidation: 'Obat terdaftar BPOM sebaiknya menggunakan format BPOM_COMPLIANT atau CONTROLLED'
        },
        medicineTypes: {
          NARKOTIKA: 'Wajib menggunakan format CONTROLLED',
          PSIKOTROPIKA: 'Wajib menggunakan format CONTROLLED',
          OBAT_KERAS: 'Disarankan format BPOM_COMPLIANT atau STANDARD',
          OBAT_BEBAS: 'Dapat menggunakan format BASIC atau STANDARD',
          GENERIK: 'Disarankan format GENERIC atau BPOM_COMPLIANT',
          IMPOR: 'Disarankan format IMPORT atau BPOM_COMPLIANT'
        }
      };

      return {
        success: true,
        data: rules,
        message: 'Aturan validasi batch number berhasil diambil'
      };
    } catch (error) {
      throw new BadRequestException({
        success: false,
        message: 'Gagal mengambil aturan validasi',
        error: error.message
      });
    }
  }

  /**
   * Get batch audit trail
   * GET /api/procurement/batch-validation/audit/:batchNumber
   */
  @Get('audit/:batchNumber')
  async getBatchAuditTrail(
    @Param('batchNumber') batchNumber: string,
    @Query('limit') limit?: string,
    @Query('offset') offset?: string
  ) {
    try {
      if (!batchNumber) {
        throw new BadRequestException('Batch number wajib diisi');
      }

      const auditTrail = await this.batchAuditService.getBatchAuditTrail(
        batchNumber,
        limit ? parseInt(limit) : 50,
        offset ? parseInt(offset) : 0
      );

      return {
        success: true,
        data: auditTrail,
        message: `Audit trail untuk batch ${batchNumber} berhasil diambil`
      };
    } catch (error) {
      throw new BadRequestException({
        success: false,
        message: 'Gagal mengambil audit trail batch',
        error: error.message
      });
    }
  }

  /**
   * Query audit logs with filters
   * GET /api/procurement/batch-validation/audit-logs
   */
  @Get('audit-logs')
  async queryAuditLogs(@Query() query: any) {
    try {
      const auditQuery: BatchAuditQuery = {
        batchNumber: query.batchNumber,
        productId: query.productId,
        supplierId: query.supplierId,
        userId: query.userId,
        action: query.action,
        status: query.status,
        dateFrom: query.dateFrom ? new Date(query.dateFrom) : undefined,
        dateTo: query.dateTo ? new Date(query.dateTo) : undefined,
        limit: query.limit ? parseInt(query.limit) : 50,
        offset: query.offset ? parseInt(query.offset) : 0,
      };

      const result = await this.batchAuditService.queryAuditLogs(auditQuery);

      return {
        success: true,
        data: result,
        message: 'Audit logs berhasil diambil'
      };
    } catch (error) {
      throw new BadRequestException({
        success: false,
        message: 'Gagal mengambil audit logs',
        error: error.message
      });
    }
  }

  /**
   * Get batch audit statistics
   * GET /api/procurement/batch-validation/audit-stats/:batchNumber
   */
  @Get('audit-stats/:batchNumber')
  async getBatchAuditStats(@Param('batchNumber') batchNumber: string) {
    try {
      if (!batchNumber) {
        throw new BadRequestException('Batch number wajib diisi');
      }

      const stats = await this.batchAuditService.getBatchAuditStats(batchNumber);

      return {
        success: true,
        data: stats,
        message: `Statistik audit untuk batch ${batchNumber} berhasil diambil`
      };
    } catch (error) {
      throw new BadRequestException({
        success: false,
        message: 'Gagal mengambil statistik audit',
        error: error.message
      });
    }
  }

  /**
   * Helper method to get comprehensive batch usage history
   */
  private async getBatchUsageHistory(batchNumber: string) {
    try {
      // Get inventory items with this batch number
      const inventoryItems = await this.prisma.inventoryItem.findMany({
        where: {
          batchNumber: {
            equals: batchNumber,
            mode: 'insensitive'
          }
        },
        include: {
          product: { select: { name: true } },
          unit: { select: { name: true } },
          supplier: { select: { name: true } },
          stockMovements: {
            orderBy: { createdAt: 'desc' },
            take: 50 // Limit to recent movements
          }
        }
      });

      // Get goods receipt items with this batch number
      const goodsReceiptItems = await this.prisma.goodsReceiptItem.findMany({
        where: {
          batchNumber: {
            equals: batchNumber,
            mode: 'insensitive'
          }
        },
        include: {
          product: { select: { name: true } },
          unit: { select: { name: true } },
          goodsReceipt: {
            include: {
              supplier: { select: { name: true } }
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      });

      // Get sale items with this batch number
      const saleItems = await this.prisma.saleItem.findMany({
        where: {
          batchNumber: {
            equals: batchNumber,
            mode: 'insensitive'
          }
        },
        include: {
          product: { select: { name: true } },
          unit: { select: { name: true } },
          sale: { select: { saleNumber: true, saleDate: true } }
        },
        orderBy: { createdAt: 'desc' }
      });

      // Compile usage history
      const usageHistory: any[] = [];

      // Add goods receipt entries
      goodsReceiptItems.forEach(item => {
        usageHistory.push({
          id: item.id,
          type: 'goods_receipt',
          productName: item.product.name,
          quantity: item.quantityReceived,
          unitName: item.unit.name,
          date: item.createdAt,
          reference: item.goodsReceipt.supplier.name,
          notes: `Penerimaan barang - ${item.notes || ''}`
        });
      });

      // Add sale entries
      saleItems.forEach(item => {
        usageHistory.push({
          id: item.id,
          type: 'sale',
          productName: item.product.name,
          quantity: -item.quantity, // Negative for outgoing
          unitName: item.unit.name,
          date: item.createdAt,
          reference: item.sale.saleNumber,
          notes: `Penjualan - ${item.notes || ''}`
        });
      });

      // Add stock movement entries
      inventoryItems.forEach(invItem => {
        invItem.stockMovements.forEach(movement => {
          usageHistory.push({
            id: movement.id,
            type: 'adjustment',
            productName: invItem.product.name,
            quantity: movement.quantity,
            unitName: invItem.unit.name,
            date: movement.createdAt,
            reference: movement.referenceNumber || 'Penyesuaian Stok',
            notes: movement.reason || movement.notes || ''
          });
        });
      });

      // Sort by date (newest first)
      usageHistory.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

      // Calculate totals
      const totalReceived = goodsReceiptItems.reduce((sum, item) => sum + item.quantityReceived, 0);
      const totalSold = saleItems.reduce((sum, item) => sum + item.quantity, 0);
      const currentStock = inventoryItems.reduce((sum, item) => sum + item.quantityOnHand, 0);

      // Get latest activity
      const lastActivity = usageHistory.length > 0 ? usageHistory[0].date : null;
      const createdAt = goodsReceiptItems.length > 0 ? goodsReceiptItems[goodsReceiptItems.length - 1].createdAt : null;

      // Determine status
      let status: 'active' | 'depleted' | 'expired' | 'recalled' = 'active';
      if (currentStock === 0) {
        status = 'depleted';
      } else if (inventoryItems.some(item => item.expiryDate && item.expiryDate <= new Date())) {
        status = 'expired';
      }

      // Get expiry date and storage locations
      const expiryDate = inventoryItems.find(item => item.expiryDate)?.expiryDate || undefined;
      const storageLocations = [...new Set(inventoryItems.map(item => item.location).filter(Boolean))] as string[];

      return {
        batchNumber,
        usageHistory,
        totalReceived,
        totalUsed: totalSold,
        currentStock,
        lastActivity,
        createdAt,
        status,
        expiryDate,
        storageLocations
      };
    } catch (error) {
      console.error('Error getting batch usage history:', error);
      throw new BadRequestException('Gagal mengambil riwayat penggunaan batch');
    }
  }
}
