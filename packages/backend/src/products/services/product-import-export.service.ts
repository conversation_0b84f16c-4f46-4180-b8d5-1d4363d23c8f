import { Injectable, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { ImportProductDto, ImportResultDto, ImportErrorDto, ExportProductQueryDto } from '../dto/import-product.dto';
import { ProductType, ProductCategory, MedicineClassification } from '@prisma/client';
import * as csv from 'csv-parser';
import * as csvWriter from 'csv-writer';
import * as XLSX from 'xlsx';
import { createReadStream, createWriteStream } from 'fs';
import { join } from 'path';

@Injectable()
export class ProductImportExportService {
  constructor(private prisma: PrismaService) {}

  /**
   * Generate CSV template for product import
   */
  async generateTemplate(): Promise<string> {
    const templatePath = join(process.cwd(), 'uploads', 'templates', 'product-template.csv');
    
    // Ensure directory exists
    const fs = require('fs');
    const dir = join(process.cwd(), 'uploads', 'templates');
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    const writer = csvWriter.createObjectCsvWriter({
      path: templatePath,
      header: [
        { id: 'code', title: 'Kode Produk' },
        { id: 'name', title: 'Nama Produk' },
        { id: 'genericName', title: 'Nama Generik' },
        { id: 'type', title: 'Jenis Produk' },
        { id: 'category', title: 'Kategori' },
        { id: 'manufacturer', title: 'Produsen' },
        { id: 'bpomNumber', title: 'Nomor BPOM' },
        { id: 'medicineClassification', title: 'Klasifikasi Obat' },
        { id: 'regulatorySymbol', title: 'Simbol Regulasi' },
        { id: 'baseUnitId', title: 'ID Unit Dasar' },
        { id: 'minimumStock', title: 'Stok Minimum' },
        { id: 'maximumStock', title: 'Stok Maksimum' },
        { id: 'reorderPoint', title: 'Titik Reorder' },
        { id: 'description', title: 'Deskripsi' },
        { id: 'activeIngredient', title: 'Bahan Aktif' },
        { id: 'strength', title: 'Kekuatan' },
        { id: 'dosageForm', title: 'Bentuk Sediaan' },
        { id: 'indication', title: 'Indikasi' },
        { id: 'contraindication', title: 'Kontraindikasi' },
        { id: 'sideEffects', title: 'Efek Samping' },
        { id: 'dosage', title: 'Dosis' },
        { id: 'storage', title: 'Penyimpanan' },
        { id: 'notes', title: 'Catatan' },
      ],
    });

    // Sample data with Indonesian pharmacy examples
    const sampleData = [
      {
        code: 'MED-001',
        name: 'Paracetamol 500mg',
        genericName: 'Paracetamol',
        type: 'MEDICINE',
        category: 'ANALGESIC',
        manufacturer: 'PT Kimia Farma',
        bpomNumber: 'DKL1234567890A1',
        medicineClassification: 'OBAT_BEBAS',
        regulatorySymbol: 'Lingkaran Hijau',
        baseUnitId: 'unit-tablet-id',
        minimumStock: 100,
        maximumStock: 1000,
        reorderPoint: 200,
        description: 'Obat pereda nyeri dan penurun demam',
        activeIngredient: 'Paracetamol 500mg',
        strength: '500mg',
        dosageForm: 'Tablet',
        indication: 'Nyeri ringan hingga sedang, demam',
        contraindication: 'Hipersensitif terhadap paracetamol',
        sideEffects: 'Jarang: ruam kulit, mual',
        dosage: '1-2 tablet 3-4 kali sehari',
        storage: 'Simpan di tempat sejuk dan kering',
        notes: 'Tidak boleh melebihi 8 tablet per hari',
      },
      {
        code: 'DEV-001',
        name: 'Termometer Digital',
        genericName: '',
        type: 'MEDICAL_DEVICE',
        category: 'MEDICAL_DEVICE',
        manufacturer: 'PT Alat Kesehatan Indonesia',
        bpomNumber: 'AKD1234567890',
        medicineClassification: 'NON_MEDICINE',
        regulatorySymbol: '',
        baseUnitId: 'unit-piece-id',
        minimumStock: 10,
        maximumStock: 100,
        reorderPoint: 20,
        description: 'Termometer digital untuk mengukur suhu tubuh',
        activeIngredient: '',
        strength: '',
        dosageForm: '',
        indication: 'Pengukuran suhu tubuh',
        contraindication: '',
        sideEffects: '',
        dosage: '',
        storage: 'Simpan di tempat kering, hindari benturan',
        notes: 'Kalibrasi berkala diperlukan',
      },
    ];

    await writer.writeRecords(sampleData);
    return templatePath;
  }

  /**
   * Process CSV import file
   */
  async processImport(filePath: string, userId: string): Promise<ImportResultDto> {
    const results: ImportProductDto[] = [];
    const errors: ImportErrorDto[] = [];
    let rowNumber = 0;

    return new Promise((resolve, reject) => {
      createReadStream(filePath)
        .pipe(csv())
        .on('data', (data) => {
          rowNumber++;
          try {
            const result = this.validateAndTransformRow(data, rowNumber);
            if (result.errors.errors.length > 0) {
              errors.push(result.errors);
            } else if (result.data) {
              results.push(result.data);
            }
          } catch (error) {
            errors.push({
              row: rowNumber,
              code: data.code || '',
              name: data.name || '',
              errors: [`Error parsing row: ${error.message}`],
            });
          }
        })
        .on('end', async () => {
          try {
            const importResult = await this.saveProducts(results, errors, userId);
            resolve(importResult);
          } catch (error) {
            reject(error);
          }
        })
        .on('error', (error) => {
          reject(new BadRequestException(`Error reading CSV file: ${error.message}`));
        });
    });
  }

  /**
   * Validate and transform CSV row data
   */
  private validateAndTransformRow(data: any, rowNumber: number): {
    data: ImportProductDto | null;
    errors: ImportErrorDto;
  } {
    const errors: string[] = [];
    const code = data['Kode Produk']?.trim();
    const name = data['Nama Produk']?.trim();

    // Required field validation
    if (!code) {
      errors.push('Kode produk wajib diisi');
    }
    if (!name) {
      errors.push('Nama produk wajib diisi');
    }

    // Validate product type
    const type = data['Jenis Produk']?.trim();
    if (!type || !Object.values(ProductType).includes(type as ProductType)) {
      errors.push(`Jenis produk tidak valid. Pilihan: ${Object.values(ProductType).join(', ')}`);
    }

    // Validate category
    const category = data['Kategori']?.trim();
    if (!category || !Object.values(ProductCategory).includes(category as ProductCategory)) {
      errors.push(`Kategori tidak valid. Pilihan: ${Object.values(ProductCategory).join(', ')}`);
    }

    // Validate medicine classification
    const medicineClassification = data['Klasifikasi Obat']?.trim();
    if (!medicineClassification || !Object.values(MedicineClassification).includes(medicineClassification as MedicineClassification)) {
      errors.push(`Klasifikasi obat tidak valid. Pilihan: ${Object.values(MedicineClassification).join(', ')}`);
    }

    // Validate base unit ID (required)
    const baseUnitId = data['ID Unit Dasar']?.trim();
    if (!baseUnitId) {
      errors.push('ID Unit Dasar wajib diisi');
    }

    // Validate numeric fields
    const minimumStock = data['Stok Minimum'] ? parseInt(data['Stok Minimum']) : undefined;
    const maximumStock = data['Stok Maksimum'] ? parseInt(data['Stok Maksimum']) : undefined;
    const reorderPoint = data['Titik Reorder'] ? parseInt(data['Titik Reorder']) : undefined;

    if (minimumStock !== undefined && (isNaN(minimumStock) || minimumStock < 0)) {
      errors.push('Stok minimum harus berupa angka positif');
    }
    if (maximumStock !== undefined && (isNaN(maximumStock) || maximumStock < 0)) {
      errors.push('Stok maksimum harus berupa angka positif');
    }
    if (reorderPoint !== undefined && (isNaN(reorderPoint) || reorderPoint < 0)) {
      errors.push('Titik reorder harus berupa angka positif');
    }

    if (minimumStock !== undefined && maximumStock !== undefined && minimumStock > maximumStock) {
      errors.push('Stok minimum tidak boleh lebih besar dari stok maksimum');
    }

    const errorResult: ImportErrorDto = {
      row: rowNumber,
      code: code || '',
      name: name || '',
      errors,
    };

    if (errors.length > 0) {
      return { data: null, errors: errorResult };
    }

    const productData: ImportProductDto = {
      code,
      name,
      genericName: data['Nama Generik']?.trim() || undefined,
      type: type as ProductType,
      category: category as ProductCategory,
      manufacturer: data['Produsen']?.trim() || undefined,
      bpomNumber: data['Nomor BPOM']?.trim() || undefined,
      medicineClassification: medicineClassification as MedicineClassification,
      regulatorySymbol: data['Simbol Regulasi']?.trim() || undefined,
      baseUnitId,
      minimumStock,
      maximumStock,
      reorderPoint,
      description: data['Deskripsi']?.trim() || undefined,
      activeIngredient: data['Bahan Aktif']?.trim() || undefined,
      strength: data['Kekuatan']?.trim() || undefined,
      dosageForm: data['Bentuk Sediaan']?.trim() || undefined,
      indication: data['Indikasi']?.trim() || undefined,
      contraindication: data['Kontraindikasi']?.trim() || undefined,
      sideEffects: data['Efek Samping']?.trim() || undefined,
      dosage: data['Dosis']?.trim() || undefined,
      storage: data['Penyimpanan']?.trim() || undefined,
      notes: data['Catatan']?.trim() || undefined,
    };

    return { data: productData, errors: errorResult };
  }

  /**
   * Save validated products to database
   */
  private async saveProducts(
    products: ImportProductDto[],
    validationErrors: ImportErrorDto[],
    userId: string,
  ): Promise<ImportResultDto> {
    const importedProducts: any[] = [];
    const saveErrors = [...validationErrors];

    for (const product of products) {
      try {
        // Check if product code already exists
        const existingProduct = await this.prisma.product.findUnique({
          where: { code: product.code },
        });

        if (existingProduct) {
          saveErrors.push({
            row: 0, // We don't have row number here
            code: product.code,
            name: product.name,
            errors: [`Produk dengan kode ${product.code} sudah ada`],
          });
          continue;
        }

        // Verify base unit exists
        const baseUnit = await this.prisma.productUnit.findUnique({
          where: { id: product.baseUnitId },
        });

        if (!baseUnit) {
          saveErrors.push({
            row: 0,
            code: product.code,
            name: product.name,
            errors: [`Unit dasar dengan ID ${product.baseUnitId} tidak ditemukan`],
          });
          continue;
        }

        // Create product
        const createdProduct = await this.prisma.product.create({
          data: {
            ...product,
            isActive: true,
            createdBy: userId,
            updatedBy: userId,
          },
          include: {
            baseUnit: true,
            createdByUser: {
              select: {
                firstName: true,
                lastName: true,
              },
            },
          },
        });

        importedProducts.push(createdProduct);
      } catch (error) {
        console.error(`Error saving product ${product.code}:`, error);
        saveErrors.push({
          row: 0,
          code: product.code,
          name: product.name,
          errors: [`Gagal menyimpan produk: ${error.message}`],
        });
      }
    }

    return {
      success: saveErrors.length === 0,
      totalRows: products.length + validationErrors.length,
      successfulImports: importedProducts.length,
      failedImports: saveErrors.length,
      errors: saveErrors,
      importedProducts,
    };
  }

  /**
   * Export products to CSV or XLSX
   */
  async exportProducts(query: ExportProductQueryDto): Promise<string> {
    // Build where clause for filtering
    const where: any = {};

    if (query.search) {
      where.OR = [
        { name: { contains: query.search, mode: 'insensitive' } },
        { code: { contains: query.search, mode: 'insensitive' } },
        { genericName: { contains: query.search, mode: 'insensitive' } },
        { manufacturer: { contains: query.search, mode: 'insensitive' } },
      ];
    }

    if (query.type && query.type !== 'all') {
      where.type = query.type;
    }

    if (query.category && query.category !== 'all') {
      where.category = query.category;
    }

    if (query.medicineClassification && query.medicineClassification !== 'all') {
      where.medicineClassification = query.medicineClassification;
    }

    if (query.manufacturer && query.manufacturer !== 'all') {
      where.manufacturer = { contains: query.manufacturer, mode: 'insensitive' };
    }

    if (query.isActive && query.isActive !== 'all') {
      where.isActive = query.isActive === 'true';
    }

    if (query.ids && query.ids.length > 0) {
      where.id = { in: query.ids };
    }

    // Fetch products
    const products = await this.prisma.product.findMany({
      where,
      include: {
        baseUnit: true,
        createdByUser: {
          select: {
            firstName: true,
            lastName: true,
          },
        },
        unitHierarchies: {
          include: {
            unit: true,
          },
        },
        _count: {
          select: {
            unitHierarchies: true,
          },
        },
      },
      orderBy: { name: 'asc' },
    });

    const format = query.format || 'csv';
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `products-export-${timestamp}.${format}`;
    const filePath = join(process.cwd(), 'uploads', 'exports', filename);

    // Ensure directory exists
    const fs = require('fs');
    const dir = join(process.cwd(), 'uploads', 'exports');
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    if (format === 'csv') {
      await this.exportToCSV(products, filePath);
    } else {
      await this.exportToXLSX(products, filePath);
    }

    return filePath;
  }

  /**
   * Export products to CSV format
   */
  private async exportToCSV(products: any[], filePath: string): Promise<void> {
    const writer = csvWriter.createObjectCsvWriter({
      path: filePath,
      header: [
        { id: 'code', title: 'Kode Produk' },
        { id: 'name', title: 'Nama Produk' },
        { id: 'genericName', title: 'Nama Generik' },
        { id: 'type', title: 'Jenis Produk' },
        { id: 'category', title: 'Kategori' },
        { id: 'manufacturer', title: 'Produsen' },
        { id: 'bpomNumber', title: 'Nomor BPOM' },
        { id: 'medicineClassification', title: 'Klasifikasi Obat' },
        { id: 'regulatorySymbol', title: 'Simbol Regulasi' },
        { id: 'baseUnit', title: 'Unit Dasar' },
        { id: 'minimumStock', title: 'Stok Minimum' },
        { id: 'maximumStock', title: 'Stok Maksimum' },
        { id: 'reorderPoint', title: 'Titik Reorder' },
        { id: 'isActive', title: 'Status Aktif' },
        { id: 'description', title: 'Deskripsi' },
        { id: 'activeIngredient', title: 'Bahan Aktif' },
        { id: 'strength', title: 'Kekuatan' },
        { id: 'dosageForm', title: 'Bentuk Sediaan' },
        { id: 'indication', title: 'Indikasi' },
        { id: 'contraindication', title: 'Kontraindikasi' },
        { id: 'sideEffects', title: 'Efek Samping' },
        { id: 'dosage', title: 'Dosis' },
        { id: 'storage', title: 'Penyimpanan' },
        { id: 'unitHierarchies', title: 'Jumlah Unit Hierarki' },
        { id: 'createdBy', title: 'Dibuat Oleh' },
        { id: 'createdAt', title: 'Tanggal Dibuat' },
        { id: 'notes', title: 'Catatan' },
      ],
    });

    const records = products.map((product) => ({
      code: product.code,
      name: product.name,
      genericName: product.genericName || '',
      type: product.type,
      category: product.category,
      manufacturer: product.manufacturer || '',
      bpomNumber: product.bpomNumber || '',
      medicineClassification: product.medicineClassification,
      regulatorySymbol: product.regulatorySymbol || '',
      baseUnit: product.baseUnit ? `${product.baseUnit.name} (${product.baseUnit.abbreviation})` : '',
      minimumStock: product.minimumStock || '',
      maximumStock: product.maximumStock || '',
      reorderPoint: product.reorderPoint || '',
      isActive: product.isActive ? 'Aktif' : 'Tidak Aktif',
      description: product.description || '',
      activeIngredient: product.activeIngredient || '',
      strength: product.strength || '',
      dosageForm: product.dosageForm || '',
      indication: product.indication || '',
      contraindication: product.contraindication || '',
      sideEffects: product.sideEffects || '',
      dosage: product.dosage || '',
      storage: product.storage || '',
      unitHierarchies: product._count.unitHierarchies,
      createdBy: product.createdByUser ? `${product.createdByUser.firstName} ${product.createdByUser.lastName}` : '',
      createdAt: new Date(product.createdAt).toLocaleDateString('id-ID'),
      notes: product.notes || '',
    }));

    await writer.writeRecords(records);
  }

  /**
   * Export products to XLSX format
   */
  private async exportToXLSX(products: any[], filePath: string): Promise<void> {
    const records = products.map((product) => ({
      'Kode Produk': product.code,
      'Nama Produk': product.name,
      'Nama Generik': product.genericName || '',
      'Jenis Produk': product.type,
      'Kategori': product.category,
      'Produsen': product.manufacturer || '',
      'Nomor BPOM': product.bpomNumber || '',
      'Klasifikasi Obat': product.medicineClassification,
      'Simbol Regulasi': product.regulatorySymbol || '',
      'Unit Dasar': product.baseUnit ? `${product.baseUnit.name} (${product.baseUnit.abbreviation})` : '',
      'Stok Minimum': product.minimumStock || '',
      'Stok Maksimum': product.maximumStock || '',
      'Titik Reorder': product.reorderPoint || '',
      'Status Aktif': product.isActive ? 'Aktif' : 'Tidak Aktif',
      'Deskripsi': product.description || '',
      'Bahan Aktif': product.activeIngredient || '',
      'Kekuatan': product.strength || '',
      'Bentuk Sediaan': product.dosageForm || '',
      'Indikasi': product.indication || '',
      'Kontraindikasi': product.contraindication || '',
      'Efek Samping': product.sideEffects || '',
      'Dosis': product.dosage || '',
      'Penyimpanan': product.storage || '',
      'Jumlah Unit Hierarki': product._count.unitHierarchies,
      'Dibuat Oleh': product.createdByUser ? `${product.createdByUser.firstName} ${product.createdByUser.lastName}` : '',
      'Tanggal Dibuat': new Date(product.createdAt).toLocaleDateString('id-ID'),
      'Catatan': product.notes || '',
    }));

    const worksheet = XLSX.utils.json_to_sheet(records);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Products');

    XLSX.writeFile(workbook, filePath);
  }
}
