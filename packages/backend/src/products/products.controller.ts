import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
  BadRequestException,
  UseInterceptors,
  UploadedFile,
  Res,
} from '@nestjs/common';
import { ProductsService } from './products.service';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { ProductQueryDto } from './dto/product-query.dto';
import { ExportProductQueryDto } from './dto/import-product.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AdminGuard } from '../settings/guards/admin.guard';
import { ManagerGuard } from '../suppliers/guards/manager.guard';
import { ProductCodeGeneratorService } from './product-code-generator.service';
import { ProductImportExportService } from './services/product-import-export.service';
import { FileInterceptor } from '@nestjs/platform-express';
import { Response } from 'express';

@Controller('products')
@UseGuards(JwtAuthGuard)
export class ProductsController {
  constructor(
    private readonly productsService: ProductsService,
    private readonly productCodeGeneratorService: ProductCodeGeneratorService,
    private readonly productImportExportService: ProductImportExportService,
  ) {}

  @Post()
  @UseGuards(ManagerGuard) // Admin or Pharmacist can create
  async create(@Body() createProductDto: CreateProductDto, @Request() req) {
    return this.productsService.create(createProductDto, req.user.id);
  }

  @Get()
  async findAll(@Query() query: ProductQueryDto) {
    return this.productsService.findAll(query);
  }

  @Get('stats')
  async getStats() {
    return this.productsService.getStats();
  }

  // Product code generation endpoints (must come before parameterized routes)
  @Get('generate-code/:type')
  async generateProductCode(@Param('type') type: string) {
    // Validate product type
    const validTypes = ['MEDICINE', 'MEDICAL_DEVICE', 'SUPPLEMENT', 'COSMETIC', 'GENERAL'];
    if (!validTypes.includes(type)) {
      throw new BadRequestException('Jenis produk tidak valid');
    }

    return {
      code: await this.productCodeGeneratorService.generateProductCode(type as any),
    };
  }

  @Get('validate-code/:code')
  async validateProductCode(@Param('code') code: string) {
    return {
      isUnique: await this.productCodeGeneratorService.validateCodeUniqueness(code),
    };
  }

  // Import/Export endpoints (must come before parameterized routes)
  @Get('template')
  @UseGuards(ManagerGuard)
  async downloadTemplate(@Res() res: Response) {
    try {
      const templatePath = await this.productImportExportService.generateTemplate();
      const fs = require('fs');

      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', 'attachment; filename="template-product.csv"');

      const fileStream = fs.createReadStream(templatePath);
      fileStream.pipe(res);
    } catch (error) {
      throw new BadRequestException(`Gagal mengunduh template: ${error.message}`);
    }
  }

  @Post('import')
  @UseGuards(ManagerGuard)
  @UseInterceptors(FileInterceptor('file', {
    storage: require('multer').diskStorage({
      destination: './uploads/imports',
      filename: (req, file, cb) => {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, `product-import-${uniqueSuffix}.csv`);
      },
    }),
    fileFilter: (req, file, cb) => {
      if (file.mimetype === 'text/csv' || file.mimetype === 'application/vnd.ms-excel') {
        cb(null, true);
      } else {
        cb(new BadRequestException('Hanya file CSV yang diizinkan'), false);
      }
    },
    limits: { fileSize: 5 * 1024 * 1024 }, // 5MB
  }))
  async importProducts(
    @UploadedFile() file: Express.Multer.File,
    @Request() req,
  ) {
    if (!file) {
      throw new BadRequestException('File CSV wajib diunggah');
    }

    try {
      const result = await this.productImportExportService.processImport(file.path, req.user.id);

      // Clean up uploaded file
      const fs = require('fs');
      if (fs.existsSync(file.path)) {
        fs.unlinkSync(file.path);
      }

      return result;
    } catch (error) {
      // Clean up uploaded file on error
      const fs = require('fs');
      if (file && fs.existsSync(file.path)) {
        fs.unlinkSync(file.path);
      }
      throw new BadRequestException(`Gagal memproses import: ${error.message}`);
    }
  }

  @Get('export')
  @UseGuards(ManagerGuard)
  async exportProducts(
    @Query() query: ExportProductQueryDto,
    @Res() res: Response,
  ) {
    try {
      const filePath = await this.productImportExportService.exportProducts(query);
      const fs = require('fs');

      const format = query.format || 'csv';
      const mimeType = format === 'xlsx'
        ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        : 'text/csv';

      const timestamp = new Date().toISOString().split('T')[0];
      const filename = `products-export-${timestamp}.${format}`;

      res.setHeader('Content-Type', mimeType);
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

      const fileStream = fs.createReadStream(filePath);
      fileStream.pipe(res);

      // Clean up file after sending
      fileStream.on('end', () => {
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
        }
      });
    } catch (error) {
      throw new BadRequestException(`Gagal mengekspor data: ${error.message}`);
    }
  }

  @Patch(':id/deactivate')
  @UseGuards(ManagerGuard) // Admin or Pharmacist can deactivate
  async deactivate(@Param('id') id: string, @Request() req) {
    return this.productsService.deactivate(id, req.user.id);
  }

  @Patch(':id/activate')
  @UseGuards(ManagerGuard) // Admin or Pharmacist can activate
  async activate(@Param('id') id: string, @Request() req) {
    return this.productsService.activate(id, req.user.id);
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    return this.productsService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(ManagerGuard) // Admin or Pharmacist can update
  async update(
    @Param('id') id: string,
    @Body() updateProductDto: UpdateProductDto,
    @Request() req,
  ) {
    return this.productsService.update(id, updateProductDto, req.user.id);
  }

  @Delete(':id')
  @UseGuards(AdminGuard) // Only Admin can hard delete
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id') id: string) {
    await this.productsService.hardDelete(id);
  }
}
