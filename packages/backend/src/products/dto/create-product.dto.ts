import { IsString, IsOptional, IsEnum, IsInt, IsArray, ValidateNested, IsNumber, Min } from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { ProductType, ProductCategory, MedicineClassification } from '@prisma/client';

export class CreateProductUnitHierarchyDto {
  @IsString()
  unitId: string;

  @IsOptional()
  @IsString()
  parentUnitId?: string; // NULL for top-level unit

  @Transform(({ value }) => parseFloat(value))
  @IsNumber()
  @Min(0.0001)
  conversionFactor: number; // How many of this unit = 1 parent unit

  @IsInt()
  @Min(0)
  level: number; // 0 = base unit, 1 = next level up, etc.

  @IsOptional()
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  @IsNumber()
  @Min(0)
  sellingPrice?: number; // Price per this unit

  @IsOptional()
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  @IsNumber()
  @Min(0)
  costPrice?: number; // Cost per this unit
}

export class CreateProductDto {
  @IsString()
  code: string;

  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  genericName?: string;

  @IsEnum(ProductType)
  type: ProductType = ProductType.MEDICINE;

  @IsEnum(ProductCategory)
  category: ProductCategory = ProductCategory.OTHER;

  @IsOptional()
  @IsString()
  manufacturer?: string;

  // Indonesian Pharmacy Specific Fields
  @IsOptional()
  @IsString()
  bpomNumber?: string; // Indonesian drug registration number

  @IsEnum(MedicineClassification)
  medicineClassification: MedicineClassification = MedicineClassification.NON_MEDICINE;

  @IsOptional()
  @IsString()
  regulatorySymbol?: string; // Official Indonesian regulatory symbol description

  // Unit and Pricing Information
  @IsString()
  baseUnitId: string; // Smallest trackable unit (e.g., tablet, ml)

  @IsOptional()
  @IsInt()
  @Min(0)
  minimumStock?: number; // In base units

  @IsOptional()
  @IsInt()
  @Min(0)
  maximumStock?: number; // In base units

  @IsOptional()
  @IsInt()
  @Min(0)
  reorderPoint?: number; // In base units

  // Product Information
  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsString()
  activeIngredient?: string; // Kandungan aktif

  @IsOptional()
  @IsString()
  strength?: string; // Kekuatan (e.g., "500mg", "10ml")

  @IsOptional()
  @IsString()
  dosageForm?: string; // Bentuk sediaan (tablet, sirup, kapsul, etc.)

  @IsOptional()
  @IsString()
  indication?: string; // Indikasi

  @IsOptional()
  @IsString()
  contraindication?: string; // Kontraindikasi

  @IsOptional()
  @IsString()
  sideEffects?: string; // Efek samping

  @IsOptional()
  @IsString()
  dosage?: string; // Dosis

  @IsOptional()
  @IsString()
  storage?: string; // Penyimpanan

  @IsOptional()
  @IsString()
  notes?: string;

  // Unit Hierarchies
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateProductUnitHierarchyDto)
  unitHierarchies?: CreateProductUnitHierarchyDto[];
}
