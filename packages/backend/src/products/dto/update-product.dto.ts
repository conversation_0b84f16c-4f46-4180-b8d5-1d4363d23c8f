import { PartialType } from '@nestjs/mapped-types';
import { CreateProductDto, CreateProductUnitHierarchyDto } from './create-product.dto';
import { IsBoolean, IsOptional, IsArray, ValidateNested, IsString } from 'class-validator';
import { Type } from 'class-transformer';

export class UpdateProductUnitHierarchyDto extends CreateProductUnitHierarchyDto {
  @IsOptional()
  @IsString()
  id?: string;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class UpdateProductDto extends PartialType(CreateProductDto) {
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  // Override unitHierarchies to use UpdateProductUnitHierarchyDto
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpdateProductUnitHierarchyDto)
  unitHierarchies?: UpdateProductUnitHierarchyDto[];
}
