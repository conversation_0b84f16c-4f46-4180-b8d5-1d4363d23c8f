import { IsOptional, IsString, IsEnum, IsInt, <PERSON>, <PERSON>, IsIn, IsBoolean } from 'class-validator';
import { Transform } from 'class-transformer';
import { ProductType, ProductCategory, MedicineClassification } from '@prisma/client';

export class ProductQueryDto {
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsInt()
  @Min(1)
  page?: number = 1;

  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @IsEnum(ProductType)
  type?: ProductType;

  @IsOptional()
  @IsEnum(ProductCategory)
  category?: ProductCategory;

  @IsOptional()
  @IsEnum(MedicineClassification)
  medicineClassification?: MedicineClassification;

  @IsOptional()
  @IsString()
  manufacturer?: string;

  @IsOptional()
  @Transform(({ value }) => value === 'true')
  @IsBoolean()
  isActive?: boolean;

  @IsOptional()
  @IsString()
  baseUnitId?: string;

  @IsOptional()
  @IsString()
  @IsIn(['name', 'code', 'type', 'category', 'manufacturer', 'medicineClassification', 'createdAt', 'updatedAt'])
  sortBy?: string = 'createdAt';

  @IsOptional()
  @IsString()
  @IsIn(['asc', 'desc'])
  sortOrder?: 'asc' | 'desc' = 'desc';

  // Additional filters for enhanced search
  @IsOptional()
  @IsString()
  genericName?: string;

  @IsOptional()
  @IsString()
  bpomNumber?: string;

  @IsOptional()
  @IsString()
  activeIngredient?: string;

  @IsOptional()
  @IsString()
  dosageForm?: string;

  @IsOptional()
  @IsString()
  strength?: string;

  @IsOptional()
  @IsString()
  createdBy?: string;

  @IsOptional()
  @IsString()
  dateFrom?: string; // ISO date string

  @IsOptional()
  @IsString()
  dateTo?: string; // ISO date string

  // Stock level filters
  @IsOptional()
  @Transform(({ value }) => value === 'true')
  @IsBoolean()
  lowStock?: boolean; // Products below reorder point

  @IsOptional()
  @Transform(({ value }) => value === 'true')
  @IsBoolean()
  outOfStock?: boolean; // Products with zero stock

  @IsOptional()
  @Transform(({ value }) => value === 'true')
  @IsBoolean()
  overStock?: boolean; // Products above maximum stock
}
