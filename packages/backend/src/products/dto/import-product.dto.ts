import { IsString, IsOptional, <PERSON>E<PERSON>, IsInt, IsNumber, IsArray } from 'class-validator';
import { Transform } from 'class-transformer';
import { ProductType, ProductCategory, MedicineClassification } from '@prisma/client';

export class ImportProductDto {
  @IsString()
  code: string;

  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  genericName?: string;

  @IsEnum(ProductType)
  type: ProductType;

  @IsEnum(ProductCategory)
  category: ProductCategory;

  @IsOptional()
  @IsString()
  manufacturer?: string;

  // Indonesian Pharmacy Specific Fields
  @IsOptional()
  @IsString()
  bpomNumber?: string;

  @IsEnum(MedicineClassification)
  medicineClassification: MedicineClassification;

  @IsOptional()
  @IsString()
  regulatorySymbol?: string;

  // Unit and Pricing Information
  @IsString()
  baseUnitId: string;

  @IsOptional()
  @IsNumber()
  minimumStock?: number;

  @IsOptional()
  @IsNumber()
  maximumStock?: number;

  @IsOptional()
  @IsNumber()
  reorderPoint?: number;

  // Product Information
  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsString()
  activeIngredient?: string;

  @IsOptional()
  @IsString()
  strength?: string;

  @IsOptional()
  @IsString()
  dosageForm?: string;

  @IsOptional()
  @IsString()
  indication?: string;

  @IsOptional()
  @IsString()
  contraindication?: string;

  @IsOptional()
  @IsString()
  sideEffects?: string;

  @IsOptional()
  @IsString()
  dosage?: string;

  @IsOptional()
  @IsString()
  storage?: string;

  @IsOptional()
  @IsString()
  notes?: string;
}

export class ExportProductQueryDto {
  @IsOptional()
  @IsString()
  format?: 'csv' | 'xlsx';

  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @IsString()
  type?: string;

  @IsOptional()
  @IsString()
  category?: string;

  @IsOptional()
  @IsString()
  medicineClassification?: string;

  @IsOptional()
  @IsString()
  manufacturer?: string;

  @IsOptional()
  @IsString()
  isActive?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  ids?: string[];
}

export class ImportErrorDto {
  row: number;
  code: string;
  name: string;
  errors: string[];
}

export class ImportResultDto {
  success: boolean;
  totalRows: number;
  successfulImports: number;
  failedImports: number;
  errors: ImportErrorDto[];
  importedProducts: any[];
}
