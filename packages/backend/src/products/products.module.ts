import { Module } from '@nestjs/common';
import { ProductsService } from './products.service';
import { ProductsController } from './products.controller';
import { ProductCodeGeneratorService } from './product-code-generator.service';
import { ProductImportExportService } from './services/product-import-export.service';
import { PrismaModule } from '../prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [ProductsController],
  providers: [ProductsService, ProductCodeGeneratorService, ProductImportExportService],
  exports: [ProductsService, ProductCodeGeneratorService, ProductImportExportService],
})
export class ProductsModule {}
