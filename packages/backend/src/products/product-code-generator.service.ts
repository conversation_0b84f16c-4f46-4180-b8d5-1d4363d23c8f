import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { ProductType } from '@prisma/client';

@Injectable()
export class ProductCodeGeneratorService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Generate product code based on type with format {PREFIX}-{SEQUENCE}
   * Examples: MED-001, DEV-002, SUP-003, COS-004, GEN-005
   */
  async generateProductCode(type: ProductType): Promise<string> {
    try {
      const prefix = this.getProductTypePrefix(type);
      const sequence = await this.getNextSequenceNumber(prefix);
      const code = `${prefix}-${sequence.toString().padStart(3, '0')}`;
      
      // Ensure the generated code is unique (double-check)
      const isUnique = await this.validateCodeUniqueness(code);
      if (!isUnique) {
        // If somehow not unique, try with next sequence
        const nextSequence = await this.getNextSequenceNumber(prefix);
        return `${prefix}-${nextSequence.toString().padStart(3, '0')}`;
      }
      
      return code;
    } catch (error) {
      console.error('Error generating product code:', error);
      // Fallback code with timestamp
      const timestamp = Date.now().toString().slice(-3);
      return `GEN-${timestamp}`;
    }
  }

  /**
   * Validate if a product code is unique
   */
  async validateCodeUniqueness(code: string): Promise<boolean> {
    const existingProduct = await this.prisma.product.findFirst({
      where: { code },
    });
    
    return !existingProduct;
  }

  /**
   * Get the next sequence number for a given prefix
   */
  private async getNextSequenceNumber(prefix: string): Promise<number> {
    // Find the highest sequence number for this prefix
    const products = await this.prisma.product.findMany({
      where: {
        code: {
          startsWith: `${prefix}-`,
        },
      },
      select: {
        code: true,
      },
      orderBy: {
        code: 'desc',
      },
    });

    let maxSequence = 0;
    
    for (const product of products) {
      const codeParts = product.code.split('-');
      if (codeParts.length === 2 && codeParts[0] === prefix) {
        const sequence = parseInt(codeParts[1], 10);
        if (!isNaN(sequence) && sequence > maxSequence) {
          maxSequence = sequence;
        }
      }
    }

    return maxSequence + 1;
  }

  /**
   * Get prefix based on product type
   */
  private getProductTypePrefix(type: ProductType): string {
    switch (type) {
      case ProductType.MEDICINE:
        return 'MED';
      case ProductType.MEDICAL_DEVICE:
        return 'DEV';
      case ProductType.SUPPLEMENT:
        return 'SUP';
      case ProductType.COSMETIC:
        return 'COS';
      case ProductType.GENERAL:
        return 'GEN';
      default:
        return 'GEN';
    }
  }

  /**
   * Generate a new code if the provided one is not unique
   */
  async ensureUniqueCode(proposedCode?: string, type?: ProductType): Promise<string> {
    // If no code provided, generate a new one
    if (!proposedCode || !type) {
      return this.generateProductCode(type || ProductType.GENERAL);
    }

    // If provided code is unique, use it
    const isUnique = await this.validateCodeUniqueness(proposedCode);
    if (isUnique) {
      return proposedCode;
    }

    // If not unique, generate a new one
    console.warn(`Product code ${proposedCode} is not unique, generating new one`);
    return this.generateProductCode(type);
  }
}
