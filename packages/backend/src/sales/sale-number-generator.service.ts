import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';

@Injectable()
export class SaleNumberGeneratorService {
  constructor(private prisma: PrismaService) { }

  async generateSaleNumber(): Promise<string> {
    const today = new Date();
    const year = today.getFullYear();
    const month = (today.getMonth() + 1).toString().padStart(2, '0');
    const day = today.getDate().toString().padStart(2, '0');

    // Format: TRX-YYYYMMDD-XXX
    const datePrefix = `TRX-${year}${month}${day}`;

    // Use a more robust approach to find the next sequence
    // Get all sales for today and extract sequences
    const existingSales = await this.prisma.sale.findMany({
      where: {
        saleNumber: {
          startsWith: datePrefix,
        },
      },
      select: {
        saleNumber: true,
      },
    });

    // Extract all sequence numbers and find the maximum
    const sequences = existingSales
      .map(sale => {
        const match = sale.saleNumber.match(/-(\d{3})$/);
        return match ? parseInt(match[1], 10) : 0;
      })
      .filter(seq => seq > 0);

    const maxSequence = sequences.length > 0 ? Math.max(...sequences) : 0;
    const nextSequence = maxSequence + 1;

    // Add unique microsecond timestamp and random digits to ensure uniqueness
    // Combine sequence number with microsecond timestamp for uniqueness
    const milliseconds = Date.now() % 1000;
    const microseconds = Math.floor(Math.random() * 1000);
    const uniqueCode = ((nextSequence * 1000) + milliseconds + microseconds) % 1000;
    
    // Format the sequence with leading zeros
    const formattedSequence = Math.max(1, uniqueCode).toString().padStart(3, '0');
    
    const saleNumber = `${datePrefix}-${formattedSequence}`;
    
    // Double-check uniqueness (important for high-concurrency environments)
    const exists = await this.validateSaleNumberUniqueness(saleNumber);
    if (!exists) {
      // If a collision is detected, recursively try again with a different random value
      return this.generateSaleNumber();
    }
    
    return saleNumber;
  }

  async validateSaleNumberUniqueness(saleNumber: string): Promise<boolean> {
    const existing = await this.prisma.sale.findUnique({
      where: { saleNumber },
    });
    return !existing;
  }
}
