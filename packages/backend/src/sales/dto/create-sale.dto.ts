import { IsString, IsOptional, IsEnum, IsArray, ValidateNested, IsNumber, IsInt, Min, IsDateString, ArrayMinSize } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { PaymentMethod } from '@prisma/client';

export class CreateSaleItemDto {
  @IsString()
  productId: string;

  @IsString()
  unitId: string;

  @Transform(({ value }) => parseInt(value))
  @IsInt()
  @Min(1)
  quantity: number;

  @Transform(({ value }) => typeof value === 'string' ? parseFloat(value) : value)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0.01)
  unitPrice: number;

  // Discount Information (item-level)
  @IsOptional()
  @IsString()
  discountType?: string; // PERCENTAGE, FIXED_AMOUNT

  @IsOptional()
  @Transform(({ value }) => value ? (typeof value === 'string' ? parseFloat(value) : value) : undefined)
  @IsNumber({ maxDecimalPlaces: 2 })
  discountValue?: number;

  @IsOptional()
  @IsString()
  notes?: string;
}

export class CreateSaleDto {
  @IsOptional()
  @IsString()
  saleNumber?: string; // Will be auto-generated if not provided

  @IsOptional()
  @IsString()
  customerId?: string; // Optional for walk-in customers

  @IsOptional()
  @IsString()
  cashierId?: string; // Will be set from authenticated user if not provided

  @IsOptional()
  @IsDateString()
  saleDate?: string;

  // Discount Information (sale-level)
  @IsOptional()
  @IsString()
  discountType?: string; // PERCENTAGE, FIXED_AMOUNT

  @IsOptional()
  @Transform(({ value }) => value ? (typeof value === 'string' ? parseFloat(value) : value) : undefined)
  @IsNumber({ maxDecimalPlaces: 2 })
  discountValue?: number;

  @IsOptional()
  @Transform(({ value }) => value ? (typeof value === 'string' ? parseFloat(value) : value) : 0)
  @IsNumber({ maxDecimalPlaces: 2 })
  taxAmount?: number;

  // Payment Information
  @IsEnum(PaymentMethod)
  paymentMethod: PaymentMethod;

  @Transform(({ value }) => typeof value === 'string' ? parseFloat(value) : value)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  amountPaid: number;

  // Customer Information (for walk-in customers)
  @IsOptional()
  @IsString()
  customerName?: string;

  @IsOptional()
  @IsString()
  customerPhone?: string;

  @IsOptional()
  @IsString()
  notes?: string;

  // Sale Items
  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => CreateSaleItemDto)
  items: CreateSaleItemDto[];
}
