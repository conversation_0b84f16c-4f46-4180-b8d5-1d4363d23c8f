import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { PharmacySettingsService } from '../pharmacy/pharmacy-settings.service';
import { TemplateService } from '../templates/template.service';
import * as puppeteer from 'puppeteer';

export interface ReceiptData {
  // Header Information
  pharmacy: {
    name: string;
    address: string;
    phone: string;
    email?: string;
    license?: string;
  };

  // Transaction Information
  transaction: {
    saleNumber: string;
    date: string;
    time: string;
    cashier: {
      name: string;
      id: string;
    };
  };

  // Customer Information
  customer: {
    name: string;
    phone?: string;
    type: 'WALK_IN' | 'REGISTERED';
    membershipNumber?: string;
  };

  // Items
  items: Array<{
    name: string;
    code: string;
    quantity: number;
    unit: string;
    unitPrice: number;
    discount: number;
    subtotal: number;
    manufacturer?: string;
  }>;

  // Totals
  totals: {
    subtotal: number;
    discount: number;
    afterDiscount: number;
    tax: number;
    total: number;
  };

  // Payment Information
  payment: {
    method: string;
    amountPaid: number;
    change: number;
  };

  // Footer Information
  footer: {
    thankYouMessage: string;
    returnPolicy: string;
    notes?: string;
  };

  // Thermal Printer Formatting Hints
  formatting: {
    paperWidth: 58 | 80; // mm
    lineBreaks: {
      afterHeader: number;
      afterTransaction: number;
      afterCustomer: number;
      afterItems: number;
      afterTotals: number;
      afterPayment: number;
      beforeFooter: number;
    };
    alignment: {
      header: 'center' | 'left' | 'right';
      items: 'left' | 'right';
      totals: 'right';
      footer: 'center';
    };
    fontSizes: {
      header: 'normal' | 'large' | 'small';
      items: 'normal' | 'small';
      totals: 'normal' | 'large';
      footer: 'small';
    };
  };
}

@Injectable()
export class ReceiptService {
  constructor(
    private prisma: PrismaService,
    private pharmacySettingsService: PharmacySettingsService,
    private templateService: TemplateService,
  ) { }

  /**
   * Generate receipt data for a completed sale
   */
  async generateReceipt(saleId: string): Promise<ReceiptData> {
    // Get sale with all related data
    const sale = await this.prisma.sale.findUnique({
      where: { id: saleId },
      include: {
        customer: true,
        cashier: true,
        saleItems: {
          include: {
            product: true,
            unit: true,
          },
        },
      },
    });

    if (!sale) {
      throw new NotFoundException('Transaksi tidak ditemukan');
    }

    // Get pharmacy information (in a real system, this would come from settings)
    const pharmacyInfo = await this.getPharmacyInfo();

    // Format date and time
    const saleDate = new Date(sale.saleDate);
    const formattedDate = saleDate.toLocaleDateString('id-ID', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
    const formattedTime = saleDate.toLocaleTimeString('id-ID', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });

    // Format customer information
    const customer = {
      name: sale.customerName || 'Walk-in Customer',
      phone: sale.customerPhone || undefined,
      type: sale.customerId ? 'REGISTERED' as const : 'WALK_IN' as const,
      membershipNumber: sale.customer?.code,
    };

    // Format items
    const items = sale.saleItems.map(item => ({
      name: item.product.name,
      code: item.product.code,
      quantity: item.quantity,
      unit: item.unit.abbreviation,
      unitPrice: Number(item.unitPrice),
      discount: Number(item.discountAmount),
      subtotal: Number(item.totalPrice),
      manufacturer: item.product.manufacturer || undefined,
    }));

    // Calculate totals
    const itemsSubtotal = items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);
    const itemsDiscount = items.reduce((sum, item) => sum + item.discount, 0);
    const saleDiscount = Number(sale.discountAmount);
    const totalDiscount = itemsDiscount + saleDiscount;

    const totals = {
      subtotal: itemsSubtotal,
      discount: totalDiscount,
      afterDiscount: Number(sale.subtotal),
      tax: Number(sale.taxAmount),
      total: Number(sale.totalAmount),
    };

    // Format payment information
    const payment = {
      method: this.formatPaymentMethod(sale.paymentMethod),
      amountPaid: Number(sale.amountPaid),
      change: Number(sale.changeAmount),
    };

    // Generate receipt data
    const receiptData: ReceiptData = {
      pharmacy: pharmacyInfo,
      transaction: {
        saleNumber: sale.saleNumber,
        date: formattedDate,
        time: formattedTime,
        cashier: {
          name: `${sale.cashier.firstName} ${sale.cashier.lastName}`,
          id: sale.cashier.id,
        },
      },
      customer,
      items,
      totals,
      payment,
      footer: {
        thankYouMessage: 'Terima kasih atas kunjungan Anda',
        returnPolicy: 'Barang yang sudah dibeli tidak dapat dikembalikan kecuali ada kesalahan dari pihak apotek',
        notes: sale.notes || undefined,
      },
      formatting: this.getThermalPrinterFormatting(),
    };

    return receiptData;
  }

  /**
   * Get pharmacy information from settings
   */
  private async getPharmacyInfo() {
    return this.pharmacySettingsService.getPharmacyInfo();
  }

  /**
   * Format payment method for display
   */
  private formatPaymentMethod(method: string): string {
    const methodMap: Record<string, string> = {
      CASH: 'Tunai',
      TRANSFER: 'Transfer Bank',
      CREDIT: 'Kartu Kredit',
      GIRO: 'Giro',
    };
    return methodMap[method] || method;
  }

  /**
   * Get thermal printer formatting configuration
   */
  private getThermalPrinterFormatting() {
    return {
      paperWidth: 58 as const, // 58mm thermal paper (most common)
      lineBreaks: {
        afterHeader: 2,
        afterTransaction: 1,
        afterCustomer: 1,
        afterItems: 2,
        afterTotals: 1,
        afterPayment: 2,
        beforeFooter: 1,
      },
      alignment: {
        header: 'center' as const,
        items: 'left' as const,
        totals: 'right' as const,
        footer: 'center' as const,
      },
      fontSizes: {
        header: 'normal' as const,
        items: 'small' as const,
        totals: 'normal' as const,
        footer: 'small' as const,
      },
    };
  }

  /**
   * Generate receipt for 80mm thermal paper
   */
  async generateReceiptWide(saleId: string): Promise<ReceiptData> {
    const receipt = await this.generateReceipt(saleId);

    // Modify formatting for 80mm paper
    receipt.formatting.paperWidth = 80;
    receipt.formatting.fontSizes.header = 'large';
    receipt.formatting.fontSizes.totals = 'large';

    return receipt;
  }

  /**
   * Validate that receipt can be generated for this sale
   */
  async validateReceiptGeneration(saleId: string): Promise<{ canGenerate: boolean; reason?: string }> {
    try {
      const sale = await this.prisma.sale.findUnique({
        where: { id: saleId },
      });

      if (!sale) {
        return { canGenerate: false, reason: 'Transaksi tidak ditemukan' };
      }

      if (sale.status === 'DRAFT') {
        return { canGenerate: false, reason: 'Tidak dapat membuat struk untuk transaksi draft' };
      }

      if (sale.status === 'CANCELLED') {
        return { canGenerate: false, reason: 'Tidak dapat membuat struk untuk transaksi yang dibatalkan' };
      }

      return { canGenerate: true };
    } catch (error) {
      return { canGenerate: false, reason: 'Transaksi tidak ditemukan' };
    }
  }

  /**
   * Generate HTML from receipt data using template
   */
  generateReceiptHtml(receiptData: ReceiptData): string {
    return this.templateService.generateReceiptHtml(receiptData);
  }

  /**
   * Generate PDF receipt from HTML template
   */
  async generateReceiptPdf(saleId: string, isWide: boolean = false): Promise<Buffer> {
    const receiptData = isWide
      ? await this.generateReceiptWide(saleId)
      : await this.generateReceipt(saleId);

    const html = this.generateReceiptHtml(receiptData);

    const browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    try {
      const page = await browser.newPage();
      await page.setContent(html, { waitUntil: 'networkidle0' });

      // Calculate appropriate height based on content and paper width
      const paperWidth = isWide ? 80 : 58;
      const estimatedHeight = Math.max(200, receiptData.items.length * 15 + 150); // Dynamic height based on content

      const pdfBuffer = await page.pdf({
        width: `${paperWidth}mm`,
        height: `${estimatedHeight}mm`,
        margin: {
          top: '2mm',
          right: '2mm',
          bottom: '2mm',
          left: '2mm'
        },
        printBackground: true,
        preferCSSPageSize: false, // Disable CSS page size to use our custom dimensions
        format: undefined // Don't use predefined format, use custom width/height
      });

      return Buffer.from(pdfBuffer);
    } finally {
      await browser.close();
    }
  }
}
