import { Module } from '@nestjs/common';
import { SalesService } from './sales.service';
import { SalesController } from './sales.controller';
import { SaleNumberGeneratorService } from './sale-number-generator.service';
import { ReceiptService } from './receipt.service';
import { TemplateService } from '../templates/template.service';
import { PrismaModule } from '../prisma/prisma.module';
import { InventoryModule } from '../inventory/inventory.module';
import { ProductsModule } from '../products/products.module';
import { PharmacyModule } from '../pharmacy/pharmacy.module';
import { UnitConversionService } from '../common/services/unit-conversion.service';

@Module({
  imports: [PrismaModule, InventoryModule, ProductsModule, PharmacyModule],
  controllers: [SalesController],
  providers: [SalesService, SaleNumberGeneratorService, ReceiptService, TemplateService, UnitConversionService],
  exports: [SalesService, SaleNumberGeneratorService, ReceiptService, TemplateService, UnitConversionService],
})
export class SalesModule { }
