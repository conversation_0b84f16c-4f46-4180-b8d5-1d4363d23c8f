import { Injectable, OnModuleInit } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';

@Injectable()
export class PrismaService extends PrismaClient implements OnModuleInit {
  constructor() {
    super({
      // CRITICAL SAFETY: In test environment, ensure we only connect to test database
      datasources: {
        db: {
          url: process.env.DATABASE_URL,
        },
      },
    });

    // SAFETY CHECK: Prevent connection to non-test database in test environment
    if (process.env.NODE_ENV === 'test') {
      const dbUrl = process.env.DATABASE_URL;
      if (!dbUrl?.includes('_test')) {
        throw new Error(
          `🚨 CRITICAL: PrismaService refusing to connect to non-test database in test environment!\n` +
          `DATABASE_URL: ${dbUrl}\n` +
          `Expected: A database URL containing '_test'\n` +
          `This prevents accidental data loss in development/production databases.`
        );
      }
    }
  }

  async onModuleInit() {
    await this.$connect();

    // Additional runtime safety check
    if (process.env.NODE_ENV === 'test') {
      try {
        const result = await this.$queryRaw<[{ current_database: string }]>`SELECT current_database()`;
        const currentDb = result[0]?.current_database;

        if (!currentDb?.includes('_test')) {
          throw new Error(
            `🚨 CRITICAL: PrismaService connected to wrong database: ${currentDb}\n` +
            `Expected: A database with '_test' suffix\n` +
            `Disconnecting to prevent data loss.`
          );
        }

        console.log(`🔒 PrismaService: Confirmed connection to test database: ${currentDb}`);
      } catch (error) {
        await this.$disconnect();
        throw error;
      }
    }
  }
}
