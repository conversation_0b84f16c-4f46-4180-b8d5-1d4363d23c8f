/**
 * Enhanced Pino Logging System Usage Examples
 * 
 * This file demonstrates how to use the enhanced Pino logging system
 * with auto-rotating files, structured logging, and proper error handling.
 */

import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { LogFileManager, createStandaloneLogger } from '../common/utils';

/**
 * Example service demonstrating proper logging practices
 */
@Injectable()
export class ExampleService {
  private readonly logger = new Logger(ExampleService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly logFileManager: LogFileManager,
  ) {}

  /**
   * Example: Basic structured logging
   */
  async processOrder(orderId: string, customerId: string) {
    // Log the start of operation with context
    this.logger.log('Processing order started', {
      orderId,
      customerId,
      timestamp: new Date().toISOString(),
      operation: 'process_order',
    });

    try {
      // Simulate processing
      await this.simulateOrderProcessing(orderId);

      // Log successful completion
      this.logger.log('Order processed successfully', {
        orderId,
        customerId,
        status: 'completed',
        processingTime: '2.5s',
      });

      return { success: true, orderId };
    } catch (error) {
      // Log error with full context
      this.logger.error('Order processing failed', error, {
        orderId,
        customerId,
        operation: 'process_order',
        errorType: error.constructor.name,
        timestamp: new Date().toISOString(),
      });

      throw error;
    }
  }

  /**
   * Example: Different log levels in action
   */
  async demonstrateLogLevels() {
    // TRACE: Very detailed debugging (development only)
    this.logger.verbose('Entering method demonstrateLogLevels', {
      method: 'demonstrateLogLevels',
      className: ExampleService.name,
    });

    // DEBUG: Detailed debugging information
    this.logger.debug('Processing configuration', {
      environment: this.configService.get('NODE_ENV'),
      logLevel: this.configService.get('LOG_LEVEL'),
    });

    // INFO: General application flow
    this.logger.log('Demonstrating different log levels', {
      availableLevels: ['trace', 'debug', 'info', 'warn', 'error', 'fatal'],
    });

    // WARN: Potential issues
    this.logger.warn('This is a warning message', {
      reason: 'demonstration_purpose',
      severity: 'low',
    });

    // ERROR: Error conditions (this won't throw, just logs)
    this.logger.error('This is an error message for demonstration', null, {
      reason: 'demonstration_purpose',
      severity: 'high',
    });
  }

  /**
   * Example: Performance logging
   */
  async performanceLoggingExample() {
    const startTime = Date.now();
    const operationId = `op_${Date.now()}`;

    this.logger.log('Performance operation started', {
      operationId,
      startTime: new Date(startTime).toISOString(),
    });

    try {
      // Simulate some work
      await new Promise(resolve => setTimeout(resolve, 1000));

      const endTime = Date.now();
      const duration = endTime - startTime;

      this.logger.log('Performance operation completed', {
        operationId,
        duration: `${duration}ms`,
        endTime: new Date(endTime).toISOString(),
        performance: {
          fast: duration < 500,
          acceptable: duration < 2000,
          slow: duration >= 2000,
        },
      });

      // Log performance warning if slow
      if (duration > 2000) {
        this.logger.warn('Slow operation detected', {
          operationId,
          duration: `${duration}ms`,
          threshold: '2000ms',
          recommendation: 'Consider optimization',
        });
      }

    } catch (error) {
      const endTime = Date.now();
      this.logger.error('Performance operation failed', error, {
        operationId,
        duration: `${endTime - startTime}ms`,
        failurePoint: 'execution',
      });
    }
  }

  /**
   * Example: Log file management operations
   */
  async logFileManagementExample() {
    this.logger.log('Demonstrating log file management capabilities');

    try {
      // Get current log statistics
      const stats = await this.logFileManager.getLogDirectoryStats();
      
      this.logger.log('Current log directory statistics', {
        totalFiles: stats.totalFiles,
        totalSize: this.logFileManager.formatFileSize(stats.totalSize),
        expiredFiles: stats.expiredFiles,
        oldestFile: stats.oldestFile?.filename,
        newestFile: stats.newestFile?.filename,
      });

      // Get expired files
      const expiredFiles = await this.logFileManager.getExpiredLogFiles();
      
      if (expiredFiles.length > 0) {
        this.logger.warn('Found expired log files', {
          count: expiredFiles.length,
          files: expiredFiles.map(f => ({
            filename: f.filename,
            age: f.age,
            size: this.logFileManager.formatFileSize(f.size),
          })),
        });

        // Perform manual cleanup
        const cleanupResult = await this.logFileManager.performManualCleanup();
        
        this.logger.log('Manual log cleanup completed', {
          deletedFiles: cleanupResult.deletedFiles,
          beforeStats: {
            files: cleanupResult.beforeStats.totalFiles,
            size: this.logFileManager.formatFileSize(cleanupResult.beforeStats.totalSize),
          },
          afterStats: {
            files: cleanupResult.afterStats.totalFiles,
            size: this.logFileManager.formatFileSize(cleanupResult.afterStats.totalSize),
          },
        });
      } else {
        this.logger.log('No expired log files found', {
          retentionDays: this.configService.get('LOG_RETENTION_DAYS', 30),
        });
      }

    } catch (error) {
      this.logger.error('Log file management operation failed', error);
    }
  }

  /**
   * Example: Using standalone logger for utilities
   */
  async standaloneLoggerExample() {
    // Create a standalone logger for a specific context
    const utilityLogger = createStandaloneLogger(this.configService, 'DataMigration');

    utilityLogger.info('Starting data migration utility', {
      version: '1.0.0',
      environment: this.configService.get('NODE_ENV'),
    });

    try {
      // Simulate migration steps
      utilityLogger.info('Step 1: Backing up existing data');
      await new Promise(resolve => setTimeout(resolve, 500));

      utilityLogger.info('Step 2: Applying schema changes');
      await new Promise(resolve => setTimeout(resolve, 500));

      utilityLogger.info('Step 3: Migrating data');
      await new Promise(resolve => setTimeout(resolve, 500));

      utilityLogger.info('Data migration completed successfully', {
        totalSteps: 3,
        duration: '1.5s',
        status: 'success',
      });

    } catch (error) {
      utilityLogger.error('Data migration failed', error, {
        step: 'unknown',
        rollbackRequired: true,
      });
    }
  }

  /**
   * Example: Security-aware logging (avoiding sensitive data)
   */
  async securityAwareLoggingExample(userEmail: string, password: string) {
    // ✅ Good: Log user action without sensitive data
    this.logger.log('User authentication attempt', {
      userEmail,
      timestamp: new Date().toISOString(),
      ipAddress: '***********', // In real app, get from request
      userAgent: 'Mozilla/5.0...', // In real app, get from request
      // Note: password is NOT logged (automatically redacted by config)
    });

    // ✅ Good: Log authentication result
    this.logger.log('Authentication successful', {
      userEmail,
      sessionId: 'sess_123456789',
      loginMethod: 'email_password',
    });

    // ❌ Bad: This would be automatically redacted by our configuration
    this.logger.debug('Debug info', {
      userEmail,
      password, // This will be redacted automatically
      token: 'jwt_token_here', // This will be redacted automatically
    });
  }

  /**
   * Private helper method
   */
  private async simulateOrderProcessing(orderId: string): Promise<void> {
    // Simulate some processing time
    await new Promise(resolve => setTimeout(resolve, Math.random() * 1000));
    
    // Randomly simulate an error for demonstration
    if (Math.random() < 0.1) {
      throw new Error(`Order processing failed for order ${orderId}`);
    }
  }
}

/**
 * Example: Using the enhanced logging system in a controller
 */
export class ExampleController {
  private readonly logger = new Logger(ExampleController.name);

  constructor(private readonly exampleService: ExampleService) {}

  async handleRequest(req: any, res: any) {
    const requestId = `req_${Date.now()}`;
    
    // Log incoming request
    this.logger.log('Incoming request', {
      requestId,
      method: req.method,
      url: req.url,
      userAgent: req.headers['user-agent'],
      ip: req.ip,
    });

    try {
      const result = await this.exampleService.processOrder('order_123', 'customer_456');
      
      // Log successful response
      this.logger.log('Request completed successfully', {
        requestId,
        statusCode: 200,
        responseTime: '150ms',
      });

      return result;
    } catch (error) {
      // Log error response
      this.logger.error('Request failed', error, {
        requestId,
        statusCode: 500,
        errorMessage: error.message,
      });

      throw error;
    }
  }
}

/**
 * Example: Application startup logging
 */
export async function logApplicationStartup(configService: ConfigService) {
  const logger = createStandaloneLogger(configService, 'Application');

  logger.info('🚀 Pharmacy Backend Application Starting', {
    version: '1.0.0',
    environment: configService.get('NODE_ENV'),
    port: configService.get('PORT'),
    nodeVersion: process.version,
    platform: process.platform,
    arch: process.arch,
  });

  // Log configuration (without sensitive data)
  logger.info('📊 Application Configuration', {
    logLevel: configService.get('LOG_LEVEL'),
    logDirectory: configService.get('LOG_DIRECTORY'),
    logRetentionDays: configService.get('LOG_RETENTION_DAYS'),
    logtailEnabled: !!configService.get('LOGTAIL_TOKEN'),
  });

  logger.info('✅ Application startup logging completed');
}
