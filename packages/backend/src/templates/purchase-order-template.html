<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Purchase Order - {{purchaseOrder.orderNumber}}</title>
    <style>
        /* Base styles for professional document */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', 'Helvetica', sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            background: #fff;
            padding: 20mm;
        }

        .document-container {
            width: 100%;
            max-width: 210mm; /* A4 width */
            margin: 0 auto;
            background: white;
            color: black;
        }

        /* Typography */
        .text-center { text-align: center; }
        .text-left { text-align: left; }
        .text-right { text-align: right; }
        .font-bold { font-weight: bold; }
        .font-large { font-size: 16px; font-weight: bold; }
        .font-medium { font-size: 14px; font-weight: bold; }
        .font-small { font-size: 10px; }

        /* Layout utilities */
        .mb-1 { margin-bottom: 5px; }
        .mb-2 { margin-bottom: 10px; }
        .mb-3 { margin-bottom: 15px; }
        .mb-4 { margin-bottom: 20px; }
        .mt-1 { margin-top: 5px; }
        .mt-2 { margin-top: 10px; }
        .mt-3 { margin-top: 15px; }
        .mt-4 { margin-top: 20px; }

        /* Flex utilities */
        .flex { display: flex; }
        .justify-between { justify-content: space-between; }
        .items-start { align-items: flex-start; }
        .items-center { align-items: center; }

        /* Grid utilities */
        .grid-2 { 
            display: grid; 
            grid-template-columns: 1fr 1fr; 
            gap: 20px; 
        }

        /* Borders and separators */
        .border-bottom {
            border-bottom: 1px solid #333;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }

        .border-top {
            border-top: 1px solid #333;
            padding-top: 10px;
            margin-top: 15px;
        }

        /* Header section */
        .header {
            margin-bottom: 30px;
        }

        .company-name {
            font-size: 20px;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 5px;
        }

        .company-info {
            font-size: 11px;
            color: #666;
            line-height: 1.3;
        }

        /* Document title */
        .document-title {
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            color: #1f2937;
            margin: 30px 0;
            text-transform: uppercase;
            letter-spacing: 2px;
        }

        /* Info sections */
        .info-section {
            background: #f8fafc;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #e2e8f0;
        }

        .info-title {
            font-size: 14px;
            font-weight: bold;
            color: #374151;
            margin-bottom: 10px;
            border-bottom: 1px solid #d1d5db;
            padding-bottom: 5px;
        }

        .info-row {
            display: flex;
            margin-bottom: 5px;
        }

        .info-label {
            font-weight: bold;
            width: 120px;
            color: #4b5563;
        }

        .info-value {
            flex: 1;
            color: #1f2937;
        }

        /* Table styles */
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 11px;
        }

        .items-table th {
            background: #374151;
            color: white;
            padding: 12px 8px;
            text-align: left;
            font-weight: bold;
            border: 1px solid #4b5563;
        }

        .items-table td {
            padding: 10px 8px;
            border: 1px solid #d1d5db;
            vertical-align: top;
        }

        .items-table tbody tr:nth-child(even) {
            background: #f9fafb;
        }

        .items-table tbody tr:hover {
            background: #f3f4f6;
        }

        /* Totals section */
        .totals-section {
            margin-top: 20px;
            display: flex;
            justify-content: flex-end;
        }

        .totals-table {
            width: 300px;
            border-collapse: collapse;
        }

        .totals-table td {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
        }

        .totals-table .label {
            background: #f3f4f6;
            font-weight: bold;
            text-align: right;
            width: 150px;
        }

        .totals-table .value {
            text-align: right;
            font-weight: bold;
        }

        .totals-table .grand-total {
            background: #374151;
            color: white;
            font-size: 14px;
        }

        /* Signature section */
        .signature-section {
            margin-top: 40px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
        }

        .signature-box {
            text-align: center;
            border: 1px solid #d1d5db;
            padding: 20px;
            border-radius: 5px;
            background: #f9fafb;
        }

        .signature-title {
            font-weight: bold;
            margin-bottom: 50px;
            color: #374151;
        }

        .signature-line {
            border-top: 1px solid #333;
            margin-top: 50px;
            padding-top: 5px;
            font-size: 10px;
            color: #666;
        }

        /* Footer */
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
            font-size: 10px;
            color: #666;
            text-align: center;
        }

        /* Print styles */
        @media print {
            body {
                margin: 0;
                padding: 15mm;
                font-size: 11px;
            }

            .document-container {
                width: 100%;
                max-width: none;
            }

            .no-print {
                display: none !important;
            }

            * {
                color: black !important;
                background: white !important;
            }

            .info-section {
                background: white !important;
                border: 1px solid #ccc !important;
            }

            .items-table th {
                background: #f0f0f0 !important;
                color: black !important;
            }

            .totals-table .grand-total {
                background: #f0f0f0 !important;
                color: black !important;
            }

            .signature-box {
                background: white !important;
            }
        }
    </style>
</head>
<body>
    <div class="document-container">
        <!-- Header Section -->
        <div class="header">
            <div class="flex justify-between items-start">
                <div>
                    <div class="company-name">{{pharmacy.name}}</div>
                    <div class="company-info">
                        {{pharmacy.address}}<br>
                        Telepon: {{pharmacy.phone}}
                        {{#if pharmacy.email}}<br>Email: {{pharmacy.email}}{{/if}}
                        {{#if pharmacy.license}}<br>SIPA: {{pharmacy.license}}{{/if}}
                    </div>
                </div>
                <div class="text-right">
                    <div class="font-bold">Tanggal: {{purchaseOrder.orderDate}}</div>
                    <div class="font-small mt-1">Dicetak pada: {{currentDateTime}}</div>
                </div>
            </div>
        </div>

        <!-- Document Title -->
        <div class="document-title">Purchase Order</div>

        <!-- Purchase Order and Supplier Information -->
        <div class="grid-2 mb-4">
            <!-- Purchase Order Info -->
            <div class="info-section">
                <div class="info-title">Informasi Purchase Order</div>
                <div class="info-row">
                    <span class="info-label">Nomor PO:</span>
                    <span class="info-value font-bold">{{purchaseOrder.orderNumber}}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Tanggal Order:</span>
                    <span class="info-value">{{purchaseOrder.orderDate}}</span>
                </div>
                {{#if purchaseOrder.expectedDelivery}}
                <div class="info-row">
                    <span class="info-label">Estimasi Kirim:</span>
                    <span class="info-value">{{purchaseOrder.expectedDelivery}}</span>
                </div>
                {{/if}}
                <div class="info-row">
                    <span class="info-label">Status:</span>
                    <span class="info-value">{{purchaseOrder.status}}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Dibuat oleh:</span>
                    <span class="info-value">{{createdBy.name}}</span>
                </div>
            </div>

            <!-- Supplier Info -->
            <div class="info-section">
                <div class="info-title">Informasi Supplier</div>
                <div class="info-row">
                    <span class="info-label">Nama:</span>
                    <span class="info-value font-bold">{{supplier.name}}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Kode:</span>
                    <span class="info-value">{{supplier.code}}</span>
                </div>
                {{#if supplier.address}}
                <div class="info-row">
                    <span class="info-label">Alamat:</span>
                    <span class="info-value">{{supplier.address}}</span>
                </div>
                {{/if}}
                {{#if supplier.phone}}
                <div class="info-row">
                    <span class="info-label">Telepon:</span>
                    <span class="info-value">{{supplier.phone}}</span>
                </div>
                {{/if}}
                {{#if supplier.email}}
                <div class="info-row">
                    <span class="info-label">Email:</span>
                    <span class="info-value">{{supplier.email}}</span>
                </div>
                {{/if}}
            </div>
        </div>

        <!-- Items Table -->
        <table class="items-table">
            <thead>
                <tr>
                    <th style="width: 5%;">No</th>
                    <th style="width: 15%;">Kode Produk</th>
                    <th style="width: 30%;">Nama Produk</th>
                    <th style="width: 10%;">Qty</th>
                    <th style="width: 8%;">Unit</th>
                    <th style="width: 12%;">Harga Satuan</th>
                    <th style="width: 10%;">Diskon</th>
                    <th style="width: 10%;">Total</th>
                </tr>
            </thead>
            <tbody>
                {{#each items}}
                <tr>
                    <td class="text-center">{{@index}}</td>
                    <td>{{productCode}}</td>
                    <td>
                        <div class="font-bold">{{productName}}</div>
                        {{#if manufacturer}}<div class="font-small">{{manufacturer}}</div>{{/if}}
                        {{#if notes}}<div class="font-small" style="color: #666;">{{notes}}</div>{{/if}}
                    </td>
                    <td class="text-right">{{quantityOrdered}}</td>
                    <td>{{unit}}</td>
                    <td class="text-right">{{formatCurrency unitPrice}}</td>
                    <td class="text-right">{{formatCurrency discountAmount}}</td>
                    <td class="text-right font-bold">{{formatCurrency totalPrice}}</td>
                </tr>
                {{/each}}
            </tbody>
        </table>

        <!-- Totals Section -->
        <div class="totals-section">
            <table class="totals-table">
                <tr>
                    <td class="label">Subtotal:</td>
                    <td class="value">{{formatCurrency totals.subtotal}}</td>
                </tr>
                {{#if totals.discountAmount}}
                <tr>
                    <td class="label">Total Diskon:</td>
                    <td class="value">-{{formatCurrency totals.discountAmount}}</td>
                </tr>
                {{/if}}
                {{#if totals.taxAmount}}
                <tr>
                    <td class="label">Pajak (PPN):</td>
                    <td class="value">{{formatCurrency totals.taxAmount}}</td>
                </tr>
                {{/if}}
                <tr class="grand-total">
                    <td class="label">TOTAL:</td>
                    <td class="value">{{formatCurrency totals.totalAmount}}</td>
                </tr>
            </table>
        </div>

        <!-- Payment and Delivery Information -->
        {{#if payment.terms}}
        <div class="info-section mt-4">
            <div class="info-title">Informasi Pembayaran & Pengiriman</div>
            <div class="grid-2">
                <div>
                    {{#if payment.method}}
                    <div class="info-row">
                        <span class="info-label">Metode Bayar:</span>
                        <span class="info-value">{{payment.method}}</span>
                    </div>
                    {{/if}}
                    {{#if payment.terms}}
                    <div class="info-row">
                        <span class="info-label">Termin Bayar:</span>
                        <span class="info-value">{{payment.terms}} hari</span>
                    </div>
                    {{/if}}
                </div>
                <div>
                    {{#if delivery.address}}
                    <div class="info-row">
                        <span class="info-label">Alamat Kirim:</span>
                        <span class="info-value">{{delivery.address}}</span>
                    </div>
                    {{/if}}
                    {{#if delivery.contact}}
                    <div class="info-row">
                        <span class="info-label">Kontak:</span>
                        <span class="info-value">{{delivery.contact}}</span>
                    </div>
                    {{/if}}
                </div>
            </div>
        </div>
        {{/if}}

        <!-- Notes -->
        {{#if purchaseOrder.notes}}
        <div class="info-section mt-4">
            <div class="info-title">Catatan</div>
            <div>{{purchaseOrder.notes}}</div>
        </div>
        {{/if}}

        <!-- Signature Section -->
        <div class="signature-section">
            <div class="signature-box">
                <div class="signature-title">Dibuat Oleh</div>
                <div class="signature-line">
                    {{createdBy.name}}<br>
                    {{pharmacy.name}}
                </div>
            </div>
            <div class="signature-box">
                <div class="signature-title">Disetujui Oleh</div>
                <div class="signature-line">
                    Supplier<br>
                    {{supplier.name}}
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <div>Dokumen ini dibuat secara otomatis oleh sistem manajemen apotek</div>
            {{#if footer.notes}}
            <div class="mt-1">{{footer.notes}}</div>
            {{/if}}
        </div>
    </div>
</body>
</html>
