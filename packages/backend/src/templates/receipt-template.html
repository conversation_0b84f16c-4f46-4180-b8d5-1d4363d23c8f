<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Struk Transaksi - {{transaction.saleNumber}}</title>
    <style>
        /* Base styles for thermal printer compatibility */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier New', 'Consolas', monospace;
            font-size: 12px;
            line-height: 1.3;
            color: #000;
            background: #fff;
            padding: 4mm;
            width: 100%;
        }

        /* Paper width configurations */
        .receipt-container {
            width: 100%;
            max-width: {{paperWidth}}mm;
            margin: 0 auto;
            background: white;
            color: black;
        }

        /* Typography */
        .text-center { text-align: center; }
        .text-left { text-align: left; }
        .text-right { text-align: right; }
        .font-bold { font-weight: bold; }
        .font-large { font-size: 14px; font-weight: bold; }
        .font-small { font-size: 10px; }
        .font-xs { font-size: 9px; }

        /* Layout utilities */
        .mb-1 { margin-bottom: 2px; }
        .mb-2 { margin-bottom: 4px; }
        .mb-3 { margin-bottom: 6px; }
        .mt-1 { margin-top: 2px; }
        .mt-2 { margin-top: 4px; }
        .mt-3 { margin-top: 6px; }

        /* Flex utilities for alignment */
        .flex { display: flex; }
        .justify-between { justify-content: space-between; }
        .items-center { align-items: center; }

        /* Borders and separators */
        .border-dashed {
            border-top: 1px dashed #333;
            margin: 3px 0;
        }

        .border-solid {
            border-top: 1px solid #333;
            margin: 3px 0;
        }

        /* Header section */
        .header {
            text-align: center;
            margin-bottom: 6px;
        }

        .pharmacy-name {
            font-size: {{headerFontSize}};
            font-weight: bold;
            margin-bottom: 2px;
        }

        .pharmacy-info {
            font-size: 11px;
            line-height: 1.2;
        }

        /* Transaction info */
        .transaction-header {
            text-align: center;
            font-weight: bold;
            margin: 6px 0 4px 0;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 1px;
            font-size: 11px;
        }

        /* Items section */
        .items-header {
            text-align: center;
            font-weight: bold;
            margin: 4px 0;
        }

        .item {
            margin-bottom: 4px;
            font-size: 10px;
        }

        .item-name {
            font-weight: bold;
            margin-bottom: 1px;
        }

        .item-details {
            font-size: 9px;
            color: #333;
            margin-bottom: 1px;
        }

        .item-calculation {
            display: flex;
            justify-content: space-between;
            font-size: 10px;
        }

        .item-discount {
            display: flex;
            justify-content: space-between;
            font-size: 9px;
            color: #333;
        }

        /* Totals section */
        .totals {
            margin-top: 6px;
        }

        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 1px;
            font-size: 11px;
        }

        .grand-total {
            display: flex;
            justify-content: space-between;
            font-weight: bold;
            font-size: {{totalsFontSize}};
            margin-top: 2px;
            padding-top: 2px;
            border-top: 1px solid #333;
        }

        /* Payment section */
        .payment-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 1px;
            font-size: 11px;
        }

        /* Footer section */
        .footer {
            text-align: center;
            margin-top: 8px;
            font-size: 9px;
            line-height: 1.3;
        }

        .thank-you {
            font-weight: bold;
            margin-bottom: 4px;
        }

        .return-policy {
            margin: 4px 0;
            white-space: pre-wrap;
        }

        .notes {
            font-style: italic;
            margin: 4px 0;
        }

        .print-time {
            margin-top: 6px;
            font-size: 8px;
            color: #666;
        }

        /* Print-specific styles */
        @media print {
            body {
                margin: 0;
                padding: 2mm;
                font-size: 11px;
            }

            .receipt-container {
                width: 100%;
                max-width: none;
            }

            /* Hide elements that shouldn't print */
            .no-print {
                display: none !important;
            }

            /* Ensure black text on white background */
            * {
                color: black !important;
                background: white !important;
            }

            /* Page break control */
            .receipt-container {
                page-break-inside: avoid;
            }
        }

        /* Responsive adjustments for different paper widths */
        @media (max-width: 60mm) {
            body { font-size: 10px; }
            .pharmacy-name { font-size: 12px; }
            .item { font-size: 9px; }
            .item-details { font-size: 8px; }
        }

        @media (min-width: 75mm) {
            body { font-size: 13px; }
            .pharmacy-name { font-size: 16px; }
            .item { font-size: 11px; }
        }

        /* Line break utilities */
        .line-break-1::after { content: "\A"; white-space: pre; }
        .line-break-2::after { content: "\A\A"; white-space: pre; }
        .line-break-3::after { content: "\A\A\A"; white-space: pre; }
    </style>
</head>
<body>
    <div class="receipt-container">
        <!-- Header Section -->
        <div class="header">
            <div class="pharmacy-name">{{pharmacy.name}}</div>
            <div class="pharmacy-info">
                {{pharmacy.address}}<br>
                Tel: {{pharmacy.phone}}
                {{#if pharmacy.email}}<br>Email: {{pharmacy.email}}{{/if}}
                {{#if pharmacy.license}}<br>SIPA: {{pharmacy.license}}{{/if}}
            </div>
        </div>

        <div class="border-dashed"></div>

        <!-- Transaction Header -->
        <div class="transaction-header">STRUK PENJUALAN</div>
        <div class="border-dashed"></div>

        <!-- Transaction Information -->
        <div class="info-row">
            <span>No. Transaksi:</span>
            <span>{{transaction.saleNumber}}</span>
        </div>
        <div class="info-row">
            <span>Tanggal:</span>
            <span>{{transaction.date}}</span>
        </div>
        <div class="info-row">
            <span>Waktu:</span>
            <span>{{transaction.time}}</span>
        </div>
        <div class="info-row">
            <span>Kasir:</span>
            <span>{{transaction.cashier.name}}</span>
        </div>

        <div class="border-dashed"></div>

        <!-- Customer Information -->
        <div class="info-row">
            <span>Pelanggan:</span>
            <span>{{customer.name}}</span>
        </div>
        {{#if customer.phone}}
        <div class="info-row">
            <span>Telepon:</span>
            <span>{{customer.phone}}</span>
        </div>
        {{/if}}
        {{#if customer.membershipNumber}}
        <div class="info-row">
            <span>No. Member:</span>
            <span>{{customer.membershipNumber}}</span>
        </div>
        {{/if}}

        <div class="border-dashed"></div>

        <!-- Items Header -->
        <div class="items-header">DETAIL PEMBELIAN</div>
        <div class="border-dashed"></div>

        <!-- Items List -->
        {{#each items}}
        <div class="item">
            <div class="item-name">{{name}}</div>
            <div class="item-details">{{code}}{{#if manufacturer}} • {{manufacturer}}{{/if}}</div>
            <div class="item-calculation">
                <span>{{quantity}} {{unit}} x {{formatCurrency unitPrice}}</span>
                <span>{{formatCurrency subtotal}}</span>
            </div>
            {{#if discount}}
            <div class="item-discount">
                <span>Diskon:</span>
                <span>-{{formatCurrency discount}}</span>
            </div>
            {{/if}}
        </div>
        {{/each}}

        <div class="border-dashed"></div>

        <!-- Totals Section -->
        <div class="totals">
            <div class="total-row">
                <span>Subtotal:</span>
                <span>{{formatCurrency totals.subtotal}}</span>
            </div>
            {{#if totals.discount}}
            <div class="total-row">
                <span>Total Diskon:</span>
                <span>-{{formatCurrency totals.discount}}</span>
            </div>
            {{/if}}
            {{#if totals.tax}}
            <div class="total-row">
                <span>Pajak:</span>
                <span>{{formatCurrency totals.tax}}</span>
            </div>
            {{/if}}
            <div class="grand-total">
                <span>TOTAL:</span>
                <span>{{formatCurrency totals.total}}</span>
            </div>
        </div>

        <div class="border-dashed"></div>

        <!-- Payment Information -->
        <div class="payment-row">
            <span>Metode Bayar:</span>
            <span>{{payment.method}}</span>
        </div>
        <div class="payment-row">
            <span>Jumlah Bayar:</span>
            <span>{{formatCurrency payment.amountPaid}}</span>
        </div>
        {{#if payment.change}}
        <div class="payment-row">
            <span>Kembalian:</span>
            <span>{{formatCurrency payment.change}}</span>
        </div>
        {{/if}}

        <div class="border-dashed"></div>

        <!-- Footer Section -->
        <div class="footer">
            <div class="thank-you">{{footer.thankYouMessage}}</div>
            <div class="return-policy">{{footer.returnPolicy}}</div>
            {{#if footer.notes}}
            <div class="notes">Catatan: {{footer.notes}}</div>
            {{/if}}
            <div class="print-time">Dicetak pada: {{currentDateTime}}</div>
        </div>
    </div>
</body>
</html>
