import { Injectable } from '@nestjs/common';
import { readFileSync } from 'fs';
import { join } from 'path';
import { ReceiptData } from '../sales/receipt.service';
import { PurchaseOrderReceiptData } from '../procurement/interfaces/purchase-order-receipt.interface';

@Injectable()
export class TemplateService {
  private receiptTemplate: string;
  private purchaseOrderTemplate: string;

  constructor() {
    // Load templates on service initialization
    this.loadReceiptTemplate();
    this.loadPurchaseOrderTemplate();
  }

  /**
   * Load the receipt HTML template from file
   */
  private loadReceiptTemplate(): void {
    try {
      // Try multiple possible paths for the template
      const possiblePaths = [
        join(__dirname, '..', 'templates', 'receipt-template.html'),
        join(__dirname, '..', '..', 'src', 'templates', 'receipt-template.html'),
        join(process.cwd(), 'src', 'templates', 'receipt-template.html'),
        join(process.cwd(), 'packages', 'backend', 'src', 'templates', 'receipt-template.html')
      ];

      let templateLoaded = false;
      for (const templatePath of possiblePaths) {
        try {
          this.receiptTemplate = readFileSync(templatePath, 'utf-8');
          console.log(`Receipt template loaded from: ${templatePath}`);
          templateLoaded = true;
          break;
        } catch (err) {
          // Continue to next path
        }
      }

      if (!templateLoaded) {
        throw new Error('Template file not found in any expected location');
      }
    } catch (error) {
      console.error('Failed to load receipt template:', error);
      // Fallback to a basic template if file loading fails
      this.receiptTemplate = this.getBasicReceiptTemplate();
    }
  }

  /**
   * Load the purchase order HTML template from file
   */
  private loadPurchaseOrderTemplate(): void {
    try {
      // Try multiple possible paths for the template
      const possiblePaths = [
        join(__dirname, '..', 'templates', 'purchase-order-template.html'),
        join(__dirname, '..', '..', 'src', 'templates', 'purchase-order-template.html'),
        join(process.cwd(), 'src', 'templates', 'purchase-order-template.html'),
        join(process.cwd(), 'packages', 'backend', 'src', 'templates', 'purchase-order-template.html')
      ];

      let templateLoaded = false;
      for (const templatePath of possiblePaths) {
        try {
          this.purchaseOrderTemplate = readFileSync(templatePath, 'utf-8');
          console.log(`Purchase order template loaded from: ${templatePath}`);
          templateLoaded = true;
          break;
        } catch (err) {
          // Continue to next path
        }
      }

      if (!templateLoaded) {
        throw new Error('Purchase order template file not found in any expected location');
      }
    } catch (error) {
      console.error('Failed to load purchase order template:', error);
      // Fallback to a basic template if file loading fails
      this.purchaseOrderTemplate = this.getBasicPurchaseOrderTemplate();
    }
  }

  /**
   * Generate HTML from receipt template with data
   */
  generateReceiptHtml(receiptData: ReceiptData): string {
    let html = this.receiptTemplate;

    // Replace template variables with actual data
    html = this.replaceTemplateVariables(html, receiptData);

    return html;
  }

  /**
   * Generate HTML from purchase order template with data
   */
  generatePurchaseOrderHtml(purchaseOrderData: PurchaseOrderReceiptData): string {
    let html = this.purchaseOrderTemplate;

    // Replace template variables with actual data
    html = this.replacePurchaseOrderVariables(html, purchaseOrderData);

    return html;
  }

  /**
   * Replace template placeholders with actual data
   */
  private replaceTemplateVariables(template: string, data: ReceiptData): string {
    let html = template;

    // Replace simple variables
    html = html.replace(/\{\{pharmacy\.name\}\}/g, this.escapeHtml(data.pharmacy.name));
    html = html.replace(/\{\{pharmacy\.address\}\}/g, this.escapeHtml(data.pharmacy.address));
    html = html.replace(/\{\{pharmacy\.phone\}\}/g, this.escapeHtml(data.pharmacy.phone));
    html = html.replace(/\{\{pharmacy\.email\}\}/g, data.pharmacy.email ? this.escapeHtml(data.pharmacy.email) : '');
    html = html.replace(/\{\{pharmacy\.license\}\}/g, data.pharmacy.license ? this.escapeHtml(data.pharmacy.license) : '');

    // Transaction data
    html = html.replace(/\{\{transaction\.saleNumber\}\}/g, this.escapeHtml(data.transaction.saleNumber));
    html = html.replace(/\{\{transaction\.date\}\}/g, this.escapeHtml(data.transaction.date));
    html = html.replace(/\{\{transaction\.time\}\}/g, this.escapeHtml(data.transaction.time));
    html = html.replace(/\{\{transaction\.cashier\.name\}\}/g, this.escapeHtml(data.transaction.cashier.name));

    // Customer data
    html = html.replace(/\{\{customer\.name\}\}/g, this.escapeHtml(data.customer.name));
    html = html.replace(/\{\{customer\.phone\}\}/g, data.customer.phone ? this.escapeHtml(data.customer.phone) : '');
    html = html.replace(/\{\{customer\.membershipNumber\}\}/g, data.customer.membershipNumber ? this.escapeHtml(data.customer.membershipNumber) : '');

    // Totals
    html = html.replace(/\{\{formatCurrency totals\.subtotal\}\}/g, this.formatCurrency(data.totals.subtotal));
    html = html.replace(/\{\{formatCurrency totals\.discount\}\}/g, this.formatCurrency(data.totals.discount));
    html = html.replace(/\{\{formatCurrency totals\.tax\}\}/g, this.formatCurrency(data.totals.tax));
    html = html.replace(/\{\{formatCurrency totals\.total\}\}/g, this.formatCurrency(data.totals.total));

    // Payment data
    html = html.replace(/\{\{payment\.method\}\}/g, this.escapeHtml(data.payment.method));
    html = html.replace(/\{\{formatCurrency payment\.amountPaid\}\}/g, this.formatCurrency(data.payment.amountPaid));
    html = html.replace(/\{\{formatCurrency payment\.change\}\}/g, this.formatCurrency(data.payment.change));

    // Footer data
    html = html.replace(/\{\{footer\.thankYouMessage\}\}/g, this.escapeHtml(data.footer.thankYouMessage));
    html = html.replace(/\{\{footer\.returnPolicy\}\}/g, this.escapeHtml(data.footer.returnPolicy));
    html = html.replace(/\{\{footer\.notes\}\}/g, data.footer.notes ? this.escapeHtml(data.footer.notes) : '');

    // Formatting variables
    html = html.replace(/\{\{paperWidth\}\}/g, data.formatting.paperWidth.toString());
    html = html.replace(/\{\{headerFontSize\}\}/g, this.getFontSize(data.formatting.fontSizes.header));
    html = html.replace(/\{\{totalsFontSize\}\}/g, this.getFontSize(data.formatting.fontSizes.totals));

    // Current date/time
    html = html.replace(/\{\{currentDateTime\}\}/g, new Date().toLocaleString('id-ID'));

    // Handle conditional blocks and loops
    html = this.processConditionals(html, data);
    html = this.processItemsLoop(html, data.items);

    return html;
  }

  /**
   * Process conditional blocks ({{#if}} and {{/if}})
   */
  private processConditionals(html: string, data: ReceiptData): string {
    // Process pharmacy email conditional
    html = html.replace(/\{\{#if pharmacy\.email\}\}(.*?)\{\{\/if\}\}/gs,
      data.pharmacy.email ? '$1' : '');

    // Process pharmacy license conditional
    html = html.replace(/\{\{#if pharmacy\.license\}\}(.*?)\{\{\/if\}\}/gs,
      data.pharmacy.license ? '$1' : '');

    // Process customer phone conditional
    html = html.replace(/\{\{#if customer\.phone\}\}(.*?)\{\{\/if\}\}/gs,
      data.customer.phone ? '$1' : '');

    // Process customer membership number conditional
    html = html.replace(/\{\{#if customer\.membershipNumber\}\}(.*?)\{\{\/if\}\}/gs,
      data.customer.membershipNumber ? '$1' : '');

    // Process totals conditionals
    html = html.replace(/\{\{#if totals\.discount\}\}(.*?)\{\{\/if\}\}/gs,
      data.totals.discount > 0 ? '$1' : '');

    html = html.replace(/\{\{#if totals\.tax\}\}(.*?)\{\{\/if\}\}/gs,
      data.totals.tax > 0 ? '$1' : '');

    // Process payment change conditional
    html = html.replace(/\{\{#if payment\.change\}\}(.*?)\{\{\/if\}\}/gs,
      data.payment.change > 0 ? '$1' : '');

    // Process footer notes conditional
    html = html.replace(/\{\{#if footer\.notes\}\}(.*?)\{\{\/if\}\}/gs,
      data.footer.notes ? '$1' : '');

    return html;
  }

  /**
   * Process items loop ({{#each items}})
   */
  private processItemsLoop(html: string, items: ReceiptData['items']): string {
    const itemsRegex = /\{\{#each items\}\}(.*?)\{\{\/each\}\}/gs;
    const match = itemsRegex.exec(html);

    if (!match) return html;

    const itemTemplate = match[1];
    let itemsHtml = '';

    items.forEach(item => {
      let itemHtml = itemTemplate;

      // Replace item variables
      itemHtml = itemHtml.replace(/\{\{name\}\}/g, this.escapeHtml(item.name));
      itemHtml = itemHtml.replace(/\{\{code\}\}/g, this.escapeHtml(item.code));
      itemHtml = itemHtml.replace(/\{\{quantity\}\}/g, item.quantity.toString());
      itemHtml = itemHtml.replace(/\{\{unit\}\}/g, this.escapeHtml(item.unit));
      itemHtml = itemHtml.replace(/\{\{formatCurrency unitPrice\}\}/g, this.formatCurrency(item.unitPrice));
      itemHtml = itemHtml.replace(/\{\{formatCurrency subtotal\}\}/g, this.formatCurrency(item.subtotal));
      itemHtml = itemHtml.replace(/\{\{formatCurrency discount\}\}/g, this.formatCurrency(item.discount));
      itemHtml = itemHtml.replace(/\{\{manufacturer\}\}/g, item.manufacturer ? this.escapeHtml(item.manufacturer) : '');

      // Handle item conditionals
      itemHtml = itemHtml.replace(/\{\{#if manufacturer\}\}(.*?)\{\{\/if\}\}/gs,
        item.manufacturer ? '$1' : '');
      itemHtml = itemHtml.replace(/\{\{#if discount\}\}(.*?)\{\{\/if\}\}/gs,
        item.discount > 0 ? '$1' : '');

      itemsHtml += itemHtml;
    });

    return html.replace(itemsRegex, itemsHtml);
  }

  /**
   * Replace purchase order template placeholders with actual data
   */
  private replacePurchaseOrderVariables(template: string, data: PurchaseOrderReceiptData): string {
    let html = template;

    // Replace pharmacy/company information
    html = html.replace(/\{\{pharmacy\.name\}\}/g, this.escapeHtml(data.pharmacy.name));
    html = html.replace(/\{\{pharmacy\.address\}\}/g, this.escapeHtml(data.pharmacy.address));
    html = html.replace(/\{\{pharmacy\.phone\}\}/g, this.escapeHtml(data.pharmacy.phone));
    html = html.replace(/\{\{pharmacy\.email\}\}/g, data.pharmacy.email ? this.escapeHtml(data.pharmacy.email) : '');
    html = html.replace(/\{\{pharmacy\.license\}\}/g, data.pharmacy.license ? this.escapeHtml(data.pharmacy.license) : '');

    // Replace purchase order information
    html = html.replace(/\{\{purchaseOrder\.orderNumber\}\}/g, this.escapeHtml(data.purchaseOrder.orderNumber));
    html = html.replace(/\{\{purchaseOrder\.orderDate\}\}/g, this.escapeHtml(data.purchaseOrder.orderDate));
    html = html.replace(/\{\{purchaseOrder\.expectedDelivery\}\}/g, data.purchaseOrder.expectedDelivery ? this.escapeHtml(data.purchaseOrder.expectedDelivery) : '');
    html = html.replace(/\{\{purchaseOrder\.status\}\}/g, this.escapeHtml(data.purchaseOrder.status));
    html = html.replace(/\{\{purchaseOrder\.notes\}\}/g, data.purchaseOrder.notes ? this.escapeHtml(data.purchaseOrder.notes) : '');

    // Replace supplier information
    html = html.replace(/\{\{supplier\.name\}\}/g, this.escapeHtml(data.supplier.name));
    html = html.replace(/\{\{supplier\.code\}\}/g, this.escapeHtml(data.supplier.code));
    html = html.replace(/\{\{supplier\.address\}\}/g, data.supplier.address ? this.escapeHtml(data.supplier.address) : '');
    html = html.replace(/\{\{supplier\.phone\}\}/g, data.supplier.phone ? this.escapeHtml(data.supplier.phone) : '');
    html = html.replace(/\{\{supplier\.email\}\}/g, data.supplier.email ? this.escapeHtml(data.supplier.email) : '');

    // Replace created by information
    html = html.replace(/\{\{createdBy\.name\}\}/g, this.escapeHtml(data.createdBy.name));
    html = html.replace(/\{\{createdBy\.email\}\}/g, this.escapeHtml(data.createdBy.email));

    // Replace totals
    html = html.replace(/\{\{formatCurrency totals\.subtotal\}\}/g, this.formatCurrency(data.totals.subtotal));
    html = html.replace(/\{\{formatCurrency totals\.discountAmount\}\}/g, this.formatCurrency(data.totals.discountAmount));
    html = html.replace(/\{\{formatCurrency totals\.taxAmount\}\}/g, this.formatCurrency(data.totals.taxAmount));
    html = html.replace(/\{\{formatCurrency totals\.totalAmount\}\}/g, this.formatCurrency(data.totals.totalAmount));

    // Replace payment and delivery information
    html = html.replace(/\{\{payment\.method\}\}/g, data.payment.method ? this.escapeHtml(data.payment.method) : '');
    html = html.replace(/\{\{payment\.terms\}\}/g, data.payment.terms ? data.payment.terms.toString() : '');
    html = html.replace(/\{\{delivery\.address\}\}/g, data.delivery.address ? this.escapeHtml(data.delivery.address) : '');
    html = html.replace(/\{\{delivery\.contact\}\}/g, data.delivery.contact ? this.escapeHtml(data.delivery.contact) : '');

    // Replace footer information
    html = html.replace(/\{\{footer\.notes\}\}/g, data.footer.notes ? this.escapeHtml(data.footer.notes) : '');

    // Current date/time
    html = html.replace(/\{\{currentDateTime\}\}/g, new Date().toLocaleString('id-ID'));

    // Handle conditional blocks and loops
    html = this.processPurchaseOrderConditionals(html, data);
    html = this.processPurchaseOrderItemsLoop(html, data.items);

    return html;
  }

  /**
   * Format currency for Indonesian Rupiah
   */
  private formatCurrency(amount: number): string {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  }

  /**
   * Get CSS font size from formatting enum
   */
  private getFontSize(size: 'normal' | 'large' | 'small'): string {
    switch (size) {
      case 'large': return '16px';
      case 'small': return '10px';
      default: return '12px';
    }
  }

  /**
   * Escape HTML to prevent XSS
   */
  private escapeHtml(text: string): string {
    const div = { innerHTML: '' } as any;
    div.textContent = text;
    return div.innerHTML || text.replace(/[&<>"']/g, (match: string) => {
      const escapeMap: { [key: string]: string } = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#39;'
      };
      return escapeMap[match];
    });
  }

  /**
   * Process purchase order conditional blocks ({{#if}} and {{/if}})
   */
  private processPurchaseOrderConditionals(html: string, data: PurchaseOrderReceiptData): string {
    // Process pharmacy email conditional
    html = html.replace(/\{\{#if pharmacy\.email\}\}(.*?)\{\{\/if\}\}/gs,
      data.pharmacy.email ? '$1' : '');

    // Process pharmacy license conditional
    html = html.replace(/\{\{#if pharmacy\.license\}\}(.*?)\{\{\/if\}\}/gs,
      data.pharmacy.license ? '$1' : '');

    // Process purchase order expected delivery conditional
    html = html.replace(/\{\{#if purchaseOrder\.expectedDelivery\}\}(.*?)\{\{\/if\}\}/gs,
      data.purchaseOrder.expectedDelivery ? '$1' : '');

    // Process purchase order notes conditional
    html = html.replace(/\{\{#if purchaseOrder\.notes\}\}(.*?)\{\{\/if\}\}/gs,
      data.purchaseOrder.notes ? '$1' : '');

    // Process supplier conditionals
    html = html.replace(/\{\{#if supplier\.address\}\}(.*?)\{\{\/if\}\}/gs,
      data.supplier.address ? '$1' : '');
    html = html.replace(/\{\{#if supplier\.phone\}\}(.*?)\{\{\/if\}\}/gs,
      data.supplier.phone ? '$1' : '');
    html = html.replace(/\{\{#if supplier\.email\}\}(.*?)\{\{\/if\}\}/gs,
      data.supplier.email ? '$1' : '');

    // Process totals conditionals
    html = html.replace(/\{\{#if totals\.discountAmount\}\}(.*?)\{\{\/if\}\}/gs,
      data.totals.discountAmount > 0 ? '$1' : '');
    html = html.replace(/\{\{#if totals\.taxAmount\}\}(.*?)\{\{\/if\}\}/gs,
      data.totals.taxAmount > 0 ? '$1' : '');

    // Process payment and delivery conditionals
    html = html.replace(/\{\{#if payment\.method\}\}(.*?)\{\{\/if\}\}/gs,
      data.payment.method ? '$1' : '');
    html = html.replace(/\{\{#if payment\.terms\}\}(.*?)\{\{\/if\}\}/gs,
      data.payment.terms ? '$1' : '');
    html = html.replace(/\{\{#if delivery\.address\}\}(.*?)\{\{\/if\}\}/gs,
      data.delivery.address ? '$1' : '');
    html = html.replace(/\{\{#if delivery\.contact\}\}(.*?)\{\{\/if\}\}/gs,
      data.delivery.contact ? '$1' : '');

    // Process footer notes conditional
    html = html.replace(/\{\{#if footer\.notes\}\}(.*?)\{\{\/if\}\}/gs,
      data.footer.notes ? '$1' : '');

    return html;
  }

  /**
   * Process purchase order items loop ({{#each items}})
   */
  private processPurchaseOrderItemsLoop(html: string, items: PurchaseOrderReceiptData['items']): string {
    const itemsRegex = /\{\{#each items\}\}(.*?)\{\{\/each\}\}/gs;
    const match = itemsRegex.exec(html);

    if (!match) return html;

    const itemTemplate = match[1];
    let itemsHtml = '';

    items.forEach((item: PurchaseOrderReceiptData['items'][0], index: number) => {
      let itemHtml = itemTemplate;

      // Replace item variables
      itemHtml = itemHtml.replace(/\{\{@index\}\}/g, (index + 1).toString());
      itemHtml = itemHtml.replace(/\{\{productCode\}\}/g, this.escapeHtml(item.productCode));
      itemHtml = itemHtml.replace(/\{\{productName\}\}/g, this.escapeHtml(item.productName));
      itemHtml = itemHtml.replace(/\{\{manufacturer\}\}/g, item.manufacturer ? this.escapeHtml(item.manufacturer) : '');
      itemHtml = itemHtml.replace(/\{\{quantityOrdered\}\}/g, item.quantityOrdered.toString());
      itemHtml = itemHtml.replace(/\{\{unit\}\}/g, this.escapeHtml(item.unit));
      itemHtml = itemHtml.replace(/\{\{formatCurrency unitPrice\}\}/g, this.formatCurrency(item.unitPrice));
      itemHtml = itemHtml.replace(/\{\{formatCurrency discountAmount\}\}/g, this.formatCurrency(item.discountAmount));
      itemHtml = itemHtml.replace(/\{\{formatCurrency totalPrice\}\}/g, this.formatCurrency(item.totalPrice));
      itemHtml = itemHtml.replace(/\{\{notes\}\}/g, item.notes ? this.escapeHtml(item.notes) : '');

      // Handle item conditionals
      itemHtml = itemHtml.replace(/\{\{#if manufacturer\}\}(.*?)\{\{\/if\}\}/gs,
        item.manufacturer ? '$1' : '');
      itemHtml = itemHtml.replace(/\{\{#if notes\}\}(.*?)\{\{\/if\}\}/gs,
        item.notes ? '$1' : '');

      itemsHtml += itemHtml;
    });

    return html.replace(itemsRegex, itemsHtml);
  }

  /**
   * Fallback basic template if file loading fails
   */
  private getBasicReceiptTemplate(): string {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Receipt</title>
    <style>
        body { font-family: 'Courier New', monospace; font-size: 12px; margin: 0; padding: 10px; }
        .center { text-align: center; }
        .bold { font-weight: bold; }
    </style>
</head>
<body>
    <div class="center bold">{{pharmacy.name}}</div>
    <div class="center">{{pharmacy.address}}</div>
    <div class="center">{{pharmacy.phone}}</div>
    <hr>
    <div>No: {{transaction.saleNumber}}</div>
    <div>Tanggal: {{transaction.date}} {{transaction.time}}</div>
    <div>Kasir: {{transaction.cashier.name}}</div>
    <div>Pelanggan: {{customer.name}}</div>
    <hr>
    {{#each items}}
    <div>{{name}} - {{quantity}} {{unit}} x {{formatCurrency unitPrice}} = {{formatCurrency subtotal}}</div>
    {{/each}}
    <hr>
    <div class="bold">Total: {{formatCurrency totals.total}}</div>
    <div>Bayar: {{formatCurrency payment.amountPaid}}</div>
    <div>Kembalian: {{formatCurrency payment.change}}</div>
    <hr>
    <div class="center">{{footer.thankYouMessage}}</div>
</body>
</html>`;
  }

  /**
   * Fallback basic purchase order template if file loading fails
   */
  private getBasicPurchaseOrderTemplate(): string {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Purchase Order</title>
    <style>
        body { font-family: Arial, sans-serif; font-size: 12px; margin: 20px; }
        .header { text-align: center; margin-bottom: 20px; }
        .bold { font-weight: bold; }
        .info { margin: 10px 0; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { border: 1px solid #ccc; padding: 8px; text-align: left; }
        th { background: #f0f0f0; }
        .totals { text-align: right; margin-top: 20px; }
    </style>
</head>
<body>
    <div class="header">
        <div class="bold">{{pharmacy.name}}</div>
        <div>{{pharmacy.address}}</div>
        <div>{{pharmacy.phone}}</div>
    </div>
    <h2>PURCHASE ORDER</h2>
    <div class="info">
        <div><strong>PO Number:</strong> {{purchaseOrder.orderNumber}}</div>
        <div><strong>Date:</strong> {{purchaseOrder.orderDate}}</div>
        <div><strong>Supplier:</strong> {{supplier.name}} ({{supplier.code}})</div>
        <div><strong>Created by:</strong> {{createdBy.name}}</div>
    </div>
    <table>
        <thead>
            <tr>
                <th>No</th>
                <th>Product Code</th>
                <th>Product Name</th>
                <th>Qty</th>
                <th>Unit</th>
                <th>Unit Price</th>
                <th>Total</th>
            </tr>
        </thead>
        <tbody>
            {{#each items}}
            <tr>
                <td>{{@index}}</td>
                <td>{{productCode}}</td>
                <td>{{productName}}</td>
                <td>{{quantityOrdered}}</td>
                <td>{{unit}}</td>
                <td>{{formatCurrency unitPrice}}</td>
                <td>{{formatCurrency totalPrice}}</td>
            </tr>
            {{/each}}
        </tbody>
    </table>
    <div class="totals">
        <div><strong>Subtotal: {{formatCurrency totals.subtotal}}</strong></div>
        <div><strong>Tax: {{formatCurrency totals.taxAmount}}</strong></div>
        <div><strong>TOTAL: {{formatCurrency totals.totalAmount}}</strong></div>
    </div>
</body>
</html>`;
  }
}
