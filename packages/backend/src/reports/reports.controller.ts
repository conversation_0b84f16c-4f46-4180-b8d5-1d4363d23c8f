import { Controller, Get, Param, Res, NotFoundException, UseGuards } from '@nestjs/common';
import { Response } from 'express';
import * as fs from 'fs';
import * as path from 'path';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@Controller('reports')
@UseGuards(JwtAuthGuard)
export class ReportsController {
  private readonly reportsDir = path.join(process.cwd(), 'temp', 'reports');

  @Get('download/:fileName')
  async downloadReport(@Param('fileName') fileName: string, @Res() res: Response) {
    const filePath = path.join(this.reportsDir, fileName);

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      throw new NotFoundException('File laporan tidak ditemukan atau sudah kedaluwarsa');
    }

    // Get file stats
    const stats = fs.statSync(filePath);
    const now = new Date();
    const fileAge = now.getTime() - stats.mtime.getTime();
    const oneHour = 60 * 60 * 1000;

    // Check if file is expired (older than 1 hour)
    if (fileAge > oneHour) {
      // Delete expired file
      fs.unlinkSync(filePath);
      throw new NotFoundException('File laporan sudah kedaluwarsa');
    }

    // Determine content type based on file extension
    const ext = path.extname(fileName).toLowerCase();
    let contentType = 'application/octet-stream';
    
    switch (ext) {
      case '.pdf':
        contentType = 'application/pdf';
        break;
      case '.xlsx':
        contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
        break;
      case '.csv':
        contentType = 'text/csv';
        break;
    }

    // Set response headers
    res.setHeader('Content-Type', contentType);
    res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
    res.setHeader('Content-Length', stats.size);

    // Stream the file
    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(res);

    // Clean up file after download (optional)
    fileStream.on('end', () => {
      // Optionally delete the file after successful download
      // fs.unlinkSync(filePath);
    });
  }
}
