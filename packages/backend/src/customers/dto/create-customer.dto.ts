import { IsString, IsOptional, IsEmail, IsEnum, IsDateString, IsBoolean, IsInt, Min } from 'class-validator';
import { Transform } from 'class-transformer';
import { CustomerType } from '@prisma/client';

export class CreateCustomerDto {
  @IsOptional()
  @IsString()
  code?: string; // Will be auto-generated if not provided

  @IsEnum(CustomerType)
  type: CustomerType;

  // Personal Information
  @IsOptional()
  @IsString()
  firstName?: string;

  @IsOptional()
  @IsString()
  lastName?: string;

  @IsString()
  fullName: string; // Required for all customers

  @IsOptional()
  @IsString()
  phoneNumber?: string;

  @IsOptional()
  @IsEmail()
  email?: string;

  @IsOptional()
  @IsDateString()
  dateOfBirth?: string;

  @IsOptional()
  @IsString()
  gender?: string; // M/F

  // Address Information
  @IsOptional()
  @IsString()
  address?: string;

  @IsOptional()
  @IsString()
  city?: string;

  @IsOptional()
  @IsString()
  province?: string;

  @IsOptional()
  @IsString()
  postalCode?: string;

  // Membership Information
  @IsOptional()
  @IsString()
  membershipNumber?: string;

  @IsOptional()
  @IsString()
  membershipLevel?: string; // BRONZE, SILVER, GOLD, PLATINUM

  @IsOptional()
  @Transform(({ value }) => value ? parseInt(value) : 0)
  @IsInt()
  @Min(0)
  loyaltyPoints?: number;

  // Metadata
  @IsOptional()
  @IsString()
  notes?: string;

  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  @IsBoolean()
  isActive?: boolean;
}
