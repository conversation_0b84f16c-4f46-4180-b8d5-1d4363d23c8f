import { Module } from '@nestjs/common';
import { CustomersService } from './customers.service';
import { CustomersController } from './customers.controller';
import { CustomerCodeGeneratorService } from './customer-code-generator.service';
import { PrismaModule } from '../prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [CustomersController],
  providers: [CustomersService, CustomerCodeGeneratorService],
  exports: [CustomersService, CustomerCodeGeneratorService],
})
export class CustomersModule {}
