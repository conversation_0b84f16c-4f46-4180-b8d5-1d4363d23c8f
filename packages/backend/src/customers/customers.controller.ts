import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
  BadRequestException,
} from '@nestjs/common';
import { CustomersService } from './customers.service';
import { CreateCustomerDto } from './dto/create-customer.dto';
import { UpdateCustomerDto } from './dto/update-customer.dto';
import { CustomerQueryDto } from './dto/customer-query.dto';
import { CustomerCodeGeneratorService } from './customer-code-generator.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ManagerGuard } from '../suppliers/guards/manager.guard';

@Controller('customers')
@UseGuards(JwtAuthGuard)
export class CustomersController {
  constructor(
    private readonly customersService: CustomersService,
    private readonly customerCodeGeneratorService: CustomerCodeGeneratorService,
  ) {}

  @Post()
  async create(@Body() createCustomerDto: CreateCustomerDto, @Request() req) {
    return this.customersService.create(createCustomerDto, req.user.id);
  }

  @Get()
  async findAll(@Query() query: CustomerQueryDto) {
    return this.customersService.findAll(query);
  }

  @Get('stats')
  async getStats() {
    return this.customersService.getStats();
  }

  // Customer code generation endpoints (must come before parameterized routes)
  @Get('generate-code/:type')
  async generateCustomerCode(@Param('type') type: string) {
    // Validate customer type
    const validTypes = ['WALK_IN', 'REGISTERED'];
    if (!validTypes.includes(type)) {
      throw new BadRequestException('Jenis pelanggan tidak valid');
    }

    return {
      code: await this.customerCodeGeneratorService.generateCustomerCode(type as any),
    };
  }

  @Get('validate-code/:code')
  async validateCustomerCode(@Param('code') code: string) {
    return {
      isUnique: await this.customerCodeGeneratorService.validateCodeUniqueness(code),
    };
  }

  @Patch(':id/deactivate')
  @UseGuards(ManagerGuard) // Admin or Pharmacist can deactivate
  async deactivate(@Param('id') id: string, @Request() req) {
    return this.customersService.deactivate(id, req.user.id);
  }

  @Patch(':id/activate')
  @UseGuards(ManagerGuard) // Admin or Pharmacist can activate
  async activate(@Param('id') id: string, @Request() req) {
    return this.customersService.activate(id, req.user.id);
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    return this.customersService.findOne(id);
  }

  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updateCustomerDto: UpdateCustomerDto,
    @Request() req,
  ) {
    return this.customersService.update(id, updateCustomerDto, req.user.id);
  }

  @Delete(':id')
  @UseGuards(ManagerGuard) // Admin or Pharmacist can delete
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id') id: string) {
    await this.customersService.remove(id);
  }
}
