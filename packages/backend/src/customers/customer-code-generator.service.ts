import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CustomerType } from '@prisma/client';

@Injectable()
export class CustomerCodeGeneratorService {
  constructor(private prisma: PrismaService) {}

  async generateCustomerCode(type: CustomerType): Promise<string> {
    const prefix = this.getCodePrefix(type);
    const today = new Date();
    const year = today.getFullYear().toString().slice(-2);
    const month = (today.getMonth() + 1).toString().padStart(2, '0');
    
    // Find the highest sequence number for today
    const datePrefix = `${prefix}${year}${month}`;
    const existingCodes = await this.prisma.customer.findMany({
      where: {
        code: {
          startsWith: datePrefix,
        },
      },
      select: {
        code: true,
      },
      orderBy: {
        code: 'desc',
      },
      take: 1,
    });

    let sequence = 1;
    if (existingCodes.length > 0) {
      const lastCode = existingCodes[0].code;
      const lastSequence = parseInt(lastCode.slice(-3));
      sequence = lastSequence + 1;
    }

    return `${datePrefix}${sequence.toString().padStart(3, '0')}`;
  }

  private getCodePrefix(type: CustomerType): string {
    switch (type) {
      case 'WALK_IN':
        return 'WI'; // Walk-In
      case 'REGISTERED':
        return 'REG'; // Registered
      default:
        return 'CUS'; // Customer
    }
  }

  async validateCodeUniqueness(code: string): Promise<boolean> {
    const existing = await this.prisma.customer.findUnique({
      where: { code },
    });
    return !existing;
  }
}
