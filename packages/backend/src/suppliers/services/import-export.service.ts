import { Injectable, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { ValidationUtils } from '../../common/utils/validation.utils';
import { ImportSupplierDto, ImportResultDto, ImportErrorDto, ExportQueryDto } from '../dto/import-supplier.dto';
import { SupplierType, PaymentMethod, SupplierStatus } from '@prisma/client';
import * as csv from 'csv-parser';
import * as csvWriter from 'csv-writer';
import * as XLSX from 'xlsx';
import { createReadStream, createWriteStream } from 'fs';
import { join } from 'path';

@Injectable()
export class ImportExportService {
  constructor(private prisma: PrismaService) {}

  /**
   * Generate CSV template for supplier import
   */
  async generateTemplate(): Promise<string> {
    const templatePath = join(process.cwd(), 'uploads', 'templates', 'supplier-template.csv');
    
    // Ensure directory exists
    const fs = require('fs');
    const dir = join(process.cwd(), 'uploads', 'templates');
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    const writer = csvWriter.createObjectCsvWriter({
      path: templatePath,
      header: [
        { id: 'code', title: 'Kode Supplier' },
        { id: 'name', title: 'Nama Supplier' },
        { id: 'type', title: 'Jenis (PBF/DISTRIBUTOR/MANUFACTURER/LOCAL)' },
        { id: 'address', title: 'Alamat' },
        { id: 'city', title: 'Kota' },
        { id: 'province', title: 'Provinsi' },
        { id: 'postalCode', title: 'Kode Pos' },
        { id: 'phone', title: 'Telepon' },
        { id: 'email', title: 'Email' },
        { id: 'website', title: 'Website' },
        { id: 'npwp', title: 'NPWP' },
        { id: 'licenseNumber', title: 'Nomor Izin' },
        { id: 'pharmacyLicense', title: 'Izin Apotek/PBF' },
        { id: 'creditLimit', title: 'Limit Kredit' },
        { id: 'paymentTerms', title: 'Termin Pembayaran (hari)' },
        { id: 'preferredPayment', title: 'Metode Pembayaran (CASH/TRANSFER/CREDIT)' },
        { id: 'bankName', title: 'Nama Bank' },
        { id: 'bankAccount', title: 'Nomor Rekening' },
        { id: 'bankAccountName', title: 'Nama Pemegang Rekening' },
        { id: 'notes', title: 'Catatan' },
      ],
    });

    // Sample data
    const sampleData = [
      {
        code: 'SUP001',
        name: 'PT Kimia Farma Trading & Distribution',
        type: 'PBF',
        address: 'Jl. Veteran No. 9',
        city: 'Jakarta Pusat',
        province: 'DKI Jakarta',
        postalCode: '10110',
        phone: '+62 21 3441991',
        email: '<EMAIL>',
        website: 'https://www.kimiafarma.co.id',
        npwp: '************-001.001',
        licenseNumber: 'PBF.0001/31.74.02.1009.13.07/2024',
        pharmacyLicense: 'SIA.0001/DINKES/31.74/2024',
        creditLimit: '********',
        paymentTerms: '30',
        preferredPayment: 'TRANSFER',
        bankName: 'Bank Mandiri',
        bankAccount: '**********',
        bankAccountName: 'PT Kimia Farma Trading & Distribution',
        notes: 'Supplier utama untuk produk farmasi',
      },
      {
        code: 'SUP002',
        name: 'PT Kalbe Farma Tbk',
        type: 'MANUFACTURER',
        address: 'Jl. Letjen Suprapto Kav. 4',
        city: 'Jakarta Pusat',
        province: 'DKI Jakarta',
        postalCode: '10510',
        phone: '+62 21 ********',
        email: '<EMAIL>',
        website: 'https://www.kalbe.co.id',
        npwp: '************-002.002',
        licenseNumber: 'MFG.0002/31.74.02.1009.13.07/2024',
        pharmacyLicense: '',
        creditLimit: '*********',
        paymentTerms: '45',
        preferredPayment: 'TRANSFER',
        bankName: 'Bank BCA',
        bankAccount: '**********',
        bankAccountName: 'PT Kalbe Farma Tbk',
        notes: 'Manufacturer produk obat dan suplemen',
      },
    ];

    await writer.writeRecords(sampleData);

    return templatePath;
  }

  /**
   * Process CSV import file
   */
  async processImport(filePath: string, userId: string): Promise<ImportResultDto> {
    const results: ImportSupplierDto[] = [];
    const errors: ImportErrorDto[] = [];
    let rowNumber = 0;

    return new Promise((resolve, reject) => {
      createReadStream(filePath)
        .pipe(csv())
        .on('data', (data) => {
          rowNumber++;
          try {
            const result = this.validateAndTransformRow(data, rowNumber);
            if (result.errors.errors.length > 0) {
              errors.push(result.errors);
            } else if (result.data) {
              results.push(result.data);
            }
          } catch (error) {
            errors.push({
              row: rowNumber,
              code: data.code || '',
              name: data.name || '',
              errors: [`Error parsing row: ${error.message}`],
            });
          }
        })
        .on('end', async () => {
          try {
            const importResult = await this.importSuppliers(results, userId);
            resolve({
              success: true,
              totalRows: rowNumber,
              successfulImports: importResult.successful,
              failedImports: importResult.failed.length + errors.length,
              errors: [...errors, ...importResult.failed],
              importedSuppliers: importResult.imported,
            });
          } catch (error) {
            reject(error);
          }
        })
        .on('error', (error) => {
          reject(new BadRequestException(`Error reading CSV file: ${error.message}`));
        });
    });
  }

  /**
   * Validate and transform a single row from CSV
   */
  private validateAndTransformRow(data: any, rowNumber: number): { data?: ImportSupplierDto; errors: ImportErrorDto } {
    const errors: string[] = [];
    const supplier: Partial<ImportSupplierDto> = {};



    // Required fields validation
    if (!data['Kode Supplier'] || !data['Kode Supplier'].trim()) {
      errors.push('Kode supplier wajib diisi');
    } else {
      supplier.code = data['Kode Supplier'].trim();
    }

    if (!data['Nama Supplier'] || !data['Nama Supplier'].trim()) {
      errors.push('Nama supplier wajib diisi');
    } else {
      supplier.name = data['Nama Supplier'].trim();
    }

    // Supplier type validation
    const typeMapping = {
      'PBF': SupplierType.PBF,
      'DISTRIBUTOR': SupplierType.DISTRIBUTOR,
      'MANUFACTURER': SupplierType.MANUFACTURER,
      'LOCAL': SupplierType.LOCAL,
    };

    const typeValue = data['Jenis (PBF/DISTRIBUTOR/MANUFACTURER/LOCAL)']?.trim().toUpperCase();
    if (!typeValue || !typeMapping[typeValue]) {
      errors.push('Jenis supplier tidak valid. Harus salah satu dari: PBF, DISTRIBUTOR, MANUFACTURER, LOCAL');
    } else {
      supplier.type = typeMapping[typeValue];
    }

    // Optional fields with validation
    if (data['Email'] && data['Email'].trim()) {
      if (!ValidationUtils.validateEmail(data['Email'].trim())) {
        errors.push('Format email tidak valid');
      } else {
        supplier.email = data['Email'].trim();
      }
    }

    if (data['Website'] && data['Website'].trim()) {
      if (!ValidationUtils.validateURL(data['Website'].trim())) {
        errors.push('Format website tidak valid');
      } else {
        supplier.website = data['Website'].trim();
      }
    }

    if (data['NPWP'] && data['NPWP'].trim()) {
      if (!ValidationUtils.validateNPWP(data['NPWP'].trim())) {
        errors.push('Format NPWP tidak valid. Format yang benar: XX.XXX.XXX.X-XXX.XXX');
      } else {
        const formattedNPWP = ValidationUtils.formatNPWP(data['NPWP'].trim());
        if (formattedNPWP) {
          supplier.npwp = formattedNPWP;
        }
      }
    }

    if (data['Telepon'] && data['Telepon'].trim()) {
      if (!ValidationUtils.validateIndonesianPhone(data['Telepon'].trim())) {
        errors.push('Format nomor telepon tidak valid');
      } else {
        supplier.phone = data['Telepon'].trim();
      }
    }

    if (data['Kode Pos'] && data['Kode Pos'].trim()) {
      if (!ValidationUtils.validateIndonesianPostalCode(data['Kode Pos'].trim())) {
        errors.push('Format kode pos tidak valid. Harus 5 digit');
      } else {
        supplier.postalCode = data['Kode Pos'].trim();
      }
    }

    // Numeric fields
    if (data['Limit Kredit'] && data['Limit Kredit'].trim()) {
      const creditLimit = ValidationUtils.parseCurrency(data['Limit Kredit'].trim());
      if (creditLimit === null || !ValidationUtils.validatePositiveNumber(creditLimit)) {
        errors.push('Limit kredit harus berupa angka positif');
      } else {
        supplier.creditLimit = creditLimit;
      }
    }

    if (data['Termin Pembayaran (hari)'] && data['Termin Pembayaran (hari)'].trim()) {
      const paymentTerms = Number(data['Termin Pembayaran (hari)'].trim());
      if (isNaN(paymentTerms) || !ValidationUtils.validatePositiveNumber(paymentTerms)) {
        errors.push('Termin pembayaran harus berupa angka positif');
      } else {
        supplier.paymentTerms = paymentTerms;
      }
    }

    // Payment method validation
    const paymentMapping = {
      'CASH': PaymentMethod.CASH,
      'TRANSFER': PaymentMethod.TRANSFER,
      'CREDIT': PaymentMethod.CREDIT,
    };

    const paymentValue = data['Metode Pembayaran (CASH/TRANSFER/CREDIT)']?.trim().toUpperCase();
    if (paymentValue && !paymentMapping[paymentValue]) {
      errors.push('Metode pembayaran tidak valid. Harus salah satu dari: CASH, TRANSFER, CREDIT');
    } else if (paymentValue) {
      supplier.preferredPayment = paymentMapping[paymentValue];
    }

    // Simple string fields
    const stringFields = [
      { csv: 'Alamat', field: 'address' },
      { csv: 'Kota', field: 'city' },
      { csv: 'Provinsi', field: 'province' },
      { csv: 'Nomor Izin', field: 'licenseNumber' },
      { csv: 'Izin Apotek/PBF', field: 'pharmacyLicense' },
      { csv: 'Nama Bank', field: 'bankName' },
      { csv: 'Nomor Rekening', field: 'bankAccount' },
      { csv: 'Nama Pemegang Rekening', field: 'bankAccountName' },
      { csv: 'Catatan', field: 'notes' },
    ];

    stringFields.forEach(({ csv, field }) => {
      if (data[csv] && data[csv].trim()) {
        supplier[field] = data[csv].trim();
      }
    });



    return {
      data: errors.length === 0 ? supplier as ImportSupplierDto : undefined,
      errors: {
        row: rowNumber,
        code: supplier.code || '',
        name: supplier.name || '',
        errors,
      },
    };
  }

  /**
   * Import validated suppliers to database
   */
  private async importSuppliers(suppliers: ImportSupplierDto[], userId: string): Promise<{
    successful: number;
    failed: ImportErrorDto[];
    imported: any[];
  }> {
    const failed: ImportErrorDto[] = [];
    const imported: any[] = [];
    let successful = 0;

    for (const [index, supplierData] of suppliers.entries()) {
      try {
        // Check if supplier code already exists
        const existingSupplier = await this.prisma.supplier.findUnique({
          where: { code: supplierData.code },
        });

        if (existingSupplier) {
          failed.push({
            row: index + 1,
            code: supplierData.code,
            name: supplierData.name,
            errors: ['Kode supplier sudah digunakan'],
          });
          continue;
        }

        // Create supplier
        const supplier = await this.prisma.supplier.create({
          data: {
            ...supplierData,
            status: SupplierStatus.ACTIVE,
            creditLimit: supplierData.creditLimit ? new (require('@prisma/client')).Prisma.Decimal(supplierData.creditLimit) : null,
            createdBy: userId,
            updatedBy: userId,
          },
          include: {
            createdByUser: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        });

        imported.push(supplier);
        successful++;
      } catch (error) {
        failed.push({
          row: index + 1,
          code: supplierData.code,
          name: supplierData.name,
          errors: [`Database error: ${error.message}`],
        });
      }
    }

    return { successful, failed, imported };
  }

  /**
   * Export suppliers to CSV or XLSX
   */
  async exportSuppliers(query: ExportQueryDto): Promise<string> {
    // Build where clause based on query
    const where: any = {};

    if (query.search) {
      where.OR = [
        { name: { contains: query.search, mode: 'insensitive' } },
        { code: { contains: query.search, mode: 'insensitive' } },
        { email: { contains: query.search, mode: 'insensitive' } },
      ];
    }

    if (query.type) {
      where.type = query.type;
    }

    if (query.status) {
      where.status = query.status;
    }

    if (query.province) {
      where.province = { contains: query.province, mode: 'insensitive' };
    }

    if (query.ids && query.ids.length > 0) {
      where.id = { in: query.ids };
    }

    // Fetch suppliers
    const suppliers = await this.prisma.supplier.findMany({
      where,
      include: {
        createdByUser: {
          select: {
            firstName: true,
            lastName: true,
          },
        },
        _count: {
          select: {
            contacts: true,
            documents: true,
            payments: true,
          },
        },
      },
      orderBy: { name: 'asc' },
    });

    const format = query.format || 'csv';
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `suppliers-export-${timestamp}.${format}`;
    const filePath = join(process.cwd(), 'uploads', 'exports', filename);

    // Ensure directory exists
    const fs = require('fs');
    const dir = join(process.cwd(), 'uploads', 'exports');
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    if (format === 'xlsx') {
      await this.exportToXLSX(suppliers, filePath);
    } else {
      await this.exportToCSV(suppliers, filePath);
    }

    return filePath;
  }

  /**
   * Export to CSV format
   */
  private async exportToCSV(suppliers: any[], filePath: string): Promise<void> {
    const writer = csvWriter.createObjectCsvWriter({
      path: filePath,
      header: [
        { id: 'code', title: 'Kode Supplier' },
        { id: 'name', title: 'Nama Supplier' },
        { id: 'type', title: 'Jenis' },
        { id: 'status', title: 'Status' },
        { id: 'address', title: 'Alamat' },
        { id: 'city', title: 'Kota' },
        { id: 'province', title: 'Provinsi' },
        { id: 'postalCode', title: 'Kode Pos' },
        { id: 'phone', title: 'Telepon' },
        { id: 'email', title: 'Email' },
        { id: 'website', title: 'Website' },
        { id: 'npwp', title: 'NPWP' },
        { id: 'licenseNumber', title: 'Nomor Izin' },
        { id: 'pharmacyLicense', title: 'Izin Apotek/PBF' },
        { id: 'creditLimit', title: 'Limit Kredit' },
        { id: 'paymentTerms', title: 'Termin Pembayaran' },
        { id: 'preferredPayment', title: 'Metode Pembayaran' },
        { id: 'bankName', title: 'Nama Bank' },
        { id: 'bankAccount', title: 'Nomor Rekening' },
        { id: 'bankAccountName', title: 'Nama Pemegang Rekening' },
        { id: 'contactsCount', title: 'Jumlah Kontak' },
        { id: 'documentsCount', title: 'Jumlah Dokumen' },
        { id: 'paymentsCount', title: 'Jumlah Pembayaran' },
        { id: 'createdBy', title: 'Dibuat Oleh' },
        { id: 'createdAt', title: 'Tanggal Dibuat' },
        { id: 'notes', title: 'Catatan' },
      ],
    });

    const records = suppliers.map(supplier => ({
      code: supplier.code,
      name: supplier.name,
      type: supplier.type,
      status: supplier.status,
      address: supplier.address || '',
      city: supplier.city || '',
      province: supplier.province || '',
      postalCode: supplier.postalCode || '',
      phone: supplier.phone || '',
      email: supplier.email || '',
      website: supplier.website || '',
      npwp: supplier.npwp || '',
      licenseNumber: supplier.licenseNumber || '',
      pharmacyLicense: supplier.pharmacyLicense || '',
      creditLimit: supplier.creditLimit ? supplier.creditLimit.toString() : '',
      paymentTerms: supplier.paymentTerms || '',
      preferredPayment: supplier.preferredPayment || '',
      bankName: supplier.bankName || '',
      bankAccount: supplier.bankAccount || '',
      bankAccountName: supplier.bankAccountName || '',
      contactsCount: supplier._count.contacts,
      documentsCount: supplier._count.documents,
      paymentsCount: supplier._count.payments,
      createdBy: supplier.createdByUser ? `${supplier.createdByUser.firstName} ${supplier.createdByUser.lastName}` : '',
      createdAt: new Date(supplier.createdAt).toLocaleDateString('id-ID'),
      notes: supplier.notes || '',
    }));

    await writer.writeRecords(records);
  }

  /**
   * Export to XLSX format
   */
  private async exportToXLSX(suppliers: any[], filePath: string): Promise<void> {
    const records = suppliers.map(supplier => ({
      'Kode Supplier': supplier.code,
      'Nama Supplier': supplier.name,
      'Jenis': supplier.type,
      'Status': supplier.status,
      'Alamat': supplier.address || '',
      'Kota': supplier.city || '',
      'Provinsi': supplier.province || '',
      'Kode Pos': supplier.postalCode || '',
      'Telepon': supplier.phone || '',
      'Email': supplier.email || '',
      'Website': supplier.website || '',
      'NPWP': supplier.npwp || '',
      'Nomor Izin': supplier.licenseNumber || '',
      'Izin Apotek/PBF': supplier.pharmacyLicense || '',
      'Limit Kredit': supplier.creditLimit ? supplier.creditLimit.toString() : '',
      'Termin Pembayaran': supplier.paymentTerms || '',
      'Metode Pembayaran': supplier.preferredPayment || '',
      'Nama Bank': supplier.bankName || '',
      'Nomor Rekening': supplier.bankAccount || '',
      'Nama Pemegang Rekening': supplier.bankAccountName || '',
      'Jumlah Kontak': supplier._count.contacts,
      'Jumlah Dokumen': supplier._count.documents,
      'Jumlah Pembayaran': supplier._count.payments,
      'Dibuat Oleh': supplier.createdByUser ? `${supplier.createdByUser.firstName} ${supplier.createdByUser.lastName}` : '',
      'Tanggal Dibuat': new Date(supplier.createdAt).toLocaleDateString('id-ID'),
      'Catatan': supplier.notes || '',
    }));

    const worksheet = XLSX.utils.json_to_sheet(records);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Suppliers');

    XLSX.writeFile(workbook, filePath);
  }
}
