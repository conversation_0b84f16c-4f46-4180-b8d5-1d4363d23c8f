import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { UserRole } from '@prisma/client';

@Injectable()
export class ManagerGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user) {
      throw new ForbiddenException('User not authenticated');
    }

    // Allow ADMIN and PHARMACIST roles
    const allowedRoles = [UserRole.ADMIN, UserRole.PHARMACIST];
    
    if (!allowedRoles.includes(user.role)) {
      throw new ForbiddenException('Akses ditolak. Hanya admin dan apoteker yang dapat melakukan aksi ini.');
    }

    return true;
  }
}
