import { Module } from '@nestjs/common';
import { SuppliersService } from './suppliers.service';
import { SuppliersController } from './suppliers.controller';
import { PrismaModule } from '../prisma/prisma.module';
import { SettingsModule } from '../settings/settings.module';
import { FileUploadService } from '../common/services/file-upload.service';
import { ReferenceGeneratorService } from '../common/services/reference-generator.service';
import { ImportExportService } from './services/import-export.service';
import { SupplierCodeGeneratorService } from './supplier-code-generator.service';

@Module({
  imports: [PrismaModule, SettingsModule],
  controllers: [SuppliersController],
  providers: [SuppliersService, FileUploadService, ReferenceGeneratorService, ImportExportService, SupplierCodeGeneratorService],
  exports: [SuppliersService],
})
export class SuppliersModule {}
