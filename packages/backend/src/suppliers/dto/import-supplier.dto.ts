import { IsString, <PERSON>Optional, IsEmail, IsUrl, IsEnum, IsNumber, IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { SupplierType, PaymentMethod } from '@prisma/client';

export class ImportSupplierDto {
  @IsString()
  code: string;

  @IsString()
  name: string;

  @IsEnum(SupplierType)
  type: SupplierType;

  // Contact Information
  @IsOptional()
  @IsString()
  address?: string;

  @IsOptional()
  @IsString()
  city?: string;

  @IsOptional()
  @IsString()
  province?: string;

  @IsOptional()
  @IsString()
  postalCode?: string;

  @IsOptional()
  @IsString()
  phone?: string;

  @IsOptional()
  @IsEmail()
  email?: string;

  @IsOptional()
  @IsUrl()
  website?: string;

  // Business Information
  @IsOptional()
  @IsString()
  npwp?: string;

  @IsOptional()
  @IsString()
  licenseNumber?: string;

  @IsOptional()
  @IsString()
  pharmacyLicense?: string;

  // Financial Information
  @IsOptional()
  @IsNumber()
  creditLimit?: number;

  @IsOptional()
  @IsNumber()
  paymentTerms?: number;

  @IsOptional()
  @IsEnum(PaymentMethod)
  preferredPayment?: PaymentMethod;

  // Bank Information
  @IsOptional()
  @IsString()
  bankName?: string;

  @IsOptional()
  @IsString()
  bankAccount?: string;

  @IsOptional()
  @IsString()
  bankAccountName?: string;

  @IsOptional()
  @IsString()
  notes?: string;
}

export class ImportResultDto {
  success: boolean;
  totalRows: number;
  successfulImports: number;
  failedImports: number;
  errors: ImportErrorDto[];
  importedSuppliers?: any[];
}

export class ImportErrorDto {
  row: number;
  code?: string;
  name?: string;
  errors: string[];
}

export class ExportQueryDto {
  @IsOptional()
  @IsString()
  format?: 'csv' | 'xlsx';

  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @IsEnum(SupplierType)
  type?: SupplierType;

  @IsOptional()
  @IsString()
  status?: string;

  @IsOptional()
  @IsString()
  province?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  ids?: string[];
}
