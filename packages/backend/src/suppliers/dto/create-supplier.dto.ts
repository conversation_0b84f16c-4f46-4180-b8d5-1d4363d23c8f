import { IsString, IsEmail, IsOptional, IsEnum, IsInt, IsUrl, IsBoolean, ValidateNested, IsArray } from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { SupplierType, PaymentMethod } from '@prisma/client';

export class CreateSupplierContactDto {
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  position?: string;

  @IsOptional()
  @IsString()
  phone?: string;

  @IsOptional()
  @IsEmail()
  email?: string;

  @IsOptional()
  @IsBoolean()
  isPrimary?: boolean;

  @IsOptional()
  @IsString()
  notes?: string;
}

export class UpdateSupplierContactDto extends CreateSupplierContactDto {
  @IsOptional()
  @IsString()
  id?: string;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class CreateSupplierDto {
  @IsString()
  code: string;

  @IsString()
  name: string;

  @IsEnum(SupplierType)
  type: SupplierType;

  // Contact Information
  @IsOptional()
  @IsString()
  address?: string;

  @IsOptional()
  @IsString()
  city?: string;

  @IsOptional()
  @IsString()
  province?: string;

  @IsOptional()
  @IsString()
  postalCode?: string;

  @IsOptional()
  @IsString()
  phone?: string;

  @IsOptional()
  @IsEmail()
  email?: string;

  @IsOptional()
  @IsUrl()
  website?: string;

  // Business Information
  @IsOptional()
  @IsString()
  npwp?: string;

  @IsOptional()
  @IsString()
  licenseNumber?: string;

  @IsOptional()
  @IsString()
  pharmacyLicense?: string;

  // Financial Information
  @IsOptional()
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  creditLimit?: number;

  @IsOptional()
  @IsInt()
  paymentTerms?: number;

  @IsOptional()
  @IsEnum(PaymentMethod)
  preferredPayment?: PaymentMethod;

  // Bank Information
  @IsOptional()
  @IsString()
  bankName?: string;

  @IsOptional()
  @IsString()
  bankAccount?: string;

  @IsOptional()
  @IsString()
  bankAccountName?: string;

  @IsOptional()
  @IsString()
  notes?: string;

  // Contacts
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateSupplierContactDto)
  contacts?: CreateSupplierContactDto[];
}
