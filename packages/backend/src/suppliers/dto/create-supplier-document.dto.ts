import { IsString, IsOptional, IsEnum, IsDateString, IsInt } from 'class-validator';
import { DocumentType } from '@prisma/client';

export class CreateSupplierDocumentDto {
  @IsEnum(DocumentType)
  type: DocumentType;

  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsString()
  fileName?: string;

  @IsOptional()
  @IsString()
  filePath?: string;

  @IsOptional()
  @IsInt()
  fileSize?: number;

  @IsOptional()
  @IsString()
  mimeType?: string;

  @IsOptional()
  @IsDateString()
  expiryDate?: string;
}
