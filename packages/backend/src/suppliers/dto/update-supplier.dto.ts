import { PartialType } from '@nestjs/mapped-types';
import { CreateSupplierDto, UpdateSupplierContactDto } from './create-supplier.dto';
import { IsEnum, IsOptional, IsArray, ValidateNested } from 'class-validator';
import { SupplierStatus } from '@prisma/client';
import { Type } from 'class-transformer';

export class UpdateSupplierDto extends PartialType(CreateSupplierDto) {
  @IsOptional()
  @IsEnum(SupplierStatus)
  status?: SupplierStatus;

  // Override contacts to use UpdateSupplierContactDto
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpdateSupplierContactDto)
  contacts?: UpdateSupplierContactDto[];
}
