import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { SupplierType } from '@prisma/client';

@Injectable()
export class SupplierCodeGeneratorService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Generate supplier code based on type with format {PREFIX}-{SEQUENCE}
   * Examples: PBF-001, DIST-002, MFG-003, SUP-004
   */
  async generateSupplierCode(type: SupplierType): Promise<string> {
    try {
      const prefix = this.getSupplierTypePrefix(type);
      const sequence = await this.getNextSequenceNumber(prefix);
      const code = `${prefix}-${sequence.toString().padStart(3, '0')}`;
      
      // Ensure the generated code is unique (double-check)
      const isUnique = await this.validateCodeUniqueness(code);
      if (!isUnique) {
        // If somehow not unique, try with next sequence
        const nextSequence = await this.getNextSequenceNumber(prefix);
        return `${prefix}-${nextSequence.toString().padStart(3, '0')}`;
      }
      
      return code;
    } catch (error) {
      console.error('Error generating supplier code:', error);
      // Fallback code with timestamp
      const timestamp = Date.now().toString().slice(-3);
      return `SUP-${timestamp}`;
    }
  }

  /**
   * Validate if a supplier code is unique
   */
  async validateCodeUniqueness(code: string): Promise<boolean> {
    const existingSupplier = await this.prisma.supplier.findFirst({
      where: { code },
    });
    
    return !existingSupplier;
  }

  /**
   * Get the next sequence number for a given prefix
   */
  private async getNextSequenceNumber(prefix: string): Promise<number> {
    // Find the highest sequence number for this prefix
    const suppliers = await this.prisma.supplier.findMany({
      where: {
        code: {
          startsWith: `${prefix}-`,
        },
      },
      select: {
        code: true,
      },
      orderBy: {
        code: 'desc',
      },
    });

    let maxSequence = 0;
    
    for (const supplier of suppliers) {
      const codeParts = supplier.code.split('-');
      if (codeParts.length === 2 && codeParts[0] === prefix) {
        const sequence = parseInt(codeParts[1], 10);
        if (!isNaN(sequence) && sequence > maxSequence) {
          maxSequence = sequence;
        }
      }
    }

    return maxSequence + 1;
  }

  /**
   * Get prefix based on supplier type
   */
  private getSupplierTypePrefix(type: SupplierType): string {
    switch (type) {
      case SupplierType.PBF:
        return 'PBF';
      case SupplierType.DISTRIBUTOR:
        return 'DIST';
      case SupplierType.MANUFACTURER:
        return 'MFG';
      case SupplierType.LOCAL:
        return 'SUP';
      default:
        return 'SUP';
    }
  }

  /**
   * Generate a new code if the provided one is not unique
   */
  async ensureUniqueCode(proposedCode?: string, type?: SupplierType): Promise<string> {
    // If no code provided, generate a new one
    if (!proposedCode || !type) {
      return this.generateSupplierCode(type || SupplierType.LOCAL);
    }

    // If provided code is unique, use it
    const isUnique = await this.validateCodeUniqueness(proposedCode);
    if (isUnique) {
      return proposedCode;
    }

    // If not unique, generate a new one
    console.warn(`Supplier code ${proposedCode} is not unique, generating new one`);
    return this.generateSupplierCode(type);
  }
}
