import { Module } from '@nestjs/common';
import { InventoryService } from './inventory.service';
import { InventoryController } from './inventory.controller';
import { StockAllocationService } from './services/stock-allocation.service';
import { StockConsumptionService } from './services/stock-consumption.service';
import { AllocationReportService } from './services/allocation-report.service';
import { InventoryReportService } from './services/inventory-report.service';
import { PrismaModule } from '../prisma/prisma.module';
import { UnitConversionService } from '../common/services/unit-conversion.service';

@Module({
  imports: [PrismaModule],
  controllers: [InventoryController],
  providers: [
    InventoryService,
    StockAllocationService,
    StockConsumptionService,
    AllocationReportService,
    InventoryReportService,
    UnitConversionService
  ],
  exports: [
    InventoryService,
    StockAllocationService,
    StockConsumptionService,
    AllocationReportService,
    InventoryReportService,
    UnitConversionService
  ],
})
export class InventoryModule { }
