import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { StockMovementType } from '@prisma/client';
import { ReferenceType } from '../dto/reference-types.enum';
import { UnitConversionService } from '../../common/services/unit-conversion.service';

export interface ConsumedItem {
  productName: string;
  batchNumber: string;
  quantity: number;
  unit: string;
  movementId: string;
}

@Injectable()
export class StockConsumptionService {
  constructor(
    private prisma: PrismaService,
    private unitConversionService: UnitConversionService
  ) { }

  /**
   * Consumes allocated stock for a sale by decreasing the physical stock (quantityOnHand)
   * and creating OUT stock movement records
   * 
   * @param saleId - ID of the sale to consume stock for
   * @param userId - ID of the user performing the consumption
   * @param notes - Optional notes for the stock movement
   * @param tx - Optional transaction object to use instead of creating a new one
   * @returns Object with success status and details of consumed stock
   */
  async consumeAllocatedStock(saleId: string, userId: string, notes?: string, tx?: any) {
    // Use the provided transaction or create a new one
    if (tx) {
      return this.processStockConsumption(tx, saleId, userId, notes);
    } else {
      // Process the stock consumption in a transaction
      return this.prisma.$transaction(async (txn) => {
        return this.processStockConsumption(txn, saleId, userId, notes);
      });
    }
  }

  /**
   * Helper method to process stock consumption within a transaction
   */
  private async processStockConsumption(tx: any, saleId: string, userId: string, notes?: string) {
    // First, validate the sale exists and is in COMPLETED status
    const sale = await tx.sale.findUnique({
      where: { id: saleId },
      include: {
        saleItems: {
          include: {
            product: true,
            unit: true
          }
        }
      }
    });

    if (!sale) {
      throw new NotFoundException(`Penjualan dengan ID ${saleId} tidak ditemukan`);
    }

    if (sale.status !== 'COMPLETED') {
      throw new BadRequestException(
        `Tidak dapat mengkonsumsi stok untuk penjualan dengan status ${sale.status}. Status harus COMPLETED.`
      );
    }

    const consumedItems: ConsumedItem[] = [];

    // Group sale items by product to handle mixed units correctly
    const productGroups = new Map<string, any[]>();
    for (const saleItem of sale.saleItems) {
      if (!productGroups.has(saleItem.productId)) {
        productGroups.set(saleItem.productId, []);
      }
      productGroups.get(saleItem.productId)!.push(saleItem);
    }

    // Process each product group
    for (const [productId, productItems] of productGroups) {
      // Get all inventory items for this product that have allocated stock
      const inventoryItems = await tx.inventoryItem.findMany({
        where: {
          productId: productId,
          quantityAllocated: { gt: 0 },
          isActive: true
        },
        include: {
          product: true,
          unit: true
        },
        orderBy: [
          { expiryDate: 'asc' },
          { receivedDate: 'asc' }
        ]
      });

      if (inventoryItems.length === 0) {
        throw new BadRequestException(`Tidak ada stok yang dialokasikan untuk produk ${productItems[0].product.name}`);
      }

      // Calculate total demand in base units for this product
      let totalDemandInBaseUnits = 0;
      for (const saleItem of productItems) {
        const conversionResult = await this.unitConversionService.convertToBaseUnit(
          saleItem.productId,
          saleItem.unitId,
          saleItem.quantity
        );

        if (!conversionResult.success) {
          throw new BadRequestException(
            `Gagal mengkonversi unit untuk produk ${saleItem.product.name}: ${conversionResult.error}`
          );
        }

        totalDemandInBaseUnits += conversionResult.convertedQuantity!;
      }

      // Round up to match allocation logic
      const quantityToConsume = Math.ceil(totalDemandInBaseUnits);
      let remainingQuantity = quantityToConsume;

      // Process each inventory item with allocated stock
      for (const inventoryItem of inventoryItems) {
        if (remainingQuantity <= 0) break;

        // Calculate how much to consume from this item
        const quantityToConsume = Math.min(remainingQuantity, inventoryItem.quantityAllocated);

        if (quantityToConsume <= 0) continue;

        // Update inventory item: decrease quantityOnHand and quantityAllocated
        await tx.inventoryItem.update({
          where: { id: inventoryItem.id },
          data: {
            quantityOnHand: { decrement: quantityToConsume },
            quantityAllocated: { decrement: quantityToConsume }
          }
        });

        // Create OUT stock movement
        const stockMovement = await tx.stockMovement.create({
          data: {
            inventoryItemId: inventoryItem.id,
            type: StockMovementType.OUT,
            quantity: -quantityToConsume, // Negative for OUT movements
            unitPrice: inventoryItem.costPrice,
            referenceType: ReferenceType.SALE,
            referenceId: saleId,
            referenceNumber: sale.saleNumber,
            reason: `Pengeluaran stok untuk penjualan #${sale.saleNumber}`,
            notes: notes || `Stok dikonsumsi dari alokasi`,
            movementDate: new Date(),
            createdBy: userId
          }
        });

        consumedItems.push({
          productName: inventoryItem.product.name,
          batchNumber: inventoryItem.batchNumber || 'N/A',
          quantity: quantityToConsume,
          unit: inventoryItem.unit.name,
          movementId: stockMovement.id
        });

        remainingQuantity -= quantityToConsume;
      }

      // If we couldn't consume all the requested quantity
      if (remainingQuantity > 0) {
        throw new BadRequestException(
          `Stok yang dialokasikan tidak mencukupi untuk ${productItems[0].product.name}. Kekurangan: ${remainingQuantity} unit`
        );
      }
    }

    return {
      success: true,
      saleNumber: sale.saleNumber,
      consumedItems,
      message: `Berhasil mengkonsumsi stok untuk penjualan #${sale.saleNumber}`
    };
  }
} 