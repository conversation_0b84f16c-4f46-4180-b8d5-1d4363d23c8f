import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
  BadRequestException,
  NotFoundException,
  UseInterceptors,
  UploadedFile,
  Res,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { Response } from 'express';
import { InventoryService } from './inventory.service';
import { CreateInventoryItemDto } from './dto/create-inventory-item.dto';
import { UpdateInventoryItemDto } from './dto/update-inventory-item.dto';
import { InventoryQueryDto } from './dto/inventory-query.dto';
import { StockAdjustmentDto } from './dto/stock-adjustment.dto';
import { ActivateInventoryItemDto } from './dto/activate-inventory-item.dto';
import { DeactivateInventoryItemDto } from './dto/deactivate-inventory-item.dto';
import { StockAllocationDto } from './dto/stock-allocation.dto';
import { StockConsumptionDto, StockAvailabilityDto } from './dto/stock-consumption.dto';
import { AllocationReportRequestDto } from './dto/allocation-report.dto';
import { StockAllocationService } from './services/stock-allocation.service';
import { AllocationReportService } from './services/allocation-report.service';
import { InventoryReportService } from './services/inventory-report.service';
import {
  InventoryReportRequestDto,
  ImportInventoryDto,
  ImportInventoryResponse
} from './dto/inventory-report.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AdminGuard } from '../settings/guards/admin.guard';
import { ManagerGuard } from '../suppliers/guards/manager.guard';
import { StockConsumptionService } from './services/stock-consumption.service';
import { UserRole } from '@prisma/client';

@Controller('inventory')
@UseGuards(JwtAuthGuard)
export class InventoryController {
  constructor(
    private readonly inventoryService: InventoryService,
    private readonly stockAllocationService: StockAllocationService,
    private readonly stockConsumptionService: StockConsumptionService,
    private readonly allocationReportService: AllocationReportService,
    private readonly inventoryReportService: InventoryReportService
  ) { }

  @Post()
  @UseGuards(ManagerGuard) // Admin or Pharmacist can create
  async create(@Body() createInventoryItemDto: CreateInventoryItemDto, @Request() req: any) {
    return this.inventoryService.create(createInventoryItemDto, req.user.id);
  }

  @Get()
  async findAll(@Query() query: InventoryQueryDto) {
    return this.inventoryService.findAll(query);
  }

  @Get('stats')
  async getStats() {
    return this.inventoryService.getStats();
  }

  @Get('allocation-history')
  async getAllocationHistory(
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('productId') productId?: string,
    @Query('userId') userId?: string,
    @Query('method') method?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    const pageNum = page ? parseInt(page) : 1;
    const limitNum = limit ? parseInt(limit) : 20;

    const filters: any = {};
    if (productId) filters.productId = productId;
    if (userId) filters.userId = userId;
    if (method) filters.method = method;
    if (startDate) filters.startDate = new Date(startDate);
    if (endDate) filters.endDate = new Date(endDate);

    return this.inventoryService.getAllocationHistory(pageNum, limitNum, filters);
  }

  @Get('import-template')
  @UseGuards(ManagerGuard)
  async getImportTemplate(@Query('format') format: string = 'xlsx', @Res() res: Response) {
    try {
      const { buffer, filename, contentType } = await this.inventoryReportService.generateImportTemplate(format);

      res.setHeader('Content-Type', contentType);
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      res.setHeader('Content-Length', buffer.length);

      res.send(buffer);
    } catch (error) {
      throw new BadRequestException(`Gagal membuat template: ${error.message}`);
    }
  }

  @Get('reports/:reportId/progress')
  @UseGuards(ManagerGuard)
  async getReportProgress(@Param('reportId') reportId: string) {
    try {
      return await this.inventoryReportService.getReportProgress(reportId);
    } catch (error) {
      throw new NotFoundException(`Progress laporan tidak ditemukan: ${error.message}`);
    }
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    return this.inventoryService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(ManagerGuard) // Admin or Pharmacist can update
  async update(
    @Param('id') id: string,
    @Body() updateInventoryItemDto: UpdateInventoryItemDto,
    @Request() req: any,
  ) {
    return this.inventoryService.update(id, updateInventoryItemDto, req.user.id);
  }

  @Patch(':id/adjust-stock')
  @UseGuards(ManagerGuard) // Admin or Pharmacist can adjust stock
  async adjustStock(
    @Param('id') id: string,
    @Body() stockAdjustmentDto: StockAdjustmentDto,
    @Request() req: any,
  ) {
    return this.inventoryService.adjustStock(id, stockAdjustmentDto, req.user.id);
  }

  @Patch(':id/activate')
  @UseGuards(ManagerGuard) // Admin or Pharmacist can activate
  async activate(
    @Param('id') id: string,
    @Body() activateDto: ActivateInventoryItemDto,
    @Request() req: any,
  ) {
    return this.inventoryService.activate(id, activateDto, req.user.id);
  }

  @Patch(':id/deactivate')
  @UseGuards(ManagerGuard) // Admin or Pharmacist can deactivate
  async deactivate(
    @Param('id') id: string,
    @Body() deactivateDto: DeactivateInventoryItemDto,
    @Request() req: any,
  ) {
    return this.inventoryService.deactivate(id, deactivateDto, req.user.id);
  }

  @Get(':id/stock-movements')
  async getStockMovements(
    @Param('id') id: string,
    @Query('page') page?: string,
    @Query('limit') limit?: string,
  ) {
    const pageNum = page ? parseInt(page) : 1;
    const limitNum = limit ? parseInt(limit) : 20;
    return this.inventoryService.getStockMovements(id, pageNum, limitNum);
  }

  @Get('products/:productId/allocation-summary')
  async getProductAllocationSummary(@Param('productId') productId: string) {
    return this.inventoryService.getProductAllocationSummary(productId);
  }

  @Delete(':id')
  @UseGuards(AdminGuard) // Only Admin can delete
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id') id: string) {
    await this.inventoryService.remove(id);
  }

  @Post('allocate')
  @UseGuards(ManagerGuard) // Admin or Pharmacist can allocate stock
  async allocateStock(
    @Body() allocationDto: StockAllocationDto,
    @Request() req: any,
  ) {
    return this.stockAllocationService.allocateStock(allocationDto, req.user.id);
  }

  @Post('allocate/preview')
  async previewAllocation(
    @Body() allocationDto: StockAllocationDto,
    @Request() req: any,
  ) {
    const previewDto = { ...allocationDto, previewOnly: true };
    return this.stockAllocationService.allocateStock(previewDto, req.user.id);
  }

  @Get('products/:productId/stock-summary')
  async getStockSummary(@Param('productId') productId: string) {
    return this.stockAllocationService.getAvailableStockSummary(productId);
  }

  @Post('consume-stock')
  @UseGuards(ManagerGuard) // Admin or Pharmacist can consume stock
  async consumeStock(
    @Body() consumptionDto: StockConsumptionDto,
    @Request() req: any,
  ) {
    const { productId, requestedQuantity, method, ...options } = consumptionDto;
    return this.inventoryService.allocateStock(productId, requestedQuantity, method, {
      userId: req.user.id,
      ...options
    });
  }

  @Post('validate-stock-availability')
  @HttpCode(HttpStatus.OK)
  async validateStockAvailability(@Body() availabilityDto: StockAvailabilityDto) {
    const { productId, requestedQuantity } = availabilityDto;
    const result = await this.inventoryService.validateStockAvailability(productId, requestedQuantity);
    console.log(result);
    return result;
  }

  @Get('products/:productId/available-stock')
  async getAvailableStock(
    @Param('productId') productId: string,
    @Query('includeExpired') includeExpired?: string,
  ) {
    const includeExpiredBool = includeExpired === 'true';
    const totalStock = await this.inventoryService.getAvailableStock(productId, includeExpiredBool);
    return { productId, totalAvailable: totalStock, includeExpired: includeExpiredBool };
  }

  @Post('preview-stock-consumption')
  async previewStockConsumption(@Body() consumptionDto: StockConsumptionDto) {
    const { productId, requestedQuantity, method } = consumptionDto;
    return this.inventoryService.previewAllocation(productId, requestedQuantity, method);
  }

  @Post('allocation-reports/generate')
  @UseGuards(ManagerGuard) // Admin or Pharmacist can generate reports
  async generateAllocationReport(
    @Body() reportRequest: AllocationReportRequestDto,
    @Request() req: any,
  ) {
    return this.allocationReportService.generateReport(reportRequest);
  }

  @Post('reports/generate')
  @HttpCode(HttpStatus.OK)
  @UseGuards(ManagerGuard) // Admin or Pharmacist can generate reports
  async generateInventoryReport(
    @Body() reportRequest: InventoryReportRequestDto,
    @Request() req: any,
  ) {
    return this.inventoryReportService.generateReport(reportRequest);
  }

  @Post('import')
  @UseGuards(ManagerGuard) // Admin or Pharmacist can import data
  @UseInterceptors(FileInterceptor('file'))
  async importInventoryData(
    @UploadedFile() file: Express.Multer.File,
    @Body() importDto: ImportInventoryDto,
    @Request() req: any,
  ): Promise<ImportInventoryResponse> {
    if (!file) {
      throw new BadRequestException('File tidak ditemukan');
    }

    // Validate file type
    const allowedMimeTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
      'application/vnd.ms-excel', // .xls
      'text/csv', // .csv
    ];

    if (!allowedMimeTypes.includes(file.mimetype)) {
      throw new BadRequestException('Format file tidak didukung. Gunakan Excel (.xlsx, .xls) atau CSV (.csv)');
    }

    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      throw new BadRequestException('Ukuran file terlalu besar. Maksimal 10MB');
    }

    try {
      return await this.inventoryReportService.importInventoryData(file, importDto, req.user.id);
    } catch (error) {
      throw new BadRequestException(`Gagal mengimpor data: ${error.message}`);
    }
  }

  @Post('consume-stock/:saleId')
  @UseGuards(JwtAuthGuard)
  async consumeAllocatedStock(
    @Param('saleId') saleId: string,
    @Request() req,
    @Body('notes') notes?: string,
  ) {
    return this.stockConsumptionService.consumeAllocatedStock(saleId, req.user.id, notes);
  }

}
