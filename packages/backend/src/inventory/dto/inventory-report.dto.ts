import { IsString, IsEnum, IsBoolean, IsOptional, IsObject, ValidateNested, <PERSON>N<PERSON>ber, Min, Max } from 'class-validator';
import { Type, Transform } from 'class-transformer';

export enum InventoryReportType {
  STOCK_LEVELS = 'stock_levels',
  STOCK_MOVEMENTS = 'stock_movements',
  LOW_STOCK = 'low_stock',
  EXPIRY_REPORT = 'expiry_report',
  ALLOCATION_HISTORY = 'allocation_history',
  SUPPLIER_PERFORMANCE = 'supplier_performance',
  CATEGORY_ANALYSIS = 'category_analysis',
  STOCK_AGING = 'stock_aging',
  TURNOVER_ANALYSIS = 'turnover_analysis',
}

export enum ReportFormat {
  PDF = 'pdf',
  EXCEL = 'excel',
  CSV = 'csv',
}

export enum ReportLanguage {
  ID = 'id',
  EN = 'en',
}

export class ReportFiltersDto {
  @IsOptional()
  @IsString()
  startDate?: string;

  @IsOptional()
  @IsString()
  endDate?: string;

  @IsOptional()
  @IsString()
  productId?: string;

  @IsOptional()
  @IsString()
  productCategory?: string;

  @IsOptional()
  @IsString()
  productType?: string;

  @IsOptional()
  @IsString()
  supplierId?: string;

  @IsOptional()
  @IsString()
  location?: string;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  lowStockOnly?: boolean;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  expiredOnly?: boolean;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  expiringSoon?: boolean;

  @IsOptional()
  @IsNumber()
  @Min(1, { message: 'Hari kedaluwarsa harus antara 1-365 hari' })
  @Max(365, { message: 'Hari kedaluwarsa harus antara 1-365 hari' })
  @Transform(({ value }) => parseInt(value))
  expiringSoonDays?: number;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  isActive?: boolean;

  @IsOptional()
  @IsNumber()
  @Min(0)
  @Transform(({ value }) => parseInt(value))
  minQuantity?: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  @Transform(({ value }) => parseInt(value))
  maxQuantity?: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  @Transform(({ value }) => parseFloat(value))
  minValue?: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  @Transform(({ value }) => parseFloat(value))
  maxValue?: number;
}

export class UserInfoDto {
  @IsString()
  id: string;

  @IsString()
  name: string;

  @IsString()
  role: string;
}

export class PharmacyInfoDto {
  @IsString()
  name: string;

  @IsString()
  address: string;

  @IsString()
  licenseNumber: string;

  @IsString()
  pharmacistName: string;
}

export class InventoryReportRequestDto {
  @IsEnum(InventoryReportType)
  type: InventoryReportType;

  @IsEnum(ReportFormat)
  format: ReportFormat;

  @IsEnum(ReportLanguage)
  language: ReportLanguage;

  @ValidateNested()
  @Type(() => ReportFiltersDto)
  filters: ReportFiltersDto;

  @IsBoolean()
  includeDetails: boolean;

  @IsBoolean()
  includeSummary: boolean;

  @IsOptional()
  @IsBoolean()
  includeCharts?: boolean;

  @IsOptional()
  @ValidateNested()
  @Type(() => UserInfoDto)
  userInfo?: UserInfoDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => PharmacyInfoDto)
  pharmacyInfo?: PharmacyInfoDto;
}

export interface InventoryReportResponse {
  reportUrl: string;
  reportId: string;
  fileName: string;
  fileSize: number;
  expiresAt: string;
  format: string;
  type: string;
  generatedAt: string;
}

export interface ReportGenerationProgress {
  reportId: string;
  status: 'pending' | 'generating' | 'completed' | 'failed';
  progress: number; // 0-100
  message: string;
  estimatedTimeRemaining?: number; // seconds
}

// Import/Export DTOs
export class ImportInventoryDto {
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  validateOnly?: boolean;
}

export interface ImportInventoryResponse {
  success: boolean;
  message: string;
  errors?: string[];
  warnings?: string[];
  importedCount?: number;
  skippedCount?: number;
}
