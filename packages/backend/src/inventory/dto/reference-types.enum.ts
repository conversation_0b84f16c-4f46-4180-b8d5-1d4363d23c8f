/**
 * Standardized reference types for stock movements
 * This file exports the ReferenceType enum from Prisma client
 * and provides utility functions for UI display
 */
import { ReferenceType } from '@prisma/client';
export { ReferenceType };

// Add Indonesian labels for UI display
export const getReferenceTypeLabel = (type: ReferenceType): string => {
  const labels: Record<ReferenceType, string> = {
    // Sales related
    SALE: 'Penjualan',
    SALE_CANCELLATION: 'Pembatalan Penjualan',
    SALE_REFUND: 'Pengembalian Penjualan',
    SALE_DELETION: 'Penghapusan Penjualan',

    // Purchase related
    PURCHASE: 'Pembelian',
    PURCHASE_RETURN: 'Retur Pembelian',
    PURCHASE_ORDER: 'Purchase Order',
    GOODS_RECEIPT: 'Penerimaan Barang',

    // Inventory management
    INITIAL_STOCK: 'Stok Awal',
    ADJUSTMENT: 'Penyesuaian',
    TRANSFER: 'Transfer',

    // Allocation related
    ALLOCATION: 'Alokasi',
    DEALLOCATION: 'Pembatalan Alokasi',

    // Status changes
    ACTIVATION: 'Aktivasi',
    DEACTIVATION: 'Deaktivasi',

    // Customer related
    CUSTOMER_RESERVATION: 'Reservasi Pelanggan',
    CUSTOMER_ORDER: 'Pesanan Pelanggan',

    // Expiry related
    EXPIRY_WRITE_OFF: 'Penghapusan Kedaluwarsa',

    // Other
    OTHER: 'Lainnya',
  };

  return labels[type] || type;
}; 