import { IsString, IsEnum, IsBoolean, IsOptional, IsObject, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export enum ReportFormat {
  PDF = 'pdf',
  EXCEL = 'excel',
  CSV = 'csv',
}

export enum ReportLanguage {
  ID = 'id',
  EN = 'en',
}

export class UserInfoDto {
  @IsString()
  id: string;

  @IsString()
  name: string;

  @IsString()
  role: string;
}

export class PharmacyInfoDto {
  @IsString()
  name: string;

  @IsString()
  address: string;

  @IsString()
  licenseNumber: string;

  @IsString()
  pharmacistName: string;
}

export class AllocationReportRequestDto {
  @IsObject()
  allocationResult: any; // This will contain the AllocationResult object

  @IsEnum(ReportFormat)
  format: ReportFormat;

  @IsBoolean()
  includeDetails: boolean;

  @IsEnum(ReportLanguage)
  language: ReportLanguage;

  @IsOptional()
  @ValidateNested()
  @Type(() => UserInfoDto)
  userInfo?: UserInfoDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => PharmacyInfoDto)
  pharmacyInfo?: PharmacyInfoDto;
}

export interface AllocationReportResponse {
  reportUrl: string;
  reportId: string;
  fileName: string;
  fileSize: number;
  expiresAt: string;
  format: string;
}
