import { IsString, <PERSON>I<PERSON>, <PERSON><PERSON>num, IsOptional, IsBoolean, Min } from 'class-validator';
import { Transform, Type } from 'class-transformer';

export enum StockAllocationMethod {
  FIFO = 'FIFO', // First In First Out
  FEFO = 'FEFO', // First Expired First Out
}

export class StockConsumptionDto {
  @IsString()
  productId: string;

  @IsInt()
  @Min(1)
  @Type(() => Number)
  @Transform(({ value }) => parseInt(value))
  requestedQuantity: number;

  @IsEnum(StockAllocationMethod)
  method: StockAllocationMethod;

  @IsOptional()
  @IsString()
  userId?: string;

  @IsOptional()
  @IsString()
  reason?: string;

  @IsOptional()
  @IsString()
  notes?: string;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  allowPartialAllocation?: boolean = true;

  @IsOptional()
  @IsInt()
  @Min(0)
  @Type(() => Number)
  @Transform(({ value }) => value ? parseInt(value) : undefined)
  nearExpiryWarningDays?: number = 30;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  previewOnly?: boolean = false;
}

export class StockAvailabilityDto {
  @IsString()
  productId: string;

  @IsInt()
  @Min(1)
  @Type(() => Number)
  @Transform(({ value }) => parseInt(value))
  requestedQuantity: number;
}

export interface StockBatchInfo {
  inventoryItemId: string;
  batchNumber?: string;
  quantityOnHand: number;
  expiryDate?: Date;
  receivedDate: Date;
  location?: string;
}

export interface StockAvailabilityResult {
  isAvailable: boolean;
  totalAvailable: number;
  requestedQuantity: number;
  shortfall: number;
  batchCount: number;
  batches: StockBatchInfo[];
}

export interface StockConsumptionBatch {
  inventoryItemId: string;
  batchNumber?: string;
  expiryDate?: Date;
  receivedDate: Date;
  availableQuantity: number;
  allocatedQuantity: number;
  costPrice: number;
  location?: string;
  isNearExpiry?: boolean;
  daysUntilExpiry?: number;
}

export interface StockConsumptionResult {
  success: boolean;
  productId: string;
  requestedQuantity: number;
  allocatedQuantity: number;
  method: StockAllocationMethod;
  batches: StockConsumptionBatch[];
  shortfall?: number;
  warnings: string[];
  errors: string[];
  totalCost: number;
  averageCostPrice: number;
  previewOnly: boolean;
  timestamp: Date;
}

export interface StockConsumptionOptions {
  userId?: string;
  reason?: string;
  notes?: string;
  allowPartialAllocation?: boolean;
  nearExpiryWarningDays?: number;
  previewOnly?: boolean;
  respectMinimumStock?: boolean;
  minimumStockLevel?: number;
}
