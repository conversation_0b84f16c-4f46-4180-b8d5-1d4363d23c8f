import { PartialType } from '@nestjs/mapped-types';
import { CreateInventoryItemDto } from './create-inventory-item.dto';
import { IsOptional, IsInt, IsNumber, Min } from 'class-validator';
import { Transform, Type } from 'class-transformer';

export class UpdateInventoryItemDto extends PartialType(CreateInventoryItemDto) {
  // Override quantityOnHand to make it optional for updates
  @IsOptional()
  @IsInt()
  @Min(0)
  @Type(() => Number)
  @Transform(({ value }) => value ? parseInt(value) : undefined)
  quantityOnHand?: number;

  // Override price fields to ensure proper validation
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Type(() => Number)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  costPrice?: number;

  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Type(() => Number)
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  sellingPrice?: number;
}
