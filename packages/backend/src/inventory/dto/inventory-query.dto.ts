import { IsOptional, IsString, IsInt, IsBoolean, IsIn, Min, IsEnum, IsNumber, IsDateString, IsArray } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { ProductType, ProductCategory } from '@prisma/client';

export class InventoryQueryDto {
  @IsOptional()
  @IsInt()
  @Min(1)
  @Transform(({ value }) => value ? parseInt(value) : 1)
  page?: number = 1;

  @IsOptional()
  @IsInt()
  @Min(1)
  @Transform(({ value }) => value ? parseInt(value) : 10)
  limit?: number = 10;

  @IsOptional()
  @IsString()
  cursor?: string;

  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @IsString()
  searchOperator?: 'contains' | 'startsWith' | 'endsWith' | 'equals' = 'contains';

  @IsOptional()
  @IsString()
  productId?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @Transform(({ value }) => Array.isArray(value) ? value : value ? [value] : undefined)
  productIds?: string[];

  @IsOptional()
  @IsString()
  supplierId?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @Transform(({ value }) => Array.isArray(value) ? value : value ? [value] : undefined)
  supplierIds?: string[];

  @IsOptional()
  @IsString()
  location?: string;

  @IsOptional()
  @IsString()
  batchNumber?: string;

  @IsOptional()
  @IsEnum(ProductType)
  productType?: ProductType;

  @IsOptional()
  @IsEnum(ProductCategory)
  productCategory?: ProductCategory;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return undefined;
  })
  isActive?: boolean;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true')
  lowStock?: boolean;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  lowStockThreshold?: number;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true')
  expiringSoon?: boolean;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  expiringSoonDays?: number = 30;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true')
  expired?: boolean;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  quantityMin?: number;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  quantityMax?: number;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  costPriceMin?: number;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  costPriceMax?: number;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  sellingPriceMin?: number;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  sellingPriceMax?: number;

  @IsOptional()
  @IsDateString()
  expiryDateFrom?: string;

  @IsOptional()
  @IsDateString()
  expiryDateTo?: string;

  @IsOptional()
  @IsDateString()
  receivedDateFrom?: string;

  @IsOptional()
  @IsDateString()
  receivedDateTo?: string;

  @IsOptional()
  @IsDateString()
  createdDateFrom?: string;

  @IsOptional()
  @IsDateString()
  createdDateTo?: string;

  @IsOptional()
  @IsString()
  @IsIn([
    'createdAt',
    'updatedAt',
    'quantityOnHand',
    'expiryDate',
    'receivedDate',
    'costPrice',
    'sellingPrice',
    'totalValue',
    'stockValue',
    'lastMovementDate',
    'expiryProximity',
    'stockVelocity',
    'productName',
    'productCode',
    'productType',
    'productCategory',
    'supplierName',
    'supplierCode',
    'unitName',
    'batchNumber',
    'location'
  ])
  sortBy?: string = 'createdAt';

  @IsOptional()
  @IsString()
  @IsIn(['asc', 'desc'])
  sortOrder?: 'asc' | 'desc' = 'desc';

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @Transform(({ value }) => Array.isArray(value) ? value : value ? [value] : undefined)
  sortFields?: string[];

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true')
  includeAggregations?: boolean;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true')
  includeMovementStats?: boolean;

  @IsOptional()
  @IsString()
  @IsIn(['offset', 'cursor'])
  paginationType?: 'offset' | 'cursor' = 'offset';
}
