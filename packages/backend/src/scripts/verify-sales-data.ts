import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function verifySalesData() {
  console.log('🔍 Verifying Sales and Customer Data...');
  console.log('');

  try {
    // Check customers
    const customerStats = await prisma.customer.groupBy({
      by: ['type'],
      _count: { id: true }
    });

    console.log('👥 Customer Statistics:');
    customerStats.forEach(stat => {
      console.log(`   • ${stat.type}: ${stat._count.id} customers`);
    });

    // Check sales by status
    const salesStats = await prisma.sale.groupBy({
      by: ['status'],
      _count: { id: true },
      _sum: { totalAmount: true }
    });

    console.log('');
    console.log('💰 Sales Statistics:');
    salesStats.forEach(stat => {
      const avgAmount = stat._sum.totalAmount ? Number(stat._sum.totalAmount) / stat._count.id : 0;
      console.log(`   • ${stat.status}: ${stat._count.id} transactions (Total: Rp ${Number(stat._sum.totalAmount || 0).toLocaleString('id-ID')})`);
    });

    // Check payment methods
    const paymentStats = await prisma.sale.groupBy({
      by: ['paymentMethod'],
      _count: { id: true },
      _sum: { totalAmount: true }
    });

    console.log('');
    console.log('💳 Payment Method Statistics:');
    paymentStats.forEach(stat => {
      console.log(`   • ${stat.paymentMethod}: ${stat._count.id} transactions (Total: Rp ${Number(stat._sum.totalAmount || 0).toLocaleString('id-ID')})`);
    });

    // Check sale items
    const saleItemCount = await prisma.saleItem.count();
    const avgItemsPerSale = saleItemCount / (await prisma.sale.count());

    console.log('');
    console.log('📦 Sale Items Statistics:');
    console.log(`   • Total sale items: ${saleItemCount}`);
    console.log(`   • Average items per sale: ${avgItemsPerSale.toFixed(2)}`);

    // Check stock movements
    const stockMovementCount = await prisma.stockMovement.count({
      where: { referenceType: 'SALE' }
    });

    console.log('');
    console.log('📈 Stock Movement Statistics:');
    console.log(`   • Sale-related stock movements: ${stockMovementCount}`);

    // Check data integrity
    console.log('');
    console.log('🔍 Data Integrity Checks:');

    // Check if all completed sales have corresponding stock movements
    const completedSales = await prisma.sale.count({
      where: { status: 'COMPLETED' }
    });

    const completedSaleItems = await prisma.saleItem.count({
      where: { 
        sale: { status: 'COMPLETED' }
      }
    });

    console.log(`   • Completed sales: ${completedSales}`);
    console.log(`   • Completed sale items: ${completedSaleItems}`);
    console.log(`   • Stock movements for sales: ${stockMovementCount}`);
    console.log(`   • Integrity check: ${completedSaleItems === stockMovementCount ? '✅ PASS' : '❌ FAIL'}`);

    // Check customer relationships
    const salesWithCustomers = await prisma.sale.count({
      where: { customerId: { not: null } }
    });

    const salesWithWalkInInfo = await prisma.sale.count({
      where: { 
        customerId: null,
        customerName: { not: null }
      }
    });

    const totalSales = await prisma.sale.count();

    console.log(`   • Sales with registered customers: ${salesWithCustomers}`);
    console.log(`   • Sales with walk-in customer info: ${salesWithWalkInInfo}`);
    console.log(`   • Total sales: ${totalSales}`);
    console.log(`   • Customer relationship check: ${(salesWithCustomers + salesWithWalkInInfo) === totalSales ? '✅ PASS' : '❌ FAIL'}`);

    console.log('');
    console.log('✅ Data verification completed successfully!');

  } catch (error) {
    console.error('❌ Error verifying data:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  verifySalesData();
}
