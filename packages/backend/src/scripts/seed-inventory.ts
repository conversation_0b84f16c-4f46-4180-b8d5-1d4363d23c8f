import { PrismaClient, StockMovementType, MedicineClassification } from '@prisma/client';

const prisma = new PrismaClient();

// Indonesian storage locations for pharmacy context
const STORAGE_LOCATIONS = [
  'Rak A-1', 'Rak A-2', 'Rak A-3', 'Rak A-4', 'Rak A-5',
  'Rak B-1', 'Rak B-2', 'Rak B-3', 'Rak B-4', 'Rak B-5',
  'Rak C-1', 'Rak C-2', 'Rak C-3', 'Rak C-4', 'Rak C-5',
  '<PERSON><PERSON>ng Utama', '<PERSON><PERSON><PERSON>gin', '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>otrop<PERSON>',
  'Area Dispensing', 'Area OTC', 'Area Herbal'
];

// Indonesian batch number patterns
const BATCH_PREFIXES = [
  'BTH', 'LOT', 'BTC', 'KMF', 'KLB', 'IFM', 'CMB', 'SNB',
  'DXM', 'BNF', 'PYR', 'DVL', 'MHK', 'TMP', 'PHR'
];

// Supplier lot reference patterns
const SUPPLIER_LOT_PREFIXES = [
  'SUP', 'SLT', 'REF', 'INV', 'PO', 'DO', 'GR', 'RCV'
];

// Utility functions
function getRandomElement<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)];
}

function getRandomInt(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

function generateBatchNumber(): string {
  const prefix = getRandomElement(BATCH_PREFIXES);
  const year = new Date().getFullYear().toString().slice(-2);
  const month = (new Date().getMonth() + 1).toString().padStart(2, '0');
  const sequence = getRandomInt(1000, 9999);
  return `${prefix}${year}${month}${sequence}`;
}

function generateSupplierLotReference(): string {
  const prefix = getRandomElement(SUPPLIER_LOT_PREFIXES);
  const sequence = getRandomInt(100000, 999999);
  return `${prefix}-${sequence}`;
}

// Generate realistic received dates for FIFO testing
function generateReceivedDate(): Date {
  const now = new Date();
  const daysAgo = getRandomInt(1, 365); // 1 day to 1 year ago
  const receivedDate = new Date(now);
  receivedDate.setDate(now.getDate() - daysAgo);
  return receivedDate;
}

// Generate realistic expiry dates for FEFO testing
function generateExpiryDate(receivedDate: Date, medicineClassification: MedicineClassification): Date | null {
  // Non-medicine items don't have expiry dates
  if (medicineClassification === MedicineClassification.NON_MEDICINE) {
    return null;
  }

  const baseDate = new Date(receivedDate);
  let shelfLifeDays: number;

  // Different shelf life based on medicine type
  switch (medicineClassification) {
    case MedicineClassification.OBAT_BEBAS:
    case MedicineClassification.OBAT_BEBAS_TERBATAS:
      shelfLifeDays = getRandomInt(730, 1095); // 2-3 years
      break;
    case MedicineClassification.OBAT_KERAS:
    case MedicineClassification.NARKOTIKA:
    case MedicineClassification.PSIKOTROPIKA:
      shelfLifeDays = getRandomInt(1095, 1825); // 3-5 years
      break;
    case MedicineClassification.JAMU:
    case MedicineClassification.OBAT_HERBAL_TERSTANDAR:
    case MedicineClassification.FITOFARMAKA:
      shelfLifeDays = getRandomInt(365, 730); // 1-2 years
      break;
    default:
      shelfLifeDays = getRandomInt(730, 1095); // Default 2-3 years
  }

  const expiryDate = new Date(baseDate);
  expiryDate.setDate(baseDate.getDate() + shelfLifeDays);
  return expiryDate;
}

// Generate realistic quantities based on unit type and medicine classification
function generateQuantity(unitName: string, medicineClassification: MedicineClassification): number {
  const highVolumeTypes = [
    MedicineClassification.OBAT_BEBAS,
    MedicineClassification.OBAT_BEBAS_TERBATAS,
    MedicineClassification.JAMU
  ] as MedicineClassification[];
  const isHighVolume = highVolumeTypes.includes(medicineClassification);

  const controlledTypes = [
    MedicineClassification.NARKOTIKA,
    MedicineClassification.PSIKOTROPIKA
  ] as MedicineClassification[];
  const isControlled = controlledTypes.includes(medicineClassification);

  // Base quantities by unit type
  let baseMin: number, baseMax: number;

  switch (unitName.toLowerCase()) {
    case 'tablet':
    case 'kaplet':
    case 'kapsul':
      baseMin = isControlled ? 50 : 100;
      baseMax = isControlled ? 500 : (isHighVolume ? 2000 : 1000);
      break;
    case 'strip':
    case 'blister':
      baseMin = isControlled ? 5 : 10;
      baseMax = isControlled ? 50 : (isHighVolume ? 200 : 100);
      break;
    case 'box':
    case 'dus':
      baseMin = isControlled ? 2 : 5;
      baseMax = isControlled ? 20 : (isHighVolume ? 100 : 50);
      break;
    case 'mililiter':
    case 'ml':
      baseMin = isControlled ? 100 : 500;
      baseMax = isControlled ? 1000 : (isHighVolume ? 5000 : 2000);
      break;
    case 'botol':
    case 'fles':
      baseMin = isControlled ? 5 : 10;
      baseMax = isControlled ? 30 : (isHighVolume ? 100 : 50);
      break;
    case 'gram':
    case 'g':
      baseMin = isControlled ? 50 : 100;
      baseMax = isControlled ? 500 : (isHighVolume ? 2000 : 1000);
      break;
    case 'tube':
      baseMin = isControlled ? 5 : 10;
      baseMax = isControlled ? 30 : (isHighVolume ? 100 : 50);
      break;
    default:
      baseMin = isControlled ? 10 : 50;
      baseMax = isControlled ? 100 : (isHighVolume ? 500 : 200);
  }

  return getRandomInt(baseMin, baseMax);
}

// Generate realistic cost and selling prices with Indonesian pharmacy margins
function generatePrices(baseSellingPrice: number, baseCostPrice: number) {
  // Add some variation to base prices (±20%)
  const variation = 0.8 + (Math.random() * 0.4); // 0.8 to 1.2
  const costPrice = Math.round(baseCostPrice * variation);
  const sellingPrice = Math.round(baseSellingPrice * variation);
  
  return { costPrice, sellingPrice };
}

export async function seedInventoryItems() {
  console.log('🌱 Seeding Inventory Items...');

  // Environment safety check
  const nodeEnv = process.env.NODE_ENV;
  if (nodeEnv === 'production') {
    console.log('❌ Cannot run inventory seeder in production environment!');
    console.log('💡 This seeder is only allowed in development mode for safety.');
    return;
  }

  console.log(`✅ Environment check passed: ${nodeEnv || 'development'}`);

  // Check if inventory items already exist
  const existingCount = await prisma.inventoryItem.count();
  if (existingCount > 0) {
    console.log(`⚠️  Found ${existingCount} existing inventory items. Skipping seeding to avoid duplicates.`);
    console.log('💡 To re-seed, please clear the inventory_items table first.');
    return;
  }

  // Get all products with their unit hierarchies and suppliers
  const products = await prisma.product.findMany({
    include: {
      unitHierarchies: {
        include: {
          unit: true
        },
        orderBy: { level: 'asc' }
      },
      baseUnit: true
    }
  });

  if (products.length === 0) {
    console.log('⚠️  No products found. Please run product seeder first.');
    return;
  }

  // Get all active suppliers
  const suppliers = await prisma.supplier.findMany({
    where: { status: 'ACTIVE' }
  });

  if (suppliers.length === 0) {
    console.log('⚠️  No active suppliers found. Please run supplier seeder first.');
    return;
  }

  console.log(`📦 Found ${products.length} products and ${suppliers.length} suppliers`);

  const inventoryItems: any[] = [];

  for (const product of products) {
    console.log(`📋 Processing product: ${product.name} (${product.code})`);

    // Generate 2-5 batches per product for realistic FIFO/FEFO testing
    const batchCount = getRandomInt(2, 5);
    
    for (let batchIndex = 0; batchIndex < batchCount; batchIndex++) {
      // Select a random unit from the product's hierarchy (prefer base unit for most batches)
      const useBaseUnit = batchIndex === 0 || Math.random() < 0.7; // 70% chance to use base unit
      const selectedHierarchy = useBaseUnit 
        ? product.unitHierarchies.find(h => h.level === 0) || product.unitHierarchies[0]
        : getRandomElement(product.unitHierarchies);

      if (!selectedHierarchy) {
        console.log(`⚠️  No unit hierarchy found for product ${product.code}, skipping...`);
        continue;
      }

      // Select a random supplier
      const supplier = getRandomElement(suppliers);
      
      // Generate realistic dates
      const receivedDate = generateReceivedDate();
      const expiryDate = generateExpiryDate(receivedDate, product.medicineClassification);
      
      // Generate quantities and prices
      const quantity = generateQuantity(selectedHierarchy.unit.name, product.medicineClassification);
      const { costPrice, sellingPrice } = generatePrices(
        Number(selectedHierarchy.sellingPrice || 1000),
        Number(selectedHierarchy.costPrice || 600)
      );

      // Generate batch information
      const batchNumber = generateBatchNumber();
      const location = getRandomElement(STORAGE_LOCATIONS);
      
      // Create some variation in active status (95% active, 5% inactive for testing)
      const isActive = Math.random() > 0.05;
      
      // Create inventory item
      const inventoryItem = {
        productId: product.id,
        unitId: selectedHierarchy.unitId,
        batchNumber,
        expiryDate,
        quantityOnHand: quantity,
        costPrice,
        sellingPrice,
        location,
        receivedDate,
        supplierId: supplier.id,
        isActive,
        notes: `Batch ${batchIndex + 1} dari ${batchCount} - ${generateSupplierLotReference()}`,
        createdAt: receivedDate,
        updatedAt: receivedDate
      };

      inventoryItems.push(inventoryItem);
    }
  }

  console.log(`📊 Generated ${inventoryItems.length} inventory items`);

  // Create inventory items in batches
  const batchSize = 50;
  const createdItems: any[] = [];

  for (let i = 0; i < inventoryItems.length; i += batchSize) {
    const batch = inventoryItems.slice(i, i + batchSize);

    try {
      const result = await prisma.inventoryItem.createMany({
        data: batch,
        skipDuplicates: true
      });

      console.log(`📦 Created inventory batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(inventoryItems.length / batchSize)} (${result.count} items)`);

      // Get the created items for stock movements
      const batchItems = await prisma.inventoryItem.findMany({
        where: {
          productId: { in: batch.map(item => item.productId) },
          createdAt: { gte: new Date(Date.now() - 5000) } // Items created in last 5 seconds
        }
      });

      createdItems.push(...batchItems);
    } catch (error) {
      console.error(`❌ Error creating inventory batch ${Math.floor(i / batchSize) + 1}:`, error);
    }
  }

  console.log(`✅ Successfully seeded ${createdItems.length} inventory items!`);

  // Now create initial stock movements for each inventory item
  await seedInitialStockMovements(createdItems);
}

async function seedInitialStockMovements(inventoryItems: any[]) {
  console.log('🌱 Seeding Initial Stock Movements...');

  const stockMovements: any[] = [];

  for (const item of inventoryItems) {
    // Create initial IN movement for each inventory item
    const movement = {
      inventoryItemId: item.id,
      type: StockMovementType.IN,
      quantity: item.quantityOnHand,
      unitPrice: item.costPrice,
      referenceType: 'PURCHASE',
      referenceNumber: `PO-${generateSupplierLotReference()}`,
      reason: 'Penerimaan barang dari supplier',
      notes: `Batch: ${item.batchNumber} - Penerimaan awal stok`,
      movementDate: item.receivedDate,
      createdAt: item.receivedDate,
      createdBy: 'system'
    };

    stockMovements.push(movement);

    // Randomly create some additional movements for realistic history (30% chance)
    if (Math.random() < 0.3) {
      // Create a small adjustment movement
      const adjustmentQuantity = getRandomInt(-5, 10);
      if (adjustmentQuantity !== 0) {
        const adjustmentDate = new Date(item.receivedDate);
        adjustmentDate.setDate(adjustmentDate.getDate() + getRandomInt(1, 30));

        const adjustmentMovement = {
          inventoryItemId: item.id,
          type: StockMovementType.ADJUSTMENT,
          quantity: adjustmentQuantity,
          unitPrice: item.costPrice,
          referenceType: 'ADJUSTMENT',
          referenceNumber: `ADJ-${Date.now()}-${getRandomInt(1000, 9999)}`,
          reason: adjustmentQuantity > 0 ? 'Koreksi stok - penambahan' : 'Koreksi stok - pengurangan',
          notes: 'Penyesuaian stok berdasarkan stock opname',
          movementDate: adjustmentDate,
          createdAt: adjustmentDate,
          createdBy: 'system'
        };

        stockMovements.push(adjustmentMovement);
      }
    }
  }

  console.log(`📊 Generated ${stockMovements.length} stock movements`);

  // Create stock movements in batches
  const batchSize = 100;
  for (let i = 0; i < stockMovements.length; i += batchSize) {
    const batch = stockMovements.slice(i, i + batchSize);

    try {
      const result = await prisma.stockMovement.createMany({
        data: batch,
        skipDuplicates: true
      });

      console.log(`📈 Created stock movements batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(stockMovements.length / batchSize)} (${result.count} movements)`);
    } catch (error) {
      console.error(`❌ Error creating stock movements batch ${Math.floor(i / batchSize) + 1}:`, error);
    }
  }

  console.log(`✅ Successfully seeded ${stockMovements.length} stock movements!`);
}

// Create some special test scenarios for FIFO/FEFO testing
async function seedSpecialTestScenarios() {
  console.log('🌱 Seeding Special Test Scenarios...');

  // Get a few products for special scenarios
  const testProducts = await prisma.product.findMany({
    include: {
      unitHierarchies: {
        include: { unit: true },
        orderBy: { level: 'asc' }
      }
    },
    take: 3
  });

  if (testProducts.length === 0) {
    console.log('⚠️  No products available for test scenarios');
    return;
  }

  const suppliers = await prisma.supplier.findMany({
    where: { status: 'ACTIVE' },
    take: 2
  });

  if (suppliers.length === 0) {
    console.log('⚠️  No suppliers available for test scenarios');
    return;
  }

  const specialItems: any[] = [];
  const now = new Date();

  for (const product of testProducts) {
    const baseHierarchy = product.unitHierarchies.find(h => h.level === 0);
    if (!baseHierarchy) continue;

    // Scenario 1: Near expiry items (expires in 15 days)
    const nearExpiryDate = new Date(now);
    nearExpiryDate.setDate(now.getDate() + 15);

    specialItems.push({
      productId: product.id,
      unitId: baseHierarchy.unitId,
      batchNumber: `NEAR-${generateBatchNumber()}`,
      expiryDate: nearExpiryDate,
      quantityOnHand: getRandomInt(50, 200),
      costPrice: Number(baseHierarchy.costPrice || 1000),
      sellingPrice: Number(baseHierarchy.sellingPrice || 1500),
      location: 'Area Khusus - Near Expiry',
      receivedDate: new Date(now.getTime() - (180 * 24 * 60 * 60 * 1000)), // 6 months ago
      supplierId: suppliers[0].id,
      isActive: true,
      notes: 'Test scenario: Near expiry untuk testing FEFO',
      createdAt: now,
      updatedAt: now
    });

    // Scenario 2: Very old batch (FIFO testing)
    const oldReceiveDate = new Date(now);
    oldReceiveDate.setDate(now.getDate() - 300); // 10 months ago

    const oldExpiryDate = new Date(oldReceiveDate);
    oldExpiryDate.setDate(oldReceiveDate.getDate() + 730); // 2 years shelf life

    specialItems.push({
      productId: product.id,
      unitId: baseHierarchy.unitId,
      batchNumber: `OLD-${generateBatchNumber()}`,
      expiryDate: oldExpiryDate,
      quantityOnHand: getRandomInt(30, 100),
      costPrice: Number(baseHierarchy.costPrice || 1000),
      sellingPrice: Number(baseHierarchy.sellingPrice || 1500),
      location: 'Gudang Utama',
      receivedDate: oldReceiveDate,
      supplierId: suppliers[1].id,
      isActive: true,
      notes: 'Test scenario: Batch lama untuk testing FIFO',
      createdAt: oldReceiveDate,
      updatedAt: oldReceiveDate
    });

    // Scenario 3: Fresh batch (newest)
    const freshReceiveDate = new Date(now);
    freshReceiveDate.setDate(now.getDate() - 7); // 1 week ago

    const freshExpiryDate = new Date(freshReceiveDate);
    freshExpiryDate.setDate(freshReceiveDate.getDate() + 1095); // 3 years shelf life

    specialItems.push({
      productId: product.id,
      unitId: baseHierarchy.unitId,
      batchNumber: `FRESH-${generateBatchNumber()}`,
      expiryDate: freshExpiryDate,
      quantityOnHand: getRandomInt(100, 500),
      costPrice: Number(baseHierarchy.costPrice || 1000),
      sellingPrice: Number(baseHierarchy.sellingPrice || 1500),
      location: 'Area Penerimaan',
      receivedDate: freshReceiveDate,
      supplierId: suppliers[0].id,
      isActive: true,
      notes: 'Test scenario: Batch fresh untuk testing FIFO/FEFO',
      createdAt: freshReceiveDate,
      updatedAt: freshReceiveDate
    });
  }

  if (specialItems.length > 0) {
    try {
      const result = await prisma.inventoryItem.createMany({
        data: specialItems,
        skipDuplicates: true
      });

      console.log(`🧪 Created ${result.count} special test scenario items`);

      // Create stock movements for special items
      const createdSpecialItems = await prisma.inventoryItem.findMany({
        where: {
          batchNumber: { startsWith: 'NEAR-' },
          OR: [
            { batchNumber: { startsWith: 'OLD-' } },
            { batchNumber: { startsWith: 'FRESH-' } }
          ]
        }
      });

      await seedInitialStockMovements(createdSpecialItems);

    } catch (error) {
      console.error('❌ Error creating special test scenarios:', error);
    }
  }
}

// Main seeding function
export async function seedAllInventoryData() {
  try {
    console.log('🚀 Starting comprehensive inventory seeding...');
    console.log('📋 This seeder will create realistic inventory data for Indonesian pharmacy operations');
    console.log('🔬 Including FIFO/FEFO test scenarios, multiple batches, and proper stock movements');
    console.log('');

    await seedInventoryItems();
    await seedSpecialTestScenarios();

    console.log('');
    console.log('🎉 All inventory data seeded successfully!');
    console.log('📊 Summary:');

    const stats = await prisma.inventoryItem.aggregate({
      _count: { id: true },
      _sum: { quantityOnHand: true }
    });

    const movementCount = await prisma.stockMovement.count();

    console.log(`   • Total inventory items: ${stats._count.id}`);
    console.log(`   • Total quantity on hand: ${stats._sum.quantityOnHand}`);
    console.log(`   • Total stock movements: ${movementCount}`);
    console.log('');
    console.log('✅ Ready for FIFO/FEFO testing and inventory management operations!');

  } catch (error) {
    console.error('❌ Error seeding inventory data:', error);
    throw error;
  }
}

// CLI execution
async function main() {
  try {
    await seedAllInventoryData();
  } catch (error) {
    console.error('❌ Inventory seeding failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  main();
}
