import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function verifyInventoryData() {
  console.log('🔍 Verifying Inventory Seeder Results...\n');

  try {
    // Check products with PSIKOTROPIKA classification
    const psikotropikaProducts = await prisma.product.findMany({
      where: { medicineClassification: 'PSIKOTROPIKA' },
      include: {
        unitHierarchies: {
          include: { unit: true }
        }
      }
    });

    console.log('📋 PSIKOTROPIKA Products:');
    psikotropikaProducts.forEach(product => {
      console.log(`   • ${product.name} (${product.code})`);
      console.log(`     Classification: ${product.medicineClassification}`);
      console.log(`     Units: ${product.unitHierarchies.length} levels`);
    });
    console.log('');

    // Check inventory items summary
    const inventoryStats = await prisma.inventoryItem.aggregate({
      _count: { id: true },
      _sum: { quantityOnHand: true }
    });

    console.log('📦 Inventory Items Summary:');
    console.log(`   • Total items: ${inventoryStats._count.id}`);
    console.log(`   • Total quantity: ${inventoryStats._sum.quantityOnHand}`);
    console.log('');

    // Check inventory by medicine classification - using raw query instead of groupBy with include

    // Get classification breakdown
    const classificationBreakdown = await prisma.$queryRaw`
      SELECT 
        p."medicineClassification",
        COUNT(i.id) as item_count,
        SUM(i."quantityOnHand") as total_quantity
      FROM "inventory_items" i
      JOIN "products" p ON i."productId" = p.id
      GROUP BY p."medicineClassification"
      ORDER BY item_count DESC
    `;

    console.log('🏷️  Inventory by Medicine Classification:');
    (classificationBreakdown as any[]).forEach(item => {
      console.log(`   • ${item.medicineClassification}: ${item.item_count} items, ${item.total_quantity} total qty`);
    });
    console.log('');

    // Check stock movements
    const movementStats = await prisma.stockMovement.aggregate({
      _count: { id: true }
    });

    console.log('📈 Stock Movements:');
    console.log(`   • Total movements: ${movementStats._count.id}`);
    console.log('');

    // Check special test scenarios
    const specialScenarios = await prisma.inventoryItem.findMany({
      where: {
        OR: [
          { batchNumber: { startsWith: 'NEAR-' } },
          { batchNumber: { startsWith: 'OLD-' } },
          { batchNumber: { startsWith: 'FRESH-' } }
        ]
      },
      include: {
        product: { select: { name: true, code: true } }
      }
    });

    console.log('🧪 Special Test Scenarios:');
    specialScenarios.forEach(item => {
      const scenarioType = item.batchNumber?.startsWith('NEAR-') ? 'Near Expiry' :
                          item.batchNumber?.startsWith('OLD-') ? 'Old Batch' :
                          item.batchNumber?.startsWith('FRESH-') ? 'Fresh Batch' : 'Unknown';
      console.log(`   • ${scenarioType}: ${item.product.name} (${item.batchNumber})`);
      console.log(`     Quantity: ${item.quantityOnHand}, Expiry: ${item.expiryDate?.toDateString() || 'N/A'}`);
    });
    console.log('');

    // Check FIFO/FEFO readiness
    const fifoTestData = await prisma.inventoryItem.findMany({
      where: { 
        isActive: true,
        quantityOnHand: { gt: 0 }
      },
      orderBy: { receivedDate: 'asc' },
      take: 5,
      include: {
        product: { select: { name: true } }
      }
    });

    console.log('⏰ FIFO Test Data (Oldest 5 items):');
    fifoTestData.forEach(item => {
      console.log(`   • ${item.product.name}: Received ${item.receivedDate.toDateString()}, Qty: ${item.quantityOnHand}`);
    });
    console.log('');

    const fefoTestData = await prisma.inventoryItem.findMany({
      where: { 
        isActive: true,
        quantityOnHand: { gt: 0 },
        expiryDate: { not: null }
      },
      orderBy: { expiryDate: 'asc' },
      take: 5,
      include: {
        product: { select: { name: true } }
      }
    });

    console.log('📅 FEFO Test Data (Earliest Expiry 5 items):');
    fefoTestData.forEach(item => {
      console.log(`   • ${item.product.name}: Expires ${item.expiryDate?.toDateString()}, Qty: ${item.quantityOnHand}`);
    });
    console.log('');

    console.log('✅ Inventory seeder verification completed successfully!');
    console.log('🎯 Ready for FIFO/FEFO testing and inventory management operations!');

  } catch (error) {
    console.error('❌ Error verifying inventory data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  verifyInventoryData();
}
