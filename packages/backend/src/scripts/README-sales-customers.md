# Sales and Customer Data Seeder

This seeder creates comprehensive sales and customer test data for the Indonesian pharmacy store application.

## Overview

The `seed-sales-customers.ts` script generates realistic test data including:

### Customer Data
- **Registered Customers (70%)**: Complete customer profiles with membership information
- **Walk-in Customers (30%)**: Simple customer records for quick transactions

### Sales Data
- **200 Sales Transactions** with various statuses:
  - 80% COMPLETED transactions
  - 15% DRAFT transactions
  - 5% CANCELLED transactions
- **Multiple Payment Methods**: Cash (60%), Transfer (30%), Credit/Giro (10%)
- **Realistic Transaction Dates**: Spanning the last 6 months
- **Variable Items per Sale**: 1-5 items per transaction

### Features
- **FIFO/FEFO Inventory Allocation**: Proper stock movements for completed sales
- **Discounts**: Both item-level and sale-level discounts
- **Tax Calculations**: 11% PPN applied to 30% of transactions
- **Indonesian Context**: Names, addresses, and phone numbers using Indonesian patterns
- **Data Integrity**: All relationships properly maintained

## Usage

### Run Sales & Customer Seeder Only
```bash
cd packages/backend
npx ts-node src/scripts/seed-sales-customers.ts
```

### Run Complete Seeding (includes sales & customers)
```bash
cd packages/backend
npm run seed
```

### Verify Data Integrity
```bash
cd packages/backend
npx ts-node src/scripts/verify-sales-data.ts
```

## Safety Features

- **Environment Protection**: Only runs in development mode
- **Duplicate Prevention**: Checks for existing data before seeding
- **Batch Processing**: Creates data in manageable batches
- **Error Handling**: Comprehensive error reporting

## Generated Data Structure

### Customers (100 total)
- **70 Registered Customers**:
  - Complete personal information
  - Indonesian names and addresses
  - Membership levels (BRONZE, SILVER, GOLD, PLATINUM)
  - Loyalty points (0-5,000)
  - Email addresses and phone numbers

- **30 Walk-in Customers**:
  - Simple names (e.g., "Ibu Sari", "Bapak Budi")
  - Optional phone numbers (70% have phones)
  - No membership information

### Sales Transactions (200 total)
- **Transaction Numbers**: Format `TRX-YYYYMMDD-XXX`
- **Realistic Timing**: Business hours (8 AM - 10 PM)
- **Customer Assignment**:
  - 70% use registered customers
  - 30% are walk-in with customer name/phone
- **Financial Calculations**:
  - Subtotal, discounts, tax, and total amounts
  - Proper change calculation for cash payments

### Sale Items (580 total, avg 2.9 per sale)
- **Inventory Integration**: Uses existing inventory items
- **Batch Tracking**: Records batch numbers and expiry dates
- **Pricing**: Uses inventory selling prices with realistic variations
- **Discounts**: 20% chance of item-level discounts (5-20%)

### Stock Movements (476 for completed sales)
- **FIFO/FEFO Compliance**: Proper inventory allocation
- **Reference Tracking**: Links to sale transactions
- **Audit Trail**: Complete movement history

## Data Verification

The verification script checks:
- Customer distribution by type
- Sales statistics by status and payment method
- Data integrity between sales, items, and stock movements
- Customer relationship consistency

## Integration

This seeder integrates with:
- **Inventory Seeder**: Uses existing inventory items
- **User Seeder**: Uses existing cashiers/pharmacists
- **Product Seeder**: References product information

## Development Notes

- Uses Indonesian naming conventions and locations
- Generates realistic pharmacy transaction patterns
- Maintains referential integrity across all tables
- Supports both registered and walk-in customer workflows
- Creates proper audit trails for inventory management

## Example Output

```
🚀 Starting comprehensive sales and customer seeding...
📋 This seeder will create realistic sales and customer data for Indonesian pharmacy operations
💰 Including various payment methods, discounts, and transaction statuses

✅ Successfully seeded 100 customers!
✅ Successfully seeded 200 sales transactions!

📊 Summary:
   📋 Customers:
     • REGISTERED: 70 customers
     • WALK_IN: 30 customers
   💰 Sales:
     • COMPLETED: 160 transactions (Avg: Rp 33,336,519)
     • DRAFT: 32 transactions (Avg: Rp 28,730,379)
     • CANCELLED: 8 transactions (Avg: Rp 4,239,558)
   💵 Total Sales: 200 transactions worth Rp 6,287,131.61

✅ Ready for sales operations and customer management!
```
