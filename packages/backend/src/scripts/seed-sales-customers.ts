import { PrismaClient, CustomerType, SaleStatus, PaymentMethod, StockMovementType } from '@prisma/client';

const prisma = new PrismaClient();

// Indonesian customer names for realistic data
const INDONESIAN_FIRST_NAMES = {
  male: [
    '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>'
  ],
  female: [
    '<PERSON>i', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'In<PERSON>', '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>',
    '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>ah', 'Elsa', 'Farah'
  ]
};

const INDONESIAN_LAST_NAMES = [
  'Pratama', 'Sari', '<PERSON>ijaya', 'Putri', '<PERSON>o', '<PERSON><PERSON>i', '<PERSON>rniawan', 'Dewi',
  '<PERSON>iawan', '<PERSON>graini', '<PERSON><PERSON>', '<PERSON>hayu', 'Nugro<PERSON>', '<PERSON>harani', '<PERSON>putra', 'Wulandari',
  'Hidayat', 'Safitri', 'Gunawan', 'Puspita', 'Ramadhan', 'Kusuma', 'Hakim', 'Indrawati',
  'Firmansyah', 'Handayani', 'Syahputra', 'Novita', 'Maulana', 'Fitriani'
];

// Indonesian cities and provinces
const INDONESIAN_LOCATIONS = [
  { city: 'Jakarta Selatan', province: 'DKI Jakarta' },
  { city: 'Jakarta Pusat', province: 'DKI Jakarta' },
  { city: 'Jakarta Utara', province: 'DKI Jakarta' },
  { city: 'Bandung', province: 'Jawa Barat' },
  { city: 'Bekasi', province: 'Jawa Barat' },
  { city: 'Depok', province: 'Jawa Barat' },
  { city: 'Tangerang', province: 'Banten' },
  { city: 'Surabaya', province: 'Jawa Timur' },
  { city: 'Malang', province: 'Jawa Timur' },
  { city: 'Semarang', province: 'Jawa Tengah' },
  { city: 'Yogyakarta', province: 'DI Yogyakarta' },
  { city: 'Medan', province: 'Sumatera Utara' },
  { city: 'Palembang', province: 'Sumatera Selatan' },
  { city: 'Makassar', province: 'Sulawesi Selatan' },
  { city: 'Denpasar', province: 'Bali' }
];

// Indonesian street names
const INDONESIAN_STREETS = [
  'Jl. Sudirman', 'Jl. Thamrin', 'Jl. Gatot Subroto', 'Jl. Kuningan', 'Jl. Senayan',
  'Jl. Kemang', 'Jl. Menteng', 'Jl. Kelapa Gading', 'Jl. Pondok Indah', 'Jl. Kebayoran',
  'Jl. Cikini', 'Jl. Salemba', 'Jl. Matraman', 'Jl. Tebet', 'Jl. Pasar Minggu',
  'Jl. Raya Bogor', 'Jl. Raya Depok', 'Jl. Ahmad Yani', 'Jl. Diponegoro', 'Jl. Veteran'
];

// Membership levels
const MEMBERSHIP_LEVELS = ['BRONZE', 'SILVER', 'GOLD', 'PLATINUM'];

// Walk-in customer names (simple names for quick transactions)
const WALK_IN_NAMES = [
  'Ibu Sari', 'Bapak Budi', 'Ibu Dewi', 'Bapak Ahmad', 'Ibu Fitri', 'Bapak Joko',
  'Ibu Maya', 'Bapak Eko', 'Ibu Ratna', 'Bapak Hadi', 'Ibu Ani', 'Bapak Dedi',
  'Ibu Tika', 'Bapak Agus', 'Ibu Novi', 'Bapak Wahyu', 'Ibu Gita', 'Bapak Rizki'
];

// Utility functions
function getRandomElement<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)];
}

function getRandomInt(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

function generateCustomerCode(type: CustomerType, index: number): string {
  const prefix = type === CustomerType.REGISTERED ? 'REG' : 'WLK';
  const year = new Date().getFullYear().toString().slice(-2);
  const sequence = (index + 1).toString().padStart(4, '0');
  return `${prefix}${year}${sequence}`;
}

function generateMembershipNumber(): string {
  const year = new Date().getFullYear();
  const sequence = getRandomInt(100000, 999999);
  return `MBR${year}${sequence}`;
}

function generatePhoneNumber(): string {
  const prefixes = ['0812', '0813', '0821', '0822', '0851', '0852', '0853', '0856', '0857', '0858'];
  const prefix = getRandomElement(prefixes);
  const number = getRandomInt(10000000, 99999999);
  return `${prefix}${number}`;
}

function generateEmail(firstName: string, lastName: string): string {
  const domains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com'];
  const domain = getRandomElement(domains);
  const username = `${firstName.toLowerCase()}.${lastName.toLowerCase()}${getRandomInt(1, 999)}`;
  return `${username}@${domain}`;
}

function generateDateOfBirth(): Date {
  const now = new Date();
  const minAge = 18;
  const maxAge = 70;
  const ageInYears = getRandomInt(minAge, maxAge);
  const birthYear = now.getFullYear() - ageInYears;
  const birthMonth = getRandomInt(0, 11);
  const birthDay = getRandomInt(1, 28); // Safe day for all months
  return new Date(birthYear, birthMonth, birthDay);
}

function generatePostalCode(): string {
  return getRandomInt(10000, 99999).toString();
}

// Generate realistic transaction dates spanning recent months
function generateTransactionDate(): Date {
  const now = new Date();
  const daysAgo = getRandomInt(1, 180); // 1 day to 6 months ago
  const transactionDate = new Date(now);
  transactionDate.setDate(now.getDate() - daysAgo);

  // Adjust for business hours (8 AM to 10 PM)
  const hour = getRandomInt(8, 22);
  const minute = getRandomInt(0, 59);
  transactionDate.setHours(hour, minute, 0, 0);

  return transactionDate;
}

function generateSaleNumber(date: Date): string {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const sequence = getRandomInt(1, 999).toString().padStart(3, '0');
  return `TRX-${year}${month}${day}-${sequence}`;
}

// Customer seeding functions
export async function seedCustomers() {
  console.log('🌱 Seeding Customers...');

  // Environment safety check
  const nodeEnv = process.env.NODE_ENV;
  if (nodeEnv === 'production') {
    console.log('❌ Cannot run customer seeder in production environment!');
    console.log('💡 This seeder is only allowed in development mode for safety.');
    return;
  }

  console.log(`✅ Environment check passed: ${nodeEnv || 'development'}`);

  // Check if customers already exist
  const existingCount = await prisma.customer.count();
  if (existingCount > 0) {
    console.log(`⚠️  Found ${existingCount} existing customers. Skipping seeding to avoid duplicates.`);
    console.log('💡 To re-seed, please clear the customers table first.');
    return;
  }

  const customers: any[] = [];

  // Generate registered customers (70% of total)
  const registeredCount = 70;
  console.log(`📋 Generating ${registeredCount} registered customers...`);

  for (let i = 0; i < registeredCount; i++) {
    const gender = Math.random() > 0.5 ? 'M' : 'F';
    const firstNamePool = gender === 'M' ? INDONESIAN_FIRST_NAMES.male : INDONESIAN_FIRST_NAMES.female;
    const firstName = getRandomElement(firstNamePool);
    const lastName = getRandomElement(INDONESIAN_LAST_NAMES);
    const fullName = `${firstName} ${lastName}`;
    const location = getRandomElement(INDONESIAN_LOCATIONS);
    const street = getRandomElement(INDONESIAN_STREETS);
    const membershipLevel = getRandomElement(MEMBERSHIP_LEVELS);

    const customer = {
      code: generateCustomerCode(CustomerType.REGISTERED, i),
      type: CustomerType.REGISTERED,
      firstName,
      lastName,
      fullName,
      phoneNumber: generatePhoneNumber(),
      email: generateEmail(firstName, lastName),
      dateOfBirth: generateDateOfBirth(),
      gender,
      address: `${street} No. ${getRandomInt(1, 200)}`,
      city: location.city,
      province: location.province,
      postalCode: generatePostalCode(),
      membershipNumber: generateMembershipNumber(),
      membershipLevel,
      loyaltyPoints: getRandomInt(0, 5000),
      notes: `Pelanggan terdaftar dengan level membership ${membershipLevel}`,
      isActive: Math.random() > 0.05, // 95% active
      createdAt: generateTransactionDate(),
      updatedAt: new Date()
    };

    customers.push(customer);
  }

  // Generate walk-in customers (30% of total)
  const walkInCount = 30;
  console.log(`📋 Generating ${walkInCount} walk-in customers...`);

  for (let i = 0; i < walkInCount; i++) {
    const fullName = getRandomElement(WALK_IN_NAMES);
    const hasPhone = Math.random() > 0.3; // 70% have phone numbers

    const customer = {
      code: generateCustomerCode(CustomerType.WALK_IN, i),
      type: CustomerType.WALK_IN,
      fullName,
      phoneNumber: hasPhone ? generatePhoneNumber() : null,
      loyaltyPoints: 0,
      notes: 'Pelanggan walk-in',
      isActive: true,
      createdAt: generateTransactionDate(),
      updatedAt: new Date()
    };

    customers.push(customer);
  }

  console.log(`📊 Generated ${customers.length} customers`);

  // Create customers in batches
  const batchSize = 25;
  for (let i = 0; i < customers.length; i += batchSize) {
    const batch = customers.slice(i, i + batchSize);

    try {
      const result = await prisma.customer.createMany({
        data: batch,
        skipDuplicates: true
      });

      console.log(`👥 Created customer batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(customers.length / batchSize)} (${result.count} customers)`);
    } catch (error) {
      console.error(`❌ Error creating customer batch ${Math.floor(i / batchSize) + 1}:`, error);
    }
  }

  console.log(`✅ Successfully seeded ${customers.length} customers!`);
}

// Sales seeding functions
export async function seedSales() {
  console.log('🌱 Seeding Sales Transactions...');

  // Environment safety check
  const nodeEnv = process.env.NODE_ENV;
  if (nodeEnv === 'production') {
    console.log('❌ Cannot run sales seeder in production environment!');
    console.log('💡 This seeder is only allowed in development mode for safety.');
    return;
  }

  console.log(`✅ Environment check passed: ${nodeEnv || 'development'}`);

  // Check if sales already exist
  const existingCount = await prisma.sale.count();
  if (existingCount > 0) {
    console.log(`⚠️  Found ${existingCount} existing sales. Skipping seeding to avoid duplicates.`);
    console.log('💡 To re-seed, please clear the sales table first.');
    return;
  }

  // Get required data
  const customers = await prisma.customer.findMany();
  const cashiers = await prisma.user.findMany({
    where: { role: { in: ['CASHIER', 'PHARMACIST', 'ADMIN'] } }
  });
  const inventoryItems = await prisma.inventoryItem.findMany({
    where: {
      isActive: true,
      quantityOnHand: { gt: 0 }
    },
    include: {
      product: {
        include: {
          unitHierarchies: {
            include: { unit: true },
            orderBy: { level: 'asc' }
          }
        }
      },
      unit: true
    }
  });

  if (customers.length === 0) {
    console.log('⚠️  No customers found. Please run customer seeder first.');
    return;
  }

  if (cashiers.length === 0) {
    console.log('⚠️  No cashiers found. Please ensure users are seeded first.');
    return;
  }

  if (inventoryItems.length === 0) {
    console.log('⚠️  No inventory items found. Please run inventory seeder first.');
    return;
  }

  console.log(`📦 Found ${customers.length} customers, ${cashiers.length} cashiers, ${inventoryItems.length} inventory items`);

  const sales: any[] = [];
  const saleItems: any[] = [];
  const stockMovements: any[] = [];

  // Generate sales transactions
  const salesCount = 200; // Generate 200 sales transactions
  console.log(`📋 Generating ${salesCount} sales transactions...`);

  for (let i = 0; i < salesCount; i++) {
    const saleDate = generateTransactionDate();
    const saleNumber = `${generateSaleNumber(saleDate)}-${i.toString().padStart(3, '0')}`;
    const cashier = getRandomElement(cashiers);

    // Determine customer (70% registered, 30% walk-in)
    const useRegisteredCustomer = Math.random() < 0.7;
    let customerId: string | null = null;
    let customerName: string | null = null;
    let customerPhone: string | null = null;

    if (useRegisteredCustomer) {
      const registeredCustomers = customers.filter(c => c.type === CustomerType.REGISTERED);
      if (registeredCustomers.length > 0) {
        const customer = getRandomElement(registeredCustomers);
        customerId = customer.id;
      }
    } else {
      // Walk-in customer
      customerName = getRandomElement(WALK_IN_NAMES);
      customerPhone = Math.random() > 0.5 ? generatePhoneNumber() : null;
    }

    // Determine sale status (80% completed, 15% draft, 5% cancelled)
    let status: SaleStatus;
    const statusRand = Math.random();
    if (statusRand < 0.8) {
      status = SaleStatus.COMPLETED;
    } else if (statusRand < 0.95) {
      status = SaleStatus.DRAFT;
    } else {
      status = SaleStatus.CANCELLED;
    }

    // Generate sale items (1-5 items per sale)
    const itemCount = getRandomInt(1, 5);
    const selectedItems: any[] = [];
    let subtotal = 0;

    for (let j = 0; j < itemCount; j++) {
      const inventoryItem = getRandomElement(inventoryItems);
      const maxQuantity = Math.min(inventoryItem.quantityOnHand, 10); // Max 10 units per item
      const quantity = getRandomInt(1, maxQuantity);
      const unitPrice = Number(inventoryItem.sellingPrice || Number(inventoryItem.costPrice) * 1.3);
      const totalPrice = quantity * unitPrice;

      // Apply random item-level discount (20% chance)
      let discountAmount = 0;
      let discountType: string | null = null;
      let discountValue: number | null = null;

      if (Math.random() < 0.2) {
        discountType = Math.random() > 0.5 ? 'PERCENTAGE' : 'FIXED_AMOUNT';
        if (discountType === 'PERCENTAGE') {
          discountValue = getRandomInt(5, 20); // 5-20% discount
          discountAmount = (totalPrice * discountValue) / 100;
        } else {
          discountAmount = getRandomInt(1000, 5000); // Rp 1,000 - 5,000 discount
          discountValue = discountAmount;
        }
        discountAmount = Math.min(discountAmount, totalPrice); // Don't exceed total price
      }

      const finalItemPrice = totalPrice - discountAmount;
      subtotal += finalItemPrice;

      selectedItems.push({
        inventoryItem,
        quantity,
        unitPrice,
        totalPrice,
        discountType,
        discountValue,
        discountAmount,
        finalPrice: finalItemPrice
      });
    }

    // Apply sale-level discount (10% chance)
    let saleDiscountAmount = 0;
    let saleDiscountType: string | null = null;
    let saleDiscountValue: number | null = null;

    if (Math.random() < 0.1) {
      saleDiscountType = Math.random() > 0.5 ? 'PERCENTAGE' : 'FIXED_AMOUNT';
      if (saleDiscountType === 'PERCENTAGE') {
        saleDiscountValue = getRandomInt(5, 15); // 5-15% discount
        saleDiscountAmount = (subtotal * saleDiscountValue) / 100;
      } else {
        saleDiscountAmount = getRandomInt(5000, 20000); // Rp 5,000 - 20,000 discount
        saleDiscountValue = saleDiscountAmount;
      }
      saleDiscountAmount = Math.min(saleDiscountAmount, subtotal); // Don't exceed subtotal
    }

    // Calculate tax (PPN 11% for some items, 30% chance)
    const taxAmount = Math.random() < 0.3 ? (subtotal - saleDiscountAmount) * 0.11 : 0;
    const totalAmount = subtotal - saleDiscountAmount + taxAmount;

    // Payment information
    const paymentMethod = getRandomElement([
      PaymentMethod.CASH, PaymentMethod.CASH, PaymentMethod.CASH, // 60% cash
      PaymentMethod.TRANSFER, PaymentMethod.TRANSFER, // 40% transfer
      PaymentMethod.CREDIT, PaymentMethod.GIRO // 10% each
    ]);

    let amountPaid = 0;
    let changeAmount = 0;

    if (status === SaleStatus.COMPLETED) {
      if (paymentMethod === PaymentMethod.CASH) {
        // For cash, sometimes overpay to generate change
        const overpayChance = Math.random();
        if (overpayChance < 0.3) {
          // Round up to nearest 5000 or 10000
          const roundedAmount = Math.ceil(totalAmount / 5000) * 5000;
          amountPaid = roundedAmount;
          changeAmount = amountPaid - totalAmount;
        } else {
          amountPaid = totalAmount;
        }
      } else {
        amountPaid = totalAmount; // Exact payment for non-cash
      }
    } else if (status === SaleStatus.DRAFT) {
      amountPaid = 0; // No payment for draft
    } else {
      amountPaid = 0; // No payment for cancelled
    }

    const sale = {
      id: `sale_${i}_${Date.now()}`,
      saleNumber,
      customerId,
      cashierId: cashier.id,
      status,
      saleDate,
      subtotal,
      discountType: saleDiscountType,
      discountValue: saleDiscountValue,
      discountAmount: saleDiscountAmount,
      taxAmount,
      totalAmount,
      paymentMethod,
      amountPaid,
      changeAmount,
      customerName,
      customerPhone,
      notes: status === SaleStatus.CANCELLED ? 'Transaksi dibatalkan oleh pelanggan' : null,
      createdAt: saleDate,
      updatedAt: saleDate
    };

    sales.push(sale);

    // Create sale items
    selectedItems.forEach((item, index) => {
      const saleItem = {
        id: `item_${i}_${index}_${Date.now()}`,
        saleId: sale.id,
        productId: item.inventoryItem.productId,
        unitId: item.inventoryItem.unitId,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        totalPrice: item.totalPrice,
        discountType: item.discountType,
        discountValue: item.discountValue,
        discountAmount: item.discountAmount,
        batchNumber: item.inventoryItem.batchNumber,
        expiryDate: item.inventoryItem.expiryDate,
        notes: null,
        createdAt: saleDate
      };

      saleItems.push(saleItem);

      // Create stock movement for completed sales
      if (status === SaleStatus.COMPLETED) {
        const stockMovement = {
          id: `movement_${i}_${index}_${Date.now()}`,
          inventoryItemId: item.inventoryItem.id,
          type: StockMovementType.OUT,
          quantity: -item.quantity, // Negative for OUT movement
          unitPrice: item.unitPrice,
          referenceType: 'SALE',
          referenceId: sale.id,
          referenceNumber: saleNumber,
          reason: 'Penjualan produk kepada pelanggan',
          notes: `Batch: ${item.inventoryItem.batchNumber || 'N/A'}`,
          movementDate: saleDate,
          createdAt: saleDate,
          createdBy: cashier.id
        };

        stockMovements.push(stockMovement);
      }
    });
  }

  console.log(`📊 Generated ${sales.length} sales, ${saleItems.length} sale items, ${stockMovements.length} stock movements`);

  // Create sales in batches
  const batchSize = 25;
  for (let i = 0; i < sales.length; i += batchSize) {
    const batch = sales.slice(i, i + batchSize);

    try {
      const result = await prisma.sale.createMany({
        data: batch,
        skipDuplicates: true
      });

      console.log(`💰 Created sales batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(sales.length / batchSize)} (${result.count} sales)`);
    } catch (error) {
      console.error(`❌ Error creating sales batch ${Math.floor(i / batchSize) + 1}:`, error);
    }
  }

  // Create sale items in batches
  for (let i = 0; i < saleItems.length; i += batchSize) {
    const batch = saleItems.slice(i, i + batchSize);

    try {
      const result = await prisma.saleItem.createMany({
        data: batch,
        skipDuplicates: true
      });

      console.log(`📦 Created sale items batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(saleItems.length / batchSize)} (${result.count} items)`);
    } catch (error) {
      console.error(`❌ Error creating sale items batch ${Math.floor(i / batchSize) + 1}:`, error);
    }
  }

  // Create stock movements in batches
  if (stockMovements.length > 0) {
    for (let i = 0; i < stockMovements.length; i += batchSize) {
      const batch = stockMovements.slice(i, i + batchSize);

      try {
        const result = await prisma.stockMovement.createMany({
          data: batch,
          skipDuplicates: true
        });

        console.log(`📈 Created stock movements batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(stockMovements.length / batchSize)} (${result.count} movements)`);
      } catch (error) {
        console.error(`❌ Error creating stock movements batch ${Math.floor(i / batchSize) + 1}:`, error);
      }
    }
  }

  console.log(`✅ Successfully seeded ${sales.length} sales transactions!`);
}

// Main seeding function
export async function seedAllSalesCustomerData() {
  try {
    console.log('🚀 Starting comprehensive sales and customer seeding...');
    console.log('📋 This seeder will create realistic sales and customer data for Indonesian pharmacy operations');
    console.log('💰 Including various payment methods, discounts, and transaction statuses');
    console.log('');

    await seedCustomers();
    await seedSales();

    console.log('');
    console.log('🎉 All sales and customer data seeded successfully!');
    console.log('📊 Summary:');

    const customerStats = await prisma.customer.groupBy({
      by: ['type'],
      _count: { id: true }
    });

    const salesStats = await prisma.sale.groupBy({
      by: ['status'],
      _count: { id: true },
      _sum: { totalAmount: true }
    });

    const totalSalesAmount = await prisma.sale.aggregate({
      _sum: { totalAmount: true },
      _count: { id: true }
    });

    console.log('   📋 Customers:');
    customerStats.forEach(stat => {
      console.log(`     • ${stat.type}: ${stat._count.id} customers`);
    });

    console.log('   💰 Sales:');
    salesStats.forEach(stat => {
      const avgAmount = stat._sum.totalAmount ? Number(stat._sum.totalAmount) / stat._count.id : 0;
      console.log(`     • ${stat.status}: ${stat._count.id} transactions (Avg: Rp ${avgAmount.toLocaleString('id-ID')})`);
    });

    console.log(`   💵 Total Sales: ${totalSalesAmount._count.id} transactions worth Rp ${Number(totalSalesAmount._sum.totalAmount || 0).toLocaleString('id-ID')}`);
    console.log('');
    console.log('✅ Ready for sales operations and customer management!');

  } catch (error) {
    console.error('❌ Error seeding sales and customer data:', error);
    throw error;
  }
}

// CLI execution
async function main() {
  try {
    await seedAllSalesCustomerData();
  } catch (error) {
    console.error('❌ Sales and customer seeding failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  main();
}
