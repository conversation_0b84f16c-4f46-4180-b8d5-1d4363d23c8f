# Indonesian Pharmacy Inventory Seeder

## Overview

The inventory seeder (`seed-inventory.ts`) creates comprehensive, realistic inventory data for the Indonesian pharmacy application. It generates multiple batches per product with varying scenarios to support FIFO/FEFO testing and inventory management operations.

## Features

### 🔒 Environment Safety
- **Development Only**: Automatically checks `NODE_ENV` and prevents execution in production
- **Duplicate Prevention**: Checks for existing inventory items and skips seeding to avoid duplicates
- **Safe Execution**: Can be run multiple times without corrupting data

### 🇮🇩 Indonesian Pharmacy Context
- **Medicine Classifications**: Supports all Indonesian medicine types including the newly added `PSIKOTROPIKA`
  - `OBAT_BEBAS` - Over-the-counter medicines
  - `OBAT_BEBAS_TERBATAS` - Limited OTC with warnings
  - `OBAT_KERAS` - Prescription medicines
  - `NARKOTIKA` - Controlled narcotics
  - `PSIKOTROPIKA` - Controlled psychotropic substances
  - `JAMU` - Traditional herbal remedies
  - `OBAT_HERBAL_TERSTANDAR` - Standardized herbal medicines
  - `FITOFARMAKA` - Clinical tested traditional medicines

- **Storage Locations**: Realistic Indonesian pharmacy storage areas
  - Standard racks (Rak A-1, Rak B-2, etc.)
  - Specialized storage (<PERSON><PERSON><PERSON>, Lemari Na<PERSON>, <PERSON><PERSON><PERSON>rop<PERSON>)
  - Operational areas (Area Dispensing, Area OTC, Area Herbal)

- **Batch Numbers**: Indonesian pharmaceutical batch number patterns
  - Company-specific prefixes (KMF, KLB, CMB, etc.)
  - Date-based sequences
  - Supplier lot references

### 📊 Realistic Data Generation

#### Quantity Management
- **Medicine-Type Based**: Different quantity ranges based on classification
  - High volume: OTC medicines, jamu (larger quantities)
  - Controlled substances: Narkotika, psikotropika (smaller, controlled quantities)
  - Regular medicines: Standard pharmacy quantities

- **Unit-Type Aware**: Quantities appropriate for each unit type
  - Tablets/Capsules: 100-2000 pieces
  - Strips/Blisters: 10-200 units
  - Liquids (ml): 500-5000 ml
  - Tubes/Bottles: 10-100 units

#### Pricing Strategy
- **Indonesian Market Rates**: Realistic cost and selling prices
- **Margin Variation**: ±20% price variation for realistic market conditions
- **Unit Hierarchy**: Proper pricing across different unit levels

#### Date Management
- **FIFO Testing**: Received dates spanning 1-365 days ago
- **FEFO Testing**: Expiry dates based on medicine type and shelf life
  - OTC medicines: 2-3 years shelf life
  - Prescription medicines: 3-5 years shelf life
  - Traditional medicines: 1-2 years shelf life
  - Controlled substances: 3-5 years shelf life

### 🧪 Special Test Scenarios

The seeder creates specific test scenarios for comprehensive FIFO/FEFO testing:

1. **Near Expiry Items**: Products expiring in 15 days
2. **Old Batches**: Items received 10 months ago (FIFO priority)
3. **Fresh Batches**: Recently received items (1 week ago)

Each scenario includes proper batch numbering (`NEAR-`, `OLD-`, `FRESH-` prefixes) for easy identification.

### 📈 Stock Movement Tracking

- **Initial Movements**: Creates `IN` movements for all inventory items
- **Purchase References**: Realistic purchase order numbers
- **Adjustment History**: 30% chance of additional adjustment movements
- **Audit Trail**: Complete movement history with Indonesian descriptions

## Database Schema Integration

### New PSIKOTROPIKA Classification
The seeder includes support for the newly added `PSIKOTROPIKA` classification in the `MedicineClassification` enum:

```sql
-- Migration: 20250611120733_add_psikotropika_classification
ALTER TYPE "MedicineClassification" ADD VALUE 'PSIKOTROPIKA';
```

### Sample Products
Includes a new sample product demonstrating PSIKOTROPIKA classification:
- **Alprazolam 0.5mg** (MED-009)
- Proper controlled substance handling
- Smaller package sizes
- Premium pricing
- Special storage requirements

## Usage

### Run Inventory Seeder Only
```bash
npx ts-node ./src/scripts/seed-inventory.ts
```

### Run Complete Seeding (includes inventory)
```bash
pnpm run db:seed
```

### Prerequisites
1. Database must be running and accessible
2. Supplier data must be seeded first
3. Product data must be seeded first
4. Environment must be development (not production)

## Generated Data Summary

### Inventory Items
- **2-5 batches per product**: Realistic multi-batch inventory
- **Multiple unit levels**: Base units and packaging units
- **Varied suppliers**: Random supplier assignment
- **Mixed status**: 95% active, 5% inactive for testing

### Stock Movements
- **Initial IN movements**: For all inventory items
- **Random adjustments**: 30% of items get additional movements
- **Proper references**: Purchase orders, adjustment numbers
- **Indonesian descriptions**: All movement reasons in Indonesian

### Storage Distribution
- **Organized locations**: Proper pharmacy storage areas
- **Special handling**: Controlled substances in secure locations
- **Operational flow**: Items distributed across receiving, dispensing, and storage areas

## Integration with Existing Systems

### FIFO/FEFO Compatibility
- Works seamlessly with `StockAllocationService`
- Supports both FIFO and FEFO allocation methods
- Provides realistic test scenarios for allocation logic

### Inventory Management
- Compatible with existing `InventoryService`
- Supports all inventory operations (adjustments, transfers, etc.)
- Provides comprehensive data for analytics and reporting

### Indonesian Compliance
- Follows Indonesian pharmacy regulations
- Proper medicine classification handling
- Controlled substance management
- Audit trail requirements

## Safety Features

1. **Environment Checks**: Prevents production execution
2. **Duplicate Prevention**: Safe re-execution
3. **Transaction Safety**: Batch processing with error handling
4. **Data Integrity**: Proper foreign key relationships
5. **Rollback Support**: Can be cleared and re-seeded

## Performance

- **Batch Processing**: Creates items in batches of 50
- **Optimized Queries**: Efficient database operations
- **Progress Tracking**: Console output for monitoring
- **Memory Efficient**: Processes data in chunks

## Testing Support

The seeder provides comprehensive data for testing:
- **FIFO/FEFO algorithms**
- **Stock allocation logic**
- **Inventory analytics**
- **Expiry date management**
- **Multi-batch scenarios**
- **Controlled substance handling**

## Future Enhancements

Potential improvements for the seeder:
1. **Configurable quantities**: Environment-based quantity ranges
2. **Custom scenarios**: Additional test scenario types
3. **Seasonal variations**: Time-based inventory patterns
4. **Supplier preferences**: Product-supplier relationship modeling
5. **Cost fluctuations**: Historical price variations
