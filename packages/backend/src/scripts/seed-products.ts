import { PrismaClient, UnitType, ProductType, ProductCategory, MedicineClassification } from '@prisma/client';

const prisma = new PrismaClient();

async function seedProductUnits() {
  console.log('🌱 Seeding Product Units...');

  const units = [
    // ===== MEDICINE DOSAGE FORM UNITS (Bentuk Sediaan Obat) =====

    // Solid Forms (Bentuk Padat)
    { name: 'Tablet', abbreviation: 'tab', type: UnitType.COUNT, isBaseUnit: true, description: 'Tablet - bentuk sediaan padat bulat atau oval' },
    { name: 'Kaplet', abbreviation: 'kaplet', type: UnitType.COUNT, isBaseUnit: true, description: 'Kaplet - tablet salut selaput berbentuk kapsul' },
    { name: '<PERSON><PERSON><PERSON>', abbreviation: 'kaps', type: UnitType.COUNT, isBaseUnit: true, description: 'Kapsul - bentuk sediaan berupa cangkang keras atau lunak' },
    { name: 'Pil', abbreviation: 'pil', type: UnitType.COUNT, isBaseUnit: true, description: 'Pil - bentuk sediaan bulat kecil tradisional' },
    { name: '<PERSON><PERSON><PERSON>', abbreviation: 'puyer', type: UnitType.COUNT, isBaseUnit: true, description: 'Puyer - serbuk obat yang dibungkus kertas perkamen' },
    { name: 'Lozenges', abbreviation: 'loz', type: UnitType.COUNT, isBaseUnit: true, description: 'Lozenges - tablet hisap untuk tenggorokan' },

    // Liquid Forms (Bentuk Cair)
    { name: 'Mililiter', abbreviation: 'ml', type: UnitType.VOLUME, isBaseUnit: true, description: 'Mililiter - satuan volume untuk sediaan cair' },
    { name: 'Liter', abbreviation: 'L', type: UnitType.VOLUME, isBaseUnit: false, description: 'Liter - satuan volume besar untuk sediaan cair' },
    { name: 'Sirup', abbreviation: 'syr', type: UnitType.VOLUME, isBaseUnit: true, description: 'Sirup - sediaan cair manis, diukur dalam ml' },
    { name: 'Suspensi', abbreviation: 'susp', type: UnitType.VOLUME, isBaseUnit: true, description: 'Suspensi - sediaan cair dengan partikel tersuspensi' },
    { name: 'Emulsi', abbreviation: 'emul', type: UnitType.VOLUME, isBaseUnit: true, description: 'Emulsi - sediaan cair campuran minyak dan air' },
    { name: 'Tetes', abbreviation: 'tts', type: UnitType.COUNT, isBaseUnit: true, description: 'Tetes - satuan untuk obat tetes (drops)' },
    { name: 'Tetes Mata', abbreviation: 'tm', type: UnitType.VOLUME, isBaseUnit: true, description: 'Tetes mata - obat tetes untuk mata' },
    { name: 'Tetes Telinga', abbreviation: 'tt', type: UnitType.VOLUME, isBaseUnit: true, description: 'Tetes telinga - obat tetes untuk telinga' },
    { name: 'Tetes Hidung', abbreviation: 'th', type: UnitType.VOLUME, isBaseUnit: true, description: 'Tetes hidung - obat tetes untuk hidung' },
    { name: 'Tetes Mulut', abbreviation: 'tmul', type: UnitType.VOLUME, isBaseUnit: true, description: 'Tetes mulut - obat tetes oral' },

    // Semi-solid Forms (Bentuk Semi Padat)
    { name: 'Gram', abbreviation: 'g', type: UnitType.WEIGHT, isBaseUnit: true, description: 'Gram - satuan berat untuk sediaan semi padat' },
    { name: 'Miligram', abbreviation: 'mg', type: UnitType.WEIGHT, isBaseUnit: true, description: 'Miligram - satuan berat kecil untuk sediaan aktif' },
    { name: 'Kilogram', abbreviation: 'kg', type: UnitType.WEIGHT, isBaseUnit: false, description: 'Kilogram - satuan berat besar' },
    { name: 'Salep', abbreviation: 'salep', type: UnitType.WEIGHT, isBaseUnit: true, description: 'Salep - sediaan semi padat berminyak, diukur dalam gram' },
    { name: 'Krim', abbreviation: 'krim', type: UnitType.WEIGHT, isBaseUnit: true, description: 'Krim - sediaan semi padat emulsi, diukur dalam gram' },
    { name: 'Gel', abbreviation: 'gel', type: UnitType.WEIGHT, isBaseUnit: true, description: 'Gel - sediaan semi padat jernih, diukur dalam gram' },
    { name: 'Pasta', abbreviation: 'pasta', type: UnitType.WEIGHT, isBaseUnit: true, description: 'Pasta - sediaan semi padat kental, diukur dalam gram' },

    // Other Forms (Bentuk Lainnya)
    { name: 'Supositoria', abbreviation: 'supp', type: UnitType.COUNT, isBaseUnit: true, description: 'Supositoria - sediaan padat untuk rektal/vaginal' },
    { name: 'Ovula', abbreviation: 'ovul', type: UnitType.COUNT, isBaseUnit: true, description: 'Ovula - supositoria khusus untuk vaginal' },
    { name: 'Puff', abbreviation: 'puff', type: UnitType.COUNT, isBaseUnit: true, description: 'Puff - dosis semprotan untuk inhaler' },
    { name: 'Dosis', abbreviation: 'dos', type: UnitType.COUNT, isBaseUnit: true, description: 'Dosis - satuan takaran obat' },
    { name: 'Ampul', abbreviation: 'amp', type: UnitType.COUNT, isBaseUnit: true, description: 'Ampul - wadah gelas tertutup untuk injeksi' },
    { name: 'Vial', abbreviation: 'vial', type: UnitType.COUNT, isBaseUnit: true, description: 'Vial - wadah kaca/plastik untuk injeksi' },

    // ===== PACKAGING/SALES UNITS (Satuan Kemasan) =====

    { name: 'Strip', abbreviation: 'strip', type: UnitType.PACKAGE, isBaseUnit: false, description: 'Strip/Blister - kemasan lembaran untuk tablet/kapsul' },
    { name: 'Blister', abbreviation: 'blstr', type: UnitType.PACKAGE, isBaseUnit: false, description: 'Blister - kemasan plastik transparan untuk tablet/kapsul' },
    { name: 'Botol', abbreviation: 'btl', type: UnitType.PACKAGE, isBaseUnit: false, description: 'Botol - wadah untuk cairan atau tablet curah' },
    { name: 'Fles', abbreviation: 'fls', type: UnitType.PACKAGE, isBaseUnit: false, description: 'Fles - botol kaca untuk sediaan cair' },
    { name: 'Tube', abbreviation: 'tube', type: UnitType.PACKAGE, isBaseUnit: false, description: 'Tube - kemasan untuk salep, krim, gel' },
    { name: 'Sachet', abbreviation: 'scht', type: UnitType.PACKAGE, isBaseUnit: false, description: 'Sachet - kemasan sekali pakai untuk puyer/serbuk' },
    { name: 'Box', abbreviation: 'box', type: UnitType.PACKAGE, isBaseUnit: false, description: 'Box - kemasan luar/dus untuk produk farmasi' },
    { name: 'Dus', abbreviation: 'dus', type: UnitType.PACKAGE, isBaseUnit: false, description: 'Dus - kemasan karton luar untuk obat' },
    { name: 'Kotak', abbreviation: 'ktk', type: UnitType.PACKAGE, isBaseUnit: false, description: 'Kotak - kemasan berbentuk kotak untuk berbagai sediaan' },
    { name: 'Pot', abbreviation: 'pot', type: UnitType.PACKAGE, isBaseUnit: false, description: 'Pot - wadah kecil untuk sediaan racikan/compounding' },
    { name: 'Rol', abbreviation: 'rol', type: UnitType.PACKAGE, isBaseUnit: false, description: 'Rol/Gulung - kemasan gulungan untuk perban/plester' },
    { name: 'Gulung', abbreviation: 'glg', type: UnitType.PACKAGE, isBaseUnit: false, description: 'Gulung - kemasan berbentuk gulungan' },

    // ===== NON-MEDICINE/MEDICAL DEVICE UNITS (Alat Kesehatan) =====

    { name: 'Buah', abbreviation: 'bh', type: UnitType.COUNT, isBaseUnit: true, description: 'Buah - satuan untuk alat kesehatan individual' },
    { name: 'Pcs', abbreviation: 'pcs', type: UnitType.COUNT, isBaseUnit: true, description: 'Pieces - satuan untuk item individual (sinonim buah)' },
    { name: 'Lembar', abbreviation: 'lbr', type: UnitType.COUNT, isBaseUnit: true, description: 'Lembar - satuan untuk item berbentuk lembaran' },
    { name: 'Pasang', abbreviation: 'psg', type: UnitType.COUNT, isBaseUnit: true, description: 'Pasang - satuan untuk item berpasangan (sepatu, sarung tangan)' },
    { name: 'Set', abbreviation: 'set', type: UnitType.COUNT, isBaseUnit: true, description: 'Set - satuan untuk kit/perangkat lengkap alat kesehatan' },

    // ===== ADDITIONAL MEASUREMENT UNITS =====

    { name: 'Meter', abbreviation: 'm', type: UnitType.LENGTH, isBaseUnit: true, description: 'Meter - satuan panjang untuk perban, selang, dll' },
    { name: 'Sentimeter', abbreviation: 'cm', type: UnitType.LENGTH, isBaseUnit: true, description: 'Sentimeter - satuan panjang kecil' },
    { name: 'Milimeter', abbreviation: 'mm', type: UnitType.LENGTH, isBaseUnit: true, description: 'Milimeter - satuan panjang sangat kecil' },
  ];

  for (const unit of units) {
    await prisma.productUnit.upsert({
      where: { name_type: { name: unit.name, type: unit.type } },
      update: unit,
      create: unit,
    });
  }

  console.log('✅ Product Units seeded successfully!');
}

async function seedProducts() {
  console.log('🌱 Seeding Products...');

  // Get unit IDs for reference
  const tabletUnit = await prisma.productUnit.findFirst({ where: { name: 'Tablet' } });
  const stripUnit = await prisma.productUnit.findFirst({ where: { name: 'Strip' } });
  const boxUnit = await prisma.productUnit.findFirst({ where: { name: 'Box' } });
  const mlUnit = await prisma.productUnit.findFirst({ where: { name: 'Mililiter' } });
  const botolUnit = await prisma.productUnit.findFirst({ where: { name: 'Botol' } });
  const gramUnit = await prisma.productUnit.findFirst({ where: { name: 'Gram' } });
  const tubeUnit = await prisma.productUnit.findFirst({ where: { name: 'Tube' } });
  const kapsulUnit = await prisma.productUnit.findFirst({ where: { name: 'Kapsul' } });

  if (!tabletUnit || !stripUnit || !boxUnit || !mlUnit || !botolUnit || !gramUnit || !tubeUnit || !kapsulUnit) {
    throw new Error('Required product units not found. Please run seedProductUnits first.');
  }

  const products = [
    // Common Indonesian Medicines
    {
      code: 'MED-001',
      name: 'Paracetamol 500mg',
      genericName: 'Paracetamol',
      type: ProductType.MEDICINE,
      category: ProductCategory.ANALGESIC,
      manufacturer: 'PT Kimia Farma',
      bpomNumber: 'DKL1234567890A1',
      medicineClassification: MedicineClassification.OBAT_BEBAS,
      regulatorySymbol: 'Lingkaran hijau - Obat bebas yang dapat dibeli tanpa resep dokter',
      baseUnitId: tabletUnit.id,
      minimumStock: 100,
      maximumStock: 1000,
      reorderPoint: 200,
      description: 'Obat penurun demam dan pereda nyeri',
      activeIngredient: 'Paracetamol 500mg',
      strength: '500mg',
      dosageForm: 'Tablet',
      indication: 'Demam, sakit kepala, nyeri ringan hingga sedang',
      contraindication: 'Hipersensitif terhadap paracetamol',
      sideEffects: 'Jarang: ruam kulit, mual',
      dosage: 'Dewasa: 1-2 tablet, 3-4 kali sehari',
      storage: 'Simpan di tempat sejuk dan kering, terhindar dari cahaya matahari',
    },
    {
      code: 'MED-002',
      name: 'Amoxicillin 500mg',
      genericName: 'Amoxicillin',
      type: ProductType.MEDICINE,
      category: ProductCategory.ANTIBIOTIC,
      manufacturer: 'PT Kalbe Farma',
      bpomNumber: 'DKL2345678901B2',
      medicineClassification: MedicineClassification.OBAT_KERAS,
      regulatorySymbol: 'Lingkaran merah dengan huruf "K" - Obat keras yang memerlukan resep dokter',
      baseUnitId: kapsulUnit.id,
      minimumStock: 50,
      maximumStock: 500,
      reorderPoint: 100,
      description: 'Antibiotik untuk infeksi bakteri',
      activeIngredient: 'Amoxicillin trihydrate 500mg',
      strength: '500mg',
      dosageForm: 'Kapsul',
      indication: 'Infeksi saluran pernapasan, infeksi saluran kemih, infeksi kulit',
      contraindication: 'Hipersensitif terhadap penisilin',
      sideEffects: 'Diare, mual, muntah, ruam kulit',
      dosage: 'Dewasa: 1 kapsul, 3 kali sehari',
      storage: 'Simpan di tempat sejuk dan kering',
    },
    {
      code: 'MED-003',
      name: 'OBH Combi Batuk Berdahak',
      genericName: 'Bromhexine HCl + Guaifenesin + Dextromethorphan HBr',
      type: ProductType.MEDICINE,
      category: ProductCategory.COUGH_COLD,
      manufacturer: 'PT Combiphar',
      bpomNumber: 'DTL3456789012C3',
      medicineClassification: MedicineClassification.OBAT_BEBAS_TERBATAS,
      regulatorySymbol: 'Lingkaran biru dengan peringatan P.No. - Obat bebas terbatas dengan peringatan khusus',
      baseUnitId: mlUnit.id,
      minimumStock: 500,
      maximumStock: 2000,
      reorderPoint: 800,
      description: 'Sirup obat batuk berdahak',
      activeIngredient: 'Bromhexine HCl 4mg, Guaifenesin 100mg, Dextromethorphan HBr 15mg per 5ml',
      strength: '4mg/100mg/15mg per 5ml',
      dosageForm: 'Sirup',
      indication: 'Batuk berdahak',
      contraindication: 'Hipersensitif terhadap komponen obat',
      sideEffects: 'Mengantuk, mual, pusing',
      dosage: 'Dewasa: 3 sendok teh (15ml), 3 kali sehari',
      storage: 'Simpan di tempat sejuk, jangan dibekukan',
    },
    {
      code: 'MED-004',
      name: 'Betadine Solution 60ml',
      genericName: 'Povidone Iodine',
      type: ProductType.MEDICINE,
      category: ProductCategory.TOPICAL,
      manufacturer: 'PT Mahakam Beta Farma',
      bpomNumber: 'DTL4567890123D4',
      medicineClassification: MedicineClassification.OBAT_BEBAS,
      regulatorySymbol: 'Lingkaran hijau - Obat bebas untuk antiseptik luar',
      baseUnitId: mlUnit.id,
      minimumStock: 200,
      maximumStock: 800,
      reorderPoint: 300,
      description: 'Antiseptik untuk luka luar',
      activeIngredient: 'Povidone Iodine 10%',
      strength: '10%',
      dosageForm: 'Larutan',
      indication: 'Antiseptik untuk luka kecil, goresan, luka bakar ringan',
      contraindication: 'Hipersensitif terhadap iodine',
      sideEffects: 'Iritasi kulit pada penggunaan berlebihan',
      dosage: 'Oleskan secukupnya pada area yang terluka',
      storage: 'Simpan di tempat sejuk dan kering',
    },
    {
      code: 'MED-005',
      name: 'Antasida DOEN Tablet',
      genericName: 'Aluminium Hydroxide + Magnesium Hydroxide',
      type: ProductType.MEDICINE,
      category: ProductCategory.ANTACID,
      manufacturer: 'PT Dankos Farma',
      bpomNumber: 'DKL5678901234E5',
      medicineClassification: MedicineClassification.OBAT_BEBAS,
      regulatorySymbol: 'Lingkaran hijau - Obat bebas untuk gangguan pencernaan',
      baseUnitId: tabletUnit.id,
      minimumStock: 150,
      maximumStock: 600,
      reorderPoint: 250,
      description: 'Obat maag dan gangguan pencernaan',
      activeIngredient: 'Aluminium Hydroxide 200mg, Magnesium Hydroxide 200mg',
      strength: '200mg/200mg',
      dosageForm: 'Tablet kunyah',
      indication: 'Maag, kelebihan asam lambung, gangguan pencernaan',
      contraindication: 'Gangguan ginjal berat',
      sideEffects: 'Konstipasi atau diare ringan',
      dosage: 'Dewasa: 1-2 tablet, 3-4 kali sehari setelah makan',
      storage: 'Simpan di tempat sejuk dan kering',
    },
    {
      code: 'MED-006',
      name: 'Salep Gentamicin 0.1%',
      genericName: 'Gentamicin Sulfate',
      type: ProductType.MEDICINE,
      category: ProductCategory.TOPICAL,
      manufacturer: 'PT Bernofarm',
      bpomNumber: 'DKL6789012345F6',
      medicineClassification: MedicineClassification.OBAT_KERAS,
      regulatorySymbol: 'Lingkaran merah dengan huruf "K" - Obat keras topikal',
      baseUnitId: gramUnit.id,
      minimumStock: 50,
      maximumStock: 200,
      reorderPoint: 80,
      description: 'Salep antibiotik untuk infeksi kulit',
      activeIngredient: 'Gentamicin Sulfate 0.1%',
      strength: '0.1%',
      dosageForm: 'Salep',
      indication: 'Infeksi kulit dan jaringan lunak',
      contraindication: 'Hipersensitif terhadap gentamicin',
      sideEffects: 'Iritasi kulit ringan',
      dosage: 'Oleskan tipis 2-3 kali sehari',
      storage: 'Simpan di tempat sejuk, jangan dibekukan',
    },
    {
      code: 'MED-007',
      name: 'Vitamin C 1000mg Effervescent',
      genericName: 'Ascorbic Acid',
      type: ProductType.MEDICINE,
      category: ProductCategory.VITAMIN,
      manufacturer: 'PT Sido Muncul',
      bpomNumber: 'DTL7890123456G7',
      medicineClassification: MedicineClassification.OBAT_BEBAS,
      regulatorySymbol: 'Lingkaran hijau - Suplemen vitamin bebas',
      baseUnitId: tabletUnit.id,
      minimumStock: 200,
      maximumStock: 1000,
      reorderPoint: 400,
      description: 'Vitamin C effervescent untuk daya tahan tubuh',
      activeIngredient: 'Ascorbic Acid 1000mg',
      strength: '1000mg',
      dosageForm: 'Tablet Effervescent',
      indication: 'Suplemen vitamin C, meningkatkan daya tahan tubuh',
      contraindication: 'Batu ginjal, gangguan ginjal berat',
      sideEffects: 'Gangguan pencernaan pada dosis tinggi',
      dosage: 'Dewasa: 1 tablet per hari, larutkan dalam air',
      storage: 'Simpan di tempat kering, tutup rapat setelah dibuka',
    },
    {
      code: 'MED-008',
      name: 'Jamu Tolak Angin Cair',
      genericName: 'Herbal Traditional Medicine',
      type: ProductType.MEDICINE,
      category: ProductCategory.COUGH_COLD,
      manufacturer: 'PT Sido Muncul',
      bpomNumber: 'TR8901234567H8',
      medicineClassification: MedicineClassification.JAMU,
      regulatorySymbol: 'Daun hijau - Jamu tradisional berdasarkan pengetahuan turun temurun',
      baseUnitId: mlUnit.id,
      minimumStock: 300,
      maximumStock: 1200,
      reorderPoint: 500,
      description: 'Jamu tradisional untuk masuk angin dan flu',
      activeIngredient: 'Ekstrak jahe, meniran, temulawak, dan rempah tradisional',
      strength: 'Ekstrak herbal',
      dosageForm: 'Cairan Jamu',
      indication: 'Masuk angin, flu, badan tidak enak',
      contraindication: 'Hipersensitif terhadap komponen herbal',
      sideEffects: 'Umumnya aman, dapat menyebabkan rasa hangat',
      dosage: 'Dewasa: 15ml, 2-3 kali sehari',
      storage: 'Simpan di tempat sejuk, kocok sebelum diminum',
    },
    {
      code: 'MED-009',
      name: 'Alprazolam 0.5mg',
      genericName: 'Alprazolam',
      type: ProductType.MEDICINE,
      category: ProductCategory.OTHER,
      manufacturer: 'PT Dexa Medica',
      bpomNumber: 'DKL9012345678I9',
      medicineClassification: MedicineClassification.PSIKOTROPIKA,
      regulatorySymbol: 'Lingkaran biru dengan tanda silang merah - Obat psikotropika yang memerlukan resep dokter dan pencatatan khusus',
      baseUnitId: tabletUnit.id,
      minimumStock: 20,
      maximumStock: 100,
      reorderPoint: 30,
      description: 'Obat psikotropika untuk gangguan kecemasan',
      activeIngredient: 'Alprazolam 0.5mg',
      strength: '0.5mg',
      dosageForm: 'Tablet',
      indication: 'Gangguan kecemasan, gangguan panik',
      contraindication: 'Hipersensitif terhadap benzodiazepine, glaukoma sudut sempit, myasthenia gravis',
      sideEffects: 'Mengantuk, pusing, gangguan koordinasi, ketergantungan',
      dosage: 'Dewasa: 0.25-0.5mg, 2-3 kali sehari sesuai petunjuk dokter',
      storage: 'Simpan di tempat sejuk dan kering, jauh dari jangkauan anak-anak, dalam lemari khusus psikotropika',
    },
  ];

  for (const product of products) {
    await prisma.product.upsert({
      where: { code: product.code },
      update: product,
      create: product,
    });
  }

  console.log('✅ Products seeded successfully!');
}

async function seedProductUnitHierarchies() {
  console.log('🌱 Seeding Product Unit Hierarchies...');

  // Get products and units
  const paracetamol = await prisma.product.findFirst({ where: { code: 'MED-001' } });
  const amoxicillin = await prisma.product.findFirst({ where: { code: 'MED-002' } });
  const obhCombi = await prisma.product.findFirst({ where: { code: 'MED-003' } });
  const betadine = await prisma.product.findFirst({ where: { code: 'MED-004' } });
  const antasida = await prisma.product.findFirst({ where: { code: 'MED-005' } });
  const gentamicin = await prisma.product.findFirst({ where: { code: 'MED-006' } });
  const vitaminC = await prisma.product.findFirst({ where: { code: 'MED-007' } });
  const jamuTolakAngin = await prisma.product.findFirst({ where: { code: 'MED-008' } });
  const alprazolam = await prisma.product.findFirst({ where: { code: 'MED-009' } });

  const tabletUnit = await prisma.productUnit.findFirst({ where: { name: 'Tablet' } });
  const stripUnit = await prisma.productUnit.findFirst({ where: { name: 'Strip' } });
  const boxUnit = await prisma.productUnit.findFirst({ where: { name: 'Box' } });
  const mlUnit = await prisma.productUnit.findFirst({ where: { name: 'Mililiter' } });
  const botolUnit = await prisma.productUnit.findFirst({ where: { name: 'Botol' } });
  const kapsulUnit = await prisma.productUnit.findFirst({ where: { name: 'Kapsul' } });
  const gramUnit = await prisma.productUnit.findFirst({ where: { name: 'Gram' } });
  const tubeUnit = await prisma.productUnit.findFirst({ where: { name: 'Tube' } });
  const sachetUnit = await prisma.productUnit.findFirst({ where: { name: 'Sachet' } });

  if (!paracetamol || !amoxicillin || !obhCombi || !betadine || !antasida || !gentamicin || !vitaminC || !jamuTolakAngin || !alprazolam) {
    throw new Error('Required products not found. Please run seedProducts first.');
  }

  if (!tabletUnit || !stripUnit || !boxUnit || !mlUnit || !botolUnit || !kapsulUnit || !gramUnit || !tubeUnit || !sachetUnit) {
    throw new Error('Required product units not found. Please run seedProductUnits first.');
  }

  const hierarchies = [
    // Paracetamol 500mg (Tablet → Strip → Box)
    {
      productId: paracetamol.id,
      unitId: tabletUnit.id,
      parentUnitId: null,
      conversionFactor: 1,
      level: 0,
      sellingPrice: 500, // Rp 500 per tablet
      costPrice: 300,
    },
    {
      productId: paracetamol.id,
      unitId: stripUnit.id,
      parentUnitId: null,
      conversionFactor: 10, // 1 strip = 10 tablets
      level: 1,
      sellingPrice: 4500, // Rp 4,500 per strip (10% discount)
      costPrice: 2800,
    },
    {
      productId: paracetamol.id,
      unitId: boxUnit.id,
      parentUnitId: null,
      conversionFactor: 100, // 1 box = 100 tablets (10 strips)
      level: 2,
      sellingPrice: 42000, // Rp 42,000 per box (16% discount)
      costPrice: 26000,
    },

    // Amoxicillin 500mg (Kapsul → Strip → Box)
    {
      productId: amoxicillin.id,
      unitId: kapsulUnit.id,
      parentUnitId: null,
      conversionFactor: 1,
      level: 0,
      sellingPrice: 1200, // Rp 1,200 per capsule
      costPrice: 800,
    },
    {
      productId: amoxicillin.id,
      unitId: stripUnit.id,
      parentUnitId: null,
      conversionFactor: 10, // 1 strip = 10 capsules
      level: 1,
      sellingPrice: 11000, // Rp 11,000 per strip
      costPrice: 7500,
    },
    {
      productId: amoxicillin.id,
      unitId: boxUnit.id,
      parentUnitId: null,
      conversionFactor: 100, // 1 box = 100 capsules
      level: 2,
      sellingPrice: 105000, // Rp 105,000 per box
      costPrice: 72000,
    },

    // OBH Combi (ml → Botol)
    {
      productId: obhCombi.id,
      unitId: mlUnit.id,
      parentUnitId: null,
      conversionFactor: 1,
      level: 0,
      sellingPrice: 300, // Rp 300 per ml
      costPrice: 200,
    },
    {
      productId: obhCombi.id,
      unitId: botolUnit.id,
      parentUnitId: null,
      conversionFactor: 60, // 1 bottle = 60ml
      level: 1,
      sellingPrice: 16500, // Rp 16,500 per bottle
      costPrice: 11000,
    },

    // Betadine Solution (ml → Botol)
    {
      productId: betadine.id,
      unitId: mlUnit.id,
      parentUnitId: null,
      conversionFactor: 1,
      level: 0,
      sellingPrice: 400, // Rp 400 per ml
      costPrice: 280,
    },
    {
      productId: betadine.id,
      unitId: botolUnit.id,
      parentUnitId: null,
      conversionFactor: 60, // 1 bottle = 60ml
      level: 1,
      sellingPrice: 22000, // Rp 22,000 per bottle
      costPrice: 15500,
    },

    // Antasida DOEN (Tablet → Strip → Box)
    {
      productId: antasida.id,
      unitId: tabletUnit.id,
      parentUnitId: null,
      conversionFactor: 1,
      level: 0,
      sellingPrice: 800, // Rp 800 per tablet
      costPrice: 500,
    },
    {
      productId: antasida.id,
      unitId: stripUnit.id,
      parentUnitId: null,
      conversionFactor: 10, // 1 strip = 10 tablets
      level: 1,
      sellingPrice: 7200, // Rp 7,200 per strip
      costPrice: 4500,
    },
    {
      productId: antasida.id,
      unitId: boxUnit.id,
      parentUnitId: null,
      conversionFactor: 50, // 1 box = 50 tablets (5 strips)
      level: 2,
      sellingPrice: 34000, // Rp 34,000 per box
      costPrice: 21000,
    },

    // Salep Gentamicin (Gram → Tube)
    {
      productId: gentamicin.id,
      unitId: gramUnit.id,
      parentUnitId: null,
      conversionFactor: 1,
      level: 0,
      sellingPrice: 2000, // Rp 2,000 per gram
      costPrice: 1200,
    },
    {
      productId: gentamicin.id,
      unitId: tubeUnit.id,
      parentUnitId: null,
      conversionFactor: 5, // 1 tube = 5 grams
      level: 1,
      sellingPrice: 9000, // Rp 9,000 per tube (10% discount)
      costPrice: 5500,
    },

    // Vitamin C Effervescent (Tablet → Tube → Box)
    {
      productId: vitaminC.id,
      unitId: tabletUnit.id,
      parentUnitId: null,
      conversionFactor: 1,
      level: 0,
      sellingPrice: 3000, // Rp 3,000 per tablet
      costPrice: 2000,
    },
    {
      productId: vitaminC.id,
      unitId: tubeUnit.id,
      parentUnitId: null,
      conversionFactor: 10, // 1 tube = 10 tablets
      level: 1,
      sellingPrice: 27000, // Rp 27,000 per tube (10% discount)
      costPrice: 18000,
    },
    {
      productId: vitaminC.id,
      unitId: boxUnit.id,
      parentUnitId: null,
      conversionFactor: 60, // 1 box = 60 tablets (6 tubes)
      level: 2,
      sellingPrice: 150000, // Rp 150,000 per box (17% discount)
      costPrice: 105000,
    },

    // Jamu Tolak Angin (ml → Sachet → Box)
    {
      productId: jamuTolakAngin.id,
      unitId: mlUnit.id,
      parentUnitId: null,
      conversionFactor: 1,
      level: 0,
      sellingPrice: 500, // Rp 500 per ml
      costPrice: 300,
    },
    {
      productId: jamuTolakAngin.id,
      unitId: sachetUnit.id,
      parentUnitId: null,
      conversionFactor: 15, // 1 sachet = 15ml
      level: 1,
      sellingPrice: 7000, // Rp 7,000 per sachet
      costPrice: 4200,
    },
    {
      productId: jamuTolakAngin.id,
      unitId: boxUnit.id,
      parentUnitId: null,
      conversionFactor: 120, // 1 box = 120ml (8 sachets)
      level: 2,
      sellingPrice: 52000, // Rp 52,000 per box (7% discount)
      costPrice: 31000,
    },

    // Alprazolam 0.5mg (Tablet → Strip → Box) - PSIKOTROPIKA
    {
      productId: alprazolam.id,
      unitId: tabletUnit.id,
      parentUnitId: null,
      conversionFactor: 1,
      level: 0,
      sellingPrice: 5000, // Rp 5,000 per tablet (controlled substance premium)
      costPrice: 3000,
    },
    {
      productId: alprazolam.id,
      unitId: stripUnit.id,
      parentUnitId: null,
      conversionFactor: 10, // 1 strip = 10 tablets
      level: 1,
      sellingPrice: 47500, // Rp 47,500 per strip (5% discount)
      costPrice: 28500,
    },
    {
      productId: alprazolam.id,
      unitId: boxUnit.id,
      parentUnitId: null,
      conversionFactor: 30, // 1 box = 30 tablets (3 strips) - smaller box for controlled substance
      level: 2,
      sellingPrice: 140000, // Rp 140,000 per box (7% discount)
      costPrice: 85000,
    },
  ];

  for (const hierarchy of hierarchies) {
    await prisma.productUnitHierarchy.upsert({
      where: { productId_unitId: { productId: hierarchy.productId, unitId: hierarchy.unitId } },
      update: hierarchy,
      create: hierarchy,
    });
  }

  console.log('✅ Product Unit Hierarchies seeded successfully!');
}



export async function seedAllProductData() {
  try {
    await seedProductUnits();
    await seedProducts();
    await seedProductUnitHierarchies();
    console.log('🎉 All product data seeded successfully!');
  } catch (error) {
    console.error('❌ Error seeding product data:', error);
    throw error;
  }
}

// Run if called directly
if (require.main === module) {
  seedAllProductData()
    .catch((error) => {
      console.error(error);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}
