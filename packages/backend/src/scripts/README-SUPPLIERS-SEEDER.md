# Indonesian Pharmacy Suppliers Database Seeder

This comprehensive seeder generates realistic Indonesian pharmacy supplier data for the NestJS backend application.

## 🎯 Overview

The seeder creates **100 realistic Indonesian pharmaceutical suppliers** with complete business information, contacts, and Indonesian-specific data formats.

## 📊 Data Distribution

### Supplier Types
- **60% PBF** (Pedagang Besar Farmasi) - Major pharmaceutical distributors
- **25% DISTRIBUTOR** - Regional distributors  
- **10% MANUFACTURER** - Pharmaceutical manufacturers
- **5% LOCAL** - Smaller wholesale suppliers

### Status Distribution
- **80% ACTIVE** - Currently operating suppliers
- **15% INACTIVE** - Temporarily inactive suppliers
- **5% SUSPENDED** - Suspended suppliers

## 🇮🇩 Indonesian-Specific Features

### Company Names
- **PBF**: PT Kimia Farma, PT Kalbe Farma, PT Indofarma, PT Pharos Indonesia, etc.
- **Distributors**: PT Enseval Putera Megatrading, PT Rajawali Nusindo, etc.
- **Manufacturers**: PT Industri Farmasi Nusantara, PT Pabrik Obat Indonesia, etc.
- **Local**: CV Apotek Supplier Lokal, UD Farmasi Berkah, etc.

### Address Information
- **Complete Indonesian addresses** with realistic street names
- **10 major provinces**: DKI Jakarta, Jawa Barat, Jawa Tengah, Jawa Timur, etc.
- **Major cities** in each province
- **Valid postal codes** (5-digit format)

### Business Registration
- **NPWP Format**: XX.XXX.XXX.X-XXX.XXX (Indonesian tax ID)
- **License Numbers**: PBF.XXXX/31.74.02.1009.13.07/2024
- **Pharmacy Licenses**: SIA.XXXX/DINKES/31.74/2024

### Contact Information
- **Indonesian phone numbers**: +62 21 XXXX XXXX format
- **Mobile numbers**: +62 8XX XXXX XXXX format
- **Company email domains**: <EMAIL>
- **Indonesian websites**: www.companyname.co.id

### Financial Information
- **Credit limits**: Rp 10M - Rp 1B (based on supplier type)
- **Payment terms**: 7-90 days (varies by type)
- **Indonesian bank accounts** with major banks (BCA, Mandiri, BRI, BNI, etc.)

### Contact Persons
- **1-3 contacts per supplier**
- **Realistic Indonesian names** (first + last name combinations)
- **Professional positions**: Sales Manager, Account Manager, etc.
- **Primary contact designation**
- **90% active contact rate**

## 🚀 Usage

### Run Supplier Seeder Only
```bash
npm run seed:suppliers
```

### Run All Seeders (including suppliers)
```bash
npm run db:seed
```

### Verify Seeded Data
```bash
npm run verify:suppliers
```

## 📁 Files Structure

```
src/scripts/
├── seed-suppliers.ts          # Main supplier seeder
├── verify-suppliers.ts        # Data verification script
├── seed.ts                   # Main seeder (includes suppliers)
└── README-SUPPLIERS-SEEDER.md # This documentation
```

## 🔧 Technical Details

### Generated Data Examples

**Supplier Code Format:**
- PBF001, PBF002, ... (PBF suppliers)
- DST001, DST002, ... (Distributors)
- MFG001, MFG002, ... (Manufacturers)
- LOC001, LOC002, ... (Local suppliers)

**NPWP Example:** `91.653.192.9-814.953`

**License Example:** `PBF.1234/31.74.02.1009.13.07/2024`

**Phone Example:** `+62 21 6134 1478`

**Email Example:** `<EMAIL>`

### Credit Limits by Type
- **PBF**: Rp 100M - Rp 500M
- **Manufacturer**: Rp 200M - Rp 1B  
- **Distributor**: Rp 50M - Rp 300M
- **Local**: Rp 10M - Rp 100M

### Payment Terms by Type
- **PBF**: 30, 45, 60 days
- **Manufacturer**: 45, 60, 90 days
- **Distributor**: 14, 21, 30 days
- **Local**: 7, 14, 21 days

## 🛡️ Safety Features

- **Duplicate Prevention**: Checks for existing data before seeding
- **Batch Processing**: Creates data in batches for better performance
- **Error Handling**: Comprehensive error handling and logging
- **Rollback Safe**: Can be run multiple times safely

## 📈 Verification Output

The verification script provides:
- Total supplier count
- Distribution by type and status
- Contact statistics
- Sample supplier data
- Geographic distribution
- Data quality metrics

## 🎉 Sample Output

```
📊 Total Suppliers: 100

📈 Distribution by Type:
  PBF: 60 (60.0%)
  DISTRIBUTOR: 25 (25.0%)
  MANUFACTURER: 10 (10.0%)
  LOCAL: 5 (5.0%)

👥 Contact Information:
  Total Contacts: 184
  Primary Contacts: 100
  Active Contacts: 172

🗺️ Top 5 Provinces:
  Kalimantan Timur: 13 suppliers
  DKI Jakarta: 11 suppliers
  Jawa Barat: 10 suppliers
```

## 🔄 Re-seeding

To re-seed the data:
1. Clear existing supplier data from database
2. Run the seeder again: `npm run seed:suppliers`

The seeder automatically skips if data already exists to prevent duplicates.

---

**Created for Indonesian Pharmacy Management System**  
*Realistic data for development and testing purposes*
