import { PrismaClient, UserRole } from '@prisma/client';
import * as bcrypt from 'bcrypt';
import { seedAllSupplierData } from './seed-suppliers';
import { seedAllProductData } from './seed-products';
import { seedAllInventoryData } from './seed-inventory';
import { seedAllSalesCustomerData } from './seed-sales-customers';

const prisma = new PrismaClient();

async function seedApotekSettings() {
  console.log('🌱 Seeding Apotek Settings...');

  const settings = [
    {
      settingKey: 'pharmacyName',
      settingValue: 'Apotek Sehat Bersama',
    },
    {
      settingKey: 'pharmacyDescription',
      settingValue: 'Apotek terpercaya yang melayani kebutuhan kesehatan masyarakat dengan obat-obatan berkualitas dan pelayanan profesional.',
    },
    {
      settingKey: 'pharmacyAddress',
      settingValue: 'J<PERSON>. <PERSON><PERSON><PERSON><PERSON>. 123, <PERSON><PERSON><PERSON><PERSON>, Kecamatan Sejahtera, Jakarta Selatan 12345',
    },
    {
      settingKey: 'pharmacyPhone',
      settingValue: '+62 21 1234 5678',
    },
    {
      settingKey: 'pharmacyLicense',
      settingValue: 'SIA.503/31.74.02.1009.13.07/2024',
    },
    {
      settingKey: 'operatingHours',
      settingValue: 'Senin - Sabtu: 08:00 - 22:00 WIB, Minggu: 09:00 - 21:00 WIB',
    },
    {
      settingKey: 'emergencyContact',
      settingValue: '+62 812 3456 7890',
    },
    // Tax Configuration Settings
    {
      settingKey: 'tax.ppn.rate',
      settingValue: '11', // Current PPN rate (11% until Dec 31, 2024)
    },
    {
      settingKey: 'tax.ppn.effectiveFrom',
      settingValue: '2024-01-01T00:00:00.000Z',
    },
    {
      settingKey: 'tax.ppn.effectiveTo',
      settingValue: '2024-12-31T23:59:59.999Z',
    },
    {
      settingKey: 'tax.ppn.isActive',
      settingValue: 'true',
    },
    {
      settingKey: 'tax.ppn.description',
      settingValue: 'Pajak Pertambahan Nilai (PPN) - Tarif 11%',
    },
    {
      settingKey: 'tax.default.type',
      settingValue: 'PPN',
    },
    {
      settingKey: 'tax.calculation.mode',
      settingValue: 'exclusive', // Default to exclusive calculation
    },
  ];

  for (const setting of settings) {
    await prisma.appSettings.upsert({
      where: { settingKey: setting.settingKey },
      update: { settingValue: setting.settingValue },
      create: setting,
    });
  }

  console.log('✅ Apotek Settings seeded successfully!');
}

async function seedTaxConfiguration2025() {
  console.log('🌱 Seeding 2025 Tax Configuration...');

  // Add 2025 PPN rate configuration (12% starting January 1, 2025)
  const tax2025Settings = [
    {
      settingKey: 'tax.ppn.rate.2025',
      settingValue: '12', // New PPN rate starting 2025
    },
    {
      settingKey: 'tax.ppn.effectiveFrom.2025',
      settingValue: '2025-01-01T00:00:00.000Z',
    },
    {
      settingKey: 'tax.ppn.effectiveTo.2025',
      settingValue: null, // No end date - remains active indefinitely unless changed
    },
    {
      settingKey: 'tax.ppn.isActive.2025',
      settingValue: 'true', // Active configuration for 2025 and beyond
    },
    {
      settingKey: 'tax.ppn.description.2025',
      settingValue: 'Pajak Pertambahan Nilai (PPN) - Tarif 12% (berlaku mulai 1 Januari 2025)',
    },
  ];

  for (const setting of tax2025Settings) {
    await prisma.appSettings.upsert({
      where: { settingKey: setting.settingKey },
      update: { settingValue: setting.settingValue },
      create: setting,
    });
  }

  console.log('✅ 2025 Tax Configuration seeded successfully!');
}

async function seedUser() {
  console.log('🌱 Seeding User...');

  const password = bcrypt.hashSync('password123', 12)

  const users = [{
    email: '<EMAIL>',
    password: password,
    firstName: 'Admin',
    lastName: 'Apotek',
    role: UserRole.ADMIN,
  },
  {
    email: '<EMAIL>',
    password: password,
    firstName: 'Pharmacist',
    lastName: 'Apotek',
    role: UserRole.PHARMACIST,
  },
  {
    email: '<EMAIL>',
    password: password,
    firstName: 'Cashier',
    lastName: 'Apotek',
    role: UserRole.CASHIER,
  }];

  for (const user of users) {
    await prisma.user.upsert({
      where: { email: user.email },
      update: user,
      create: user,
    });
  }

  console.log('✅ User seeded successfully!');
}

async function main() {
  try {
    await seedApotekSettings();
    await seedTaxConfiguration2025();
    await seedUser();
    await seedAllSupplierData();
    await seedAllProductData();
    await seedAllInventoryData();
    await seedAllSalesCustomerData();
  } catch (error) {
    console.error('❌ Error seeding data:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  main();
}

export { seedApotekSettings };
