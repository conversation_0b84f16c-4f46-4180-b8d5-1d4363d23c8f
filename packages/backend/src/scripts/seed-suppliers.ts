import { PrismaClient, SupplierType, SupplierStatus, PaymentMethod } from '@prisma/client';

const prisma = new PrismaClient();

// Indonesian pharmaceutical company names
const COMPANY_NAMES = {
  PBF: [
    'PT Kimia Farma Trading & Distribution',
    'PT Kalbe Farma',
    'PT Indofarma Global Medika',
    'PT Pharos Indonesia',
    'PT Tempo Scan Pacific',
    'PT Sanbe Farma',
    'PT Dexa Medica',
    'PT Combiphar',
    'PT Mahakam Beta Farma',
    'PT Pyridam Farma',
    'PT Darya-Varia Laboratoria',
    'PT Bernofarm',
    'PT Novell Pharmaceutical Laboratories',
    'PT Merck Indonesia',
    'PT Bayer Indonesia',
    'PT Pfizer Indonesia',
    'PT Sanofi-Aventis Indonesia',
    'PT Novartis Indonesia',
    'PT Roche Indonesia',
    'PT Abbott Indonesia',
    'PT Boehringer Ingelheim Indonesia',
    'PT Merck Sharp & Dohme Indonesia',
    'PT AstraZeneca Indonesia',
    'PT GlaxoSmithKline Indonesia',
    'PT Johnson & Johnson Indonesia',
    'PT Takeda Indonesia',
    'PT Eisai Indonesia',
    'PT Dankos Farma',
    'PT Pratapa Nirmala',
    'PT Surya Dermato Medica Laboratories'
  ],
  DISTRIBUTOR: [
    'PT Distribusi Farmasi Nusantara',
    'PT Rajawali Nusindo',
    'PT Anugrah Pharmindo Lestari',
    'PT Enseval Putera Megatrading',
    'PT Tri Sapta Jaya',
    'PT Millennium Pharmacon International',
    'PT Dipa Pharmalab Intersains',
    'PT Hexpharm Jaya Laboratories',
    'PT Ifars Pharmaceutical Laboratories',
    'PT Interbat',
    'PT Lapi Laboratories',
    'PT Metiska Farma',
    'PT Nufarindo',
    'PT Otto Pharmaceutical Industries',
    'PT Phapros',
    'PT Prafa',
    'PT Sampharindo Perdana',
    'PT Soho Industri Pharmasi',
    'PT Yarindo Farmatama'
  ],
  MANUFACTURER: [
    'PT Industri Farmasi Nusantara',
    'PT Pabrik Obat Indonesia',
    'PT Manufaktur Farmasi Sejahtera',
    'PT Produksi Medika Utama',
    'PT Laboratorium Farmasi Maju',
    'PT Pabrik Kimia Farma',
    'PT Industri Obat Tradisional',
    'PT Manufaktur Herbal Indonesia'
  ],
  LOCAL: [
    'CV Apotek Supplier Lokal',
    'UD Farmasi Berkah',
    'CV Medika Jaya',
    'PT Supplier Obat Nusantara',
    'CV Kesehatan Prima',
    'UD Farmasi Sejahtera',
    'CV Apotek Mandiri',
    'PT Grosir Farmasi Indonesia'
  ]
};

// Indonesian provinces and major cities
const PROVINCES_CITIES = [
  { province: 'DKI Jakarta', cities: ['Jakarta Pusat', 'Jakarta Selatan', 'Jakarta Timur', 'Jakarta Barat', 'Jakarta Utara'] },
  { province: 'Jawa Barat', cities: ['Bandung', 'Bekasi', 'Depok', 'Bogor', 'Tangerang', 'Cimahi'] },
  { province: 'Jawa Tengah', cities: ['Semarang', 'Solo', 'Yogyakarta', 'Magelang', 'Salatiga'] },
  { province: 'Jawa Timur', cities: ['Surabaya', 'Malang', 'Kediri', 'Blitar', 'Madiun', 'Mojokerto'] },
  { province: 'Sumatera Utara', cities: ['Medan', 'Binjai', 'Tebing Tinggi', 'Pematangsiantar'] },
  { province: 'Sumatera Barat', cities: ['Padang', 'Bukittinggi', 'Payakumbuh', 'Sawahlunto'] },
  { province: 'Sumatera Selatan', cities: ['Palembang', 'Prabumulih', 'Lubuklinggau', 'Pagar Alam'] },
  { province: 'Kalimantan Timur', cities: ['Balikpapan', 'Samarinda', 'Bontang', 'Tarakan'] },
  { province: 'Sulawesi Selatan', cities: ['Makassar', 'Parepare', 'Palopo'] },
  { province: 'Bali', cities: ['Denpasar', 'Singaraja', 'Tabanan', 'Gianyar'] }
];

// Indonesian bank names
const INDONESIAN_BANKS = [
  'Bank Central Asia (BCA)',
  'Bank Mandiri',
  'Bank Rakyat Indonesia (BRI)',
  'Bank Negara Indonesia (BNI)',
  'Bank CIMB Niaga',
  'Bank Danamon',
  'Bank Permata',
  'Bank Maybank Indonesia',
  'Bank OCBC NISP',
  'Bank Panin',
  'Bank Mega',
  'Bank Bukopin',
  'Bank Sinarmas'
];

// Indonesian first and last names
const INDONESIAN_NAMES = {
  first: [
    'Ahmad', 'Budi', 'Sari', 'Dewi', 'Andi', 'Rini', 'Agus', 'Sri', 'Dedi', 'Lina',
    'Hendra', 'Maya', 'Rizki', 'Indira', 'Fajar', 'Ratna', 'Bambang', 'Sinta', 'Wahyu', 'Eka',
    'Dimas', 'Putri', 'Arief', 'Novi', 'Teguh', 'Yuni', 'Rudi', 'Fitri', 'Eko', 'Dian'
  ],
  last: [
    'Pratama', 'Sari', 'Wijaya', 'Kusuma', 'Santoso', 'Wibowo', 'Setiawan', 'Handayani',
    'Permana', 'Lestari', 'Nugroho', 'Rahayu', 'Susanto', 'Maharani', 'Gunawan', 'Safitri',
    'Kurniawan', 'Anggraini', 'Hidayat', 'Puspita', 'Firmansyah', 'Kartika', 'Suryanto', 'Melati'
  ]
};

// Contact positions
const CONTACT_POSITIONS = [
  'Sales Manager',
  'Account Manager',
  'Business Development Manager',
  'Regional Sales Manager',
  'Key Account Manager',
  'Sales Representative',
  'Customer Service Manager',
  'Marketing Manager',
  'Product Manager',
  'Area Sales Manager'
];

// Utility functions
function getRandomElement<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)];
}

function getRandomElements<T>(array: T[], count: number): T[] {
  const shuffled = [...array].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
}

function generateNPWP(): string {
  const digits = Array.from({ length: 15 }, () => Math.floor(Math.random() * 10));
  return `${digits.slice(0, 2).join('')}.${digits.slice(2, 5).join('')}.${digits.slice(5, 8).join('')}.${digits[8]}-${digits.slice(9, 12).join('')}.${digits.slice(12, 15).join('')}`;
}

function generateLicenseNumber(type: SupplierType): string {
  const year = new Date().getFullYear();
  const randomNum = Math.floor(Math.random() * 9999).toString().padStart(4, '0');
  
  switch (type) {
    case 'PBF':
      return `PBF.${randomNum}/31.74.02.1009.13.07/${year}`;
    case 'MANUFACTURER':
      return `IFI.${randomNum}/31.74.02.1009.13.07/${year}`;
    case 'DISTRIBUTOR':
      return `DIST.${randomNum}/31.74.02.1009.13.07/${year}`;
    default:
      return `SIA.${randomNum}/31.74.02.1009.13.07/${year}`;
  }
}

function generatePharmacyLicense(type: SupplierType): string {
  const year = new Date().getFullYear();
  const randomNum = Math.floor(Math.random() * 9999).toString().padStart(4, '0');
  
  if (type === 'PBF') {
    return `PBF.${randomNum}/DINKES/31.74/${year}`;
  }
  return `SIA.${randomNum}/DINKES/31.74/${year}`;
}

function generatePhoneNumber(): string {
  const prefixes = ['021', '022', '024', '031', '061', '0274', '0341'];
  const prefix = getRandomElement(prefixes);
  const number = Math.floor(Math.random() * ********) + ********;
  return `+62 ${prefix} ${number.toString().slice(0, 4)} ${number.toString().slice(4)}`;
}

function generateMobileNumber(): string {
  const prefixes = ['812', '813', '821', '822', '851', '852', '853'];
  const prefix = getRandomElement(prefixes);
  const number = Math.floor(Math.random() * ********) + ********;
  return `+62 ${prefix} ${number.toString().slice(0, 4)} ${number.toString().slice(4)}`;
}

function generateBankAccount(): string {
  return (Math.floor(Math.random() * ****************) + ****************).toString();
}

function generatePostalCode(): string {
  return (Math.floor(Math.random() * 90000) + 10000).toString();
}

function generateEmail(companyName: string, contactName?: string): string {
  const domain = companyName
    .toLowerCase()
    .replace(/pt\s+/g, '')
    .replace(/cv\s+/g, '')
    .replace(/ud\s+/g, '')
    .replace(/\s+/g, '')
    .replace(/[^a-z0-9]/g, '')
    .slice(0, 15);
  
  if (contactName) {
    const name = contactName.toLowerCase().replace(/\s+/g, '.');
    return `${name}@${domain}.co.id`;
  }
  
  return `info@${domain}.co.id`;
}

function generateWebsite(companyName: string): string {
  const domain = companyName
    .toLowerCase()
    .replace(/pt\s+/g, '')
    .replace(/cv\s+/g, '')
    .replace(/ud\s+/g, '')
    .replace(/\s+/g, '')
    .replace(/[^a-z0-9]/g, '')
    .slice(0, 15);
  
  return `https://www.${domain}.co.id`;
}

function generateSupplierCode(type: SupplierType, index: number): string {
  const typePrefix = {
    PBF: 'PBF',
    MANUFACTURER: 'MFG',
    DISTRIBUTOR: 'DST',
    LOCAL: 'LOC'
  };
  
  return `${typePrefix[type]}${(index + 1).toString().padStart(3, '0')}`;
}

function generateCreditLimit(type: SupplierType): number {
  const ranges = {
    PBF: { min: ********0, max: ********* }, // 100M - 500M
    MANUFACTURER: { min: *********, max: ********00 }, // 200M - 1B
    DISTRIBUTOR: { min: 50000000, max: ********* }, // 50M - 300M
    LOCAL: { min: ********, max: ********0 } // 10M - 100M
  };
  
  const range = ranges[type];
  return Math.floor(Math.random() * (range.max - range.min + 1)) + range.min;
}

function generatePaymentTerms(type: SupplierType): number {
  const terms = {
    PBF: [30, 45, 60],
    MANUFACTURER: [45, 60, 90],
    DISTRIBUTOR: [14, 21, 30],
    LOCAL: [7, 14, 21]
  };
  
  return getRandomElement(terms[type]);
}

export async function seedSuppliers() {
  console.log('🌱 Seeding Suppliers...');

  // Check if suppliers already exist
  const existingCount = await prisma.supplier.count();
  if (existingCount > 0) {
    console.log(`⚠️  Found ${existingCount} existing suppliers. Skipping seeding to avoid duplicates.`);
    console.log('💡 To re-seed, please clear the suppliers table first.');
    return;
  }

  const suppliers: any[] = [];
  const supplierTypes: SupplierType[] = ['PBF', 'DISTRIBUTOR', 'MANUFACTURER', 'LOCAL'];
  
  // Distribution: 60% PBF, 25% DISTRIBUTOR, 10% MANUFACTURER, 5% LOCAL
  const typeDistribution = [
    ...Array(60).fill('PBF'),
    ...Array(25).fill('DISTRIBUTOR'),
    ...Array(10).fill('MANUFACTURER'),
    ...Array(5).fill('LOCAL')
  ] as SupplierType[];

  // Status distribution: 80% ACTIVE, 15% INACTIVE, 5% SUSPENDED
  const statusDistribution = [
    ...Array(80).fill('ACTIVE'),
    ...Array(15).fill('INACTIVE'),
    ...Array(5).fill('SUSPENDED')
  ] as SupplierStatus[];

  for (let i = 0; i < 100; i++) {
    const type = typeDistribution[i];
    const status = getRandomElement(statusDistribution);
    const companyName = getRandomElement(COMPANY_NAMES[type]);
    const location = getRandomElement(PROVINCES_CITIES);
    const city = getRandomElement(location.cities);
    const bank = getRandomElement(INDONESIAN_BANKS);
    
    const supplier = {
      code: generateSupplierCode(type, i),
      name: companyName,
      type,
      status,
      address: `Jl. ${getRandomElement(['Sudirman', 'Thamrin', 'Gatot Subroto', 'Kuningan', 'Senayan', 'Kemang', 'Menteng', 'Kelapa Gading'])} No. ${Math.floor(Math.random() * 200) + 1}`,
      city,
      province: location.province,
      postalCode: generatePostalCode().toString(),
      phone: generatePhoneNumber(),
      email: generateEmail(companyName),
      website: generateWebsite(companyName),
      npwp: generateNPWP(),
      licenseNumber: generateLicenseNumber(type),
      pharmacyLicense: generatePharmacyLicense(type),
      creditLimit: generateCreditLimit(type),
      paymentTerms: generatePaymentTerms(type),
      preferredPayment: getRandomElement(['TRANSFER', 'CREDIT', 'CASH', 'GIRO'] as PaymentMethod[]),
      bankName: bank,
      bankAccount: generateBankAccount().toString(),
      bankAccountName: companyName,
      notes: `${type} supplier dengan spesialisasi distribusi produk farmasi dan kesehatan.`
    };

    suppliers.push(supplier);
  }

  // Create suppliers in batches
  const batchSize = 10;
  for (let i = 0; i < suppliers.length; i += batchSize) {
    const batch = suppliers.slice(i, i + batchSize);
    await prisma.supplier.createMany({
      data: batch,
      skipDuplicates: true
    });
    console.log(`📦 Created suppliers batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(suppliers.length / batchSize)}`);
  }

  console.log(`✅ Successfully seeded ${suppliers.length} suppliers!`);

  // Now seed contacts for each supplier
  await seedSupplierContacts();
}

export async function seedSupplierContacts() {
  console.log('🌱 Seeding Supplier Contacts...');

  // Get all suppliers
  const suppliers = await prisma.supplier.findMany({
    select: { id: true, name: true }
  });

  if (suppliers.length === 0) {
    console.log('⚠️  No suppliers found. Please seed suppliers first.');
    return;
  }

  // Check if contacts already exist
  const existingContactsCount = await prisma.supplierContact.count();
  if (existingContactsCount > 0) {
    console.log(`⚠️  Found ${existingContactsCount} existing contacts. Skipping contact seeding.`);
    return;
  }

  const contacts: any[] = [];

  for (const supplier of suppliers) {
    // Generate 1-3 contacts per supplier
    const contactCount = Math.floor(Math.random() * 3) + 1;

    for (let i = 0; i < contactCount; i++) {
      const firstName = getRandomElement(INDONESIAN_NAMES.first);
      const lastName = getRandomElement(INDONESIAN_NAMES.last);
      const fullName = `${firstName} ${lastName}`;
      const position = getRandomElement(CONTACT_POSITIONS);
      const isPrimary = i === 0; // First contact is always primary

      const contact = {
        supplierId: supplier.id,
        name: fullName,
        position,
        phone: generateMobileNumber(),
        email: generateEmail(supplier.name, fullName),
        isPrimary,
        isActive: Math.random() > 0.1, // 90% active contacts
        notes: isPrimary ? 'Kontak utama untuk komunikasi bisnis' : 'Kontak pendukung untuk koordinasi operasional'
      };

      contacts.push(contact);
    }
  }

  // Create contacts in batches
  const batchSize = 20;
  for (let i = 0; i < contacts.length; i += batchSize) {
    const batch = contacts.slice(i, i + batchSize);
    await prisma.supplierContact.createMany({
      data: batch,
      skipDuplicates: true
    });
    console.log(`👥 Created contacts batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(contacts.length / batchSize)}`);
  }

  console.log(`✅ Successfully seeded ${contacts.length} supplier contacts!`);
}

// Main seeding function
export async function seedAllSupplierData() {
  try {
    await seedSuppliers();
    console.log('🎉 All supplier data seeded successfully!');
  } catch (error) {
    console.error('❌ Error seeding supplier data:', error);
    throw error;
  }
}

// CLI execution
async function main() {
  try {
    await seedAllSupplierData();
  } catch (error) {
    console.error('❌ Seeding failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  main();
}
