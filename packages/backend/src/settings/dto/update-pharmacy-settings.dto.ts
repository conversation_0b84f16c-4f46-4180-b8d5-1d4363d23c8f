import { IsString, IsOptional } from 'class-validator';

export class UpdatePharmacySettingsDto {
  @IsOptional()
  @IsString()
  pharmacyName?: string;

  @IsOptional()
  @IsString()
  pharmacyDescription?: string;

  @IsOptional()
  @IsString()
  pharmacyAddress?: string;

  @IsOptional()
  @IsString()
  pharmacyPhone?: string;

  @IsOptional()
  @IsString()
  pharmacyLicense?: string;

  @IsOptional()
  @IsString()
  operatingHours?: string;

  @IsOptional()
  @IsString()
  emergencyContact?: string;
}
