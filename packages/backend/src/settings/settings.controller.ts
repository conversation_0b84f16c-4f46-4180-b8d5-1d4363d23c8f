import {
  Controller,
  Get,
  Put,
  Body,
  UseGuards,
} from '@nestjs/common';
import { SettingsService } from './settings.service';
import { UpdatePharmacySettingsDto } from './dto/update-pharmacy-settings.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AdminGuard } from './guards/admin.guard';

@Controller('settings')
@UseGuards(JwtAuthGuard)
export class SettingsController {
  constructor(private settingsService: SettingsService) {}

  @Get('pharmacy')
  async getPharmacySettings() {
    return this.settingsService.getPharmacySettings();
  }

  @Put('pharmacy')
  @UseGuards(AdminGuard)
  async updatePharmacySettings(@Body() updateDto: UpdatePharmacySettingsDto) {
    return this.settingsService.updatePharmacySettings(updateDto);
  }
}
