import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { UpdatePharmacySettingsDto } from './dto/update-pharmacy-settings.dto';

@Injectable()
export class SettingsService {
  constructor(private prisma: PrismaService) {}

  async getPharmacySettings() {
    const settings = await this.prisma.appSettings.findMany();
    
    // Convert array to object for easier access
    const settingsObject = settings.reduce((acc, setting) => {
      acc[setting.settingKey] = setting.settingValue;
      return acc;
    }, {} as Record<string, string | null>);

    return settingsObject;
  }

  async updatePharmacySettings(updateDto: UpdatePharmacySettingsDto) {
    const updates: Promise<any>[] = [];

    for (const [key, value] of Object.entries(updateDto)) {
      if (value !== undefined) {
        updates.push(
          this.prisma.appSettings.upsert({
            where: { settingKey: key },
            update: { settingValue: value },
            create: { settingKey: key, settingValue: value },
          })
        );
      }
    }

    await Promise.all(updates);
    return this.getPharmacySettings();
  }

  async getSetting(key: string) {
    const setting = await this.prisma.appSettings.findUnique({
      where: { settingKey: key },
    });
    return setting?.settingValue || null;
  }

  async updateSetting(key: string, value: string) {
    return this.prisma.appSettings.upsert({
      where: { settingKey: key },
      update: { settingValue: value },
      create: { settingKey: key, settingValue: value },
    });
  }
}
