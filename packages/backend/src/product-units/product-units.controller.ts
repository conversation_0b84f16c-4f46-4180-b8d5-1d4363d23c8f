import {
  Controller,
  Get,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ProductUnitsService } from './product-units.service';
import { ProductUnitQueryDto } from './dto/product-unit-query.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@Controller('product-units')
@UseGuards(JwtAuthGuard)
export class ProductUnitsController {
  constructor(private readonly productUnitsService: ProductUnitsService) {}

  @Get()
  async findAll(@Query() query: ProductUnitQueryDto) {
    return this.productUnitsService.findAll(query);
  }

  @Get('base-units')
  async findBaseUnits() {
    return this.productUnitsService.findBaseUnits();
  }

  @Get('by-type')
  async findByType(@Query('type') type?: string) {
    return this.productUnitsService.findByType(type);
  }
}
