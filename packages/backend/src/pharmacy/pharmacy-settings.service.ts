import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';

export interface PharmacyInfo {
  name: string;
  address: string;
  phone: string;
  email: string;
  license: string;
  pharmacist: string;
  taxId?: string;
}

@Injectable()
export class PharmacySettingsService {
  constructor(private prisma: PrismaService) { }

  /**
   * Get pharmacy information from database settings
   */
  async getPharmacyInfo(): Promise<PharmacyInfo> {
    try {
      // Get all pharmacy-related settings from database
      const settings = await this.prisma.appSettings.findMany({
        where: {
          settingKey: {
            in: [
              'pharmacyName',
              'pharmacyAddress',
              'pharmacyPhone',
              'pharmacyEmail',
              'pharmacyLicense',
              'pharmacyPharmacist',
              'pharmacyTaxId',
            ],
          },
        },
      });

      // Convert to key-value map
      const settingsMap = settings.reduce((acc, setting) => {
        if (setting.settingValue) {
          acc[setting.settingKey] = setting.settingValue;
        }
        return acc;
      }, {} as Record<string, string>);

      return {
        name: settingsMap['pharmacyName'] || 'Apotek Sehat Bersama',
        address: settingsMap['pharmacyAddress'] || 'Jl. Kesehatan No. 123, Jakarta Selatan 12345',
        phone: settingsMap['pharmacyPhone'] || '(021) 1234-5678',
        email: settingsMap['pharmacyEmail'] || '<EMAIL>',
        license: settingsMap['pharmacyLicense'] || 'SIA.503/31.74.02.1009.13.07/2023',
        pharmacist: settingsMap['pharmacyPharmacist'] || 'apt. Dr. Sarah Wijaya, S.Farm',
        taxId: settingsMap['pharmacyTaxId'] || '01.234.567.8-901.000',
      };
    } catch (error) {
      // Fallback to default values if database is not available
      return {
        name: 'Apotek Sehat Bersama',
        address: 'Jl. Kesehatan No. 123, Jakarta Selatan 12345',
        phone: '(021) 1234-5678',
        email: '<EMAIL>',
        license: 'SIA.503/31.74.02.1009.13.07/2023',
        pharmacist: 'apt. Dr. Sarah Wijaya, S.Farm',
        taxId: '01.234.567.8-901.000',
      };
    }
  }

  /**
   * Update pharmacy information in database settings
   */
  async updatePharmacyInfo(info: Partial<PharmacyInfo>): Promise<PharmacyInfo> {
    // Map the info fields to setting keys
    const fieldMapping = {
      name: 'pharmacyName',
      address: 'pharmacyAddress',
      phone: 'pharmacyPhone',
      email: 'pharmacyEmail',
      license: 'pharmacyLicense',
      pharmacist: 'pharmacyPharmacist',
      taxId: 'pharmacyTaxId',
    };

    // Execute updates in a transaction
    await this.prisma.$transaction(async (tx) => {
      for (const [field, value] of Object.entries(info)) {
        if (value !== undefined && fieldMapping[field as keyof PharmacyInfo]) {
          await tx.appSettings.upsert({
            where: { settingKey: fieldMapping[field as keyof PharmacyInfo] },
            update: { settingValue: value },
            create: {
              settingKey: fieldMapping[field as keyof PharmacyInfo],
              settingValue: value,
            },
          });
        }
      }
    });

    // Return the updated pharmacy info
    return this.getPharmacyInfo();
  }

  /**
   * Get receipt formatting preferences
   */
  async getReceiptSettings() {
    return {
      paperWidth: process.env.RECEIPT_PAPER_WIDTH || '58mm',
      showLogo: process.env.RECEIPT_SHOW_LOGO === 'true',
      showTaxInfo: process.env.RECEIPT_SHOW_TAX_INFO !== 'false',
      showPharmacist: process.env.RECEIPT_SHOW_PHARMACIST !== 'false',
      footerMessage: process.env.RECEIPT_FOOTER_MESSAGE || 'Terima kasih atas kunjungan Anda',
    };
  }
}
