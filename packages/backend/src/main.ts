import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Logger } from 'nestjs-pino';
import { AppModule } from './app.module';
import { BigIntSerializerInterceptor, GlobalExceptionFilter, createStandaloneLogger } from './common/utils';

async function bootstrap() {
  try {
    const app = await NestFactory.create(AppModule, { bufferLogs: true });
    const configService = app.get(ConfigService);

    // Use Pino logger
    app.useLogger(app.get(Logger));
    const logger = app.get(Logger);

    // Global exception filter for 500 errors and comprehensive error handling
    app.useGlobalFilters(app.get(GlobalExceptionFilter));

    // Enable CORS
    app.enableCors({
      origin: ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:3002', 'http://127.0.0.1:3000'],
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
      allowedHeaders: ['Content-Type', 'Authorization'],
      credentials: true,
    });

    // Global validation pipe
    app.useGlobalPipes(
      new ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
      }),
    );

    // Apply BigInt serializer interceptor globally
    app.useGlobalInterceptors(new BigIntSerializerInterceptor());

    // Global prefix
    app.setGlobalPrefix('api');

    const port = configService.get<number>('PORT') || 3001;

    await app.listen(port);

    logger.log(`🚀 Backend server berhasil berjalan di http://localhost:${port}/api`);
    logger.log(`🔧 Environment: ${process.env.NODE_ENV || 'development'}`);
    logger.log(`📊 Global exception filter aktif untuk error handling`);
    logger.log(`🛡️ Global validation pipe aktif`);
    logger.log(`🔄 BigInt serializer interceptor aktif`);

  } catch (error) {
    console.error('❌ Gagal memulai server:', error);
    process.exit(1);
  }
}

// Global error handlers for unhandled rejections and uncaught exceptions
process.on('unhandledRejection', (reason, promise) => {
  console.error('🚨 Unhandled Promise Rejection:', {
    reason: reason instanceof Error ? reason.message : reason,
    stack: reason instanceof Error ? reason.stack : undefined,
    promise: promise.toString(),
    timestamp: new Date().toISOString(),
  });

  // In production, you might want to gracefully shutdown
  if (process.env.NODE_ENV === 'production') {
    console.error('🔄 Gracefully shutting down due to unhandled rejection...');
    process.exit(1);
  }
});

process.on('uncaughtException', (error) => {
  console.error('🚨 Uncaught Exception:', {
    message: error.message,
    stack: error.stack,
    name: error.name,
    timestamp: new Date().toISOString(),
  });

  // Always exit on uncaught exceptions
  console.error('🔄 Shutting down due to uncaught exception...');
  process.exit(1);
});

// Graceful shutdown handlers
process.on('SIGTERM', () => {
  console.log('🔄 SIGTERM received, shutting down gracefully...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('🔄 SIGINT received, shutting down gracefully...');
  process.exit(0);
});

bootstrap();
