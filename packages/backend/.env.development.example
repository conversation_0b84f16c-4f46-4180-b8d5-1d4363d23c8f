# Development Environment Configuration Example
NODE_ENV=development
PORT=3001

# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/pharmacy_dev"

# JWT Configuration
JWT_SECRET="your-development-jwt-secret-key"
JWT_EXPIRES_IN="7d"

# Throttling Configuration
THROTTLE_TTL=60000
THROTTLE_LIMIT=100  # More lenient for development

# Logging Configuration
LOG_LEVEL="trace"  # Log ALL levels in development for comprehensive debugging
LOGTAIL_TOKEN=""  # Usually empty in development
LOGTAIL_INGESTING_HOST=""  # Usually empty in development

# Log File Rotation Configuration
LOG_DIRECTORY="./logs"
LOG_MAX_SIZE="5m"  # Smaller files in development for easier debugging
LOG_FREQUENCY="daily"  # Daily rotation
LOG_RETENTION_DAYS=7  # Keep logs for 7 days in development

# Development-specific settings
# Enable detailed error messages and stack traces
DEBUG=true
VERBOSE_LOGGING=true
