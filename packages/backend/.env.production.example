# Production Environment Configuration Example
NODE_ENV=production
PORT=3001

# Database Configuration
DATABASE_URL="postgresql://username:password@host:port/database"

# JWT Configuration
JWT_SECRET="your-super-secure-jwt-secret-key-for-production"
JWT_EXPIRES_IN="7d"

# Throttling Configuration
THROTTLE_TTL=60000
THROTTLE_LIMIT=10

# Logging Configuration
LOG_LEVEL="info"
LOGTAIL_TOKEN="your-betterstack-logtail-token-here"
LOGTAIL_INGESTING_HOST="https://in.logtail.com"  # Optional: Custom Logtail ingesting endpoint

# Log File Rotation Configuration
LOG_DIRECTORY="./logs"
LOG_MAX_SIZE="10m"  # Maximum file size before rotation (e.g., 10m, 100k, 1g)
LOG_FREQUENCY="daily"  # Rotation frequency: daily or hourly
LOG_RETENTION_DAYS=30  # Number of days to keep log files before cleanup
