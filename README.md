# Apotek App - Complete Authentication System

A modern, full-stack pharmacy management system specifically designed for Indonesian pharmacy stores (apotek) with secure authentication built using Next.js, NestJS, PostgreSQL, Prisma, and shadcn/ui components.

This comprehensive system provides medicine inventory management, sales transactions, medicine catalog management, stock tracking, and other essential pharmacy operations tailored for Indonesian apotek businesses.

## 🏗️ Architecture

This is a **high-performance monorepo** powered by **Turborepo** containing:

- **Frontend**: Next.js 15 with TypeScript, Tailwind CSS v4, shadcn/ui components, and NextAuth.js
- **Backend**: NestJS with TypeScript, Prisma ORM, and JWT authentication
- **Database**: PostgreSQL
- **Build System**: Turborepo with intelligent caching and parallel execution

### 🚀 Turborepo Performance
- **99% faster cached builds** (28s → 243ms)
- **Intelligent caching** system with dependency awareness
- **Parallel task execution** across packages
- **Incremental builds** - only rebuild what changed

## 🚀 Features

### Core Apotek Management Features (Planned)
- 💊 **Medicine Inventory Management** - Complete stock tracking and management
- 🛒 **Sales Transactions** - Point-of-sale system for apotek operations
- 📋 **Medicine Catalog** - Comprehensive medicine database with details
- 📊 **Stock Tracking** - Real-time inventory monitoring and alerts
- 📈 **Sales Reports** - Analytics and reporting for business insights
- 👥 **Customer Management** - Patient records and prescription history
- 🏥 **Supplier Management** - Vendor relationships and purchase orders
- 💰 **Financial Management** - Revenue tracking and expense management

### Authentication System (Implemented)
- ✅ User registration with email/password
- ✅ Secure login with JWT tokens
- ✅ Password hashing with bcrypt
- ✅ Protected routes and session management
- ✅ Input validation and error handling
- ✅ Rate limiting for security
- ✅ CORS configuration

### Frontend Features (Implemented)
- ✅ Modern UI with Tailwind CSS v4
- ✅ **shadcn/ui Component System** - Professional, accessible UI components
- ✅ Custom shadcn/ui components adapted for Tailwind CSS v4 (Button, Input, Card, Label, etc.)
- ✅ Form validation with React Hook Form + Zod
- ✅ Session management with NextAuth.js
- ✅ Responsive design optimized for apotek workflows
- ✅ Loading states and error handling
- ✅ **Indonesian Language UI** - All user-facing text in Bahasa Indonesia with shadcn/ui foundation

### Backend Features (Implemented)
- ✅ RESTful API with NestJS
- ✅ Database integration with Prisma ORM
- ✅ JWT-based authentication
- ✅ Password validation and hashing
- ✅ Global validation pipes
- ✅ Environment configuration
- ✅ Rate limiting and security

### Localization & Language Standards
- 🇮🇩 **UI Text**: All user interface elements (buttons, labels, headers, messages, placeholders) in **Bahasa Indonesia**
- 🔧 **Code**: All technical elements (variables, functions, comments, API endpoints, database schemas) in **English**
- 🎨 **Component System**: shadcn/ui provides the foundation with Indonesian text overlays while maintaining English component code

## 📋 Prerequisites

Before running this project, make sure you have:

- **Node.js** (v18 or higher)
- **Bun.js** (v1.0.0 or higher)
- **PostgreSQL** database running
- **Git**
- **Turborepo** (automatically installed with dependencies)

## 🌐 Language & Localization Standards

This project follows strict language separation guidelines for Indonesian apotek operations:

### UI Text (Bahasa Indonesia)
All user-facing interface elements must be in Indonesian:

```typescript
// ✅ Correct - UI text in Indonesian using shadcn/ui components
<Button>Masuk</Button>                    // Login
<Label>Kata Sandi</Label>                 // Password
<h1>Selamat Datang di Apotek App</h1>     // Welcome to Apotek App
<p>Pendaftaran berhasil!</p>              // Registration successful!
<Input placeholder="Masukkan email Anda" /> // Enter your email
<Card>
  <CardHeader>
    <CardTitle>Informasi Obat</CardTitle>  // Medicine Information
  </CardHeader>
</Card>
```

### Code & Technical Content (English)
All technical elements must remain in English:

```typescript
// ✅ Correct - Code in English (including shadcn/ui component definitions)
const handleUserLogin = async (credentials: LoginCredentials) => {
  // Handle user authentication
  const response = await authService.login(credentials);
  return response;
};

// shadcn/ui component imports and usage
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

// API endpoints
POST /api/auth/login
GET /api/medicines/inventory
POST /api/sales/transactions

// Database schema
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  createdAt DateTime @default(now())
}
```

### Examples of Language Separation

| Element Type | Indonesian (UI) | English (Code) |
|--------------|----------------|----------------|
| shadcn/ui Button | `<Button>Simpan Data</Button>` | `const saveData = () => {}` |
| shadcn/ui Label | `<Label>Nama Obat</Label>` | `medicineName: string` |
| Error Message | "Email tidak valid" | `INVALID_EMAIL_ERROR` |
| Card Title | `<CardTitle>Manajemen Inventori</CardTitle>` | `InventoryManagement` |
| API Response | "Data berhasil disimpan" | `data_saved_successfully` |
| Component Import | N/A | `import { Button } from "@/components/ui/button"` |

## 🎨 shadcn/ui Component System

This project uses **shadcn/ui** as the foundation for all UI components, providing:

### Component Architecture
- **Base Components**: Professional, accessible components built on Radix UI primitives
- **Tailwind CSS v4 Compatibility**: Custom adaptations for the latest Tailwind CSS version
- **Indonesian Localization**: All component text and labels in Bahasa Indonesia
- **Type Safety**: Full TypeScript support with proper type definitions

### Available Components
The following shadcn/ui components have been implemented and adapted:

- **Button** - Primary actions, secondary actions, and variants
- **Input** - Form inputs with Indonesian placeholders
- **Card** - Content containers with CardHeader, CardContent, CardTitle, CardDescription
- **Label** - Form labels in Indonesian
- **Additional components** can be easily added following the same pattern

### Implementation Example
```typescript
// Component definition (English code)
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

// Usage with Indonesian text
<Card>
  <CardHeader>
    <CardTitle>Informasi Obat</CardTitle>
  </CardHeader>
  <CardContent>
    <Button>Simpan Perubahan</Button>
  </CardContent>
</Card>
```

### Customization for Apotek App
- **Indonesian Labels**: All user-facing text adapted for Indonesian apotek terminology
- **Apotek-Specific Styling**: Color schemes and spacing optimized for pharmacy workflows
- **Accessibility**: Maintained shadcn/ui accessibility standards with Indonesian screen reader support

## 🛠️ Installation & Setup

### 1. Clone the Repository
```bash
git clone <repository-url>
cd pharmacy-store
```

### 2. Install Dependencies
```bash
pnpm install
```

This will install all dependencies including **Turborepo** for optimized build performance.

### 3. Database Setup

Make sure PostgreSQL is running and create a database:
```sql
CREATE DATABASE apotek;
```

> **Note**: The database name "apotek" reflects the Indonesian terminology for pharmacy, while maintaining English for all technical schema and code elements.

### 4. Environment Configuration

#### Backend Environment (.env)
The backend `.env` file is already configured with:
```env
DATABASE_URL="postgresql://postgres:password@localhost:5432/apotek"
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_EXPIRES_IN="7d"
PORT=3001
NODE_ENV="development"
```

#### Frontend Environment (.env.local)
The frontend `.env.local` file is already configured with:
```env
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-nextauth-secret-change-this-in-production
NEXT_PUBLIC_API_URL=http://localhost:3001/api
```

**⚠️ Important**: Change the secrets in production!

### 5. Database Migration
```bash
pnpm db:push
```

## 🚀 Running the Application

### Development Mode (Turborepo)
Start both frontend and backend in development mode with Turborepo:
```bash
pnpm dev
```

This will start:
- **Backend**: http://localhost:3001/api
- **Frontend**: http://localhost:3000

Turborepo will run both services in parallel for optimal performance.

### Individual Services
You can also run services individually using Turborepo filtering:

```bash
# Backend only
pnpm turbo dev --filter=backend

# Frontend only
pnpm turbo dev --filter=frontend

# Or using pnpm workspace filtering
pnpm --filter backend dev
pnpm --filter frontend dev
```

## ⚡ Turborepo Performance & Caching

This monorepo uses **Turborepo** for exceptional build performance and developer experience.

### 🚀 Performance Metrics
- **First build**: ~28 seconds
- **Cached build**: ~243ms (99% faster!)
- **Cache hit rate**: 100% when no changes detected
- **Parallel execution**: Tasks run simultaneously when possible

### 🎯 Key Features
- **Intelligent Caching**: Build outputs cached based on file contents and dependencies
- **Dependency Awareness**: Tasks run in correct order based on package dependencies
- **Incremental Builds**: Only rebuild packages that have changed
- **Rich Terminal UI**: Real-time task monitoring with interactive interface
- **Remote Caching**: Ready for team collaboration (optional)

### 📊 Caching Strategy
```bash
# First build (cache miss)
pnpm turbo build
# ✓ Tasks: 2 successful, 2 total
# ✓ Cached: 0 cached, 2 total
# ✓ Time: 26.782s

# Second build (cache hit)
pnpm turbo build
# ✓ Tasks: 2 successful, 2 total
# ✓ Cached: 2 cached, 2 total
# ✓ Time: 243ms >>> FULL TURBO
```

### 🛠️ Development Workflow
```bash
# Start development (parallel execution)
pnpm turbo dev

# Build with caching
pnpm turbo build

# Run tests with caching
pnpm turbo test

# Target specific packages
pnpm turbo build --filter=frontend
pnpm turbo test --filter=backend

# Preview execution plan
pnpm turbo build --dry-run
```

## 📚 API Endpoints

### Authentication Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/api/auth/register` | User registration for apotek staff |
| POST | `/api/auth/login` | User login for apotek system |
| POST | `/api/auth/logout` | User logout |
| GET | `/api/auth/me` | Get current user profile |

### Future Apotek Management Endpoints (Planned)

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/medicines` | Get medicine catalog |
| POST | `/api/medicines` | Add new medicine |
| PUT | `/api/medicines/:id` | Update medicine details |
| GET | `/api/inventory` | Get current stock levels |
| POST | `/api/sales/transactions` | Process sales transaction |
| GET | `/api/sales/reports` | Get sales reports |
| GET | `/api/customers` | Get customer records |
| POST | `/api/suppliers/orders` | Create supplier order |

### Example API Usage

#### Register a new apotek staff user:
```bash
curl -X POST http://localhost:3001/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Budi Santoso",
    "email": "<EMAIL>",
    "password": "SecurePass123"
  }'
```

#### Login:
```bash
curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecurePass123"
  }'
```

## 🧪 Testing the Authentication Flow

1. **Start the application**: `pnpm dev`
2. **Open browser**: Navigate to http://localhost:3000
3. **Register**: Click "Buat Akun" (Create Account) using shadcn/ui Button component
4. **Login**: Use your credentials to sign in with Indonesian interface
5. **Dashboard**: Access the protected dashboard with shadcn/ui components
6. **Logout**: Sign out and verify redirect to login

## 📁 Project Structure

```
pharmacy-store/
├── packages/
│   ├── backend/                    # NestJS API for apotek operations
│   │   ├── src/
│   │   │   ├── auth/              # Authentication module
│   │   │   ├── suppliers/         # Supplier management
│   │   │   ├── settings/          # Application settings
│   │   │   ├── prisma/            # Database service
│   │   │   ├── medicines/         # Medicine management (planned)
│   │   │   ├── inventory/         # Stock tracking (planned)
│   │   │   ├── sales/             # Sales transactions (planned)
│   │   │   └── main.ts            # Application entry
│   │   ├── test/
│   │   │   └── integration/       # Comprehensive integration tests
│   │   ├── prisma/
│   │   │   └── schema.prisma      # Database schema
│   │   └── package.json
│   └── frontend/                  # Next.js App with Indonesian UI
│       ├── src/
│       │   ├── app/               # Pages with Indonesian interface
│       │   │   ├── auth/          # Authentication pages
│       │   │   ├── dashboard/     # Main apotek dashboard
│       │   │   │   └── suppliers/ # Supplier management pages
│       │   │   ├── obat/          # Medicine management (planned)
│       │   │   ├── inventori/     # Inventory management (planned)
│       │   │   └── penjualan/     # Sales management (planned)
│       │   ├── components/        # shadcn/ui components (Indonesian labels)
│       │   │   └── ui/           # shadcn/ui base components
│       │   └── lib/               # Utilities and helpers
│       └── package.json
├── .turbo/                        # Turborepo cache directory
├── turbo.json                     # Turborepo configuration
├── package.json                   # Root package.json with Turborepo scripts
├── pnpm-workspace.yaml           # Monorepo configuration
├── test-turborepo.sh             # Turborepo validation script
└── README.md                      # Documentation
```

## 🔧 Available Scripts

### Turborepo Commands (Recommended)
- `pnpm turbo dev` - Start both frontend and backend in parallel
- `pnpm turbo build` - Build both packages with intelligent caching
- `pnpm turbo test` - Run all tests with caching
- `pnpm turbo test:integration` - Run integration tests
- `pnpm turbo lint` - Lint all packages with caching
- `pnpm turbo clean` - Clean all build outputs

### Turborepo with Filtering
- `pnpm turbo build --filter=frontend` - Build only frontend
- `pnpm turbo build --filter=backend` - Build only backend
- `pnpm turbo dev --filter=backend` - Start only backend in dev mode
- `pnpm turbo test --filter=backend` - Run only backend tests

### Database Scripts
- `pnpm db:push` - Push database schema
- `pnpm db:migrate` - Run database migrations
- `pnpm db:studio` - Open Prisma Studio
- `pnpm db:seed` - Seed database with sample data

### Legacy pnpm Workspace Commands
- `pnpm --filter backend dev` - Start backend in development
- `pnpm --filter frontend dev` - Start frontend in development
- `pnpm --filter backend build` - Build backend only
- `pnpm --filter frontend build` - Build frontend only

### Performance Testing
- `pnpm turbo build --dry-run` - Preview execution plan
- `pnpm turbo build --force` - Force rebuild (bypass cache)

## 🔒 Security Features

- **Password Hashing**: bcrypt with salt rounds
- **JWT Tokens**: Secure token-based authentication
- **Input Validation**: Server-side validation with class-validator
- **Rate Limiting**: Protection against brute force attacks
- **CORS**: Configured for frontend integration
- **Environment Variables**: Sensitive data protection

## 🛡️ Password Requirements

- Minimum 8 characters
- At least one uppercase letter
- At least one lowercase letter  
- At least one number

## 🚨 Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Ensure PostgreSQL is running
   - Check database credentials in `.env`
   - Verify database exists

2. **Port Already in Use**
   - Backend: Change `PORT` in backend `.env`
   - Frontend: Use `pnpm dev -- -p 3001`

3. **Authentication Not Working**
   - Check `NEXTAUTH_SECRET` and `JWT_SECRET` are set
   - Verify API URL in frontend `.env.local`

## 🔄 Next Steps for Apotek App

This authentication system provides a solid foundation for your Indonesian apotek management system. Consider adding:

### Core Apotek Features
- **Medicine Inventory Management** - Complete stock tracking system
- **Sales Transaction System** - Point-of-sale for apotek operations
- **Medicine Catalog** - Comprehensive database with Indonesian medicine names
- **Stock Alerts** - Low inventory notifications
- **Sales Reports** - Daily, weekly, monthly analytics
- **Customer Management** - Patient records and prescription history
- **Supplier Integration** - Purchase order management
- **Prescription Management** - Digital prescription handling

### Enhanced Authentication
- Email verification for apotek staff
- Password reset functionality
- Role-based access control (Admin, Pharmacist, Cashier)
- Two-factor authentication for security
- User profile management with Indonesian interface

### Localization Enhancements
- Complete Indonesian translation for all UI elements
- Indonesian date/time formatting
- Indonesian currency (Rupiah) formatting
- Local pharmacy regulations compliance

## 📝 License

This project is for educational and development purposes.

---

**🎉 Selamat!** You now have a fully functional, high-performance authentication system ready for your Indonesian apotek management application. The foundation is set for building a comprehensive pharmacy management system with:

- ⚡ **Turborepo** for 99% faster builds and intelligent caching
- 🇮🇩 **Indonesian localization** for authentic apotek workflows
- 🎨 **shadcn/ui components** for professional, accessible interfaces
- 🧪 **Comprehensive testing** with 165 integration tests
- 🏗️ **Scalable monorepo architecture** ready for all essential apotek operations
